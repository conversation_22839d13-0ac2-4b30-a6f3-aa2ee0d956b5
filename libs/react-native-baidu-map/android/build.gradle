apply plugin: 'com.android.library'
apply plugin: 'maven'
apply plugin: 'signing'

group = "org.lovebing.reactnative"
archivesBaseName = "baidumap"
version = "1.1.0"

buildscript {
    repositories {
        mavenLocal()
        google()
        jcenter()
        maven() {
            url '/usr/local/node/lib/node_modules/react-native/android'
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.1.3'
    }
}

repositories {
    mavenLocal()
    google()
    jcenter()
    maven {
        url '/usr/local/node/lib/node_modules/react-native/android'
    }
}

android {
    compileSdkVersion 28
    buildToolsVersion '28.0.3'

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28
        versionCode 1
        versionName "1.1.0"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

signing {
    sign configurations.archives
}

dependencies {
    compileOnly 'com.facebook.react:react-native:+'
    implementation files('libs/BaiduLBS_Android.jar')
    compileOnly files('src/main/assets')
}

