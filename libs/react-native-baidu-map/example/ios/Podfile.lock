PODS:
  - BaiduMapKit (4.2.0)
  - BMKLocationKit (1.3.0.2)
  - boost-for-react-native (1.63.0)
  - DoubleConversion (1.1.6)
  - Folly (2018.10.22.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - glog (0.3.5)
  - React (0.58.4):
    - React/Core (= 0.58.4)
  - react-native-baidu-map (1.0.1):
    - BaiduMapKit (= 4.2.0)
    - BMKLocationKit (= 1.3.0.2)
    - React
  - React/Core (0.58.4):
    - yoga (= 0.58.4.React)
  - React/CxxBridge (0.58.4):
    - Folly (= 2018.10.22.00)
    - React/Core
    - React/cxxreact
    - React/jsiexecutor
  - React/cxxreact (0.58.4):
    - boost-for-react-native (= 1.63.0)
    - <PERSON>olly (= 2018.10.22.00)
    - React/jsinspector
  - React/DevSupport (0.58.4):
    - React/Core
    - React/RCTWebSocket
  - React/fishhook (0.58.4)
  - React/jsi (0.58.4):
    - <PERSON>olly (= 2018.10.22.00)
  - React/jsiexecutor (0.58.4):
    - Folly (= 2018.10.22.00)
    - React/cxxreact
    - React/jsi
  - React/jsinspector (0.58.4)
  - React/RCTAnimation (0.58.4):
    - React/Core
  - React/RCTBlob (0.58.4):
    - React/Core
  - React/RCTNetwork (0.58.4):
    - React/Core
  - React/RCTText (0.58.4):
    - React/Core
  - React/RCTWebSocket (0.58.4):
    - React/Core
    - React/fishhook
    - React/RCTBlob
  - yoga (0.58.4.React)

DEPENDENCIES:
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - Folly (from `../node_modules/react-native/third-party-podspecs/Folly.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - react-native-baidu-map (from `../node_modules/react-native-baidu-map/lib/ios/react-native-baidu-map.podspec`)
  - React/Core (from `../node_modules/react-native`)
  - React/CxxBridge (from `../node_modules/react-native`)
  - React/DevSupport (from `../node_modules/react-native`)
  - React/RCTAnimation (from `../node_modules/react-native`)
  - React/RCTNetwork (from `../node_modules/react-native`)
  - React/RCTText (from `../node_modules/react-native`)
  - React/RCTWebSocket (from `../node_modules/react-native`)
  - yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/cocoapods/specs.git:
    - BaiduMapKit
    - BMKLocationKit
    - boost-for-react-native

EXTERNAL SOURCES:
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/Folly.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  React:
    :path: "../node_modules/react-native"
  react-native-baidu-map:
    :podspec: "../node_modules/react-native-baidu-map/lib/ios/react-native-baidu-map.podspec"
  yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  BaiduMapKit: b6a8f31b3d2e4e5cad4084512a0d9a3878638272
  BMKLocationKit: 6c47273f985609be0a0785f2f6f12bb7c1b7e8b6
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  DoubleConversion: bb338842f62ab1d708ceb63ec3d999f0f3d98ecd
  Folly: de497beb10f102453a1afa9edbf8cf8a251890de
  glog: aefd1eb5dda2ab95ba0938556f34b98e2da3a60d
  React: 9b873b38b92ed8012d7cdf3b965477095ed364c4
  react-native-baidu-map: 89d481e54ae5a73201be2d905befb56890417e26
  yoga: 0885622311729a02c2bc02dca97167787a51488b

PODFILE CHECKSUM: 88204c168c90611fcd40ae53788229777a5a0383

COCOAPODS: 1.5.3
