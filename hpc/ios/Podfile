platform :ios, '9.0'
project 'NAGZ_mobile.xcodeproj'
target 'NAGZ_mobile' do

  # pod 'react-native-image-picker', :path => '../node_modules/react-native-image-picker'

  pod 'RNDeviceInfo', :path => '../node_modules/react-native-device-info'
  

  pod 'react-native-orientation', :path => '../node_modules/react-native-orientation'



  pod 'react-native-contacts', :path => '../node_modules/react-native-contacts'

  pod 'RNFS', :path => '../node_modules/react-native-fs'

  pod 'RNSound', :path => '../node_modules/react-native-sound'

  pod 'react-native-splash-screen', :path => '../node_modules/react-native-splash-screen'

  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'


  pod 'React', :path => '../node_modules/react-native', :subspecs => [
    'Core',
    'CxxBridge',
    'DevSupport', 
    'RCTText',
    'RCTNetwork',
    'RCTWebSocket', 
    'RCTImage',
    'RCTActionSheet',
    'RCTAnimation',
    'RCTGeolocation',
    'RCTLinkingIOS',
    'RCTSettings',
    'RCTVibration'
  ]
  # pod 'React', :path => '../node_modules/react-native'
  pod 'yoga', :path => '../node_modules/react-native/ReactCommon/yoga'
  pod 'DoubleConversion', :podspec => '../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec'
  pod 'glog', :podspec => '../node_modules/react-native/third-party-podspecs/glog.podspec'
  pod 'Folly', :podspec => '../node_modules/react-native/third-party-podspecs/Folly.podspec'

  pod 'react-native-baidu-map', :podspec => '../node_modules/react-native-baidu-map/ios/react-native-baidu-map.podspec'

  # pod 'react-native-camera', path: '../node_modules/react-native-camera'

  pod 'RNImageCropPicker', :path => '../node_modules/react-native-image-crop-picker'

  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'

  pod 'RNReanimated', :path => '../node_modules/react-native-reanimated'

  pod 'react-native-video', :path => '../node_modules/react-native-video'

  pod 'react-native-network-info', :path => '../node_modules/react-native-network-info'
  
  pod 'react-native-geolocation', path: '../node_modules/@react-native-community/geolocation'

  pod 'react-native-webview', :path => '../node_modules/react-native-webview'

  pod 'RNCAsyncStorage', :path => '../node_modules/@react-native-community/async-storage'

  # pod 'lottie-react-native', :path => '../node_modules/lottie-react-native'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    if target.name == "React"
      target.remove_from_project
    end
  end
end

