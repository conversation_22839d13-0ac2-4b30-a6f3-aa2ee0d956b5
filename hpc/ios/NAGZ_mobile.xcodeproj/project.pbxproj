// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
		00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
		00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
		00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
		00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
		11155355CC2E4389A34EC499 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 514815625C48401AB6CD577F /* MaterialCommunityIcons.ttf */; };
		133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
		139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
		139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		16FCD574C5964E41BC1C8893 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EA4FC41C5E84433B8AEB1B07 /* libz.tbd */; };
		236FAC065EC841AE89E9EC6F /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B38FF1B1A8B143D0B9B801BD /* FontAwesome.ttf */; };
		2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2D02E4C21E0B4AEC006451C7 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		2D02E4C31E0B4AEC006451C7 /* libRCTImage-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */; };
		2D02E4C41E0B4AEC006451C7 /* libRCTLinking-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */; };
		2D02E4C51E0B4AEC006451C7 /* libRCTNetwork-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */; };
		2D02E4C61E0B4AEC006451C7 /* libRCTSettings-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */; };
		2D02E4C71E0B4AEC006451C7 /* libRCTText-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */; };
		2D02E4C81E0B4AEC006451C7 /* libRCTWebSocket-tvOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */; };
		2D02E4C91E0B4AEC006451C7 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3EA31DF850E9000B6D8A /* libReact.a */; };
		4BCF3F90A330405A968F1976 /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D19B6ADFCD9644B48FB11C7D /* Entypo.ttf */; };
		50F34C4A0C8E48AAAB6A409D /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3C06F2152D244426A94A2C48 /* FontAwesome5_Solid.ttf */; };
		654E93FA2362D59300891828 /* libbz2.1.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C8A318721FB04144004D084E /* libbz2.1.0.tbd */; };
		65AF0BFE1FE8AD8100790E94 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0BFD1FE8AD6100790E94 /* CoreLocation.framework */; };
		65AF0C001FE8ADA100790E94 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0BFF1FE8AD9400790E94 /* QuartzCore.framework */; settings = {ATTRIBUTES = (Required, ); }; };
		65AF0C021FE8AEEB00790E94 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0C011FE8AEE200790E94 /* OpenGLES.framework */; };
		65AF0C041FE8AF3700790E94 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0C031FE8AF3200790E94 /* Security.framework */; };
		65AF0C061FE8B02600790E94 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0C051FE8B02600790E94 /* libsqlite3.0.tbd */; };
		65AF0C081FE8B03E00790E94 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65AF0C071FE8B03B00790E94 /* CoreTelephony.framework */; };
		65E41E2F237E75E900D6E4CD /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		6DF4CBFAD0A9436A97545896 /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C02F220AA20A4A85B93623C4 /* SimpleLineIcons.ttf */; };
		74FF07BB23A8C13900EFCDB3 /* agzcustomer.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 74FF07BA23A8C13900EFCDB3 /* agzcustomer.ttf */; };
		822742CBD40B4B568DB2DF1B /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5D72ABD468D847578A391290 /* Octicons.ttf */; };
		832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
		9230D13FE59F453686D17CDA /* antoutline.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 217B63ADF93E4715AD562026 /* antoutline.ttf */; };
		961753EEF32A42B4AB6391A5 /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 15DC88BAF94C495C90DBCB54 /* Ionicons.ttf */; };
		AACE67F1AD4F75C239BD406D /* libPods-NAGZ_mobile.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 31A5BDA02C2749E23EFF26D6 /* libPods-NAGZ_mobile.a */; };
		ADBDB9381DFEBF1600ED6528 /* libRCTBlob.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */; };
		B07B382687684CF486D728B0 /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7C50D2992B704854B939BEBC /* Zocial.ttf */; };
		BF92A7002195142B00213C8E /* CloudPushSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BF92A6C32195142000213C8E /* CloudPushSDK.framework */; };
		BF92A7012195142B00213C8E /* AlicloudUtils.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BF92A6FD2195142700213C8E /* AlicloudUtils.framework */; };
		BF92A7022195142B00213C8E /* UTMini.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BF92A6FE2195142B00213C8E /* UTMini.framework */; };
		BF92A7032195142B00213C8E /* UTDID.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BF92A6FF2195142B00213C8E /* UTDID.framework */; };
		BF92A7052195147400213C8E /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BF92A7042195147400213C8E /* SystemConfiguration.framework */; };
		BF92A78221953EEA00213C8E /* AliyunPush.m in Sources */ = {isa = PBXBuildFile; fileRef = BF92A78121953EEA00213C8E /* AliyunPush.m */; };
		BF940D4C2254B8470029A059 /* libyoga.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3DAD3EA51DF850E9000B6D8A /* libyoga.a */; };
		BF940D4E2254B89A0029A059 /* libthird-party.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C8A316541FB03128004D084E /* libthird-party.a */; };
		BF940D4F2254B8C00029A059 /* libdouble-conversion.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C8A316581FB03128004D084E /* libdouble-conversion.a */; };
		BFBE01722315119C003CD88C /* agzIconfont.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BFBE01712315118E003CD88C /* agzIconfont.ttf */; };
		BFD52B472301650800748D19 /* RNFileOpener.m in Sources */ = {isa = PBXBuildFile; fileRef = BFD52B462301650800748D19 /* RNFileOpener.m */; };
		C275382EADF14187BB019048 /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 535F78D0FD844DC5B55FCA3F /* Feather.ttf */; };
		C80E059C1FDA22F90075C316 /* RNExitApp.m in Sources */ = {isa = PBXBuildFile; fileRef = C80E059B1FDA22F90075C316 /* RNExitApp.m */; };
		C822190B208EFD9D0046A73E /* FMDB.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = C8221905208EFD960046A73E /* FMDB.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		C822190D208EFD9D0046A73E /* Qiniu.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = C8221906208EFD960046A73E /* Qiniu.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		C83A64A41FB06AF60093F9A2 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C83A64A31FB06AF60093F9A2 /* libicucore.tbd */; };
		C83A64A61FB06B160093F9A2 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C83A64A51FB06B160093F9A2 /* libresolv.tbd */; };
		C83F2772200CA47600C49C82 /* libRNImagePicker.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C83F2771200CA45A00C49C82 /* libRNImagePicker.a */; };
		C8718A751FE3AA3100F3CBCE /* RNUpgrade.m in Sources */ = {isa = PBXBuildFile; fileRef = C8718A741FE3AA3100F3CBCE /* RNUpgrade.m */; };
		C89C5BDF1FB1628F00C03E4C /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C89C5BDE1FB1628F00C03E4C /* libc++.tbd */; };
		C8A317FE1FB0333F004D084E /* anticon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C8A317FA1FB0333F004D084E /* anticon.ttf */; };
		C8A318011FB0333F004D084E /* RNBridgeModule.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A317FD1FB0333F004D084E /* RNBridgeModule.m */; };
		C8A318171FB03A2E004D084E /* AliyunOSSiOS in Frameworks */ = {isa = PBXBuildFile; fileRef = C8A318031FB03A2D004D084E /* AliyunOSSiOS */; };
		C8A318181FB03A2E004D084E /* RCTAliyunOSS.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A318161FB03A2E004D084E /* RCTAliyunOSS.m */; };
		C8A3184E1FB03A41004D084E /* AliyunOSS.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A3181B1FB03A40004D084E /* AliyunOSS.m */; };
		C8A3184F1FB03A41004D084E /* BSDiff.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A3181E1FB03A40004D084E /* BSDiff.m */; };
		C8A318501FB03A41004D084E /* bspatch.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318201FB03A40004D084E /* bspatch.c */; };
		C8A318511FB03A41004D084E /* RCTHotUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A318231FB03A40004D084E /* RCTHotUpdate.m */; };
		C8A318521FB03A41004D084E /* RCTHotUpdateDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A318251FB03A40004D084E /* RCTHotUpdateDownloader.m */; };
		C8A318531FB03A41004D084E /* RCTHotUpdateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A318271FB03A40004D084E /* RCTHotUpdateManager.m */; };
		C8A318541FB03A41004D084E /* aescrypt.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A3182C1FB03A41004D084E /* aescrypt.c */; };
		C8A318551FB03A41004D084E /* aeskey.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A3182D1FB03A41004D084E /* aeskey.c */; };
		C8A318561FB03A41004D084E /* aestab.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A3182F1FB03A41004D084E /* aestab.c */; };
		C8A318571FB03A41004D084E /* entropy.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318331FB03A41004D084E /* entropy.c */; };
		C8A318581FB03A41004D084E /* fileenc.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318351FB03A41004D084E /* fileenc.c */; };
		C8A318591FB03A41004D084E /* hmac.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318371FB03A41004D084E /* hmac.c */; };
		C8A3185A1FB03A41004D084E /* prng.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318391FB03A41004D084E /* prng.c */; };
		C8A3185B1FB03A41004D084E /* pwd2key.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A3183B1FB03A41004D084E /* pwd2key.c */; };
		C8A3185C1FB03A41004D084E /* sha1.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A3183D1FB03A41004D084E /* sha1.c */; };
		C8A3185E1FB03A41004D084E /* ioapi.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318431FB03A41004D084E /* ioapi.c */; };
		C8A3185F1FB03A41004D084E /* mztools.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318451FB03A41004D084E /* mztools.c */; };
		C8A318601FB03A41004D084E /* unzip.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318471FB03A41004D084E /* unzip.c */; };
		C8A318611FB03A41004D084E /* zip.c in Sources */ = {isa = PBXBuildFile; fileRef = C8A318491FB03A41004D084E /* zip.c */; };
		C8A318621FB03A41004D084E /* SSZipArchive.m in Sources */ = {isa = PBXBuildFile; fileRef = C8A3184C1FB03A41004D084E /* SSZipArchive.m */; };
		C8AC53101FE27A6C003270D4 /* libRNDeviceInfo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C8AC53081FE277E2003270D4 /* libRNDeviceInfo.a */; };
		CBD8BE37C5F04BF2BCD7ED8E /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D95665BA6195462799E16DFD /* FontAwesome5_Regular.ttf */; };
		D2E9F10F937942769EB52213 /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8B4C275094E64CFFA4E8DA23 /* Foundation.ttf */; };
		D64973F2A7AB439E9541427A /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 67625734A27848A58DF21D5F /* MaterialIcons.ttf */; };
		E27013DB2624500C00709A0B /* UMCommon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E27013D82624500C00709A0B /* UMCommon.framework */; };
		E27013DC2624500C00709A0B /* UMPush.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E27013D92624500C00709A0B /* UMPush.framework */; };
		E27013DD2624500C00709A0B /* UMAnalytics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E27013DA2624500C00709A0B /* UMAnalytics.framework */; };
		E27013E42624501800709A0B /* UMAnalyticsModule.m in Sources */ = {isa = PBXBuildFile; fileRef = E27013E02624501800709A0B /* UMAnalyticsModule.m */; };
		E27013E52624501800709A0B /* RNUMConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = E27013E12624501800709A0B /* RNUMConfigure.m */; };
		E27013E62624501800709A0B /* UMPushModule.m in Sources */ = {isa = PBXBuildFile; fileRef = E27013E32624501800709A0B /* UMPushModule.m */; };
		E38B3C71298646049C242D94 /* antfill.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 25A9168BE2CB4B1B9EC4B3EB /* antfill.ttf */; };
		E8A322DB1F15460D9EB060A3 /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2EDEC68FC36C44598ED3B94C /* EvilIcons.ttf */; };
		F914F9A7BE9A4F61999E80FE /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 62D3690962664BC296D62C0D /* AntDesign.ttf */; };
		FFABEAD75B4C4E37A3F8EC49 /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2E654CCCB3C7407DAEACDD7E /* FontAwesome5_Brands.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTActionSheet;
		};
		00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTGeolocation;
		};
		00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5115D1A9E6B3D00147676;
			remoteInfo = RCTImage;
		};
		00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B511DB1A9E6C8500147676;
			remoteInfo = RCTNetwork;
		};
		00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 832C81801AAF6DEF007FA2F7;
			remoteInfo = RCTVibration;
		};
		139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTSettings;
		};
		139FDEF31B06529B00C62182 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3C86DF461ADF2C930047B81A;
			remoteInfo = RCTWebSocket;
		};
		146834031AC3E56700842450 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 83CBBA2E1A601D0E00E9B192;
			remoteInfo = React;
		};
		2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2D02E47A1E0B4A5D006451C7;
			remoteInfo = "NAGZ_mobile-tvOS";
		};
		3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A283A1D9B042B00D4039D;
			remoteInfo = "RCTImage-tvOS";
		};
		3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28471D9B043800D4039D;
			remoteInfo = "RCTLinking-tvOS";
		};
		3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28541D9B044C00D4039D;
			remoteInfo = "RCTNetwork-tvOS";
		};
		3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28611D9B046600D4039D;
			remoteInfo = "RCTSettings-tvOS";
		};
		3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A287B1D9B048500D4039D;
			remoteInfo = "RCTText-tvOS";
		};
		3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28881D9B049200D4039D;
			remoteInfo = "RCTWebSocket-tvOS";
		};
		3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28131D9B038B00D4039D;
			remoteInfo = "React-tvOS";
		};
		3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C059A1DE3340900C268FA;
			remoteInfo = yoga;
		};
		3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C06751DE3340C00C268FA;
			remoteInfo = "yoga-tvOS";
		};
		3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9251DE5FBEC00167DC4;
			remoteInfo = cxxreact;
		};
		3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9321DE5FBEE00167DC4;
			remoteInfo = "cxxreact-tvOS";
		};
		5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTAnimation;
		};
		5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28201D9B03D100D4039D;
			remoteInfo = "RCTAnimation-tvOS";
		};
		654A75F6232F65DB00C55118 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 654A75C6232F65DB00C55118 /* RNNetworkInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 045BEB3C1B52EA6A0013C1B9;
			remoteInfo = RNNetworkInfo;
		};
		654A75F8232F65DB00C55118 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 654A75C6232F65DB00C55118 /* RNNetworkInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A051C2C322B1C080001E1191;
			remoteInfo = "RNNetworkInfo-tvOS";
		};
		65A14B7E1FEC9F4700767363 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 65A14B751FEC9F4700767363 /* RCTBaiduMap.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EF3C22561CC2C30D0099E9B6;
			remoteInfo = RCTBaiduMap;
		};
		78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTLinking;
		};
		832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5119B1A9E6C1200147676;
			remoteInfo = RCTText;
		};
		ADBDB9261DFEBF0700ED6528 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 358F4ED71D1E81A9004DF814;
			remoteInfo = RCTBlob;
		};
		BFA50BCB225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EBF21BDC1FC498900052F4D5;
			remoteInfo = jsinspector;
		};
		BFA50BCD225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EBF21BFA1FC4989A0052F4D5;
			remoteInfo = "jsinspector-tvOS";
		};
		BFA50BCF225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EDEBC6D6214B3E7000DD5AC8;
			remoteInfo = jsi;
		};
		BFA50BD1225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = EDEBC73B214B45A300DD5AC8;
			remoteInfo = jsiexecutor;
		};
		BFA50BD3225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = ED296FB6214C9A0900B7C4FE;
			remoteInfo = "jsi-tvOS";
		};
		BFA50BD5225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = ED296FEE214C9CF800B7C4FE;
			remoteInfo = "jsiexecutor-tvOS";
		};
		BFA50BE1225458F5004D5542 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B6D8BA48A2D8493C82E85B3F /* RNVectorIcons.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A39873CE1EA65EE60051E01A;
			remoteInfo = "RNVectorIcons-tvOS";
		};
		C83F2770200CA45A00C49C82 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C83F276C200CA45900C49C82 /* RNImagePicker.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 014A3B5C1C6CF33500B6D375;
			remoteInfo = RNImagePicker;
		};
		C874B8EC208041D7002A8B58 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C874B8E8208041D6002A8B58 /* RCTOrientation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTOrientation;
		};
		C89C5BD91FB149D700C03E4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B6D8BA48A2D8493C82E85B3F /* RNVectorIcons.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 5DBEB1501B18CEA900B34395;
			remoteInfo = RNVectorIcons;
		};
		C89C5D0E1FB4359000C03E4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 37CFC51AD86F405598CB1D75 /* RCTContacts.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 1441618E1BD0A79300FA4F59;
			remoteInfo = RCTContacts;
		};
		C8A316531FB03128004D084E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7ECE1E25DB7D00323FB7;
			remoteInfo = "third-party";
		};
		C8A316551FB03128004D084E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D3C1EBD27B6005632C8;
			remoteInfo = "third-party-tvOS";
		};
		C8A316571FB03128004D084E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7E881E25C6D100323FB7;
			remoteInfo = "double-conversion";
		};
		C8A316591FB03128004D084E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D621EBD27B9005632C8;
			remoteInfo = "double-conversion-tvOS";
		};
		C8AC53071FE277E2003270D4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C8AC52DC1FE277E2003270D4 /* RNDeviceInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = DA5891D81BA9A9FC002B4DB2;
			remoteInfo = RNDeviceInfo;
		};
		C8AC53091FE277E2003270D4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C8AC52DC1FE277E2003270D4 /* RNDeviceInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = E72EC1401F7ABB5A0001BC90;
			remoteInfo = "RNDeviceInfo-tvOS";
		};
		E5D1A2D41FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = ADD01A681E09402E00F6D226;
			remoteInfo = "RCTBlob-tvOS";
		};
		E5D1A2E61FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3DBE0D001F3B181A0099AA32;
			remoteInfo = fishhook;
		};
		E5D1A2E81FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3DBE0D0D1F3B181C0099AA32;
			remoteInfo = "fishhook-tvOS";
		};
		E5D1A2F71FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DBF86E3C389044C3AF763916 /* BugsnagReactNative.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 8AD256131D6DE5F600C7D842;
			remoteInfo = BugsnagReactNative;
		};
		E5D1A3011FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 11E93B9AA340423F907DD871 /* RNFS.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F12AFB9B1ADAF8F800E0535D;
			remoteInfo = RNFS;
		};
		E5D1A3031FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 11E93B9AA340423F907DD871 /* RNFS.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 6456441F1EB8DA9100672408;
			remoteInfo = "RNFS-tvOS";
		};
		E5D1A3091FA332A000E30658 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F52E8B08CC3F4914978B71D2 /* SplashScreen.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D7682761D8E76B80014119E;
			remoteInfo = SplashScreen;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C8372ED7203FF80900B60889 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				C822190D208EFD9D0046A73E /* Qiniu.framework in Embed Frameworks */,
				C822190B208EFD9D0046A73E /* FMDB.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTActionSheet.xcodeproj; path = "../node_modules/react-native/Libraries/ActionSheetIOS/RCTActionSheet.xcodeproj"; sourceTree = "<group>"; };
		00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTGeolocation.xcodeproj; path = "../node_modules/react-native/Libraries/Geolocation/RCTGeolocation.xcodeproj"; sourceTree = "<group>"; };
		00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTImage.xcodeproj; path = "../node_modules/react-native/Libraries/Image/RCTImage.xcodeproj"; sourceTree = "<group>"; };
		00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTNetwork.xcodeproj; path = "../node_modules/react-native/Libraries/Network/RCTNetwork.xcodeproj"; sourceTree = "<group>"; };
		00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTVibration.xcodeproj; path = "../node_modules/react-native/Libraries/Vibration/RCTVibration.xcodeproj"; sourceTree = "<group>"; };
		11E93B9AA340423F907DD871 /* RNFS.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = RNFS.xcodeproj; path = "../node_modules/react-native-fs/RNFS.xcodeproj"; sourceTree = "<group>"; };
		139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTSettings.xcodeproj; path = "../node_modules/react-native/Libraries/Settings/RCTSettings.xcodeproj"; sourceTree = "<group>"; };
		139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTWebSocket.xcodeproj; path = "../node_modules/react-native/Libraries/WebSocket/RCTWebSocket.xcodeproj"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* NAGZ_mobile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NAGZ_mobile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = NAGZ_mobile/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = NAGZ_mobile/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = NAGZ_mobile/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = NAGZ_mobile/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = NAGZ_mobile/main.m; sourceTree = "<group>"; };
		146833FF1AC3E56700842450 /* React.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = React.xcodeproj; path = "../node_modules/react-native/React/React.xcodeproj"; sourceTree = "<group>"; };
		15DC88BAF94C495C90DBCB54 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		217B63ADF93E4715AD562026 /* antoutline.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = antoutline.ttf; path = "../node_modules/@ant-design/icons-react-native/fonts/antoutline.ttf"; sourceTree = "<group>"; };
		25A9168BE2CB4B1B9EC4B3EB /* antfill.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = antfill.ttf; path = "../node_modules/@ant-design/icons-react-native/fonts/antfill.ttf"; sourceTree = "<group>"; };
		26580D8169F8FD28971414E4 /* Pods-NAGZ_mobile.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NAGZ_mobile.debug.xcconfig"; path = "Pods/Target Support Files/Pods-NAGZ_mobile/Pods-NAGZ_mobile.debug.xcconfig"; sourceTree = "<group>"; };
		2D02E47B1E0B4A5D006451C7 /* NAGZ_mobile-tvOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "NAGZ_mobile-tvOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2D02E4901E0B4A5D006451C7 /* NAGZ_mobile-tvOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "NAGZ_mobile-tvOSTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		2E654CCCB3C7407DAEACDD7E /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		2EDEC68FC36C44598ED3B94C /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		31A5BDA02C2749E23EFF26D6 /* libPods-NAGZ_mobile.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-NAGZ_mobile.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		37CFC51AD86F405598CB1D75 /* RCTContacts.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = RCTContacts.xcodeproj; path = "../node_modules/react-native-contacts/ios/RCTContacts.xcodeproj"; sourceTree = "<group>"; };
		3C06F2152D244426A94A2C48 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		514815625C48401AB6CD577F /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		535F78D0FD844DC5B55FCA3F /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		5D72ABD468D847578A391290 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTAnimation.xcodeproj; path = "../node_modules/react-native/Libraries/NativeAnimation/RCTAnimation.xcodeproj"; sourceTree = "<group>"; };
		62D3690962664BC296D62C0D /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		654A75C6232F65DB00C55118 /* RNNetworkInfo.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNNetworkInfo.xcodeproj; path = "../node_modules/react-native-network-info/ios/RNNetworkInfo.xcodeproj"; sourceTree = "<group>"; };
		65983D552383F9F000C34D0B /* agzcustomer.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = agzcustomer.ttf; path = ../../../../../Downloads/icon/fonts/agzcustomer.ttf; sourceTree = "<group>"; };
		65A14B611FEC9F4700767363 /* BaiduMapAPI_Base.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Base.framework; sourceTree = "<group>"; };
		65A14B621FEC9F4700767363 /* BaiduMapAPI_Cloud.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Cloud.framework; sourceTree = "<group>"; };
		65A14B631FEC9F4700767363 /* BaiduMapAPI_Location.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Location.framework; sourceTree = "<group>"; };
		65A14B641FEC9F4700767363 /* BaiduMapAPI_Map.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Map.framework; sourceTree = "<group>"; };
		65A14B651FEC9F4700767363 /* BaiduMapAPI_Radar.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Radar.framework; sourceTree = "<group>"; };
		65A14B661FEC9F4700767363 /* BaiduMapAPI_Search.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Search.framework; sourceTree = "<group>"; };
		65A14B671FEC9F4700767363 /* BaiduMapAPI_Utils.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = BaiduMapAPI_Utils.framework; sourceTree = "<group>"; };
		65A14B691FEC9F4700767363 /* BaiduMapModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaiduMapModule.h; sourceTree = "<group>"; };
		65A14B6A1FEC9F4700767363 /* BaiduMapModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaiduMapModule.m; sourceTree = "<group>"; };
		65A14B6B1FEC9F4700767363 /* BaseModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaseModule.h; sourceTree = "<group>"; };
		65A14B6C1FEC9F4700767363 /* BaseModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaseModule.m; sourceTree = "<group>"; };
		65A14B6D1FEC9F4700767363 /* GeolocationModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeolocationModule.h; sourceTree = "<group>"; };
		65A14B6E1FEC9F4700767363 /* GeolocationModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GeolocationModule.m; sourceTree = "<group>"; };
		65A14B6F1FEC9F4700767363 /* RCTBaiduMapView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCTBaiduMapView.h; sourceTree = "<group>"; };
		65A14B701FEC9F4700767363 /* RCTBaiduMapView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTBaiduMapView.m; sourceTree = "<group>"; };
		65A14B711FEC9F4700767363 /* RCTBaiduMapViewManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCTBaiduMapViewManager.h; sourceTree = "<group>"; };
		65A14B721FEC9F4700767363 /* RCTBaiduMapViewManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTBaiduMapViewManager.m; sourceTree = "<group>"; };
		65A14B731FEC9F4700767363 /* RouteModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RouteModule.h; sourceTree = "<group>"; };
		65A14B741FEC9F4700767363 /* RouteModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RouteModule.m; sourceTree = "<group>"; };
		65A14B751FEC9F4700767363 /* RCTBaiduMap.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = RCTBaiduMap.xcodeproj; sourceTree = "<group>"; };
		65AF0BFD1FE8AD6100790E94 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		65AF0BFF1FE8AD9400790E94 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		65AF0C011FE8AEE200790E94 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		65AF0C031FE8AF3200790E94 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		65AF0C051FE8B02600790E94 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		65AF0C071FE8B03B00790E94 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		65AF0C091FE8B04800790E94 /* libstdc++.6.0.9.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libstdc++.6.0.9.tbd"; path = "usr/lib/libstdc++.6.0.9.tbd"; sourceTree = SDKROOT; };
		67625734A27848A58DF21D5F /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		74FF07BA23A8C13900EFCDB3 /* agzcustomer.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = agzcustomer.ttf; path = NAGZ_mobile/Font/agzcustomer.ttf; sourceTree = "<group>"; };
		78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTLinking.xcodeproj; path = "../node_modules/react-native/Libraries/LinkingIOS/RCTLinking.xcodeproj"; sourceTree = "<group>"; };
		7C50D2992B704854B939BEBC /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTText.xcodeproj; path = "../node_modules/react-native/Libraries/Text/RCTText.xcodeproj"; sourceTree = "<group>"; };
		8B4C275094E64CFFA4E8DA23 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		9C26C58D565EA6E025B22DDB /* Pods-NAGZ_mobile.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NAGZ_mobile.release.xcconfig"; path = "Pods/Target Support Files/Pods-NAGZ_mobile/Pods-NAGZ_mobile.release.xcconfig"; sourceTree = "<group>"; };
		ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTBlob.xcodeproj; path = "../node_modules/react-native/Libraries/Blob/RCTBlob.xcodeproj"; sourceTree = "<group>"; };
		B38FF1B1A8B143D0B9B801BD /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		B6D8BA48A2D8493C82E85B3F /* RNVectorIcons.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = RNVectorIcons.xcodeproj; path = "../node_modules/react-native-vector-icons/RNVectorIcons.xcodeproj"; sourceTree = "<group>"; };
		BF92A6C32195142000213C8E /* CloudPushSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = CloudPushSDK.framework; sourceTree = "<group>"; };
		BF92A6FD2195142700213C8E /* AlicloudUtils.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = AlicloudUtils.framework; sourceTree = "<group>"; };
		BF92A6FE2195142B00213C8E /* UTMini.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UTMini.framework; sourceTree = "<group>"; };
		BF92A6FF2195142B00213C8E /* UTDID.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UTDID.framework; sourceTree = "<group>"; };
		BF92A7042195147400213C8E /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		BF92A706219514A100213C8E /* NAGZ_mobile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = NAGZ_mobile.entitlements; path = NAGZ_mobile/NAGZ_mobile.entitlements; sourceTree = "<group>"; };
		BF92A78021953EEA00213C8E /* AliyunPush.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AliyunPush.h; sourceTree = "<group>"; };
		BF92A78121953EEA00213C8E /* AliyunPush.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AliyunPush.m; sourceTree = "<group>"; };
		BFA50C5C22545BD7004D5542 /* libbz2.1.0.5.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.1.0.5.tbd; path = usr/lib/libbz2.1.0.5.tbd; sourceTree = SDKROOT; };
		BFACB24E23220EC000B72F01 /* agzcustomer.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = agzcustomer.ttf; path = NAGZ_mobile/Font/agzcustomer.ttf; sourceTree = "<group>"; };
		BFBE01712315118E003CD88C /* agzIconfont.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = agzIconfont.ttf; sourceTree = "<group>"; };
		BFBE0173231511A4003CD88C /* agzIconfont.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = agzIconfont.ttf; path = NAGZ_mobile/Font/agzIconfont.ttf; sourceTree = "<group>"; };
		BFD52B45230164BB00748D19 /* RNFileOpener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNFileOpener.h; sourceTree = "<group>"; };
		BFD52B462301650800748D19 /* RNFileOpener.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNFileOpener.m; sourceTree = "<group>"; };
		C02F220AA20A4A85B93623C4 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		C80E059A1FDA22F90075C316 /* RNExitApp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNExitApp.h; sourceTree = "<group>"; };
		C80E059B1FDA22F90075C316 /* RNExitApp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNExitApp.m; sourceTree = "<group>"; };
		C8221905208EFD960046A73E /* FMDB.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = FMDB.framework; sourceTree = "<group>"; };
		C8221906208EFD960046A73E /* Qiniu.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = Qiniu.framework; sourceTree = "<group>"; };
		C83A64A11FB06ACC0093F9A2 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		C83A64A31FB06AF60093F9A2 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		C83A64A51FB06B160093F9A2 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		C83F276C200CA45900C49C82 /* RNImagePicker.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNImagePicker.xcodeproj; path = "../node_modules/react-native-image-picker/ios/RNImagePicker.xcodeproj"; sourceTree = "<group>"; };
		C8718A731FE3AA3100F3CBCE /* RNUpgrade.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNUpgrade.h; sourceTree = "<group>"; };
		C8718A741FE3AA3100F3CBCE /* RNUpgrade.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNUpgrade.m; sourceTree = "<group>"; };
		C874B8E8208041D6002A8B58 /* RCTOrientation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTOrientation.xcodeproj; path = "../node_modules/react-native-orientation/iOS/RCTOrientation.xcodeproj"; sourceTree = "<group>"; };
		C89C5BDE1FB1628F00C03E4C /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		C8A317F71FB0333F004D084E /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PrefixHeader.pch; path = NAGZ_mobile/PrefixHeader.pch; sourceTree = "<group>"; };
		C8A317F81FB0333F004D084E /* RNBridgeModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RNBridgeModule.h; path = NAGZ_mobile/RNBridgeModule.h; sourceTree = "<group>"; };
		C8A317FA1FB0333F004D084E /* anticon.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = anticon.ttf; sourceTree = "<group>"; };
		C8A317FB1FB0333F004D084E /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome.ttf; sourceTree = "<group>"; };
		C8A317FC1FB0333F004D084E /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Ionicons.ttf; sourceTree = "<group>"; };
		C8A317FD1FB0333F004D084E /* RNBridgeModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RNBridgeModule.m; path = NAGZ_mobile/RNBridgeModule.m; sourceTree = "<group>"; };
		C8A318031FB03A2D004D084E /* AliyunOSSiOS */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = AliyunOSSiOS; sourceTree = "<group>"; };
		C8A318041FB03A2D004D084E /* OSSBolts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSBolts.h; sourceTree = "<group>"; };
		C8A318051FB03A2D004D084E /* OSSCancellationToken.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSCancellationToken.h; sourceTree = "<group>"; };
		C8A318061FB03A2D004D084E /* OSSCancellationTokenRegistration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSCancellationTokenRegistration.h; sourceTree = "<group>"; };
		C8A318071FB03A2D004D084E /* OSSCancellationTokenSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSCancellationTokenSource.h; sourceTree = "<group>"; };
		C8A318081FB03A2D004D084E /* OSSClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSClient.h; sourceTree = "<group>"; };
		C8A318091FB03A2D004D084E /* OSSCompat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSCompat.h; sourceTree = "<group>"; };
		C8A3180A1FB03A2D004D084E /* OSSDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSDefine.h; sourceTree = "<group>"; };
		C8A3180B1FB03A2D004D084E /* OSSExecutor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSExecutor.h; sourceTree = "<group>"; };
		C8A3180C1FB03A2D004D084E /* OSSLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSLog.h; sourceTree = "<group>"; };
		C8A3180D1FB03A2D004D084E /* OSSModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSModel.h; sourceTree = "<group>"; };
		C8A3180E1FB03A2D004D084E /* OSSNetworking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSNetworking.h; sourceTree = "<group>"; };
		C8A3180F1FB03A2D004D084E /* OSSService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSService.h; sourceTree = "<group>"; };
		C8A318101FB03A2D004D084E /* OSSTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSTask.h; sourceTree = "<group>"; };
		C8A318111FB03A2D004D084E /* OSSTaskCompletionSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSTaskCompletionSource.h; sourceTree = "<group>"; };
		C8A318121FB03A2D004D084E /* OSSUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSUtil.h; sourceTree = "<group>"; };
		C8A318131FB03A2D004D084E /* OSSXMLDictionary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSXMLDictionary.h; sourceTree = "<group>"; };
		C8A318151FB03A2E004D084E /* RCTAliyunOSS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTAliyunOSS.h; sourceTree = "<group>"; };
		C8A318161FB03A2E004D084E /* RCTAliyunOSS.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTAliyunOSS.m; sourceTree = "<group>"; };
		C8A3181A1FB03A40004D084E /* AliyunOSS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AliyunOSS.h; sourceTree = "<group>"; };
		C8A3181B1FB03A40004D084E /* AliyunOSS.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AliyunOSS.m; sourceTree = "<group>"; };
		C8A3181D1FB03A40004D084E /* BSDiff.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSDiff.h; sourceTree = "<group>"; };
		C8A3181E1FB03A40004D084E /* BSDiff.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSDiff.m; sourceTree = "<group>"; };
		C8A318201FB03A40004D084E /* bspatch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = bspatch.c; sourceTree = "<group>"; };
		C8A318211FB03A40004D084E /* bspatch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bspatch.h; sourceTree = "<group>"; };
		C8A318221FB03A40004D084E /* RCTHotUpdate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTHotUpdate.h; sourceTree = "<group>"; };
		C8A318231FB03A40004D084E /* RCTHotUpdate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTHotUpdate.m; sourceTree = "<group>"; };
		C8A318241FB03A40004D084E /* RCTHotUpdateDownloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTHotUpdateDownloader.h; sourceTree = "<group>"; };
		C8A318251FB03A40004D084E /* RCTHotUpdateDownloader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTHotUpdateDownloader.m; sourceTree = "<group>"; };
		C8A318261FB03A40004D084E /* RCTHotUpdateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTHotUpdateManager.h; sourceTree = "<group>"; };
		C8A318271FB03A40004D084E /* RCTHotUpdateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTHotUpdateManager.m; sourceTree = "<group>"; };
		C8A3182A1FB03A41004D084E /* aes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		C8A3182B1FB03A41004D084E /* aes_via_ace.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes_via_ace.h; sourceTree = "<group>"; };
		C8A3182C1FB03A41004D084E /* aescrypt.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aescrypt.c; sourceTree = "<group>"; };
		C8A3182D1FB03A41004D084E /* aeskey.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aeskey.c; sourceTree = "<group>"; };
		C8A3182E1FB03A41004D084E /* aesopt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aesopt.h; sourceTree = "<group>"; };
		C8A3182F1FB03A41004D084E /* aestab.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aestab.c; sourceTree = "<group>"; };
		C8A318301FB03A41004D084E /* aestab.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aestab.h; sourceTree = "<group>"; };
		C8A318311FB03A41004D084E /* brg_endian.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = brg_endian.h; sourceTree = "<group>"; };
		C8A318321FB03A41004D084E /* brg_types.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = brg_types.h; sourceTree = "<group>"; };
		C8A318331FB03A41004D084E /* entropy.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = entropy.c; sourceTree = "<group>"; };
		C8A318341FB03A41004D084E /* entropy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = entropy.h; sourceTree = "<group>"; };
		C8A318351FB03A41004D084E /* fileenc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = fileenc.c; sourceTree = "<group>"; };
		C8A318361FB03A41004D084E /* fileenc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = fileenc.h; sourceTree = "<group>"; };
		C8A318371FB03A41004D084E /* hmac.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = hmac.c; sourceTree = "<group>"; };
		C8A318381FB03A41004D084E /* hmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		C8A318391FB03A41004D084E /* prng.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = prng.c; sourceTree = "<group>"; };
		C8A3183A1FB03A41004D084E /* prng.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = prng.h; sourceTree = "<group>"; };
		C8A3183B1FB03A41004D084E /* pwd2key.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pwd2key.c; sourceTree = "<group>"; };
		C8A3183C1FB03A41004D084E /* pwd2key.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pwd2key.h; sourceTree = "<group>"; };
		C8A3183D1FB03A41004D084E /* sha1.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sha1.c; sourceTree = "<group>"; };
		C8A3183E1FB03A41004D084E /* sha1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha1.h; sourceTree = "<group>"; };
		C8A3183F1FB03A41004D084E /* Common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Common.h; sourceTree = "<group>"; };
		C8A318401FB03A41004D084E /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C8A318421FB03A41004D084E /* crypt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crypt.h; sourceTree = "<group>"; };
		C8A318431FB03A41004D084E /* ioapi.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ioapi.c; sourceTree = "<group>"; };
		C8A318441FB03A41004D084E /* ioapi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ioapi.h; sourceTree = "<group>"; };
		C8A318451FB03A41004D084E /* mztools.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mztools.c; sourceTree = "<group>"; };
		C8A318461FB03A41004D084E /* mztools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mztools.h; sourceTree = "<group>"; };
		C8A318471FB03A41004D084E /* unzip.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = unzip.c; sourceTree = "<group>"; };
		C8A318481FB03A41004D084E /* unzip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = unzip.h; sourceTree = "<group>"; };
		C8A318491FB03A41004D084E /* zip.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = zip.c; sourceTree = "<group>"; };
		C8A3184A1FB03A41004D084E /* zip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = zip.h; sourceTree = "<group>"; };
		C8A3184B1FB03A41004D084E /* SSZipArchive.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SSZipArchive.h; sourceTree = "<group>"; };
		C8A3184C1FB03A41004D084E /* SSZipArchive.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SSZipArchive.m; sourceTree = "<group>"; };
		C8A3184D1FB03A41004D084E /* ZipArchive.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZipArchive.h; sourceTree = "<group>"; };
		C8A318721FB04144004D084E /* libbz2.1.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.1.0.tbd; path = usr/lib/libbz2.1.0.tbd; sourceTree = SDKROOT; };
		C8AC52DC1FE277E2003270D4 /* RNDeviceInfo.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNDeviceInfo.xcodeproj; path = "../node_modules/react-native-device-info/RNDeviceInfo.xcodeproj"; sourceTree = "<group>"; };
		D19B6ADFCD9644B48FB11C7D /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		D95665BA6195462799E16DFD /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		DBF86E3C389044C3AF763916 /* BugsnagReactNative.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = BugsnagReactNative.xcodeproj; path = "../node_modules/bugsnag-react-native/cocoa/BugsnagReactNative.xcodeproj"; sourceTree = "<group>"; };
		E27013D82624500C00709A0B /* UMCommon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMCommon.framework; sourceTree = "<group>"; };
		E27013D92624500C00709A0B /* UMPush.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMPush.framework; sourceTree = "<group>"; };
		E27013DA2624500C00709A0B /* UMAnalytics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMAnalytics.framework; sourceTree = "<group>"; };
		E27013DE2624501800709A0B /* UMAnalyticsModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UMAnalyticsModule.h; sourceTree = "<group>"; };
		E27013DF2624501800709A0B /* RNUMConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RNUMConfigure.h; sourceTree = "<group>"; };
		E27013E02624501800709A0B /* UMAnalyticsModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UMAnalyticsModule.m; sourceTree = "<group>"; };
		E27013E12624501800709A0B /* RNUMConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RNUMConfigure.m; sourceTree = "<group>"; };
		E27013E22624501800709A0B /* UMPushModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UMPushModule.h; sourceTree = "<group>"; };
		E27013E32624501800709A0B /* UMPushModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UMPushModule.m; sourceTree = "<group>"; };
		EA4FC41C5E84433B8AEB1B07 /* libz.tbd */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		F52E8B08CC3F4914978B71D2 /* SplashScreen.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = SplashScreen.xcodeproj; path = "../node_modules/react-native-splash-screen/ios/SplashScreen.xcodeproj"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65E41E2F237E75E900D6E4CD /* libRCTAnimation.a in Frameworks */,
				654E93FA2362D59300891828 /* libbz2.1.0.tbd in Frameworks */,
				BF940D4F2254B8C00029A059 /* libdouble-conversion.a in Frameworks */,
				BF940D4E2254B89A0029A059 /* libthird-party.a in Frameworks */,
				BF940D4C2254B8470029A059 /* libyoga.a in Frameworks */,
				C83A64A61FB06B160093F9A2 /* libresolv.tbd in Frameworks */,
				16FCD574C5964E41BC1C8893 /* libz.tbd in Frameworks */,
				BF92A7052195147400213C8E /* SystemConfiguration.framework in Frameworks */,
				65AF0C081FE8B03E00790E94 /* CoreTelephony.framework in Frameworks */,
				C83F2772200CA47600C49C82 /* libRNImagePicker.a in Frameworks */,
				E27013DB2624500C00709A0B /* UMCommon.framework in Frameworks */,
				E27013DD2624500C00709A0B /* UMAnalytics.framework in Frameworks */,
				65AF0C041FE8AF3700790E94 /* Security.framework in Frameworks */,
				65AF0C021FE8AEEB00790E94 /* OpenGLES.framework in Frameworks */,
				65AF0C001FE8ADA100790E94 /* QuartzCore.framework in Frameworks */,
				65AF0BFE1FE8AD8100790E94 /* CoreLocation.framework in Frameworks */,
				BF92A7002195142B00213C8E /* CloudPushSDK.framework in Frameworks */,
				C8AC53101FE27A6C003270D4 /* libRNDeviceInfo.a in Frameworks */,
				ADBDB9381DFEBF1600ED6528 /* libRCTBlob.a in Frameworks */,
				C89C5BDF1FB1628F00C03E4C /* libc++.tbd in Frameworks */,
				00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */,
				00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */,
				65AF0C061FE8B02600790E94 /* libsqlite3.0.tbd in Frameworks */,
				00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */,
				133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */,
				00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */,
				139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */,
				E27013DC2624500C00709A0B /* UMPush.framework in Frameworks */,
				832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */,
				C83A64A41FB06AF60093F9A2 /* libicucore.tbd in Frameworks */,
				00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */,
				BF92A7022195142B00213C8E /* UTMini.framework in Frameworks */,
				139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */,
				C8A318171FB03A2E004D084E /* AliyunOSSiOS in Frameworks */,
				AACE67F1AD4F75C239BD406D /* libPods-NAGZ_mobile.a in Frameworks */,
				BF92A7032195142B00213C8E /* UTDID.framework in Frameworks */,
				BF92A7012195142B00213C8E /* AlicloudUtils.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4781E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4C91E0B4AEC006451C7 /* libReact.a in Frameworks */,
				2D02E4C21E0B4AEC006451C7 /* libRCTAnimation.a in Frameworks */,
				2D02E4C31E0B4AEC006451C7 /* libRCTImage-tvOS.a in Frameworks */,
				2D02E4C41E0B4AEC006451C7 /* libRCTLinking-tvOS.a in Frameworks */,
				2D02E4C51E0B4AEC006451C7 /* libRCTNetwork-tvOS.a in Frameworks */,
				2D02E4C61E0B4AEC006451C7 /* libRCTSettings-tvOS.a in Frameworks */,
				2D02E4C71E0B4AEC006451C7 /* libRCTText-tvOS.a in Frameworks */,
				2D02E4C81E0B4AEC006451C7 /* libRCTWebSocket-tvOS.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48D1E0B4A5D006451C7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00C302A81ABCB8CE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302B61ABCB90400DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302BC1ABCB91800DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302C01ABCB91800DB3ED1 /* libRCTImage.a */,
				3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302D41ABCB9D200DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */,
				3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302E01ABCB9EE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		139105B71AF99BAD00B5F7CC /* Products */ = {
			isa = PBXGroup;
			children = (
				139105C11AF99BAD00B5F7CC /* libRCTSettings.a */,
				3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		139FDEE71B06529A00C62182 /* Products */ = {
			isa = PBXGroup;
			children = (
				139FDEF41B06529B00C62182 /* libRCTWebSocket.a */,
				3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */,
				E5D1A2E71FA332A000E30658 /* libfishhook.a */,
				E5D1A2E91FA332A000E30658 /* libfishhook-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* NAGZ_mobile */ = {
			isa = PBXGroup;
			children = (
				BF92A706219514A100213C8E /* NAGZ_mobile.entitlements */,
				C8A318C61FB045A8004D084E /* service */,
				C8A317F91FB0333F004D084E /* Font */,
				C8A317F71FB0333F004D084E /* PrefixHeader.pch */,
				C8A317F81FB0333F004D084E /* RNBridgeModule.h */,
				C8A317FD1FB0333F004D084E /* RNBridgeModule.m */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				13B07FB71A68108700A75B9A /* main.m */,
				C80E059A1FDA22F90075C316 /* RNExitApp.h */,
				C80E059B1FDA22F90075C316 /* RNExitApp.m */,
				C8718A731FE3AA3100F3CBCE /* RNUpgrade.h */,
				C8718A741FE3AA3100F3CBCE /* RNUpgrade.m */,
			);
			name = NAGZ_mobile;
			sourceTree = "<group>";
		};
		146834001AC3E56700842450 /* Products */ = {
			isa = PBXGroup;
			children = (
				146834041AC3E56700842450 /* libReact.a */,
				3DAD3EA31DF850E9000B6D8A /* libReact.a */,
				3DAD3EA51DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA71DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */,
				3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */,
				BFA50BCC225458F5004D5542 /* libjsinspector.a */,
				BFA50BCE225458F5004D5542 /* libjsinspector-tvOS.a */,
				C8A316541FB03128004D084E /* libthird-party.a */,
				C8A316561FB03128004D084E /* libthird-party.a */,
				C8A316581FB03128004D084E /* libdouble-conversion.a */,
				C8A3165A1FB03128004D084E /* libdouble-conversion.a */,
				BFA50BD0225458F5004D5542 /* libjsi.a */,
				BFA50BD2225458F5004D5542 /* libjsiexecutor.a */,
				BFA50BD4225458F5004D5542 /* libjsi-tvOS.a */,
				BFA50BD6225458F5004D5542 /* libjsiexecutor-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2ED7714A50B74F19809FAA88 /* Resources */ = {
			isa = PBXGroup;
			children = (
				BFACB24E23220EC000B72F01 /* agzcustomer.ttf */,
				BFBE0173231511A4003CD88C /* agzIconfont.ttf */,
				D19B6ADFCD9644B48FB11C7D /* Entypo.ttf */,
				2EDEC68FC36C44598ED3B94C /* EvilIcons.ttf */,
				535F78D0FD844DC5B55FCA3F /* Feather.ttf */,
				B38FF1B1A8B143D0B9B801BD /* FontAwesome.ttf */,
				8B4C275094E64CFFA4E8DA23 /* Foundation.ttf */,
				15DC88BAF94C495C90DBCB54 /* Ionicons.ttf */,
				514815625C48401AB6CD577F /* MaterialCommunityIcons.ttf */,
				67625734A27848A58DF21D5F /* MaterialIcons.ttf */,
				5D72ABD468D847578A391290 /* Octicons.ttf */,
				C02F220AA20A4A85B93623C4 /* SimpleLineIcons.ttf */,
				7C50D2992B704854B939BEBC /* Zocial.ttf */,
				62D3690962664BC296D62C0D /* AntDesign.ttf */,
				2E654CCCB3C7407DAEACDD7E /* FontAwesome5_Brands.ttf */,
				D95665BA6195462799E16DFD /* FontAwesome5_Regular.ttf */,
				3C06F2152D244426A94A2C48 /* FontAwesome5_Solid.ttf */,
				25A9168BE2CB4B1B9EC4B3EB /* antfill.ttf */,
				217B63ADF93E4715AD562026 /* antoutline.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		5E91572E1DD0AC6500FF2AA8 /* Products */ = {
			isa = PBXGroup;
			children = (
				5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */,
				5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		654A75C7232F65DB00C55118 /* Products */ = {
			isa = PBXGroup;
			children = (
				654A75F7232F65DB00C55118 /* libRNNetworkInfo.a */,
				654A75F9232F65DB00C55118 /* libRNNetworkInfo-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		65A14B5F1FEC9F4700767363 /* ios */ = {
			isa = PBXGroup;
			children = (
				65A14B601FEC9F4700767363 /* lib */,
				65A14B681FEC9F4700767363 /* RCTBaiduMap */,
				65A14B751FEC9F4700767363 /* RCTBaiduMap.xcodeproj */,
			);
			name = ios;
			path = "../node_modules/react-native-baidu-map/ios";
			sourceTree = "<group>";
		};
		65A14B601FEC9F4700767363 /* lib */ = {
			isa = PBXGroup;
			children = (
				65A14B611FEC9F4700767363 /* BaiduMapAPI_Base.framework */,
				65A14B621FEC9F4700767363 /* BaiduMapAPI_Cloud.framework */,
				65A14B631FEC9F4700767363 /* BaiduMapAPI_Location.framework */,
				65A14B641FEC9F4700767363 /* BaiduMapAPI_Map.framework */,
				65A14B651FEC9F4700767363 /* BaiduMapAPI_Radar.framework */,
				65A14B661FEC9F4700767363 /* BaiduMapAPI_Search.framework */,
				65A14B671FEC9F4700767363 /* BaiduMapAPI_Utils.framework */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		65A14B681FEC9F4700767363 /* RCTBaiduMap */ = {
			isa = PBXGroup;
			children = (
				65A14B691FEC9F4700767363 /* BaiduMapModule.h */,
				65A14B6A1FEC9F4700767363 /* BaiduMapModule.m */,
				65A14B6B1FEC9F4700767363 /* BaseModule.h */,
				65A14B6C1FEC9F4700767363 /* BaseModule.m */,
				65A14B6D1FEC9F4700767363 /* GeolocationModule.h */,
				65A14B6E1FEC9F4700767363 /* GeolocationModule.m */,
				65A14B6F1FEC9F4700767363 /* RCTBaiduMapView.h */,
				65A14B701FEC9F4700767363 /* RCTBaiduMapView.m */,
				65A14B711FEC9F4700767363 /* RCTBaiduMapViewManager.h */,
				65A14B721FEC9F4700767363 /* RCTBaiduMapViewManager.m */,
				65A14B731FEC9F4700767363 /* RouteModule.h */,
				65A14B741FEC9F4700767363 /* RouteModule.m */,
			);
			path = RCTBaiduMap;
			sourceTree = "<group>";
		};
		65A14B761FEC9F4700767363 /* Products */ = {
			isa = PBXGroup;
			children = (
				65A14B7F1FEC9F4700767363 /* libRCTBaiduMap.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		78C398B11ACF4ADC00677621 /* Products */ = {
			isa = PBXGroup;
			children = (
				78C398B91ACF4ADC00677621 /* libRCTLinking.a */,
				3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7CB3062BD7B3C51B7291F270 /* Pods */ = {
			isa = PBXGroup;
			children = (
				26580D8169F8FD28971414E4 /* Pods-NAGZ_mobile.debug.xcconfig */,
				9C26C58D565EA6E025B22DDB /* Pods-NAGZ_mobile.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				654A75C6232F65DB00C55118 /* RNNetworkInfo.xcodeproj */,
				C874B8E8208041D6002A8B58 /* RCTOrientation.xcodeproj */,
				C83F276C200CA45900C49C82 /* RNImagePicker.xcodeproj */,
				C8AC52DC1FE277E2003270D4 /* RNDeviceInfo.xcodeproj */,
				5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */,
				146833FF1AC3E56700842450 /* React.xcodeproj */,
				00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */,
				ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */,
				00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */,
				00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */,
				78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */,
				00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */,
				139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */,
				832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */,
				00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */,
				139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */,
				DBF86E3C389044C3AF763916 /* BugsnagReactNative.xcodeproj */,
				11E93B9AA340423F907DD871 /* RNFS.xcodeproj */,
				F52E8B08CC3F4914978B71D2 /* SplashScreen.xcodeproj */,
				B6D8BA48A2D8493C82E85B3F /* RNVectorIcons.xcodeproj */,
				37CFC51AD86F405598CB1D75 /* RCTContacts.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		832341B11AAA6A8300B99B32 /* Products */ = {
			isa = PBXGroup;
			children = (
				832341B51AAA6A8300B99B32 /* libRCTText.a */,
				3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				E27013C126244B1300709A0B /* UMComponent */,
				74FF07BA23A8C13900EFCDB3 /* agzcustomer.ttf */,
				BFD52B442301648F00748D19 /* FileOpener */,
				BF92A77F21953EA100213C8E /* AliyunPush */,
				C8A318191FB03A40004D084E /* RCTHotUpdate */,
				C8A318021FB03A2D004D084E /* AliyunOSSSDK */,
				C8A318141FB03A2E004D084E /* RCTAliyunOSS */,
				13B07FAE1A68108700A75B9A /* NAGZ_mobile */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				98EA560FF5EA41FC91768D6E /* Frameworks */,
				2ED7714A50B74F19809FAA88 /* Resources */,
				E5D1A2CA1FA3329E00E30658 /* Recovered References */,
				7CB3062BD7B3C51B7291F270 /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* NAGZ_mobile.app */,
				2D02E47B1E0B4A5D006451C7 /* NAGZ_mobile-tvOS.app */,
				2D02E4901E0B4A5D006451C7 /* NAGZ_mobile-tvOSTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		98EA560FF5EA41FC91768D6E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				BFA50C5C22545BD7004D5542 /* libbz2.1.0.5.tbd */,
				BF92A7042195147400213C8E /* SystemConfiguration.framework */,
				BF92A6FD2195142700213C8E /* AlicloudUtils.framework */,
				BF92A6C32195142000213C8E /* CloudPushSDK.framework */,
				BF92A6FF2195142B00213C8E /* UTDID.framework */,
				BF92A6FE2195142B00213C8E /* UTMini.framework */,
				65A14B5F1FEC9F4700767363 /* ios */,
				65AF0C091FE8B04800790E94 /* libstdc++.6.0.9.tbd */,
				65AF0C071FE8B03B00790E94 /* CoreTelephony.framework */,
				65AF0C051FE8B02600790E94 /* libsqlite3.0.tbd */,
				65AF0C031FE8AF3200790E94 /* Security.framework */,
				65AF0C011FE8AEE200790E94 /* OpenGLES.framework */,
				65AF0BFF1FE8AD9400790E94 /* QuartzCore.framework */,
				65AF0BFD1FE8AD6100790E94 /* CoreLocation.framework */,
				C89C5BDE1FB1628F00C03E4C /* libc++.tbd */,
				C83A64A51FB06B160093F9A2 /* libresolv.tbd */,
				C83A64A31FB06AF60093F9A2 /* libicucore.tbd */,
				C83A64A11FB06ACC0093F9A2 /* libsqlite3.tbd */,
				C8A318721FB04144004D084E /* libbz2.1.0.tbd */,
				EA4FC41C5E84433B8AEB1B07 /* libz.tbd */,
				31A5BDA02C2749E23EFF26D6 /* libPods-NAGZ_mobile.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		ADBDB9201DFEBF0600ED6528 /* Products */ = {
			isa = PBXGroup;
			children = (
				ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */,
				E5D1A2D51FA332A000E30658 /* libRCTBlob-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BF92A77F21953EA100213C8E /* AliyunPush */ = {
			isa = PBXGroup;
			children = (
				BF92A78021953EEA00213C8E /* AliyunPush.h */,
				BF92A78121953EEA00213C8E /* AliyunPush.m */,
			);
			path = AliyunPush;
			sourceTree = "<group>";
		};
		BFD52B442301648F00748D19 /* FileOpener */ = {
			isa = PBXGroup;
			children = (
				BFD52B45230164BB00748D19 /* RNFileOpener.h */,
				BFD52B462301650800748D19 /* RNFileOpener.m */,
			);
			path = FileOpener;
			sourceTree = "<group>";
		};
		C83F276D200CA45900C49C82 /* Products */ = {
			isa = PBXGroup;
			children = (
				C83F2771200CA45A00C49C82 /* libRNImagePicker.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C874B8E9208041D6002A8B58 /* Products */ = {
			isa = PBXGroup;
			children = (
				C874B8ED208041D7002A8B58 /* libRCTOrientation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C89C5BD61FB149D700C03E4C /* Products */ = {
			isa = PBXGroup;
			children = (
				C89C5BDA1FB149D700C03E4C /* libRNVectorIcons.a */,
				BFA50BE2225458F5004D5542 /* libRNVectorIcons-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C89C5D0B1FB4359000C03E4C /* Products */ = {
			isa = PBXGroup;
			children = (
				C89C5D0F1FB4359000C03E4C /* libRCTContacts.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C8A317F91FB0333F004D084E /* Font */ = {
			isa = PBXGroup;
			children = (
				65983D552383F9F000C34D0B /* agzcustomer.ttf */,
				BFBE01712315118E003CD88C /* agzIconfont.ttf */,
				C8A317FA1FB0333F004D084E /* anticon.ttf */,
				C8A317FB1FB0333F004D084E /* FontAwesome.ttf */,
				C8A317FC1FB0333F004D084E /* Ionicons.ttf */,
			);
			name = Font;
			path = NAGZ_mobile/Font;
			sourceTree = "<group>";
		};
		C8A318021FB03A2D004D084E /* AliyunOSSSDK */ = {
			isa = PBXGroup;
			children = (
				C8A318031FB03A2D004D084E /* AliyunOSSiOS */,
				C8A318041FB03A2D004D084E /* OSSBolts.h */,
				C8A318051FB03A2D004D084E /* OSSCancellationToken.h */,
				C8A318061FB03A2D004D084E /* OSSCancellationTokenRegistration.h */,
				C8A318071FB03A2D004D084E /* OSSCancellationTokenSource.h */,
				C8A318081FB03A2D004D084E /* OSSClient.h */,
				C8A318091FB03A2D004D084E /* OSSCompat.h */,
				C8A3180A1FB03A2D004D084E /* OSSDefine.h */,
				C8A3180B1FB03A2D004D084E /* OSSExecutor.h */,
				C8A3180C1FB03A2D004D084E /* OSSLog.h */,
				C8A3180D1FB03A2D004D084E /* OSSModel.h */,
				C8A3180E1FB03A2D004D084E /* OSSNetworking.h */,
				C8A3180F1FB03A2D004D084E /* OSSService.h */,
				C8A318101FB03A2D004D084E /* OSSTask.h */,
				C8A318111FB03A2D004D084E /* OSSTaskCompletionSource.h */,
				C8A318121FB03A2D004D084E /* OSSUtil.h */,
				C8A318131FB03A2D004D084E /* OSSXMLDictionary.h */,
			);
			path = AliyunOSSSDK;
			sourceTree = "<group>";
		};
		C8A318141FB03A2E004D084E /* RCTAliyunOSS */ = {
			isa = PBXGroup;
			children = (
				C8A318151FB03A2E004D084E /* RCTAliyunOSS.h */,
				C8A318161FB03A2E004D084E /* RCTAliyunOSS.m */,
			);
			path = RCTAliyunOSS;
			sourceTree = "<group>";
		};
		C8A318191FB03A40004D084E /* RCTHotUpdate */ = {
			isa = PBXGroup;
			children = (
				C8A3181A1FB03A40004D084E /* AliyunOSS.h */,
				C8A3181B1FB03A40004D084E /* AliyunOSS.m */,
				C8A3181C1FB03A40004D084E /* BSDiff */,
				C8A318221FB03A40004D084E /* RCTHotUpdate.h */,
				C8A318231FB03A40004D084E /* RCTHotUpdate.m */,
				C8A318241FB03A40004D084E /* RCTHotUpdateDownloader.h */,
				C8A318251FB03A40004D084E /* RCTHotUpdateDownloader.m */,
				C8A318261FB03A40004D084E /* RCTHotUpdateManager.h */,
				C8A318271FB03A40004D084E /* RCTHotUpdateManager.m */,
				C8A318281FB03A40004D084E /* SSZipArchive */,
			);
			path = RCTHotUpdate;
			sourceTree = "<group>";
		};
		C8A3181C1FB03A40004D084E /* BSDiff */ = {
			isa = PBXGroup;
			children = (
				C8A3181D1FB03A40004D084E /* BSDiff.h */,
				C8A3181E1FB03A40004D084E /* BSDiff.m */,
				C8A3181F1FB03A40004D084E /* bzip */,
			);
			path = BSDiff;
			sourceTree = "<group>";
		};
		C8A3181F1FB03A40004D084E /* bzip */ = {
			isa = PBXGroup;
			children = (
				C8A318201FB03A40004D084E /* bspatch.c */,
				C8A318211FB03A40004D084E /* bspatch.h */,
			);
			path = bzip;
			sourceTree = "<group>";
		};
		C8A318281FB03A40004D084E /* SSZipArchive */ = {
			isa = PBXGroup;
			children = (
				C8A318291FB03A40004D084E /* aes */,
				C8A3183F1FB03A41004D084E /* Common.h */,
				C8A318401FB03A41004D084E /* Info.plist */,
				C8A318411FB03A41004D084E /* minizip */,
				C8A3184B1FB03A41004D084E /* SSZipArchive.h */,
				C8A3184C1FB03A41004D084E /* SSZipArchive.m */,
				C8A3184D1FB03A41004D084E /* ZipArchive.h */,
			);
			path = SSZipArchive;
			sourceTree = "<group>";
		};
		C8A318291FB03A40004D084E /* aes */ = {
			isa = PBXGroup;
			children = (
				C8A3182A1FB03A41004D084E /* aes.h */,
				C8A3182B1FB03A41004D084E /* aes_via_ace.h */,
				C8A3182C1FB03A41004D084E /* aescrypt.c */,
				C8A3182D1FB03A41004D084E /* aeskey.c */,
				C8A3182E1FB03A41004D084E /* aesopt.h */,
				C8A3182F1FB03A41004D084E /* aestab.c */,
				C8A318301FB03A41004D084E /* aestab.h */,
				C8A318311FB03A41004D084E /* brg_endian.h */,
				C8A318321FB03A41004D084E /* brg_types.h */,
				C8A318331FB03A41004D084E /* entropy.c */,
				C8A318341FB03A41004D084E /* entropy.h */,
				C8A318351FB03A41004D084E /* fileenc.c */,
				C8A318361FB03A41004D084E /* fileenc.h */,
				C8A318371FB03A41004D084E /* hmac.c */,
				C8A318381FB03A41004D084E /* hmac.h */,
				C8A318391FB03A41004D084E /* prng.c */,
				C8A3183A1FB03A41004D084E /* prng.h */,
				C8A3183B1FB03A41004D084E /* pwd2key.c */,
				C8A3183C1FB03A41004D084E /* pwd2key.h */,
				C8A3183D1FB03A41004D084E /* sha1.c */,
				C8A3183E1FB03A41004D084E /* sha1.h */,
			);
			path = aes;
			sourceTree = "<group>";
		};
		C8A318411FB03A41004D084E /* minizip */ = {
			isa = PBXGroup;
			children = (
				C8A318421FB03A41004D084E /* crypt.h */,
				C8A318431FB03A41004D084E /* ioapi.c */,
				C8A318441FB03A41004D084E /* ioapi.h */,
				C8A318451FB03A41004D084E /* mztools.c */,
				C8A318461FB03A41004D084E /* mztools.h */,
				C8A318471FB03A41004D084E /* unzip.c */,
				C8A318481FB03A41004D084E /* unzip.h */,
				C8A318491FB03A41004D084E /* zip.c */,
				C8A3184A1FB03A41004D084E /* zip.h */,
			);
			path = minizip;
			sourceTree = "<group>";
		};
		C8A318C61FB045A8004D084E /* service */ = {
			isa = PBXGroup;
			children = (
				C8221905208EFD960046A73E /* FMDB.framework */,
				C8221906208EFD960046A73E /* Qiniu.framework */,
			);
			path = service;
			sourceTree = "<group>";
		};
		C8AC52DD1FE277E2003270D4 /* Products */ = {
			isa = PBXGroup;
			children = (
				C8AC53081FE277E2003270D4 /* libRNDeviceInfo.a */,
				C8AC530A1FE277E2003270D4 /* libRNDeviceInfo-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E27013C126244B1300709A0B /* UMComponent */ = {
			isa = PBXGroup;
			children = (
				E27013C326244B3D00709A0B /* UMComponents */,
				E27013C226244B2B00709A0B /* UMReactBridge */,
			);
			path = UMComponent;
			sourceTree = "<group>";
		};
		E27013C226244B2B00709A0B /* UMReactBridge */ = {
			isa = PBXGroup;
			children = (
				E27013DF2624501800709A0B /* RNUMConfigure.h */,
				E27013E12624501800709A0B /* RNUMConfigure.m */,
				E27013DE2624501800709A0B /* UMAnalyticsModule.h */,
				E27013E02624501800709A0B /* UMAnalyticsModule.m */,
				E27013E22624501800709A0B /* UMPushModule.h */,
				E27013E32624501800709A0B /* UMPushModule.m */,
			);
			path = UMReactBridge;
			sourceTree = "<group>";
		};
		E27013C326244B3D00709A0B /* UMComponents */ = {
			isa = PBXGroup;
			children = (
				E27013DA2624500C00709A0B /* UMAnalytics.framework */,
				E27013D82624500C00709A0B /* UMCommon.framework */,
				E27013D92624500C00709A0B /* UMPush.framework */,
			);
			path = UMComponents;
			sourceTree = "<group>";
		};
		E5D1A2CA1FA3329E00E30658 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		E5D1A2EC1FA332A000E30658 /* Products */ = {
			isa = PBXGroup;
			children = (
				E5D1A3021FA332A000E30658 /* libRNFS.a */,
				E5D1A3041FA332A000E30658 /* libRNFS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E5D1A2EE1FA332A000E30658 /* Products */ = {
			isa = PBXGroup;
			children = (
				E5D1A2F81FA332A000E30658 /* libBugsnagReactNative.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E5D1A2F21FA332A000E30658 /* Products */ = {
			isa = PBXGroup;
			children = (
				E5D1A30A1FA332A000E30658 /* libSplashScreen.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* NAGZ_mobile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "NAGZ_mobile" */;
			buildPhases = (
				79B73454FB675218B8B022DA /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				A184AC0ACD4B45838451D2B9 /* [CP] Copy Pods Resources */,
				C8372ED7203FF80900B60889 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NAGZ_mobile;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* NAGZ_mobile.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E47A1E0B4A5D006451C7 /* NAGZ_mobile-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "NAGZ_mobile-tvOS" */;
			buildPhases = (
				2D02E4771E0B4A5D006451C7 /* Sources */,
				2D02E4781E0B4A5D006451C7 /* Frameworks */,
				2D02E4791E0B4A5D006451C7 /* Resources */,
				2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "NAGZ_mobile-tvOS";
			productName = "NAGZ_mobile-tvOS";
			productReference = 2D02E47B1E0B4A5D006451C7 /* NAGZ_mobile-tvOS.app */;
			productType = "com.apple.product-type.application";
		};
		2D02E48F1E0B4A5D006451C7 /* NAGZ_mobile-tvOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "NAGZ_mobile-tvOSTests" */;
			buildPhases = (
				2D02E48C1E0B4A5D006451C7 /* Sources */,
				2D02E48D1E0B4A5D006451C7 /* Frameworks */,
				2D02E48E1E0B4A5D006451C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */,
			);
			name = "NAGZ_mobile-tvOSTests";
			productName = "NAGZ_mobile-tvOSTests";
			productReference = 2D02E4901E0B4A5D006451C7 /* NAGZ_mobile-tvOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 610;
				ORGANIZATIONNAME = "© 2021 Huapu Cloud";
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = PASS2AQLA9;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.AccessWiFi = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
					2D02E47A1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						DevelopmentTeam = PASS2AQLA9;
						ProvisioningStyle = Automatic;
					};
					2D02E48F1E0B4A5D006451C7 = {
						CreatedOnToolsVersion = 8.2.1;
						DevelopmentTeam = PASS2AQLA9;
						ProvisioningStyle = Automatic;
						TestTargetID = 2D02E47A1E0B4A5D006451C7;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "NAGZ_mobile" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = E5D1A2EE1FA332A000E30658 /* Products */;
					ProjectRef = DBF86E3C389044C3AF763916 /* BugsnagReactNative.xcodeproj */;
				},
				{
					ProductGroup = 00C302A81ABCB8CE00DB3ED1 /* Products */;
					ProjectRef = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
				},
				{
					ProductGroup = 5E91572E1DD0AC6500FF2AA8 /* Products */;
					ProjectRef = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
				},
				{
					ProductGroup = 65A14B761FEC9F4700767363 /* Products */;
					ProjectRef = 65A14B751FEC9F4700767363 /* RCTBaiduMap.xcodeproj */;
				},
				{
					ProductGroup = ADBDB9201DFEBF0600ED6528 /* Products */;
					ProjectRef = ADBDB91F1DFEBF0600ED6528 /* RCTBlob.xcodeproj */;
				},
				{
					ProductGroup = C89C5D0B1FB4359000C03E4C /* Products */;
					ProjectRef = 37CFC51AD86F405598CB1D75 /* RCTContacts.xcodeproj */;
				},
				{
					ProductGroup = 00C302B61ABCB90400DB3ED1 /* Products */;
					ProjectRef = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
				},
				{
					ProductGroup = 00C302BC1ABCB91800DB3ED1 /* Products */;
					ProjectRef = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
				},
				{
					ProductGroup = 78C398B11ACF4ADC00677621 /* Products */;
					ProjectRef = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
				},
				{
					ProductGroup = 00C302D41ABCB9D200DB3ED1 /* Products */;
					ProjectRef = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
				},
				{
					ProductGroup = C874B8E9208041D6002A8B58 /* Products */;
					ProjectRef = C874B8E8208041D6002A8B58 /* RCTOrientation.xcodeproj */;
				},
				{
					ProductGroup = 139105B71AF99BAD00B5F7CC /* Products */;
					ProjectRef = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
				},
				{
					ProductGroup = 832341B11AAA6A8300B99B32 /* Products */;
					ProjectRef = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
				},
				{
					ProductGroup = 00C302E01ABCB9EE00DB3ED1 /* Products */;
					ProjectRef = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
				},
				{
					ProductGroup = 139FDEE71B06529A00C62182 /* Products */;
					ProjectRef = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
				},
				{
					ProductGroup = 146834001AC3E56700842450 /* Products */;
					ProjectRef = 146833FF1AC3E56700842450 /* React.xcodeproj */;
				},
				{
					ProductGroup = C8AC52DD1FE277E2003270D4 /* Products */;
					ProjectRef = C8AC52DC1FE277E2003270D4 /* RNDeviceInfo.xcodeproj */;
				},
				{
					ProductGroup = E5D1A2EC1FA332A000E30658 /* Products */;
					ProjectRef = 11E93B9AA340423F907DD871 /* RNFS.xcodeproj */;
				},
				{
					ProductGroup = C83F276D200CA45900C49C82 /* Products */;
					ProjectRef = C83F276C200CA45900C49C82 /* RNImagePicker.xcodeproj */;
				},
				{
					ProductGroup = 654A75C7232F65DB00C55118 /* Products */;
					ProjectRef = 654A75C6232F65DB00C55118 /* RNNetworkInfo.xcodeproj */;
				},
				{
					ProductGroup = C89C5BD61FB149D700C03E4C /* Products */;
					ProjectRef = B6D8BA48A2D8493C82E85B3F /* RNVectorIcons.xcodeproj */;
				},
				{
					ProductGroup = E5D1A2F21FA332A000E30658 /* Products */;
					ProjectRef = F52E8B08CC3F4914978B71D2 /* SplashScreen.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* NAGZ_mobile */,
				2D02E47A1E0B4A5D006451C7 /* NAGZ_mobile-tvOS */,
				2D02E48F1E0B4A5D006451C7 /* NAGZ_mobile-tvOSTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTActionSheet.a;
			remoteRef = 00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTGeolocation.a;
			remoteRef = 00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302C01ABCB91800DB3ED1 /* libRCTImage.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTImage.a;
			remoteRef = 00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTNetwork.a;
			remoteRef = 00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTVibration.a;
			remoteRef = 00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139105C11AF99BAD00B5F7CC /* libRCTSettings.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTSettings.a;
			remoteRef = 139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139FDEF41B06529B00C62182 /* libRCTWebSocket.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTWebSocket.a;
			remoteRef = 139FDEF31B06529B00C62182 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		146834041AC3E56700842450 /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 146834031AC3E56700842450 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTImage-tvOS.a";
			remoteRef = 3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTLinking-tvOS.a";
			remoteRef = 3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTNetwork-tvOS.a";
			remoteRef = 3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTSettings-tvOS.a";
			remoteRef = 3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTText-tvOS.a";
			remoteRef = 3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTWebSocket-tvOS.a";
			remoteRef = 3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA31DF850E9000B6D8A /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA51DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA71DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		654A75F7232F65DB00C55118 /* libRNNetworkInfo.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNNetworkInfo.a;
			remoteRef = 654A75F6232F65DB00C55118 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		654A75F9232F65DB00C55118 /* libRNNetworkInfo-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNNetworkInfo-tvOS.a";
			remoteRef = 654A75F8232F65DB00C55118 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		65A14B7F1FEC9F4700767363 /* libRCTBaiduMap.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTBaiduMap.a;
			remoteRef = 65A14B7E1FEC9F4700767363 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		78C398B91ACF4ADC00677621 /* libRCTLinking.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTLinking.a;
			remoteRef = 78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		832341B51AAA6A8300B99B32 /* libRCTText.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTText.a;
			remoteRef = 832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		ADBDB9271DFEBF0700ED6528 /* libRCTBlob.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTBlob.a;
			remoteRef = ADBDB9261DFEBF0700ED6528 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BCC225458F5004D5542 /* libjsinspector.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjsinspector.a;
			remoteRef = BFA50BCB225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BCE225458F5004D5542 /* libjsinspector-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libjsinspector-tvOS.a";
			remoteRef = BFA50BCD225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BD0225458F5004D5542 /* libjsi.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjsi.a;
			remoteRef = BFA50BCF225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BD2225458F5004D5542 /* libjsiexecutor.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjsiexecutor.a;
			remoteRef = BFA50BD1225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BD4225458F5004D5542 /* libjsi-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libjsi-tvOS.a";
			remoteRef = BFA50BD3225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BD6225458F5004D5542 /* libjsiexecutor-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libjsiexecutor-tvOS.a";
			remoteRef = BFA50BD5225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BFA50BE2225458F5004D5542 /* libRNVectorIcons-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNVectorIcons-tvOS.a";
			remoteRef = BFA50BE1225458F5004D5542 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C83F2771200CA45A00C49C82 /* libRNImagePicker.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNImagePicker.a;
			remoteRef = C83F2770200CA45A00C49C82 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C874B8ED208041D7002A8B58 /* libRCTOrientation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTOrientation.a;
			remoteRef = C874B8EC208041D7002A8B58 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C89C5BDA1FB149D700C03E4C /* libRNVectorIcons.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNVectorIcons.a;
			remoteRef = C89C5BD91FB149D700C03E4C /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C89C5D0F1FB4359000C03E4C /* libRCTContacts.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTContacts.a;
			remoteRef = C89C5D0E1FB4359000C03E4C /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8A316541FB03128004D084E /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = C8A316531FB03128004D084E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8A316561FB03128004D084E /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = C8A316551FB03128004D084E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8A316581FB03128004D084E /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = C8A316571FB03128004D084E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8A3165A1FB03128004D084E /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = C8A316591FB03128004D084E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8AC53081FE277E2003270D4 /* libRNDeviceInfo.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNDeviceInfo.a;
			remoteRef = C8AC53071FE277E2003270D4 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		C8AC530A1FE277E2003270D4 /* libRNDeviceInfo-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNDeviceInfo-tvOS.a";
			remoteRef = C8AC53091FE277E2003270D4 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A2D51FA332A000E30658 /* libRCTBlob-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTBlob-tvOS.a";
			remoteRef = E5D1A2D41FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A2E71FA332A000E30658 /* libfishhook.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libfishhook.a;
			remoteRef = E5D1A2E61FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A2E91FA332A000E30658 /* libfishhook-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libfishhook-tvOS.a";
			remoteRef = E5D1A2E81FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A2F81FA332A000E30658 /* libBugsnagReactNative.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libBugsnagReactNative.a;
			remoteRef = E5D1A2F71FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A3021FA332A000E30658 /* libRNFS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNFS.a;
			remoteRef = E5D1A3011FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A3041FA332A000E30658 /* libRNFS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNFS.a;
			remoteRef = E5D1A3031FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		E5D1A30A1FA332A000E30658 /* libSplashScreen.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSplashScreen.a;
			remoteRef = E5D1A3091FA332A000E30658 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74FF07BB23A8C13900EFCDB3 /* agzcustomer.ttf in Resources */,
				BFBE01722315119C003CD88C /* agzIconfont.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
				C8A317FE1FB0333F004D084E /* anticon.ttf in Resources */,
				4BCF3F90A330405A968F1976 /* Entypo.ttf in Resources */,
				E8A322DB1F15460D9EB060A3 /* EvilIcons.ttf in Resources */,
				C275382EADF14187BB019048 /* Feather.ttf in Resources */,
				236FAC065EC841AE89E9EC6F /* FontAwesome.ttf in Resources */,
				D2E9F10F937942769EB52213 /* Foundation.ttf in Resources */,
				961753EEF32A42B4AB6391A5 /* Ionicons.ttf in Resources */,
				11155355CC2E4389A34EC499 /* MaterialCommunityIcons.ttf in Resources */,
				D64973F2A7AB439E9541427A /* MaterialIcons.ttf in Resources */,
				822742CBD40B4B568DB2DF1B /* Octicons.ttf in Resources */,
				6DF4CBFAD0A9436A97545896 /* SimpleLineIcons.ttf in Resources */,
				B07B382687684CF486D728B0 /* Zocial.ttf in Resources */,
				F914F9A7BE9A4F61999E80FE /* AntDesign.ttf in Resources */,
				FFABEAD75B4C4E37A3F8EC49 /* FontAwesome5_Brands.ttf in Resources */,
				CBD8BE37C5F04BF2BCD7ED8E /* FontAwesome5_Regular.ttf in Resources */,
				50F34C4A0C8E48AAAB6A409D /* FontAwesome5_Solid.ttf in Resources */,
				E38B3C71298646049C242D94 /* antfill.ttf in Resources */,
				9230D13FE59F453686D17CDA /* antoutline.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4791E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BD1E0B4A84006451C7 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48E1E0B4A5D006451C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 8;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 1;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		2D02E4CB1E0B4B27006451C7 /* Bundle React Native Code And Images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native Code And Images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		79B73454FB675218B8B022DA /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NAGZ_mobile-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A184AC0ACD4B45838451D2B9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NAGZ_mobile/Pods-NAGZ_mobile-resources.sh",
				"${PODS_ROOT}/BaiduMapKit/BaiduMapKit/BaiduMapAPI_Map.framework/mapapi.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/QBImagePickerController/QBImagePicker.bundle",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Feather.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Foundation.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Octicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Zocial.ttf",
				"${PODS_ROOT}/RSKImageCropper/RSKImageCropper/RSKImageCropperStrings.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/mapapi.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AntDesign.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EvilIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Feather.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Foundation.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialCommunityIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Octicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SimpleLineIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Zocial.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RSKImageCropperStrings.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NAGZ_mobile/Pods-NAGZ_mobile-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C8A318591FB03A41004D084E /* hmac.c in Sources */,
				C8A318531FB03A41004D084E /* RCTHotUpdateManager.m in Sources */,
				E27013E52624501800709A0B /* RNUMConfigure.m in Sources */,
				E27013E62624501800709A0B /* UMPushModule.m in Sources */,
				C8A318511FB03A41004D084E /* RCTHotUpdate.m in Sources */,
				C8A318561FB03A41004D084E /* aestab.c in Sources */,
				C8A318581FB03A41004D084E /* fileenc.c in Sources */,
				E27013E42624501800709A0B /* UMAnalyticsModule.m in Sources */,
				C8A318521FB03A41004D084E /* RCTHotUpdateDownloader.m in Sources */,
				C8A318551FB03A41004D084E /* aeskey.c in Sources */,
				C8A3185A1FB03A41004D084E /* prng.c in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				C8A318541FB03A41004D084E /* aescrypt.c in Sources */,
				C8A3184E1FB03A41004D084E /* AliyunOSS.m in Sources */,
				C8A3185F1FB03A41004D084E /* mztools.c in Sources */,
				C8A318601FB03A41004D084E /* unzip.c in Sources */,
				C8A318181FB03A2E004D084E /* RCTAliyunOSS.m in Sources */,
				C8A3185C1FB03A41004D084E /* sha1.c in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				BF92A78221953EEA00213C8E /* AliyunPush.m in Sources */,
				C80E059C1FDA22F90075C316 /* RNExitApp.m in Sources */,
				C8A3185E1FB03A41004D084E /* ioapi.c in Sources */,
				C8A318011FB0333F004D084E /* RNBridgeModule.m in Sources */,
				C8A318611FB03A41004D084E /* zip.c in Sources */,
				C8A3184F1FB03A41004D084E /* BSDiff.m in Sources */,
				BFD52B472301650800748D19 /* RNFileOpener.m in Sources */,
				C8A318621FB03A41004D084E /* SSZipArchive.m in Sources */,
				C8A318501FB03A41004D084E /* bspatch.c in Sources */,
				C8718A751FE3AA3100F3CBCE /* RNUpgrade.m in Sources */,
				C8A3185B1FB03A41004D084E /* pwd2key.c in Sources */,
				C8A318571FB03A41004D084E /* entropy.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E4771E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D02E4BF1E0B4AB3006451C7 /* main.m in Sources */,
				2D02E4BC1E0B4A80006451C7 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D02E48C1E0B4A5D006451C7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2D02E4921E0B4A5D006451C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2D02E47A1E0B4A5D006451C7 /* NAGZ_mobile-tvOS */;
			targetProxy = 2D02E4911E0B4A5D006451C7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			path = NAGZ_mobile;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 26580D8169F8FD28971414E4 /* Pods-NAGZ_mobile.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = NAGZ_mobile/NAGZ_mobile.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COMPRESS_PNG_FILES = NO;
				CURRENT_PROJECT_VERSION = 2022051702;
				DEAD_CODE_STRIPPING = NO;
				DEPLOYMENT_POSTPROCESSING = NO;
				DEVELOPMENT_TEAM = PASS2AQLA9;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/service",
					"$(PROJECT_DIR)/UMComponent/UMComponents",
				);
				GCC_PREFIX_HEADER = NAGZ_mobile/PrefixHeader.pch;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/bugsnag-react-native/cocoa/**",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
					"$(SRCROOT)/../node_modules/react-native-vector-icons/RNVectorIconsManager",
					"$(SRCROOT)/../node_modules/react-native-contacts/ios/RCTContacts",
					"$(SRCROOT)/../node_modules/react-native-baidu-map/ios/RCTBaiduMap",
					"$(SRCROOT)/../node_modules/react-native-wechat/ios",
					"$(SRCROOT)/../node_modules/nagz-mobile-lib/ios/NAGZ_Lib_RN/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../node_modules/react-native-orientation/iOS/RCTOrientation/**",
				);
				INFOPLIST_FILE = NAGZ_mobile/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/NAGZ_mobile/IMService/Vendors/Voice",
					"$(PROJECT_DIR)/AliyunOSSSDK",
					"$(PROJECT_DIR)/IMService/Vendors/Voice",
				);
				MARKETING_VERSION = 1.1.54;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.huapuc.app;
				PRODUCT_NAME = NAGZ_mobile;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				STRIP_PNG_TEXT = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9C26C58D565EA6E025B22DDB /* Pods-NAGZ_mobile.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = NAGZ_mobile/NAGZ_mobile.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Huapu Cloud Infomation Technology (Wuhan) Co., Ltd (PASS2AQLA9)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Huapu Cloud Infomation Technology (Wuhan) Co., Ltd (PASS2AQLA9)";
				CODE_SIGN_STYLE = Manual;
				COMPRESS_PNG_FILES = NO;
				CURRENT_PROJECT_VERSION = 2022051702;
				DEAD_CODE_STRIPPING = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				DEVELOPMENT_TEAM = PASS2AQLA9;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/service",
					"$(PROJECT_DIR)/UMComponent/UMComponents",
				);
				GCC_PREFIX_HEADER = NAGZ_mobile/PrefixHeader.pch;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/bugsnag-react-native/cocoa/**",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
					"$(SRCROOT)/../node_modules/react-native-vector-icons/RNVectorIconsManager",
					"$(SRCROOT)/../node_modules/react-native-contacts/ios/RCTContacts",
					"$(SRCROOT)/../node_modules/react-native-baidu-map/ios/RCTBaiduMap",
					"$(SRCROOT)/../node_modules/react-native-wechat/ios",
					"$(SRCROOT)/../node_modules/nagz-mobile-lib/ios/NAGZ_Lib_RN/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../node_modules/react-native-orientation/iOS/RCTOrientation/**",
				);
				INFOPLIST_FILE = NAGZ_mobile/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/NAGZ_mobile/IMService/Vendors/Voice",
					"$(PROJECT_DIR)/AliyunOSSSDK",
					"$(PROJECT_DIR)/IMService/Vendors/Voice",
				);
				MARKETING_VERSION = 1.1.54;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.huapuckay.com;
				PRODUCT_NAME = NAGZ_mobile;
				PROVISIONING_PROFILE_SPECIFIER = huapucPush;
				SDKROOT = iphoneos;
				STRIP_PNG_TEXT = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2D02E4971E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/bugsnag-react-native/cocoa/**",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
					"$(SRCROOT)/../node_modules/react-native-vector-icons/RNVectorIconsManager",
					"$(SRCROOT)/../node_modules/react-native-contacts/ios/RCTContacts",
					"$(SRCROOT)/../node_modules/react-native-wechat/ios",
					"$(SRCROOT)/../node_modules/nagz-mobile-lib/ios/NAGZ_Lib_RN/**",
				);
				INFOPLIST_FILE = "NAGZ_mobile-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.NAGZ_mobile-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Debug;
		};
		2D02E4981E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/bugsnag-react-native/cocoa/**",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
					"$(SRCROOT)/../node_modules/react-native-vector-icons/RNVectorIconsManager",
					"$(SRCROOT)/../node_modules/react-native-contacts/ios/RCTContacts",
					"$(SRCROOT)/../node_modules/react-native-wechat/ios",
					"$(SRCROOT)/../node_modules/nagz-mobile-lib/ios/NAGZ_Lib_RN/**",
				);
				INFOPLIST_FILE = "NAGZ_mobile-tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.NAGZ_mobile-tvOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.2;
			};
			name = Release;
		};
		2D02E4991E0B4A5E006451C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "NAGZ_mobile-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.NAGZ_mobile-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NAGZ_mobile-tvOS.app/NAGZ_mobile-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		2D02E49A1E0B4A5E006451C7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_NO_COMMON_BLOCKS = YES;
				INFOPLIST_FILE = "NAGZ_mobile-tvOSTests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
					"\"$(SRCROOT)/$(TARGET_NAME)\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.facebook.REACT.NAGZ_mobile-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NAGZ_mobile-tvOS.app/NAGZ_mobile-tvOS";
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = PASS2AQLA9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "$(PODS_ROOT)/boost\" $(PODS_ROOT)/boost-for-react-native";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Huapu Cloud Infomation Technology (Wuhan) Co., Ltd (PASS2AQLA9)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Huapu Cloud Infomation Technology (Wuhan) Co., Ltd (PASS2AQLA9)";
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = PASS2AQLA9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "$(PODS_ROOT)/boost\" $(PODS_ROOT)/boost-for-react-native";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "NAGZ_mobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BA1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "NAGZ_mobile-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4971E0B4A5E006451C7 /* Debug */,
				2D02E4981E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D02E4BB1E0B4A5E006451C7 /* Build configuration list for PBXNativeTarget "NAGZ_mobile-tvOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D02E4991E0B4A5E006451C7 /* Debug */,
				2D02E49A1E0B4A5E006451C7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "NAGZ_mobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
