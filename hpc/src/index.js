/**
  * Sample React Native App
  * https://github.com/facebook/react-native
  * @flow
  */
import React, { Component } from 'react'
import { Provider, connect } from 'react-redux'
import { Provider as AntdProvider, Toast } from '@ant-design/react-native'
import { createLogger } from 'redux-logger'
import debounce from 'lodash/debounce'
import SplashScreen from 'react-native-splash-screen'
import { createStore, combineReducers, applyMiddleware, compose } from 'redux'
import createSagaMiddleware from 'redux-saga'
import { Alert, NativeModules, AppState, DeviceEventEmitter, View, Dimensions, Linking } from 'react-native'
import RNFS from 'react-native-fs'
import DeviceInfo from 'react-native-device-info'
import 'moment/locale/zh-cn'

import AgzActionBtn from './components/ActionButton'
import './utils/storage/asyncStore'
import AppNavigator from './router'
import rootSaga from './sagas'
import allReducers from './reducers'
import { endPoint } from './addons/FormComponent/Attachment/uploadFiles'
import { envParm, deviceType } from './config/sysPara'
import ModalTips from './components/Login/ModalTips'
import { GO_AUTH_MODAL } from './utils/jump'
import './config/prototype'
import {
  isRolledBack,
  checkUpdate,
  downloadUpdate,
  switchVersionLater,
} from './components/HotUpdate'
// import * as WeChat from 'react-native-wechat'
import DownloadProgress from './DownloadProgress'
import NavigationService, { setTopLevelNavigator } from './NavigationService'
import DownLoadProgressModal from './DownloadProgress/DownloadProgress'
import { CAMPERMISSIONS_CAMERAERA, CAMPERMISSIONS_WRITE, CAMPERMISSIONS_LOCATION } from './utils/AppPermissions'
import component from './containers/RunTime/Form/component'

const { height, width } = Dimensions.get('window')

global.formForceUpd = false
global.Toast = Toast
const sagaMiddleware = createSagaMiddleware()
const uReducer = combineReducers(allReducers)
const { version } = envParm
const { appStoreId } = envParm
const { bucket } = envParm
const middleware = [sagaMiddleware]
// if (__DEV__) {
//   const logger = createLogger({
//     // ...options
//   })
//   middleware.push(logger)
// }
const store = createStore(
  uReducer,
  compose(applyMiddleware(...middleware)),
)
// Extensions

rootSaga(sagaMiddleware.run)

function cpr_version(curV, reqV) {
  if (curV && reqV) {
    // 将两个版本号拆成数字
    const arr1 = curV.split('.')
    const arr2 = reqV.split('.')
    const minLength = Math.min(arr1.length, arr2.length)
    let position = 0
    let diff = 0
    // 依次比较版本号每一位大小，当对比得出结果后跳出循环（后文有简单介绍）
    while (position < minLength && ((diff = parseInt(arr1[position]) - parseInt(arr2[position])) == 0)) {
      position++
    }
    diff = (diff != 0) ? diff : (arr1.length - arr2.length)
    // 若curV大于reqV，则返回true
    if (diff == 0) {
      return 'equal'
    } if (diff < 0) {
      return 'update'
    }
  } else {
    console.log('版本号不能为空')
  }
}
const onNotificationOpened = debounce((params) => {
  const otherParams = JSON.parse(params.p)
  const { appId } = otherParams
  NavigationService.navigate('messageBox', { currentAppId: appId })
}, 500)
const androidApk = null
class Nagzmobile extends Component {
  constructor(props) {
    super(props)
    this.state = {
      currentAppState: AppState.currentState,
      downloadCount: 0,
      modalVisible: false,
    }
    this.checkCount = 0
    this.navigationRef = React.createRef()
  }

  async componentDidMount() {
    if (this.navigationRef.current) { setTopLevelNavigator(this.navigationRef.current) }
    SplashScreen.hide()
    if (this.isAndroid()) {
      await CAMPERMISSIONS_CAMERAERA()
      await CAMPERMISSIONS_LOCATION()
      await CAMPERMISSIONS_WRITE()
      NativeModules.BadgeAndroid.setBadge(0)
      DeviceEventEmitter.removeAllListeners('onNotificationOpened')
      this.notifListener = DeviceEventEmitter.addListener('onNotificationOpened', onNotificationOpened)
    }
    this.nativeVersion = DeviceInfo.getVersion()
    this.doupdate()
  }

  getVersion = (i) => {
    const info = { ...i, updateUrl: `${endPoint},${envParm.imgBuck},${envParm.imgName}` }
    const verArr = info.version.split('.')
    const backVersion = `${verArr[0]}.${verArr[1]}.${verArr[2]}`
    const jsVersion = verArr[verArr.length - 1]
    const updateType = cpr_version(this.nativeVersion, backVersion)
    return {
      info, verArr, backVersion, jsVersion, updateType,
    }
  }

  showUpdateModal = (v, hash) => {
    GO_AUTH_MODAL({
      opacity: 0.5,
      children: <ModalTips
        value={bucket === 'huapuc-pd' ? '检测到新版本' : `检查到新的版本${v},需要更新才能工作正常，是否更新?\n`}
        buttonName="立即更新"
        hash={hash}
      />,
      animationIn: 'slideInUp',
      animationOut: 'slideOutDown',
    })
  }

  isAndroid = () => deviceType === 1

  downAndroid = (downURL) => {
    Alert.alert('提示', '检测到新版本', [{
      text: '前往更新',
      onPress: () => {
        this.setState({
          modalVisible: true,
        })
        const filePath = `${RNFS.ExternalCachesDirectoryPath}/app-release.apk`
        const download = RNFS.downloadFile({
          fromUrl: downURL, // apk下载地址
          toFile: filePath,
          progress: (res) => {
            this.setState({ progressValue: Math.floor(Number((res.bytesWritten / res.contentLength) * 100)) })
          },
          progressDivider: 1,
        })
        download.promise.then((result) => {
          if (result.statusCode === 200) {
            SplashScreen.hide()
            DownloadProgress.close()
            NativeModules.ApkInstaller.install(filePath)
          }
        })
      },
    },
    ], { cancelable: false })
  }

  notForce = (info) => {
    downloadUpdate(info).then((hash) => {
      switchVersionLater(hash)
      SplashScreen.hide()
    })
  }

  checkAndroidUpdate = () => {
    if (isRolledBack) {
      SplashScreen.hide()
      return
    }
    checkUpdate().then((i) => {
      let str = i.version.split('.')
      const { info, jsVersion, updateType } = this.getVersion(i)
      let downURL = `http://qbox.huapuc.com/download/huapu-cloud-v${str[0]}.${str[1]}.${str[2]}.apk`
      // update
      if (updateType === 'update' && this.isAndroid()) {
        this.downAndroid(downURL)
      } else if (updateType === 'equal' && jsVersion > version) {
        if (info.isForce) {
          downloadUpdate(info).then((hash) => {
            RNFS.hash(`${RNFS.DocumentDirectoryPath}/_update/${hash}/bundle/index.android.bundle`, 'md5').then((res) => {
              if (info.hashCode === res) {
                this.showUpdateModal(info.version, hash)
              } else if (this.checkCount < 1) {
                this.checkCount += 1
                this.checkAndroidUpdate()
              }
              SplashScreen.hide()
            }).catch(() => {
              SplashScreen.hide()
            })
          }).then((error) => { // 失败
            // console.log(error)
          })
        } else {
          this.notForce(info)
        }
      } else {
        SplashScreen.hide()
      }
    })
  }

  checkIOSUpdate = () => {
    // console.log('asndblkasmnd.,amdklanslkdnalskdn')
    NativeModules.upgrade.upgrade(appStoreId, (msg) => {
      if (msg === 'YES') {
        Alert.alert('版本更新', '检查到AppStore有新的版本,前往升级', [{ text: '更新', onPress: () => { NativeModules.upgrade.openAPPStore(appStoreId) } }])
      } else {
        if (isRolledBack) {
          SplashScreen.hide()
          return
        }
        checkUpdate().then((i) => {
          const { info, jsVersion, updateType } = this.getVersion(i)
          if (updateType === 'equal' && jsVersion > version) {
            downloadUpdate(info).then((hash) => {
              RNFS.hash(`${RNFS.LibraryDirectoryPath}/Application Support/reactnativecnhotupdate/${hash}/bundle/main.jsbundle`, 'md5').then((res) => {
                if (info.hashCode === res) {
                  this.showUpdateModal(info.version, hash)
                } else if (this.checkCount < 1) {
                  this.checkCount += 1
                  this.checkIOSUpdate()
                }
                SplashScreen.hide()
              })
            })
          }
        })
      }
    })
  }

  showDownloadProgress = (visible, progressValue) => {
    DownloadProgress.showDownloadProgressWith(visible, progressValue)
  }

  doupdate = () => {
    // if (!__DEV__) {
    if (this.isAndroid()) {
      console.log('asdn,asdmnamsdasd.msad.,mas.dandroid1')
      this.checkAndroidUpdate()
    } else {
      this.checkIOSUpdate()
    }
    // }
  }

  handleAppState = (nextAppState) => {
    if (this.state.currentAppState.match(/inactive|background/) && nextAppState === 'active') {
      this.doupdate()
    }
    this.setState({ currentAppState: nextAppState })
  }

  render() {
    if (this.state.modalVisible) {
      return (
        <DownLoadProgressModal
          visible={this.state.modalVisible}
          progressValue={this.state.progressValue}
        />
      )
    }
    return (
      <Provider store={store}>
        <AntdProvider>
          <AppNavigator ref={this.navigationRef} />
          <AgzActionBtn />
        </AntdProvider>
      </Provider>
    )
  }
}
export default Nagzmobile

