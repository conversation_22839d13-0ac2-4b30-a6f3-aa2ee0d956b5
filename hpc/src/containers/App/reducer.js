import {
  SET_GLOBAL_USER, LOGOUT_ACTION_SUCCESS, SET_CURRENTAPPID, SET_USET_SETTINGS,
} from './constants'

const reducer = (state = {}, { type, payload }) => {
  switch (type) {
    case SET_GLOBAL_USER:
      return {
        ...state,
        logined: true,
        user: {
          ...payload,
        },
      }
    case LOGOUT_ACTION_SUCCESS:
      return { logined: false }
    case SET_CURRENTAPPID:
      return {
        ...state,
        currentAppId: payload.appId,
      }
    case SET_USET_SETTINGS:
      return {
        ...state,
        userSettings: { ...payload },
      }
    default:
      return state
  }
}

export default reducer
