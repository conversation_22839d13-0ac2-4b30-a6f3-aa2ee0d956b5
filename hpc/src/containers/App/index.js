import React from 'react'
import { connect } from 'react-redux'
import PropTypes from 'prop-types'
import { SET_CURRENTAPPID } from './constants'
import { get } from '../../request'
import { setAddress, setCurrentAppId } from '../../utils/storage'
import AppHome from '../RunTime/DashBoard'

// 获取地址组件所需要的省、市、区数据
const getAddress = get('/metaDatas/address')

class App extends React.PureComponent {
  componentDidMount() {
    // setCurrentAppId(this.props.appId)
    // this.props.setCurrentAppId(this.props.appId)
    this.preGetAddress()
  }
  componentWillReceiveProps = (nextProps) => {
    // if (this.props.appId !== nextProps.appId) {
    //   setCurrentAppId(nextProps.appId)
    //   this.props.setCurrentAppId(nextProps.appId)
    // }
  };
  preGetAddress=async () => {
    const res = await getAddress()
    if (res.errorCode === '0') {
      setAddress(res.data)
    }
  }
  render() {
    return (
      <AppHome appId={this.props.appId} appName={this.props.appName} />
    )
  }
}

const mapStateToProps = state => ({
  user: state.app.user,
})

const mapDispatchToProps = dispatch => ({
  setCurrentAppId: (appId) => { dispatch({ type: SET_CURRENTAPPID, payload: { appId } }) },
})

export default connect(mapStateToProps, mapDispatchToProps)(App)

App.propTypes = {
  appId: PropTypes.string,
  appName: PropTypes.string,
  setCurrentAppId: PropTypes.func,
}
