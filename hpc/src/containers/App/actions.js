import {
  SET_GLOBAL_USER,
  GET_APPS,
  SET_CURRENTAPPID,
  SET_USET_SETTINGS,
  LOGOUT_ACTION_SUCCESS,
} from './constants'
import { setUserInfo, setCurrentAppId } from '../../utils/storage'

export const setGlobalUser = (token, user) => {
  setUserInfo(token, user)
  return {
    type: SET_GLOBAL_USER,
    payload: user,
  }
}

export const setCurrentAppIdAction = (appId) => {
  setCurrentAppId(appId)
  return {
    type: SET_CURRENTAPPID,
    payload: { appId },
  }
}

export const dispachGlobalUser = user => ({
  type: SET_GLOBAL_USER,
  payload: user,
})

export const getApps = () => ({
  type: GET_APPS,
})

export const dispachGenUserSettings = (settings) => ({
  type: SET_USET_SETTINGS,
  payload: settings,
})

export const dispachLogOut = () => ({
  type: LOGOUT_ACTION_SUCCESS,
})
