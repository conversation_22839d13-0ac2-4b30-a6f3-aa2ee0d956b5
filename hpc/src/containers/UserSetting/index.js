import React from 'react'
import { connect } from 'react-redux'

import { LOGOUT_ACTION } from '../App/constants'
import UserNav from '../../components/UserSetting/UserNav'
import { UPDATE_USER, UPDATE_USERSETTINGS } from './constants'

class UserSetting extends React.Component {
  render() {
    return (
      <UserNav {...this.props} />
    )
  }
}

const mapStateToProps = ({ app: { user } }) => ({
  user,
})
const mapDispatchToProps = (dispatch) => ({
  save: (user, id) => dispatch({ type: UPDATE_USER, payload: { user, id } }),
  logout: () => dispatch({ type: LOGOUT_ACTION }),
  saveSettings: (userSettings) => dispatch({ type: UPDATE_USERSETTINGS, payload: { userSettings } }),
})

export default connect(mapStateToProps, mapDispatchToProps)(UserSetting)
