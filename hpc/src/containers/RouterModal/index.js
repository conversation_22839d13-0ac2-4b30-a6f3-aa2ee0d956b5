import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { View, StyleSheet, Dimensions, Platform, BackHandler, Keyboard } from 'react-native'
import { View as AnimateView } from 'react-native-animatable'
import NavigationService from '../../NavigationService'

const { height: deviceHeight, width: deviceWidth } = Dimensions.get('window')
/**
 * 使用路由Lightbox弄的遮罩层
 * @param {element}  children 遮罩层的内部元素
 * @param {number}  opacity 透明度,影响背景透明度
 * @param {number}  animationTiming 过渡动画时间，单位为毫秒
 * @param {string}  animationIn 进入动画
 * @param {string}  animationOut 退出动画
 * @param {bool}  isCustomGoBack 是否自定义后退事件
 */
export default class RouterModal extends Component {
  static propTypes = {
    children: PropTypes.element,
    opacity: PropTypes.number,
    animationTiming: PropTypes.number,
    animationIn: PropTypes.oneOf(['slideInDown', 'slideInUp', 'slideInLeft', 'slideInRight']),
    animationOut: PropTypes.oneOf(['slideOutDown', 'slideOutUp', 'slideOutLeft', 'slideOutRight']),
    isCustomGoBack: PropTypes.bool,
    formUpdate: PropTypes.bool,
  }
  static defaultProps={
    animationTiming: 300,
    animationIn: 'slideInRight',
    animationOut: 'slideOutRight',
    isCustomGoBack: false,
  }

  constructor(props) {
    super(props)
    this.operations = {}
    this.state = {
      animatedViewHeight: deviceHeight,
      screenDirection: 'PORTRAIT',
    }
  }

  componentWillMount = () => {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this.keyboardDidShow)
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
    if (Platform.OS === 'android' && !this.props.isCustomGoBack) {
      BackHandler.addEventListener('hardwareBackPress', this.closeModal)
    }
  }

  componentDidMount() {
    const { animationTiming, animationIn } = this.props
    this.contentRef[animationIn](animationTiming)
  }

  componentWillReceiveProps({ formUpdate }) {
    if (this.props.formUpdate !== formUpdate) {
      if (this.operations.reload) {
        this.operations.reload('refresh')
      }
    }
  }

  componentWillUnmount() {
    if (Platform.OS === 'android' && !this.props.isCustomGoBack) {
      BackHandler.removeEventListener('hardwareBackPress', this.closeModal)
    }
  }

  keyboardDidShow=(e) => {
    this.setState({
      animatedViewHeight: deviceHeight - e.endCoordinates.height,
    })
  }
  keyboardDidHide=() => {
    this.setState({
      animatedViewHeight: deviceHeight,
    })
  }

  closeModal = (refresh, data) => {
    const { animationTiming, animationOut } = this.props
    if (this.contentRef && this.contentRef[animationOut]) this.contentRef[animationOut](animationTiming)
    if (refresh) {
      NavigationService.popAndRefresh({ refresh: data })
    } else {
      NavigationService.popAndRefresh()
    }
    return true
  }
  rotateScreen=(screenDirection) => {
    this.setState({
      screenDirection,
      animatedViewHeight: !this.state.screenDirection === 'PORTRAIT' ? deviceWidth : deviceHeight })
  }
  renderModal = () => {
    const { children } = this.props
    const width = this.state.screenDirection === 'PORTRAIT' ? deviceWidth : deviceHeight
    const { animatedViewHeight } = this.state
    return (
      <View
        style={{
          width,
          flex: 1,
        }}
      >
        {children && React.cloneElement(children, {
          closeModal: this.closeModal,
          currentHeight: animatedViewHeight,
          operations: this.operations,
          rotateScreen: this.rotateScreen,
        })}
      </View>

    )
  }
  render() {
    const { animatedViewHeight } = this.state
    const { opacity, style } = this.props
    const width = this.state.screenDirection === 'PORTRAIT' ? deviceWidth : deviceHeight
    const height = !this.state.screenDirection === 'PORTRAIT' ? deviceWidth : deviceHeight
    return (
      <View style={[{ height, width }, styles.container, style]}>
        <View style={[{ opacity: opacity || 0, backgroundColor: '#000', height, width }]} />
        <AnimateView ref={ref => (this.contentRef = ref || {})} style={[styles.container, { height: animatedViewHeight }]}>
          {this.renderModal()}
        </AnimateView>
      </View>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0)',
  },
})
