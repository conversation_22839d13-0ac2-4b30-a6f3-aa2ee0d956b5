import { call, put, select, take, fork } from 'redux-saga/effects'
import { NativeModules, Platform, AsyncStorage } from 'react-native'
import { Toast, Portal, Modal } from '@ant-design/react-native'
import MD5 from '../../utils/encryption'
import { post, get } from '../../request'
import { LOGIN, REFRESH_CAPTCHA, CHECK_LOGINED, GET_REMEMBER_MOBILE, CHECK_NEED_CAPTCHA, GET_DEFAULT_APPS, GET_DEFAULT_APPS_SUCCESS, GET_FAVOR_NAV,SETSHOW } from './constants'
import { refreshCaptchaSucceed, setRememberMobile,setShow } from './actions'
import { setGlobalUser, setCurrentAppIdAction } from '../App/actions'
import { GO_APPS_HOME, GO_APPS_BOTTOM_TAB } from '../../utils/jump'
import { deviceType } from '../../config/sysPara'

export const loginToServer = post('/users/token')
export const getImgCaptchaToServer = get('/captcha/image')
const getDefaultApps = get('/users/defaultApp')
const getNavService = appId => get(`/apps/${appId}/navs`)

const pushInit = (deviceId, type) => {
  post('/users/push/init')({ data: { deviceId, deviceType: type } })
}

export const getRandomId = state => state.loginState.randomId
export const random = () => Math.random().toString().slice(2)

export function* getCaptcha() {
  const randomId = yield call(random)
  const result = yield call(getImgCaptchaToServer, { data: { randomId, type: 0 } })
  if (result.errorCode !== '0') {
    Modal.alert('', result.errorMsg, [{ text: '确定' }])
  } else {
    yield put(refreshCaptchaSucceed({
      randomId,
      captchaImg: result.data.captcha,
    }))
  }
}

/*
export function* rememberUsername(remember, val) {
  if (remember) {
    // 记住用户名操作
    yield call(setItem, 'login_username', val)
  } else {
    yield call(removeItem, 'login_username')
  }
}
*/
const bindDeviceId = () => {
  const mpush = NativeModules.AliyunPush
  mpush.getDeviceId((s) => {
    if (s) pushInit(s, Platform.OS === 'ios' ? 'iOS' : 'ANDROID')
  })
}

export function* login() {
  while (true) {
    const {
      payload: {
        mobile, password, remember, captcha,
      },
    } = yield take(LOGIN)
    // yield put(setShow({
    //   show:true,
    // }))
    const randomId = yield select(getRandomId)
    const tKey = Toast.loading('', 0)
    const result = yield call(loginToServer, {
      data: {
        mobile, password: MD5(password), randomId, captcha, type: deviceType,
      },
    })
    // console.log('asdjklasdj;lajdja;dj;ajkd;laj;dja;sdj;', result)
    if (result.errorCode !== '0') {
      yield call(Toast.fail, result.errorMsg, 3)
      if (result.data && result.data.errorTimes >= 3) {
        yield call(getCaptcha)
      }
      Portal.remove(tKey)
    } else {
      if (result.data.user.defaultAppId && result.data.user.defaultAppId !== '0' && result.data.user.defaultAppId !== 0) {
        yield put({
          type: GET_DEFAULT_APPS_SUCCESS,
          payload: { defaultAppId: result.data.user.defaultAppId, appName: result.data.user.appName },
        })
      }
      try {
        AsyncStorage.setItem('result', JSON.stringify(result.data.user))
      } catch (e) {
        __DEV__ && console.log('储存本地用户数据失败', e)
      }
      // console.log('asjdlajdlajdlkjalkdjlasjdladajdkladj', result, JSON.stringify(result.data.user))

      // bugsnag.setUser(result.data.user.mobile, result.data.user.name, result.data.user.email)
      // yield call(Toast.success, '登录成功',1)
      yield put(setGlobalUser(result.data.token, result.data.user))
      // 在此处设置当前appId,避免进入的不是主导航，而没有触发设置当前appid事件
      yield put(setCurrentAppIdAction(result.data.user.defaultAppId))
      yield call(bindDeviceId)
      Portal.remove(tKey)
      const navRes = yield call(getNavService(result.data.user.defaultAppId))
      if (navRes.errorCode !== '0' || !navRes.data.navGroups || !navRes.data.navGroups.length) {
        yield call(GO_APPS_HOME, result.data.user.defaultAppId, result.data.user.appName)
      } else {
        // 因为路由的定义必须为静态，所以根据自定义tab标签页的tab个数，决定跳转到对应的tabstack
        const navs = navRes.data.navGroups
        yield put({
          type: GET_FAVOR_NAV,
          payload: { navs },
        })
        yield call(GO_APPS_BOTTOM_TAB, result.data.user.defaultAppId, result.data.user.appName, navs)
      }
    }
  }
}

export function* getMyDefaultApps() {
  while (true) {
    try {
      yield take(GET_DEFAULT_APPS)
      // Toast.loading('', 0)
      const res = yield call(getDefaultApps)
      if (res.errorCode !== '0') {
        yield call(Toast.fail, res.errorMsg)
      } else {
        // Toast.hide()
        if (res.data.defaultAppId && res.data.defaultAppId !== '0' && res.data.defaultAppId !== 0) {
          yield put({
            type: GET_DEFAULT_APPS_SUCCESS,
            payload: res.data,
          })
        }
        // 在此处设置当前appId,避免进入的不是主导航，而没有触发设置当前appid事件
        yield put(setCurrentAppIdAction(res.data.defaultAppId))
        const navRes = yield call(getNavService(res.data.defaultAppId))
        if (navRes.errorCode !== '0' || !navRes.data.navGroups || !navRes.data.navGroups.length) {
          yield call(GO_APPS_HOME, res.data.defaultAppId, res.data.appName)
        } else {
          // 因为路由的定义必须为静态，所以根据自定义tab标签页的tab个数，决定跳转到对应的tabstack
          const navs = navRes.data.navGroups
          yield put({
            type: GET_FAVOR_NAV,
            payload: { navs },
          })
          yield call(GO_APPS_BOTTOM_TAB, res.data.defaultAppId, res.data.appName, navs)
        }
      }
    } catch (e) {
      // Toast.hide()
      console.error(e.message)
    }
  }
}

export function* getImgCaptcha() {
  while (true) {
    try {
      yield take(REFRESH_CAPTCHA)
      yield call(getCaptcha)
    } catch (e) {
      console.error(e.message)
    }
  }
}

export default [login, getImgCaptcha, getMyDefaultApps]
