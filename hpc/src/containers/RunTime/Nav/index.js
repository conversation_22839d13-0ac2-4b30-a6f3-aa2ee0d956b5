import PropTypes from 'prop-types'
import React from 'react'
import {
  View,
  Text,
  TouchableHighlight,
} from 'react-native'
import { connect } from 'react-redux'
import AGZGrid from '../../../components/Grid'
import { GO_LIST } from '../../../utils/jump'
import { APP_NAV_GET, APP_NAV_GET_DIR } from './constants'
import { delayLongPress } from '../../../config/sysPara'
import NavigationService from '../../../NavigationService'

class Nav extends React.Component {
  navTitleRender = (title) => {
    NavigationService.refresh({
      renderTitle: () => (
        <TouchableHighlight underlayColor="rgb(210, 230, 255)" onPress={() => alert('change application')} delayLongPress={Number(delayLongPress)}>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 40 }}>
            <Text
              style={{
                height: 40,
                width: 120,
                fontSize: 18,
                color: '#000',
              }}
            >{title}</Text>
          </View>
        </TouchableHighlight>
            ),
    })
  }
  componentWillMount() {
    const { appId, dirId } = this.props
    this.props.getNavData(appId, dirId)
    this.navTitleRender(this.props.appName)
  }
  onEdit=(key, action) => {
    if (action === 'remove') {
      if (Object.keys(this.props.navTabs).length === 1) {
        this.props.goNav()
      } else {
        let lastIndex
        if (this.props.formId === key) {
          Object.keys(this.props.navTabs).forEach((item, index) => {
            if (item === key) {
              lastIndex = index - 1
            }
          })
          if (lastIndex !== -1) {
            this.changeTab(Object.keys(this.props.navTabs)[lastIndex])
          }
        }
      }
      this.props.removeTab(key)
    }
  }

// 找到指定目录
// dirId:一般是准备进入的下一层目录ID
  getOneLevelAll(levelData) {
    try {
      const dirs = (typeof levelData.dirs === 'undefined') ? [] : levelData.dirs.map(item => ({ icon: item.icon, text: item.name, id: item.id, type: 'dir' }))
      const forms = (typeof levelData.forms === 'undefined') ? [] : levelData.forms.map(item => ({ icon: item.icon, text: item.name, id: item.id, type: 'form' }))
      return [...dirs, ...forms]
    } catch (e) {
      return []
    }
  }

  render() {
    const { dirId, navData, appId } = this.props
    const appData = this.getOneLevelAll(navData)
    return (
      <View>
        <AGZGrid
          gridData={appData}
          appId={appId}
          onSelectDir={this.props.goNav}
          onSelectForm={this.props.goPage}
        />
      </View>
    )
  }
}

const mapStateToProps = ({ navState: { nav, navTabs } },
  { params }) => ({
    navData: nav,
    navTabs,
  })

const mapDispatchToProps = (dispatch, { params }) => ({
  getNavData: (appId, dirId) => dispatch({
    type: APP_NAV_GET,
    payload: { appId, dirId },
  }),
  goPage: (appId, formId, name) => GO_LIST(appId, formId, name),
  goNav: (appId, dirId) => dispatch({
    type: APP_NAV_GET_DIR,
    payload: { appId, dirId },
  }),
})

Nav.propTypes = {
  getNavData: PropTypes.func.isRequired,
  navData: PropTypes.object.isRequired, // eslint-disable-line
  appId: PropTypes.string.isRequired,
  goNav: PropTypes.func.isRequired,
  goPage: PropTypes.func.isRequired,
}

export default connect(mapStateToProps, mapDispatchToProps)(Nav)

