import PropTypes from 'prop-types'
import React from 'react'
import {
  TouchableOpacity,
  Image,
} from 'react-native'
import { connect } from 'react-redux'
import _get from 'lodash/get'
import isObject from 'lodash/isObject'
import isArray from 'lodash/isArray'
import { View } from 'react-native-animatable'

import ShortcutMenu from '../../../components/Nav/RuntimeNav'
import { GO_REPORT, GO_RUNTIMENAV, GO_CREATE, GO_FULLREPORTVIEW, GO_EDIT } from '../../../utils/jump'
import { GET_APPS } from '../../App/constants'
import { APP_NAV_GET, APP_NAV_GET_DIR, APP_SETTING_NAV_GET } from '../Nav/constants'

import { getFormData } from '../Form/sagas'
import { COMPONENT_FORM_ID, COMPONENT_LIST_ID, COMPONENT_REPORT_ID } from '../../../addons/constants'
import {
  isFirstTime,
  markSuccess,
} from '../../../components/HotUpdate'
import Lists from '../../../addons/FormComponent/List'
import expression from '../../../utils/expression'
import Header from '../../../addons/FormComponent/ModalHeader'
import SideMenu from '../../../components/SideMenu'
import { getFilterDataId, getFormFirstDataId } from '../../../components/Nav/store'

class DashBoard extends React.Component {
  state = {
    mobileOnlyListField: false,
    formId: '',
    immediatelyToList: 0,
    reportData: null,
  }

  componentDidMount() {
    this.props.getApps()
    if (isFirstTime) {
      markSuccess()
    }
  }
  componentWillReceiveProps({ user }) {
    if (this.props.user && user && this.props.user.userId !== user.userId) {
      this.props.getApps()
    }
  }
  // componentWillReceiveProps(nextProps) {
  //   if (!isTrueApp(nextProps.appId) && nextProps.appId !== this.props.appId) {
  //     this.setState({ modal2: true })
  //   } else if (nextProps.appId !== this.props.appId) {
  //     this.setState({ modal2: false })
  //   }
  // }

  // 根据appID获取消息数
  // getMsgCount = async (appId) => {
  //   const result = await get(`/apps/${appId}/notifications/count`)()
  //   if (result.errorCode !== '0') {
  //     return undefined
  //   }
  //   setItem('MessageCount', result.data ? result.data.count : 0, 926, null)
  //   return result.data.count
  // }
  goNavDesign=() => GO_RUNTIMENAV(this.props.appId, this.props.appName)

  initField=async (field, keys = '') => {
    const properties = field.properties
    const property = {}
    const props = keys ? _get(properties, keys) : properties

    for (let i = 0; i < Object.keys(props).length; i += 1) {
      const propkeys = Object.keys(props)[i]
      const currkeys = keys ? `${keys}.${propkeys}` : propkeys
      const currProp = _get(properties, currkeys)
      if (isObject(currProp) && !isArray(currProp)) {
        this.initField({ properties }, currkeys)
      } else if (expression.isExpression(currProp)) {
        const data = await expression.exec(currProp, { [expression.EXPCONTEXT_NAMESPACE]: { ...properties } })
        property[currkeys] = data === undefined ? '' : data
      }
    }
    return {
      ...field,
      properties: {
        ...field.properties,
        ...property,
      },
    }
  }

  goPage= async (appId, form) => {
    const res = await getFormData(appId, form.id, '0')()
    if (res.errorCode !== '0') {
      // Toast.fail(`message: ${res.errorMsg}\nkey: ${res.errorCode}`)
      return undefined
    }

    const fields = res.data.config.form.fields
    const formField = fields.find(field => field.id === COMPONENT_FORM_ID)
    // 表单 移动端显示字段配置 属性只有列表字段
    const mobileFieldsConfig = formField && formField.properties && formField.properties.mobileFieldsConfig ? formField.properties.mobileFieldsConfig : []
    const mobileOnlyListField = mobileFieldsConfig.length === 1 ? fields.find(field => field.id === mobileFieldsConfig[0]) : false

    if (mobileOnlyListField && mobileOnlyListField.componentId === COMPONENT_LIST_ID) {
      // 处理字段中的表达式
      const field = await this.initField(mobileOnlyListField, '')
      this.setState({
        mobileOnlyListField: field,
        formId: form.id,
        immediatelyToList: this.state.immediatelyToList + 1,
      })
    } else if (mobileOnlyListField && mobileOnlyListField.componentId === COMPONENT_REPORT_ID) {
      this.setState({
        reportData: mobileOnlyListField,
      })
      GO_FULLREPORTVIEW({
        appId: this.props.appId, item: this.state.reportData, label: this.state.reportData.properties.label, selectReport: this.state.reportData.properties.selectReport, reportParameter: this.state.reportData.properties.reportParameter,
      })
    } else if (form.filter) {
      const filter = await expression.exec(form.filter, { [expression.EXPCONTEXT_NAMESPACE]: { } })
      const dataId = await getFilterDataId(appId, form.refId, { filter })
      if (dataId) GO_EDIT(null, appId, form.refId, 'formName', dataId)
    } else if (form.formType === 2 || form.formType === '2') {
      const dataId = await getFormFirstDataId(this.props.appId, form.refId)
      if (dataId) GO_EDIT(null, this.props.appId, form.refId, 'formName', dataId)
    } else {
      this.props.goPage(appId, form)
    }
  }

  headerRender = () => ({
    leftView: (
      <TouchableOpacity
        style={{ height: '100%', justifyContent: 'center' }}
        onPress={this.props.toggleMenu}
      >
        <Image source={require('../../../images/icons-burger-outline-line-default.png')} />
      </TouchableOpacity>
    ),
    centerText: this.props.appName,
  })
  render() {
    return (
      <View
        style={{
          flex: 1,
          borderTopWidth: 1,
          borderTopColor: '#f6f6f6',
          backgroundColor: '#F8F7F7',
        }}
      >
        <Header header={this.headerRender()} />
        <ShortcutMenu appName={this.props.appName} appId={this.props.appId} />

        {
          this.state.mobileOnlyListField &&
          <Lists
            {...this.state.mobileOnlyListField.properties}
            appId={this.props.appId}
            formId={this.state.formId}
            isRunTime
            changeESVS={(isTrue) => { this.setState({ enableScrollViewScrollList: isTrue }) }}
            enableScrollViewScroll={this.state.enableScrollViewScrollList}
            immediatelyToList={this.state.immediatelyToList}
          />
        }
      </View>
    )
  }
}

const mapStateToProps = ({ app: { user, currentAppId }, homeState, navState: { nav, navTabs, navSetting } }, param) => ({
  apps: [...homeState.owned, ...homeState.joined],
  navData: nav,
  navTabs,
  navSetting,
  param,
  user,
})

const mapDispatchToProps = dispatch => ({
  getApps: () => dispatch({ type: GET_APPS }),
  getNavData: (appId, dirId) => dispatch({
    type: APP_NAV_GET,
    payload: { appId, dirId },
  }),
  goPage: (appId, form) => {
    // TODO
    if (form.formType === 1 || form.formType === '1') {
      GO_REPORT(null, appId, form.id, form.formType, form.name)
    } else {
      GO_CREATE(null, appId, form.id, 0, form.name)
    }
  },
  goNav: (appId, dirId) => dispatch({
    type: APP_NAV_GET_DIR,
    payload: { appId, dirId },
  }),
  getSettingNav: appId => dispatch({
    type: APP_SETTING_NAV_GET,
    payload: { appId },
  }),
})

DashBoard.propTypes = {
  navData: PropTypes.array.isRequired, // eslint-disable-line
  appId: PropTypes.string,
  appName: PropTypes.string,
  navSetting: PropTypes.object,// eslint-disable-line
  goPage: PropTypes.func.isRequired,
  toggleMenu: PropTypes.func,
  refreshNav: PropTypes.number,
  getApps: PropTypes.func,
}
export default connect(mapStateToProps, mapDispatchToProps)(SideMenu(DashBoard))
