import React from 'react'
import { connect } from 'react-redux'

import {
    View,
    Text,
    TouchableHighlight,
    StyleSheet,
    Dimensions,
    TouchableOpacity,
    Platform,
    ListView,
} from 'react-native'
import { SearchBar, Checkbox, WhiteSpace, Button } from '@ant-design/react-native'
import { isChinese } from './convert'
import { deviceType } from '../../../config/sysPara'
import { inviteUser } from './action'
import getColor from '../../../utils/colors'
// import Contacts from 'react-native-contacts';

const Contacts = require('react-native-contacts')

const letters = []
const SectionHeight = 20
const rowHeight = 60
let finalContact = []
let contactData = []
let totalHeight = []// 每个字母对应的城市
let { height, width } = Dimensions.get('window')
let that = null
const pinyin = require('pinyin')

const geCCC = callback => Contacts.getAll((err, contacts) => {
  finalContact = []
  contactData = []

  if (err === 'denied') {
    // error
  } else {
    // contacts returned in [] 联系人数组
    for (const value of contacts) {
      if (!value.familyName && !value.givenName && value.phoneNumbers) {
        contactData.push({ name: '未知用户', phone: value.phoneNumbers[0].number })
      } else {
        contactData.push({ name: (isChinese(value.familyName) || isChinese(value.givenName)) ? (value.familyName && value.familyName.length > 0 ? value.familyName : '') + (value.givenName && value.givenName.length > 0 ? value.givenName : '') : `${value.givenName && value.givenName.length > 0 ? value.givenName : ''} ${value.familyName && value.familyName.length > 0 ? value.familyName : ''}`, phone: (value.phoneNumbers && value.phoneNumbers.length > 0) ? value.phoneNumbers[0].number : '' })
      }
    }
     // 遍历数组 -> 判断是否是汉字 -> 是字母存入首字母  属性SecId ->汉字 转成拼音取首字母 , secID 为'other'其他分组
    for (const val of contactData) {
       // 手机号长度大于11
      if (val.phone.length > 11) {
            // 1.判断是否含+86.包含则移除
        if (val.phone.indexOf('+86') != -1) {
          val.phone = val.phone.slice(3)
        }
            // 2.移除含有空格或括号,判断是否含有-
        val.phone = val.phone.replace(/[\s|\-|\(|\)]+/g, '')
      }
       // console.log(contactData)
      if (isChinese(val.name)) {
          // console.log(val.name)
          // console.log('************************')
        const str = pinyin(val.name, {
          style: pinyin.STYLE_NORMAL,
          heteronym: true,
        })

        let pyStr = ''
        str.map((pyAll) => {
          pyStr += pyAll
        })
          // 中国用户名的全拼,用于拼音搜索
        val.piny = pyStr

        let resultStr = ''
        str.map((v0) => {
          resultStr += v0[0]
        })
           // console.log(resultStr)
        const newStr = resultStr.toUpperCase()
            // console.log(newStr);
        var asi = newStr.charAt(0).charCodeAt()
        val.secID = asi
        val.first = newStr.charAt(0)
      } else {
        var asi = val.name.toUpperCase().charAt(0).charCodeAt()
            // console.log('----')
            // console.log(val.name)
        val.secID = asi
        val.first = val.name.charAt(0).toUpperCase()
      }
    }
    const alphabet = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9']// #号代表既不是简体中文也不是英文的

    alphabet.map((v1) => {
      const oneContact = {}
      oneContact.secId = v1
      oneContact.secData = []
      contactData.map((v2) => {
        if (v2.first === v1) {
          oneContact.secData.push(v2)
        }
      })

      if (oneContact.secData.length > 0) {
        finalContact.push(oneContact)
      }
    })
    // console.log('finalContact----')
     // console.log(finalContact);
     // console.log('finalContact----')
  }
  callback(finalContact)
})

class InviteUsers extends React.Component {
  constructor(props) {
    super(props)
    const ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
    const getSectionData = (dataBlob, sectionID) => dataBlob[sectionID]

    const getRowData = (dataBlob, sectionID, rowID) => dataBlob[`${sectionID}:${rowID}`]
      // console.log(this.props.appId)
    const { appId } = this.props
      // console.log(appId)
    this.state = {
      filter: '',
      oldDataSource: [], // 存储原有列表数据用于搜索后还原列表
      defaultValue: [], // 被选中的数据
      dataSource: new ListView.DataSource({
        getSectionData, // 获取组中数据
        getRowData, // 获取行中的数据
        rowHasChanged: (r1, r2) => r1 !== r2,
        sectionHeaderHasChanged: (s1, s2) => s1 !== s2,
      }),
    }
    that = this
  }

  componentWillMount() {
    geCCC(this.formatContact.bind(this))
      // this.getAllContacts();
    const { inviteUser, inviteUserSuccess } = this.props
      // inviteUser()
     // inviteUserSuccess()
  }

  componentDidMount() {
    letters = []
    totalHeight = []
        // this.formatContact();
  }

  formatContact(finalContact) {
    let dataBlob = {},
      sectionIDs = [],
      rowIDs = []
    contact = []
    for (let i = 0; i < finalContact.length; i++) {
              // 组号放入
      sectionIDs.push(i)
              // 组头
      dataBlob[i] = finalContact[i].secId
              // 组中所有元素
      contact = finalContact[i].secData
      rowIDs[i] = []
              // 所有字母集合
      letters.push(finalContact[i].secId)
              // 遍历联系人数组
      for (let j = 0; j < contact.length; j++) {
                // 行号放入rowIds
        rowIDs[i].push(j)
                //
        dataBlob[`${i}:${j}`] = contact[j]
      }
              // 计算每个字母和下面section的总高度
      const eachHeight = SectionHeight + rowHeight * finalContact[i].secData.length
      totalHeight.push(eachHeight)
    }
            // console.log('***')
            //  console.log(dataBlob)
            //  console.log(sectionIDs)
            //  console.log(rowIDs)
            // console.log('***')
    this.setState({
      dataSource: this.state.dataSource.cloneWithRowsAndSections(dataBlob, sectionIDs, rowIDs),
      oldDataSource: this.state.dataSource.cloneWithRowsAndSections(dataBlob, sectionIDs, rowIDs),
    })
  }

  render() {
      // console.log(this.props.appId);

      // const {onClickInvite} = this.props;
    if (this.state.filter === '') {
      return (
        <View style={{ flex: 1, marginTop: Platform.OS == 'ios' ? 10 : 0 }}>
          <SearchBar
            placeholder="搜索联系人"
            onChange={this.onSearch}
            onSubmit={this.onChange}
            onClear={this.clear}
            onCancel={this.clear}
          />

          <ListView
            removeClippedSubviews={false}
            automaticallyAdjustContentInsets={false}
            enableEmptySections
            dataSource={this.state.dataSource}
            renderRow={this.renderRow}
            renderSectionHeader={this.renderSectionHeader}
            contentContainerStyle={styles.listViewStyle}
            ref={listView => this._listView = listView}
          />

          <View style={styles.letters}>
            {letters.map((letter, index) => this.renderLetters(letter, index))}
          </View>

          <View style={{ height: 0.5, backgroundColor: '#ddd' }} />
          <View style={styles.bottomView}>
            <Text style={styles.bottomText} >已选择: {this.state.defaultValue.length}人</Text>
            <Button
              style={styles.bottomButton} type="primary" size="small"
              onPress={() => {
                this.props.onClickInvite(this.state.defaultValue, this.props.appId)
              }}
            >确定 ({this.state.defaultValue.length}/{contactData.length})</Button>
          </View>
        </View>
      )
    } else {
      return (
        <View style={{ flex: 1, marginTop: Platform.OS == 'ios' ? 0 : 0 }}>
          <WhiteSpace />
          <SearchBar
            placeholder="搜索联系人"

            onChange={this.onSearch}
            onClear={this.clear}
            onSubmit={this.onChange}
            onCancel={this.clear}
          />
          <ListView
            removeClippedSubviews={false}
            enableEmptySections
            dataSource={this.state.dataSource}
            renderRow={this.renderRow}
            contentContainerStyle={styles.listViewStyle}
          />

          <View style={{ height: 0.5, backgroundColor: '#ddd' }} />
          <View style={styles.bottomView}>
            <Text style={styles.bottomText} >已选择: {this.state.defaultValue.length}人</Text>
            <Button
              style={styles.bottomButton} type="primary" size="small"
              onPress={() => {
                this.props.onClickInvite(this.state.defaultValue)
              }}
            >确定 ({this.state.defaultValue.length}/{contactData.length})</Button>
          </View>
        </View>
      )
    }
  }

    // 搜索事件
  onSearch = (value) => {
    if (value.length > 0) {
      const searchArray = []
      this.state.filter = value
      for (let i = 0; i < finalContact.length; i++) {
        let shortArr = new Array()
        shortArr = finalContact[i].secData
        if (shortArr.length > 1) {
          shortArr.map((vvCon) => {
            const str = vvCon.name
            let piny = vvCon.piny
            if (!piny || piny.length === 0) { piny = '' }
            if (str.indexOf(this.state.filter) != -1 || piny.indexOf(this.state.filter) != -1 || vvCon.phone.indexOf(this.state.filter) != -1) {
                // 包含
              searchArray.push(vvCon)
            }
          })
        } else {
          const str = shortArr[0].name
          let piny = shortArr[0].piny
          if (!piny || piny.length) { piny = '' }
          const ph = shortArr[0].phone
          if (str.indexOf(this.state.filter) != -1 || piny.indexOf(this.state.filter) != -1 || ph.indexOf(this.state.filter) != -1) {
            searchArray.push(shortArr[0])
          }
        }
      }
        // console.log(searchArray);
      var ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
      this.setState({
        dataSource: ds.cloneWithRows(searchArray),
      })
    } else {
      var ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2, sectionHeaderHasChanged: (s1, s2) => s1 !== s2 })
      this.setState({
        dataSource: this.state.oldDataSource,
      })
    }
  };

    // 清除事件
  clear = (value) => {
    this.state.filter = ''
    value = ''
      // console.log(this.state.oldDataSource);
    const ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
    this.setState({
      dataSource: this.state.oldDataSource,
    })
  };

    // 每行数据
  renderRow=(rowData, rowID, sectionID) => {
      // console.log(this.state.defaultValue)

    const color = {
      backgroundColor: getColor(parseInt(Math.random() * 18)),
    }

    const checked = this.state.defaultValue.indexOf(rowData.phone) !== -1
    return (

      <TouchableHighlight
        key={rowID} underlayColor={'#ddd'} onPress={() => {
          that.changedata(rowData)
        }}
      >
        <View style={styles.cellViewStyle}>
          <Checkbox
            style={{ marginLeft: 15, marginRight: 10 }} onChange={(e) => { e.target.checked = !e.target.checked; this.changeDefaultValue(rowData.phone) }}

            defaultChecked={checked}
          >
            {/**
        <Image style={styles.ImageStyle} source={{uri:'https://pic3.zhimg.com/ff35a298a36376b0026b932e16d37b82_im.png'}}/>
        */}
            <View style={[styles.ImageStyle, color]}>
              <Text style={{ fontSize: 18, color: '#fff', fontWeight: 'bold', textAlign: 'center', borderRadius: 9 }}>
                {rowData.name.substr(0, 1)}
              </Text>
            </View>
            <View style={styles.rightTextStyle}>
              <Text style={{ fontWeight: '600', marginTop: 0 }}>{rowData.name}</Text>
              <Text style={{ marginTop: 4 }}>{rowData.phone}</Text>
            </View>
          </Checkbox>
        </View>
      </TouchableHighlight>
    )
  }

    // 每组数据
  renderSectionHeader(sectionData, sectionID) {
      // console.log(sectionData);
      // console.log(sectionID);
    return (
      <View style={styles.headerView}>
        <Text style={styles.headerText}>{sectionData}</Text>
      </View>)
  }

    // render right index letters
  renderLetters(letter, index) {
    return (
      <TouchableOpacity key={index} activeOpacity={0.6} onPress={() => { this.scrollTo(index) }}>
        <View style={styles.letter}>
          <Text style={styles.letterText}>{letter}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  changedata=(contactName) => {
      // this.props.r
  }

    // touch right indexletters,
  scrollTo=(index) => {
    let position = 0
    for (let i = 0; i < index; i++) {
      position += totalHeight[i]
    }
    this._listView && this._listView.scrollTo({
      y: position, animated: false,
    })
  }

    //* ******处理选择及统计选中*******//
  changeDefaultValue = (data) => { // 处理选中
    const defaultValue = [...this.state.defaultValue]
    const index = defaultValue.indexOf(data)
    if (index !== -1) { // 包含
      defaultValue.splice(index, 1)
    } else {
      defaultValue.push(data)
    }

    this.setState({
      defaultValue,
    })
      // console.log(this.state.defaultValue,defaultValue);
  }

}

const mapStateToProps = ({ mobiles }, { appId }) => ({
  mobiles,
  appId,
})

const mapDispatchToProps = dispatch => ({
  onClickInvite: (contactParm, appId) => dispatch(inviteUser(contactParm, appId)),
})

export default connect(mapStateToProps, mapDispatchToProps)(InviteUsers)

// InviteUsers.propTypes = {
//   onClickInvite: React.PropTypes.func,
// }

// export default InviteUsers

const styles = StyleSheet.create({
  cellViewStyle: {
    height: 60,
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    backgroundColor: '#fff',
    borderBottomColor: '#ddd',
    alignItems: 'center',
    paddingRight: 20,
  },
  ImageStyle: {
    margin: 5,
    height: 44,
    width: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightTextStyle: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 10,
    height: 44,
    margin: 5,
  },
  rowTxtStyle: {
    height: 25,
    margin: 10,
  },
  listViewStyle: {
    marginTop: 0,
  },
  headerView: {
    height: 20,
    borderBottomColor: '#ddd',
    borderBottomWidth: 0.5,
    backgroundColor: '#f5f5f9',
  },
  headerText: {
    marginLeft: 20,
    fontWeight: '600',
  },
  bottomView: {
    height: 50,
    backgroundColor: '#fff',
    // borderTopWidth:0.5,
    borderTopColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomButton: {
    width: 90,
    marginRight: 15,
    height: 32,

  },
  bottomText: {
    width: 100,
    marginLeft: 15,
    fontSize: 15,
    color: '#2192ee',
  },
  letterText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#404040',
  },
  letters: {
    position: 'absolute',
    height: deviceType === 2 ? height - 50 - 64 - 44 : height - 50 - 44 - 54,
    top: 44,
    bottom: 0,
    right: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  letter: {
    height: 20,
    width: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

})

