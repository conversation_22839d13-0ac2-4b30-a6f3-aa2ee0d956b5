import { take, call, put, select, takeEvery, putResolve } from 'redux-saga/effects'
import { Toast, Portal } from '@ant-design/react-native'
import _get from 'lodash/get'
import set from 'lodash/set'

import cloneDeep from 'lodash/cloneDeep'
import isArray from 'lodash/isArray'
import isObject from 'lodash/isObject'
import { getComponents } from '../../../utils/storage/global'
import { get, post, put as putRequest } from '../../../request'
import * as formConstants from './constants'
import { RELOAD_LIST_DATA } from '../List/constants'
import expression from '../../../utils/expression'
import { COMPONENT_DATA_VALIDATION_ID, COMPONENT_NUMBER_ID, COMPONENT_COMPUTED_ID, COMPNENT_ATTACHMENT, isFormNumberComponent } from '../../../addons/constants'
import { parse } from '../../../utils/querystring'
import NavigationService from '../../../NavigationService'

const {
  getExpressionKeys,
  exec,
  getExpressionDataKeys,
  isExpression,
  getFieldxpressionKeys,
  ONLY_PORP_REG,
  EXPCONTEXT_NAMESPACE,
  setPropertiesSettingValue,
  findExp,
} = expression
const unRepeat = (arr) => {
  const res = []
  const len = arr.length
  for (let i = 0; i < len; i++) {
    if (res.indexOf(arr[i]) === -1) {
      res.push(arr[i])
    }
  }
  return res
}
export const getExpressionsByState = (state) => {
  const propertiesExp = state.reportFormDataState.propertiesExp
  const expressionProps = state.reportFormDataState.expressionProps
  const expressionDatas = state.reportFormDataState.expressionDatas
  return { propertiesExp, expressionProps, expressionDatas }
}

export const getFieldsByState = state => state.reportFormDataState.fields
export const getFailedVerifyFieldIds = state => state.reportFormDataState.failedVerify

export const getFieldExpressionPropsByState = state => state.reportFormDataState.fieldExpressionProps

export const getFormDataByState = state => state.reportFormDataState.data

export const getFormValidatesByState = state => state.reportFormDataState.validates

export const getQueryCondtAndData = state => state.reportFormDataState.queryCondtAndData

export const postFormData = (appId, formId) => post(`/apps/${appId}/forms/${formId}/datas`)
export const putFormData = (
  appId, formId,
  dataId,
) => putRequest(`/apps/${appId}/forms/${formId}/datas/${dataId}`)

export const getFormData = (appId, formId, dataId) => get(`/apps/${appId}/forms/${formId}/datas/${dataId}`)

export const getFormDatas = (appId, formId) => get(`/apps/${appId}/forms/${formId}/datas`)
export const preiodModelActive = (appId, periodModuleId, isActive) => putRequest(`/apps/${appId}/period/modules/${periodModuleId}/active?isActive=${isActive}`)

export const preiodModelStart = (appId, periodModuleId, isBlock) => putRequest(`/apps/${appId}/period/modules/${periodModuleId}/start?isBlock=${isBlock}`)
export function* initCreateFormData() {
  /**
   * 解决初始化时， 多层引用并且被引用的字段在引用的字段之前出现formData初始化为空的情况。
   */
  function* handler(formData, field, handled, fieldkey) {
    if (field.data && field.data.length !== 0) {
      if (formData[field.id] === undefined) {
        formData[field.id] = {} // eslint-disable-line
      }
      handled.push(field.id + fieldkey)
      const value = yield call(exec, field.data[fieldkey].value, { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...formData } })

      formData[field.id][fieldkey] = value === undefined ? null : (field.componentId === COMPONENT_NUMBER_ID ? Number(value) : value)// eslint-disable-line
    }
  }
  function* check(formData, handled, fields, field) {
    for (let i = 0; i < Object.keys(field.data).length; i += 1) {
      const fieldkey = Object.keys(field.data)[i]
      if (handled.indexOf(field.id + fieldkey) !== -1) continue
      const match1 = isExpression(field.data[fieldkey].value)
      if (match1) {
        const value = _get(field.properties, match1[1])
        const match = isExpression(value)
        if (match) {
          const expKeywords = findExp(match[1]).map(keyword => keyword.split('.')[0])
          for (let j = 0; j < expKeywords.length; j += 1) {
            const parent = fields.find(f => f.id === expKeywords[j])
            if (parent) {
              yield call(check, formData, handled, fields, parent)
            }
          }
        }
        yield call(handler, formData, field, handled, fieldkey)
      }
    }
  }
  const fields = yield select(getFieldsByState)
  const formData = {}
  for (let j = 1; j < fields.length; j += 1) {
    const field = fields[j]
    yield call(check, formData, [], fields, field)
  }
  yield putResolve({
    type: formConstants.REPORT_FORM_INIT_SUCCESS,
    payload: { formData },
  })
}

function* calComputedValue(fields) {
  const { propertiesExp } = yield select(getExpressionsByState)
  const formData = yield select(getFormDataByState)
  const handledFieldIds = []
  function* check(field) {
    if (field.componentId !== COMPONENT_COMPUTED_ID || handledFieldIds.indexOf(field.id) !== -1) return
    const defaultValue = propertiesExp[field.id].defaultValue
    const match = isExpression(defaultValue)
    if (match) {
      const expKeywords = findExp(match[1]).map(keyword => keyword.split('.')[0])
      for (let j = 0; j < expKeywords.length; j += 1) {
        const parent = fields.find(f => f.id === expKeywords[j])
        if (parent) {
          yield call(check, parent)
        }
      }
    }
    const data = yield call(exec, defaultValue, { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...formData } })
    set(field.properties, 'defaultValue', data)
    handledFieldIds.push(field.id)
    set(formData, field.id, {
      value: data,
      display: data,
    })
  }
  for (let i = 0; i < fields.length; i += 1) {
    yield call(check, fields[i])
  }
}

export function* updateFieldPropsByFromData() {
  const formData = yield select(getFormDataByState)
  const fields = yield select(getFieldsByState)
  const newField = fields.map((field) => {
    Object.keys(field.data).forEach((fieldKey) => {
      const exp = field.data[fieldKey].value
      if (typeof exp === 'string' && exp.match(ONLY_PORP_REG)) {
        const propKye = exp.match(ONLY_PORP_REG)[1]
        if (formData[field.id] && typeof formData[field.id][fieldKey] !== 'undefined') {
          set(field.properties, propKye, formData[field.id][fieldKey])
        }
      }
    })
    return field
  })
  yield call(calComputedValue, newField)
  yield putResolve({
    type: formConstants.REPORT_FIELDS_GET_SUCCESS,
    payload: { fields: newField },
  })
}

export function* initPropertiesExp() {
  const fields = yield select(getFieldsByState)
  const propertiesExpMap = {}
  const expressionProps = {}
  const expressionDatas = {}
  for (let i = 0; i < fields.length; i += 1) {
    const field = fields[i]
    const { propertiesExp, expressionProp } = yield call(getExpressionKeys, field.properties, fields)
    propertiesExpMap[field.id] = propertiesExp
    expressionProps[field.id] = expressionProp
    expressionDatas[field.id] = yield call(getExpressionDataKeys, field.data, field.properties)
  }
  const fieldExpressionProps = yield call(getFieldxpressionKeys, fields)
  yield putResolve({
    type: formConstants.REPORT_INIT_PROPERTIES_EXP,
    payload: {
      propertiesExp: propertiesExpMap, expressionProps, expressionDatas, fieldExpressionProps,
    },
  })
}

export function* initProperty(properties, keys = '') {
  const formData = yield select(getFormDataByState)
  const props = keys ? _get(properties, keys) : properties
  for (let i = 0; i < Object.keys(props).length; i += 1) {
    const propkeys = Object.keys(props)[i]
    const currkeys = keys ? `${keys}.${propkeys}` : propkeys
    const currProp = _get(properties, currkeys)
    if (isObject(currProp)) {
      yield call(initProperty, properties, currkeys)
    } else if (isExpression(currProp)) {
      const data = yield call(exec, currProp, { [EXPCONTEXT_NAMESPACE]: { ...properties, ...formData } })
      set(properties, currkeys, data === undefined ? '' : data)
    }
  }
  return properties
}

// 初始化属性
export function* initPropertiesData() {
  const fields = yield select(getFieldsByState)
  const newFields = []
  for (let i = 0; i < fields.length; i += 1) {
    const field = fields[i]
    const prop = yield call(initProperty, field.properties, '')
    newFields.push({ ...field, properties: cloneDeep(prop) })
  }
  yield putResolve({
    type: formConstants.REPORT_FIELDS_GET_SUCCESS,
    payload: { fields: newFields },
  })
}
export function getExtraProperty(components, field) {
  const component = components.find(({ type }) => type === field.type)
  const extraProperty = {}
  if (component) {
    const fieldPropertyKeys = Object.keys(field.properties)
    component.properties.forEach((property) => {
      if (fieldPropertyKeys.indexOf(property.name) === -1) {
        extraProperty[property.name] = property.value
      }
    })
  }
  return extraProperty
}

export function handleFields(originFields, components) {
  let fields = originFields.map(field => ({
    ...field,
    properties: {
      ...field.properties,
      ...getExtraProperty(components, field),
    },
  }))
  // 把属性中引用字段的表达式加上setting.value
  fields = fields.map(field => ({
    ...field,
    properties: {
      ...field.properties,
      ...setPropertiesSettingValue(field.properties, fields),
    },
  }))
  // 新增时给默认组件添加默认值
  // const query = browserHistory.getCurrentLocation().query
  /* if ((routeName === 'createPage' || routeName === 'viewPage') && query && query.refcolumn && query.refdataId) {
    const field = fields.find(({ id }) => id === query.refcolumn)
    const index = fields.findIndex(({ id }) => id === query.refcolumn)
    if (field && index !== -1) {
      fields = [...fields.slice(0, index), { ...field, properties: { ...field.properties, defaultValue: query.refdataId } }, ...fields.slice(index + 1)]
    }
  } */
  return fields
}

function* checkShouldCopy(fields, formData, isCopy) {
  if (isCopy) {
    fields.forEach((field) => {
      if (field.properties.enabled === false
        || (field.setting && field.data && isExpression(field.properties[isExpression(field.data[field.setting.value].value)[1]]))
        || isFormNumberComponent(field)
        || field.componentId === COMPNENT_ATTACHMENT
      ) {
        if (formData[field.id]) {
          formData[field.id] = {}
        }
      }
    })
  }
  yield putResolve({
    type: formConstants.REPORT_FORM_INIT_SUCCESS,
    payload: { formData },
  })
}
export function* initReportFormData() {
  while (true) {
    try {
      const {
        payload: {
          appId, formId, dataId, operator,
        },
      } = yield take(formConstants.REPORT_FORM_INIT)
      this.tKey = Toast.loading('正在加载中...', 0)
      const getFormDatasFn = yield call(getFormData, appId, formId, dataId || 0)
      const res = yield call(getFormDatasFn)
      if (res.errorCode === '0') {
        // 把组件中的属性和字段中的属性合并
        const components = yield call(getComponents)
        const fields = handleFields(res.data.config.form.fields, components)

        yield putResolve({
          type: formConstants.REPORT_FIELDS_GET_SUCCESS,
          payload: { fields },
        })
        yield call(initPropertiesExp) // 备份字段的表单式属性,更新值得时候可以找到数据
        if (dataId) {
          const formData = res.data
          yield call(checkShouldCopy, fields, formData, operator === '9')
          yield call(updateFieldPropsByFromData)
          /* TODO
          * 根据配置，取某个字段的值当做导航标题的一部分
          const tabFields = fields.find(fd => fd.id === fields.find(f => f.id === '1').properties.tabfield)
          if (tabFields) {
            const title = formData[tabFields.id][tabFields.setting.display] || ''
            yield put({
              type: APP_NAV_UPDATETABTITLE,
              payload: { title, key: `${formId}_${operator}_${dataId || 0}` },
            })
          }
          */
        } else {
          yield call(initCreateFormData)// 根据自动属性初始化表单数据,和验证数据
        }
        yield call(initPropertiesData)
        yield put({
          type: formConstants.REPORT_PAGE_INIT_DONE,
        })
      } else {
        yield call(Toast.fail, res.errorMsg)
      }
    } catch (e) {
      Toast.fail('数据请求错误', 3, () => {}, true)
      console.error(e)
    } finally {
      Portal.remove(this.tKey)
      this.tKey = null
    }
  }
}

export function findKeys(dataKeys, fieldExps) {
  let datakey = Object.keys(fieldExps).find(key => key === dataKeys)
  if (!datakey) {
    datakey = Object.keys(fieldExps).reduce((res, item) => {
      if (item.indexOf(dataKeys) === 0) {
        res.push(item)
      }
      return res
    }, [])
  }
  if (datakey) {
    if (typeof datakey === 'string') {
      datakey = [datakey]
    }
    return datakey
  }
  return []
}
export function* updatePropsByFormData(dataKeys) {
  const fieldExps = yield select(getFieldExpressionPropsByState)
  // let datakey = Object.keys(fieldExps).find(key => key === dataKeys)
  const datakeys = yield call(findKeys, dataKeys, fieldExps)
  for (let k = 0; k < datakeys.length; k += 1) {
    const key = datakeys[k]
    const fieldExp = fieldExps[key]
    const formDatas = yield select(getFormDataByState)
    const { propertiesExp } = yield select(getExpressionsByState)
    const fields = yield select(getFieldsByState)
    for (let i = 0; i < Object.keys(fieldExp).length; i += 1) {
      const fieldId = Object.keys(fieldExp)[i]
      let fieldIndex = -1
      const field = fields.find((item, idx) => {
        if (item.id === fieldId) {
          fieldIndex = idx
          return true
        }
        return false
      })
      const prop = field.properties
      for (let j = 0; j < fieldExp[fieldId].length; j += 1) {
        const propkey = fieldExp[fieldId][j]
        const exp = propertiesExp[fieldId][propkey]
        const value = yield call(exec, exp, { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...formDatas } })
        set(field.properties, propkey, value === undefined ? '' : value)
        yield call(updateprops, prop, propkey, fieldId) // eslint-disable-line
      }
      yield putResolve({
        type: formConstants.REPORT_PROPERTY_UPDATE_SUCCESS,
        payload: {
          field: { ...field, properties: { ...prop } },
          index: fieldIndex,
        },
      })
    }
  }
}

export function* updateFormData(properties, datakey, fieldId) {
  const formDatas = yield select(getFormDataByState)
  const fields = yield select(getFieldsByState)
  const field = fields.find(item => (item.id === fieldId))
  const formData = formDatas[fieldId]
  const value = yield call(exec, _get(field.data, datakey), { [EXPCONTEXT_NAMESPACE]: { ...properties, ...formDatas } })
  const dataKeys = datakey.split('.')
  if (dataKeys[1] === 'value') {
    formData[dataKeys[0]] = value
    yield putResolve({
      type: formConstants.REPORT_FORM_UPDATE_VALUE_SUCCESS,
      payload: { id: fieldId, value: formData },
    })
  }
  yield call(updatePropsByFormData, `${fieldId}.${dataKeys[0]}`)
}

function* updateFormDataByProps({
  expressionDatas, fieldId, propertykey, properties,
}) {
  if (!propertykey) return
  const willUpdatePropertyKeys = Object.keys(expressionDatas[fieldId])
    .filter(key => key.startsWith(propertykey))
  for (let i = 0; i < willUpdatePropertyKeys.length; i += 1) {
    const willUpdatePropertyKey = willUpdatePropertyKeys[i]
    for (let j = 0; j < expressionDatas[fieldId][willUpdatePropertyKey].length; j += 1) {
      const datakey = expressionDatas[fieldId][willUpdatePropertyKey][j]
      yield call(updateFormData, properties, datakey, fieldId)
    }
  }
}
export function* updateprops(properties, propertykey, fieldId) {
  const { propertiesExp, expressionProps, expressionDatas } = yield select(getExpressionsByState)
  const formDatas = yield select(getFormDataByState)
  // 更新某个属性, 这个属性可能关联某个data
  yield call(updateFormDataByProps, {
    expressionDatas,
    fieldId,
    propertykey,
    properties,
  })
  // 更新某个属性, 去查找有哪些其他属性引用了这个属性, 更新这些属性
  const expressionPropsFieldId = expressionProps[fieldId]
  // 找到 表达式-属性 的map中以这个属性为开头的值, 比如更新了defaultValue, 可能map关系是 defaultValue.f1: ['label']
  const willUpdatePropertyKeys = Object.keys(expressionPropsFieldId)
    .filter(key => key.startsWith(propertykey))
  for (let i = 0; i < willUpdatePropertyKeys.length; i += 1) {
    const willUpdatePropertyKey = willUpdatePropertyKeys[i]
    // 有可能一个属性被多个属性引用,所以expressionProps的值是个数组
    for (let j = 0; j < expressionPropsFieldId[willUpdatePropertyKey].length; j += 1) {
      const propKey = expressionPropsFieldId[willUpdatePropertyKey][j]
      // 拿到属性原始的表达式
      const orgValue = propertiesExp[fieldId][propKey]
      // 计算表达式
      const value = yield call(exec, orgValue, { [EXPCONTEXT_NAMESPACE]: { ...properties, ...formDatas } })
      set(properties, propKey, value === undefined ? '' : value)
      // 更新关联的属性
      if (expressionPropsFieldId[propKey]) {
        yield call(updateprops, properties, propKey, fieldId)
      }
      // 更新关联属性的data
      yield call(updateFormDataByProps, {
        expressionDatas,
        fieldId,
        propertykey: propKey,
        properties,
      })
    }
  }
  return properties
}

export function* formUpdateValue({ payload: { value, extraParam } }) {
  try {
    const fields = yield select(getFieldsByState)
    if (fields && fields.length !== 0) {
      let fieldIndex = -1
      const field = fields.find((item, idx) => {
        if (item.id === Object.keys(value)[0]) {
          fieldIndex = idx
          return true
        }
        return false
      })
      if (field) {
        const valueKey = Object.keys(value[field.id])[0]
        const valueData = value[field.id][valueKey]
        const prop = yield call(updateprops, set(field.properties, valueKey, valueData), valueKey, field.id)
        yield putResolve({
          type: formConstants.REPORT_PROPERTY_UPDATE_SUCCESS,
          payload: {
            field: { ...field, properties: { ...prop, $$isValidation: true } },
            index: fieldIndex,
          },
        })
      }
    }
  } catch (e) {
    console.error(e)
  }
}

function* everyAction({ payload: { value, extraParam } }) {
  if (isArray(value)) {
    for (let i = 0; i < value.length; i += 1) {
      yield call(formUpdateValue, { payload: { value: value[i], extraParam } })
    }
  } else {
    yield call(formUpdateValue, { payload: { value, extraParam } })
  }
}

export function* updateReportEveryValue() {
  yield* takeEvery(formConstants.REPORT_FORM_UPDATE_EVERY_VALUE, everyAction)
}

export function* reportUpdateValue() {
  while (true) {
    const { payload: { value, extraParam } } = yield take(formConstants.REPORT_FORM_UPDATE_VALUE)
    if (isArray(value)) {
      for (let i = 0; i < value.length; i += 1) {
        yield call(formUpdateValue, { payload: { value: value[i], extraParam } })
      }
    } else {
      yield call(formUpdateValue, { payload: { value, extraParam } })
    }
  }
}

export function* validate(fields, data, failedVerifyFieldIds, submitFieldId) {
  const validates = yield select(state => state.reportFormDataState.validates)
  const verifyTips = {}
  fields.filter(field => field.componentId === COMPONENT_DATA_VALIDATION_ID)
    .forEach((field) => {
      if (_get(data, [field.id, field.setting.value]) === false
          && field.properties.condition === true
          && !failedVerifyFieldIds[field.id]) {
        verifyTips[field.id] = field.properties
      }
    })
  for (let i = 0; i < Object.keys(verifyTips).length; i += 1) {
    const key = Object.keys(verifyTips)[i]
    if (!verifyTips[key].failedTip) {
      yield call(Toast.fail, verifyTips[key].verifytip)
    } else {
      yield put({
        type: formConstants.REPORT_VERIFYFAILED_SHOW,
        payload: { verify: { ...verifyTips[key], fieldId: key }, verifyfailedshow: true, submitFieldId },
      })
    }
    return true
  }
  return Object.keys(validates)
    .some((fieldId) => {
      const field = fields.find(({ id }) => id === fieldId)
      return (field && field.properties.required && validates[fieldId].required) || validates[fieldId].format
    }) || Object.keys(verifyTips).length > 0
}

function* blockOperator(appId, submitField) {
  const { properties: { blockOperator: { type, module } } } = submitField
  let res = {}
  if (type === '1' || type === '4') {
    const preiodModelActivefn = yield call(preiodModelActive, appId, module, type === '1')
    res = yield call(preiodModelActivefn)
  } else if (type === '2' || type === '3') {
    const preiodModelStartfn = yield call(preiodModelStart, appId, module, type === '2')
    res = yield call(preiodModelStartfn)
  }
  if (res.errorCode === '0') {
    notification.success({ message: '保存成功！！' })
  } else {
    notification.error({ message: res.errorMsg, key: res.errorCode })
  }
}

export function* submitForm() {
  while (true) {
    try {
      const {
        payload: {
          editPage, appId, formId, operator,
          dataId, fieldId: buttonId = -1, verifyfieldId,
        },
      } = yield take(formConstants.REPORT_FORM_SUBMIT)
      yield put({ type: formConstants.REPORT_FORM_UPDATE_ALL_VALUE })
      const data = yield select(getFormDataByState)
      const fields = yield select(getFieldsByState)
      const failedVerifyFieldIds = yield select(getFailedVerifyFieldIds)
      const failedVerifys = verifyfieldId ? { ...failedVerifyFieldIds, [verifyfieldId]: true } : failedVerifyFieldIds
      const isInvalid = yield call(
        validate,
        fields,
        data,
        failedVerifys,
        buttonId,
      )
      if (isInvalid) continue
      if (buttonId !== -1) {
        yield call(formUpdateValue, { payload: { value: { [buttonId]: { defaultValue: true } } } })
        const submitField = fields.find(f => f.id === buttonId)
        if (submitField.properties.blockOperator
          && submitField.properties.blockOperator.type
          && submitField.properties.blockOperator.module) {
          yield call(blockOperator, appId, submitField)
          continue
        }
      }
      let res = {}
      delete data.config
      const $$validate = Object.keys(failedVerifys).reduce((result, fid) => failedVerifys[fid] ? [...result, fid] : result, [])
      if (editPage) {
        const putFormDataFn = yield call(putFormData, appId, formId, dataId)
        res = yield call(putFormDataFn, { data: { ...data, $$validate } })
      } else {
        const postFormDataFn = yield call(postFormData, appId, formId, dataId)
        res = yield call(postFormDataFn, { data: { ...data, $$validate } })
      }
      if (res.errorCode === 'BRT-01011') {
        const fid = res.data.field
        yield put({
          type: formConstants.REPORT_VERIFYFAILED_SHOW,
          payload: { verify: { ...fields.find(f => f.id === fid).properties, fieldId: fid }, verifyfailedshow: true, submitFieldId: buttonId },
        })
        continue
      }
      if (res.errorCode !== '0') {
        yield call(Toast.fail, res.errorMsg)
      } else {
        yield call(Toast.success, '保存成功')
        /* if (buttonId !== -1) {
          const buttonField = fields.find(({ id }) => id === buttonId)
          if (buttonField) {
            const { properties: { afteroperation } } = buttonField
            if (afteroperation === '0') {
              yield putResolve({ type: formConstants.REPORT_CLEAR })
              yield putResolve({ type: formConstants.REPORT_FORM_INIT, payload: { appId, formId, dataId } })
            } else {
              yield put({
                type: APP_NAV_CLOSE_TAB,
                payload: {
                  key: `${formId}_${operator}`,
                  formId,
                  operator,
                  appId,
                },
              })
            }
          }
        } else { */
        yield putResolve({ type: formConstants.REPORT_CLEAR })
        // yield putResolve({ type: formConstants.REPORT_FORM_INIT, payload: { appId, formId, dataId } })
        yield put({ type: RELOAD_LIST_DATA }) // 强制更新列表页
        formForceUpd = !formForceUpd
        console.log('\n\n\n\n$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ submit \n\n')
        yield call(NavigationService.back) // 如果上级路由是表单，强制更新
        // }
      }
    } catch (e) {
      console.error(e.message)
    }
  }
}

export function* viewReportData() {
  while (true) {
    const {
      payload: {
        appId, formId, dataId, queryCondtAndData,
      },
    } = yield take(formConstants.REPORT_FORM_VIEW_DATA)
    const urlQuery = parse(queryCondtAndData.urlQuery)
    yield put({
      type: formConstants.REPORT_FORM_VIEW_DATA_SUCCESS,
      payload: {
        queryCondtAndData: {
          ...queryCondtAndData,
          urlQuery,
          offset: Number(urlQuery.$offset) + queryCondtAndData.dataIds.indexOf(dataId),
        },
      },
    })
    yield put(GO_VIEW(appId, formId, dataId))
  }
}

export function* goReportData() {
  while (true) {
    const {
      payload: {
        appId, formId, dataId, indexOffset,
      },
    } = yield take(formConstants.REPORT_FORM_VIEW_GODATA)
    const queryCondtAndData = yield select(getQueryCondtAndData)
    const { dataIds, urlQuery, count } = queryCondtAndData
    const toDataId = dataIds[dataIds.indexOf(dataId) + indexOffset]
    if (toDataId) {
      yield put({
        type: formConstants.REPORT_FORM_VIEW_DATA_SUCCESS,
        payload: {
          queryCondtAndData: {
            ...queryCondtAndData,
            offset: dataIds.indexOf(dataId) + indexOffset + Number(urlQuery.$offset),
          },
        },
      })
      yield put(GO_VIEW(appId, formId, toDataId))
    } else {
      const $offset = Number(urlQuery.$offset) + (indexOffset * urlQuery.$limit)
      if ($offset < count) {
        const result = yield call(getFormDatas(appId, formId), {
          data: {
            ...urlQuery,
            $offset,
            $count: false,
            $title: false,
          },
        })
        if (result.errorCode === '0') {
          const nextIds = unRepeat(result.data.items.map(({ id }) => id.value))
          yield put({
            type: formConstants.REPORT_FORM_VIEW_DATA_SUCCESS,
            payload: {
              queryCondtAndData: {
                ...queryCondtAndData,
                dataIds: nextIds,
                urlQuery: {
                  ...urlQuery,
                  $offset,
                },
                offset: queryCondtAndData.offset + indexOffset,
              },
            },
          })
          yield put(GO_VIEW(appId, formId, nextIds[indexOffset === 1 ? 0 : nextIds.length - 1]))
        }
      } else {
        yield put({
          type: formConstants.REPORT_FORM_VIEW_DATA_SUCCESS,
          payload: {
            queryCondtAndData: {
              ...queryCondtAndData,
              offset: count - 1,
            },
          },
        })
      }
    }
  }
}

export default [initReportFormData, reportUpdateValue, viewReportData, goReportData, updateReportEveryValue]
