import PropTypes from 'prop-types'
import React from 'react'
import { connect } from 'react-redux'
import * as formConstants from './constants'
import { GO_FORM_DESIGN, GO_EDIT, GO_COPY, GO_CREATE } from '../../../utils/jump'
import FormReportComponent from './component'
import { buttionIds } from '../../../components/DataTable/Store'
import { FormContext } from '../../../utils/reactContext'

const fn = () => { }

// 注意跟ReportForm同步
class Form extends React.PureComponent {
  render() {
    const findForm = this.props.fields.find(field => field.componentId === '10')
    // console.log('表单页', this.props, findForm)
    return (
      <FormContext.Provider
        value={{
          fields: this.props.fields,
          formData: this.props.data,
          goPage: this.props.goPage,
          submit: this.props.submit,
        }}
      >
        <FormReportComponent
          appName={this.props.appName}
          isCustomMainNavHeader={this.props.isCustomMainNavHeader}
          fields={this.props.fields}
          initFormData={this.props.initFormData}
          submit={this.props.submit}
          updateValue={this.props.updateValue}
          formValidate={this.props.formValidate}
          formId={this.props.formId}
          appId={this.props.appId}
          dataId={this.props.dataId}
          fieldId={this.props.fieldId}
          fromFormId={this.props.fromFormId}
          srcDataId={this.props.srcDataId}
          updateValidation={this.props.updateValidation}
          submittable={this.props.submittable}
          InvoServiceId={this.props.InvoServiceId}
          data={this.props.data}
          operator={this.props.operator}
          getNavTitle={this.props.getNavTitle}
          formTitle={this.props.formTitle}
          query={this.props.query}
          title=""
          formUpdate={this.props.formUpdate}
          isView={this.props.isView}
          doClear={this.props.doClear}
          updateEveryValue={this.props.updateEveryValue}
          updateExtraProps={this.props.updateExtraProps}
          updateEditedFlag={this.props.updateEditedFlag}
          currEditFieldId={this.props.currEditFieldId}
          viewData={this.props.viewData}
          formStatus={this.props.formStatus}
          edited={this.props.edited}
          submitInfoBox={this.props.submitInfoBox}
          hideSubmitBox={this.props.hideSubmitBox}
          refPropDatas={this.props.refPropDatas}
          refDisplay={this.props.refDisplay}
          isWorkFlowForm={findForm ? findForm.properties.isWorkFlowForm : false}
          isWfComplete={findForm ? findForm.properties.isWfComplete : false}
          currentkey={this.props.currentkey}
          fieldConfig={this.props.fieldConfig}
        />
      </FormContext.Provider>
    )
  }
}

const mapStateToProps = ({
  formDataState: {
    fields, data, validates, submittable, edited, currEditFieldId, queryCondtAndData, submitInfoBox, refPropDatas, refDisplay, currentkey, fieldConfig,
  },
  navState: { formTitle },
}, ownProps) => ({
  appName: ownProps.appName,
  fields,
  appId: ownProps.appId,
  formId: ownProps.formId,
  fieldId: ownProps.fieldId,
  dataId: ownProps.dataId,
  operator: ownProps.operator,
  fromFormId: ownProps.fromFormId,
  srcDataId: ownProps.srcDataId,
  formValidate: validates,
  submittable,
  edited,
  currEditFieldId,
  queryCondtAndData,
  formStatus: data.form_status,
  data,
  formTitle,
  InvoServiceId: ownProps.InvoServiceId,
  submitInfoBox,
  refPropDatas,
  refDisplay,
  currentkey,
  fieldConfig,
})

const mapDispatchToProps = (dispatch, ownProps) => ({
  initFormData: (appId, formId, dataId, operator, query, fieldId, fromFormId, srcDataId) => {
    dispatch({
      type: formConstants.CLEAR,
    })
    dispatch({
      type: formConstants.FORM_INIT,
      payload: {
        ...ownProps, appId, formId, dataId, operator, query, fieldId, fromFormId, srcDataId,
      },
    })
  },
  updateValue: (value, submitField) => {
    dispatch({
      type: formConstants.FORM_UPDATE_VALUE,
      payload: { value, submitField, extraParam: { ...ownProps } },
    })
  },
  updateEveryValue: (value, index) => dispatch({
    type: formConstants.FORM_UPDATE_EVERY_VALUE,
    payload: { value, index, extraParam: { ...ownProps } },
  }),
  updateExtraProps: ({ fieldId, props }) => dispatch({
    type: formConstants.UPDATE_EXTRA_PROPS,
    payload: { fieldId, props, extraParam: { ...ownProps } },
  }),
  submit: (fieldId, operator, data) => {
    dispatch({
      type: formConstants.FORM_SUBMIT,
      payload: {
        ...ownProps,
        callback: ownProps.callback,
        fieldId,
        operator,
        editPage: typeof (ownProps.dataId) !== 'undefined',
        srcFieldId: ownProps.fieldId,
        ...data,
      },
    })
  },
  goDesign: () => dispatch(GO_FORM_DESIGN(ownProps.appId, ownProps.formId, ownProps.formName)),
  goCopy: fieldId => dispatch(GO_COPY(ownProps.callback, ownProps.appId, ownProps.formId, ownProps.dataId, fieldId)),
  goEidt: () => dispatch(GO_EDIT(ownProps.appId, ownProps.formId, ownProps.dataId)),
  updateValidation: validation => dispatch({ type: formConstants.UPDATE_VALIDATION, payload: validation }),
  updateEditedFlag: fieldId => dispatch({ type: formConstants.FORM_UPDATE_FLAG, payload: fieldId }),
  hideSubmitBox: () => dispatch({ type: formConstants.FORM_SUBMIT_CHECKBOX_HIDE }),
  doClear: () => dispatch({ type: formConstants.CLEAR }),
  goPage: ({
    appId, formId, operator, dataId,
  }) => {
    if (operator === buttionIds.view || operator === buttionIds.import) {
      return GO_CREATE(fn, appId, formId)
    } else if (dataId && (operator === buttionIds.add || operator === buttionIds.delete)) {
      return GO_EDIT(fn, appId, formId, '', dataId, false)
    }
  },
})

Form.propTypes = {
  fields: PropTypes.arrayOf(PropTypes.object),
  initFormData: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired,
  goPage: PropTypes.func.isRequired,
  updateValue: PropTypes.func.isRequired,
  formValidate: PropTypes.object.isRequired,// eslint-disable-line
  formId: PropTypes.string.isRequired,
  appId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  fieldId: PropTypes.string,
  updateValidation: PropTypes.func,
  submittable: PropTypes.bool,
  InvoServiceId: PropTypes.string,
  data: PropTypes.object,// eslint-disable-line
  operator: PropTypes.string,
  getNavTitle: PropTypes.func,
  formTitle: PropTypes.string,
  query: PropTypes.string,
  currentkey: PropTypes.string,
  title: PropTypes.string,// eslint-disable-line
  formUpdate: PropTypes.any,// eslint-disable-line
  isView: PropTypes.bool,
  doClear: PropTypes.func,
  updateEveryValue: PropTypes.func,
  updateExtraProps: PropTypes.func,
  updateEditedFlag: PropTypes.func,
  currEditFieldId: PropTypes.string,
  viewData: PropTypes.arrayOf(PropTypes.object),
  formStatus: PropTypes.object,// eslint-disable-line
  edited: PropTypes.bool,
  submitInfoBox: PropTypes.shape({
    isShow: PropTypes.bool,
    isLoading: PropTypes.bool,
    fields: PropTypes.array,
  }),
  hideSubmitBox: PropTypes.func,
  refPropDatas: PropTypes.object,// eslint-disable-line
  isCustomMainNavHeader: PropTypes.bool,
}

export default connect(mapStateToProps, mapDispatchToProps)(Form)
