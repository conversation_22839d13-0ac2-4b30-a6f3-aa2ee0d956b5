import React from 'react'
import { View, Text, TouchableOpacity, Image, Platform, ImageStore, ImageEditor, BackHandler, DeviceEventEmitter } from 'react-native'
import { RNCamera } from 'react-native-camera'
import { Button } from 'nagz-mobile-lib'
import PropTypes from 'prop-types'
import { Icon, Portal, Toast } from '@ant-design/react-native'
import NavigationService from '../../../../NavigationService'
// import ImageCropPicker from 'react-native-image-crop-picker'
import ImagePicker from 'react-native-image-picker'
import { stylesFun } from './style'
import { deviceType } from '../../../../config/sysPara'
import { invocationServiceUploadReques } from '../../../../utils/event/DeviceEventEmitter'

const torchModeImgOff = require('../../../../images/icon-flash_light-off.png')
const torchModeImgOn = require('../../../../images/icon-flash_light-on.png')

const isIos = Platform.OS === 'ios'
const imgWidth = 720
// 手机拍完照 图片的宽为设计值 或 高为设计值
// 设计值指的是imgWidth的值
const ratios = {
  android: {
    width: {
      2: 0.535,
      13: 0.583,
      17: 0.78,
    },
    height: {
      2: 0.375,
      13: 0.4,
      17: 0.38,
    },
  },
  ios: {
    2: 0.5764,
    13: 0.62,
    17: 0.82,
  },
}

class CameraModal extends React.Component {
  state={
    flashMode: RNCamera.Constants.FlashMode.off,
    torchModeImg: torchModeImgOff,
    previewImg: null,
    uploadReques: null,
  }

  componentWillMount = () => {
    const { styles } = stylesFun(this.props.invocationServiceField.serviceType)
    this.styles = styles
  }

  componentDidMount=() => {
    if (this.props.doOperateFun) setTimeout(this.props.doOperateFun, 1000)
    DeviceEventEmitter.addListener(invocationServiceUploadReques, (uploadReques) => {
      this.setState({ uploadReques })
    })
  }

  componentWillReceiveProps=({ uploadReques }) => {
    if (uploadReques) {
      this.setState({ uploadReques })
    }
  }

  componentWillUnmount=() => {
    this.setState(() => { })
  }

  androidBack=() => {
    this.props.hideCamera()
    NavigationService.back()
  }

  openImageLibrary=() => {
    const options = {
      quality: 0.95,
      maxWidth: 1500,
      maxHeight: 1500,
      storageOptions: {
        skipBackup: true,
      },
    }
    ImagePicker.launchImageLibrary(options, (response) => {
      if (response.uri) {
        this.uploadReques(response)
      }
    })
  }

  uploadReques= (file) => {
    if (this.props.uploadReques) {
      this.props.uploadReques(file)
    } else if (this.state.uploadReques) {
      this.state.uploadReques(file)
    } else {
      Toast.fail('网络请求异常！', 3)
      return
    }
    this.androidBack()
  }

  takePicture=(camera) => {
    const tKey = Toast.loading('识别中...', 0)
    camera.takePictureAsync({
      width: imgWidth,
      quality: 0.5,
      fixOrientation: true,
    })
      .then((data) => {
        this.cropImage(data)
        Portal.remove(tKey)
      })
      .catch((err) => {
        console.log(err)
        Portal.remove(tKey)

        Toast.fail('打开相机失败', 3)
        setTimeout(() => {
          this.androidBack()
        }, 3000)
      })
  }

  cropImage=({ uri, width }) => {
    const type = this.props.invocationServiceField.serviceType
    const sizeHeight = imgWidth *
      (isIos ? ratios.ios[type] :
        (imgWidth === width ? ratios.android.width[type] : ratios.android.height[type]))
    const cropData = {
      offset: {
        x: width * 0.05,
        y: width / (isIos ? 6 : 8),
      },
      size: {
        width: width * 0.9,
        height: sizeHeight,
      },
    }
    ImageEditor.cropImage(
      uri,
      cropData,
      (previewImg) => {
        if (previewImg) {
          this.setState({
            previewImg,
          })
        }
      },
      (err) => {
        console.log(err)
        Toast.fail('识别失败', 3)
      },
    )
  }

  changeTorchMode=() => {
    if (this.state.flashMode === RNCamera.Constants.FlashMode.off) {
      this.setState({
        flashMode: RNCamera.Constants.FlashMode.on,
        torchModeImg: torchModeImgOn,
      })
    } else {
      this.setState({
        flashMode: RNCamera.Constants.FlashMode.off,
        torchModeImg: torchModeImgOff,
      })
    }
  }
  showPreviewImg=() => (
    <View style={this.styles.container}>
      <View style={this.styles.previewBox}>
        <Image resizeMode="stretch" style={this.styles.previewImg} source={{ uri: this.state.previewImg }} />
      </View>
      <View style={this.styles.buttonContainer}>
        <Button
          style={{ container: this.styles.buttonLeft }}
          onPress={() => {
            if (isIos)ImageStore.removeImageForTag(this.state.previewImg)
            this.setState({
              previewImg: null,
            })
          }}
        >重拍
        </Button>
        <Button
          style={{ container: this.styles.buttonRight }}
          onPress={() => {
            const date = new Date()
            this.uploadReques({ uri: this.state.previewImg, fileName: `${date.getTime()}.jpg` })
          }}
        >选择
        </Button>
      </View>
    </View>
  )

  RNCamera=() => (
    <View style={this.styles.container}>
      <RNCamera
        ref={(cam) => {
          this.camera = cam
        }}
        flashMode={this.state.flashMode}
        style={this.styles.preview}
      >
        <View style={this.styles.cameraContent}>
          <View style={this.styles.cameraContentCenterCen}>
            <View style={[this.styles.cameraContentBorderT, this.styles.cameraContentBorder]} />
            <View style={[this.styles.cameraContentBorderL, this.styles.cameraContentBorder]} />
            <View style={[this.styles.cameraContentBorderR, this.styles.cameraContentBorder]} />
            <View style={[this.styles.cameraContentBorderB, this.styles.cameraContentBorder]} />
          </View>
          <View style={this.styles.cameraContentTop} />
          <View style={this.styles.cameraContentCenter}>
            <View style={this.styles.cameraContentCenterLeft} />
            <View style={this.styles.cameraContentCenterRight} />
          </View>
          <View style={this.styles.cameraContentBottom} >
            <TouchableOpacity
              activeOpacity={0.8}
              style={this.styles.cameraContentBottomBot}
              onPress={() => { this.takePicture(this.camera) }}
            >
              <View
                style={this.styles.cameraContentBottomBotText}
              />
            </TouchableOpacity>
          </View>
        </View>
      </RNCamera>
    </View>
  )

  render() {
    return (
      <View>
        <View style={this.styles.title}>
          <View style={this.styles.titleLeft}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.androidBack}
              style={{ marginLeft: 10, marginRight: 20 }}
            >
              <Icon type="left" size="md" color="#41454B" />
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.changeTorchMode}
            >
              <Image source={this.state.torchModeImg} />
            </TouchableOpacity>
          </View>
          <View style={this.styles.titleCenter}>
            <Text style={{ fontSize: 20, color: '#41454B' }} >扫描</Text>
          </View>
          <View style={this.styles.titleRight}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.openImageLibrary}
            >
              <Text style={{ fontSize: 20, color: '#41454B', marginRight: 10 }}>相册</Text>
            </TouchableOpacity>
          </View>
        </View>
        { this.state.previewImg ? this.showPreviewImg() : this.RNCamera() }
      </View>
    )
  }
}

CameraModal.propTypes = {
  uploadReques: PropTypes.func,
  closeModal: PropTypes.func,
  hideCamera: PropTypes.func,
  doOperateFun: PropTypes.func,
  invocationServiceField: PropTypes.object,
}

export default CameraModal
