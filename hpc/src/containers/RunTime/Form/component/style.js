
import { StyleSheet, Dimensions, Platform } from 'react-native'

const width = Dimensions.get('window').width
const height = Dimensions.get('window').height
const isIos = Platform.OS === 'ios'
const titleHeight = 44 + (isIos ? 20 : 0)
const heightContent = height - titleHeight

export const windowStyles = {
  width,
  height,
}

const ratios = {
  2: 1.54,
  13: 1.42,
  17: 1.32,
}

export const stylesFun = (type) => {
  const ratio = ratios[type] || 1
  const imagePosition = {
    top: heightContent * 0.1,
    left: width * 0.05,
    width: width * 0.9,
    height: (width * 0.9) / ratio,
  }

  const styles = StyleSheet.create({
    title: {
      width,
      height: titleHeight,
      backgroundColor: '#fff',
      flexDirection: 'row',
      paddingTop: isIos ? 20 : 0,
    },
    titleLeft: {
      width: '30%',
      height: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    titleCenter: {
      width: '40%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    titleRight: {
      width: '30%',
      height: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    container: {
      width,
      height: height - titleHeight - (isIos ? 0 : 20),
      backgroundColor: '#fff',
    },
    previewBox: {
      width: '100%',
      height: '90%',
      position: 'relative',
    },
    previewImg: {
      position: 'absolute',
      width: imagePosition.width,
      height: imagePosition.height,
      left: imagePosition.left,
      top: imagePosition.top,
    },
    buttonContainer: {
      flexDirection: 'row',
      shadowColor: '#000000',
      shadowOpacity: 0.1,
      height: '10%',
      shadowOffset: {
        width: 0,
        height: 0,
      },
    },
    buttonLeft: {
      width: (width / 2),
      height: '100%',
      borderRightWidth: 0,
      shadowRadius: 0,
    },
    buttonRight: {
      width: (width / 2),
      height: '100%',
      borderLeftWidth: 0,
      shadowRadius: 0,
    },
    preview: {
      flex: 1,
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      flexDirection: 'row',
    },
    toolBar: {
      width: 200,
      margin: 40,
      backgroundColor: '#000000',
      justifyContent: 'center',
    },
    cameraContent: {
      width,
      height: heightContent - (isIos ? 0 : 20),
      flexDirection: 'column',
      position: 'relative',
    },
    cameraContentTop: {
      width: '100%',
      height: imagePosition.top,
      backgroundColor: 'rgba(0,0,0,.3)',
    },
    cameraContentCenter: {
      width: '100%',
      height: imagePosition.height,
      justifyContent: 'space-between',
      position: 'relative',
    },
    cameraContentBottom: {
      width: '100%',
      height: heightContent - imagePosition.top - imagePosition.height - (isIos ? 0 : 20),
      backgroundColor: 'rgba(0,0,0,.3)',
      alignItems: 'center',
    },
    cameraContentCenterLeft: {
      position: 'absolute',
      width: imagePosition.left,
      height: imagePosition.height,
      backgroundColor: 'rgba(0,0,0,.3)',
    },
    cameraContentCenterRight: {
      position: 'absolute',
      right: 0,
      width: imagePosition.left,
      height: imagePosition.height,
      backgroundColor: 'rgba(0,0,0,.3)',
    },
    cameraContentCenterCen: {
      position: 'absolute',
      width: imagePosition.width,
      height: imagePosition.height,
      left: imagePosition.left,
      top: imagePosition.top,
      backgroundColor: 'rgba(0,0,0,0)',
    },
    cameraContentBorder: {
      position: 'absolute', width: 20, height: 20, borderColor: '#17A9FF',
    },
    cameraContentBorderT: {
      top: 0, left: 0, borderLeftWidth: 2, borderTopWidth: 2,
    },
    cameraContentBorderL: {
      bottom: 0, left: 0, borderLeftWidth: 2, borderBottomWidth: 2,
    },
    cameraContentBorderR: {
      top: 0, right: 0, borderRightWidth: 2, borderTopWidth: 2,
    },
    cameraContentBorderB: {
      bottom: 0, right: 0, borderRightWidth: 2, borderBottomWidth: 2,
    },
    cameraContentBottomBot: {
      flex: 0,
      backgroundColor: 'rgba(0,0,0,.5)',
      borderRadius: 35,
      width: 70,
      height: 70,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      bottom: '5%',
    },
    cameraContentBottomBotText: {
      backgroundColor: '#fff',
      borderRadius: 25,
      width: 50,
      height: 50,
    },
  })

  return {
    imagePosition,
    styles,
    ratio,
  }
}

export const tabStyle = StyleSheet.create({
  centerText: {
    width: 160,
    height: 30,
    borderColor: '#E5E5E5',
    borderWidth: 1,
    borderRadius: 6,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  touchBut: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchLeftBut: {
    width: 78,
    borderBottomLeftRadius: 6,
    borderTopLeftRadius: 6,
  },
  touchRightBut: {
    width: 79,
    borderBottomRightRadius: 6,
    borderTopRightRadius: 6,
  },
  tabScrollView: {
    backgroundColor: '#fff',
    height: height - 74,
    padding: 10,
    width,
  },
  tabCom: {
    width: '50%',
    height: 44,
    flexWrap: 'wrap',
    justifyContent: 'center',
    padding: 12,
  },
  WhiteSpace: {
    backgroundColor: '#F8F7F7',
    borderBottomWidth: 1,
    borderColor: '#E5E5E5',
  },
  wfComment: {
    margin: 0,
    backgroundColor: '#fff',
    paddingLeft: 25,
    paddingRight: 25,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(201, 202, 202, 0.2)',
  },
})
