import React from 'react'
import PropTypes from 'prop-types'

import {
  Device<PERSON>ventEmitter,
  View,
  BackHandler,
  TextInput,
  Keyboard,
  Text,
  TouchableOpacity,
} from 'react-native'
import DropdownAlert from 'react-native-dropdownalert'
import { isEqual, indexOf, get } from 'lodash'
import NavigationService, {
  getNavigatorInstance,
} from '../../../../NavigationService'
import FormButtons from '../../../../components/FormButtons'
import { deviceType } from '../../../../config/sysPara'
import Fields from '../../../../components/FormDesign/Fields/Fields'
import CameraModal from './CameraModal'
import WizardDetail from '../../../../addons/FormComponent/Wizard/WizardDetail'

import SubmitInfoBox from './SubmitInfoBox'
import {
  isFormOperationComponent,
  isPicTableComponent,
  isAttendanceListComponent,
  isNotSystemComponent,
  isCopyFormDataComponent,
  isWorkFlowOptComponent,
  isTabComponent,
  COMPONENT_FORM_ID,
  COMPONENT_TAB_ID,
  COMPONENT_FORM_OPERATION_ID,
  COMPONENT_INVOCATION_SERVICE_ID,
  COMPONENT_DATA_VALIDATION_ID,
  COMPONENT_COPY_FORM_DATA_ID,
  COMPONENT_DATASET_ID,
  COMPONENT_STAT_OPERATION_ID,
  isReportComponent,
  isTextListComponent,
  isWizardComponent,
  isStatOperationComponent,
} from '../../../../addons/constants'
import { FormHeader } from '../../../../addons/FormComponent/ModalHeader'
import TabFields from './TabFields'
import SideMenu from '../../../../components/SideMenu'
import { tabStyle } from './style'
import { GO_MODAL } from '../../../../utils/jump'
import { refreshCurrDatas } from '../../../../utils/event/DeviceEventEmitter'
import ReportFields from './ReportFields'
import AttendanceListDetail from '../../../../addons/FormComponent/AttendanceList/detail'
import PicTalbeDetail from '../../../../addons/FormComponent/PicTable/PicTableDetail'
import TextlistDetail from '../../../../addons/FormComponent/TextList/TextlistDetail'

const tagTitleMap = {
  1: ' - 查看',
  2: ' - 新增',
  3: ' - 编辑',
  4: ' - 删除',
  9: ' - 复制新增',
}

class FormReportComponent extends React.Component {
  state = {
    right: <View />,
    centerText: this.props.formType === 1 ? '加载中...' : this.props.title,
    tabs: [],
    tabsNumber: 0,
    showTabsPage: false,
    borderBottomWidth: 1,
    isReport: false,
  };

  componentWillMount() {
    const {
      appId,
      formId,
      dataId,
      operator,
      fieldId,
      fromFormId,
      srcDataId,
    } = this.props
    this.props.initFormData(
      appId,
      formId,
      dataId,
      operator,
      this.props.query,
      fieldId,
      fromFormId,
      srcDataId,
    )
    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this.keyboardDidShow,
    )
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      this.keyboardDidHide,
    )
  }

  componentDidMount() {
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.androidBack)
    }
  }

  componentWillReceiveProps = ({
    appId,
    dataId,
    formId,
    fieldId,
    operator,
    fields,
    query,
    formUpdate,
    title,
    formTitle,
    fromFormId,
    srcDataId,
    isWorkFlowForm,
  }) => {
    if (
      (typeof formUpdate !== 'undefined' &&
        formUpdate !== this.props.formUpdate) ||
      this.props.appId !== appId ||
      this.props.dataId !== dataId ||
      this.props.formId !== formId ||
      this.props.operator !== operator ||
      this.props.query !== query ||
      this.props.fieldId !== fieldId ||
      this.props.fromFormId !== fromFormId ||
      this.props.srcDataId !== srcDataId
    ) {
      this.props.initFormData(
        appId,
        formId,
        dataId,
        operator,
        query,
        fieldId,
        fromFormId,
        srcDataId,
      )
    }
    if (this.props.formType === 1) {
      // 报表类型
      if (title && this.props.title !== title) {
        this.setState({ centerText: title })
      }
    } else {
      const rtitle = formTitle
      const oldTitle = this.state.centerText
      if (rtitle && (oldTitle !== rtitle || operator === '9')) {
        this.setState({ centerText: rtitle })
      }
    }
    if (!isEqual(this.props.fields, fields)) {
      this.initFields(fields)
      const buttons = fields.filter(field =>
        isFormOperationComponent(field) ||
        (isCopyFormDataComponent(field) && operator === '3') ||
        isWorkFlowOptComponent(field))
      if (buttons.length > 0) {
        this.isWorkFlowForm = isWorkFlowForm
        const rightDisplay = this.getFromButtons(buttons)
        this.setState({ right: rightDisplay })
      } else {
        this.setState({ right: '' })
      }
    }
  };

  componentWillUnmount() {
    // this.props.doClear()
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.androidBack)
    }
    if (this.keyboardDidShowListener) this.keyboardDidShowListener.remove()
    if (this.keyboardDidHideListener) this.keyboardDidHideListener.remove()
  }

  getFromButtons = (buttons) => {
    if (this.props.isView) {
      return <View />
    }
    return (
      <FormButtons
        appId={this.props.appId}
        formId={this.props.formId}
        dataId={this.props.dataId}
        submit={this.props.submit}
        isView={false}
        operator={this.props.operator}
        buttons={buttons}
        inputBlur={this.inputBlur}
        isWorkFlowForm={this.isWorkFlowForm}
      />
    )
  };

  formSubmitHandle = ({ id, successCallback }) => {
    const { submit, operator } = this.props
    submit(id, operator, { successCallback })
  };

  initFields = (fields) => {
    let tabFields = false
    let reportFields = false
    const formFields = fields.find(field => field.id === COMPONENT_FORM_ID)
    const mobileFieldsConfig =
      formFields &&
        formFields.properties &&
        formFields.properties.mobileFieldsConfig
        ? formFields.properties.mobileFieldsConfig
        : []
    if (mobileFieldsConfig && mobileFieldsConfig.length) {
      tabFields = fields.find(field =>
        mobileFieldsConfig.find(id => id === field.id && isTabComponent(field)))
      reportFields = fields.filter(field =>
        mobileFieldsConfig.find(id => id === field.id && isReportComponent(field)))
    } else {
      tabFields = fields.find(field => isTabComponent(field))
      reportFields = fields.filter(field => isReportComponent(field))
    }
    this.tabField = tabFields
    this.reportField = reportFields
    this.mobileFieldsConfig = mobileFieldsConfig
    if (tabFields) {
      this.otherTabs(mobileFieldsConfig, fields)
    } else {
      this.setState({ tabs: [] })
    }
  };

  otherTabs = (mobileFieldsConfig, fields) => {
    const tabs = {}
    const otherTabs = []
    let tabts = []
    // 需要排除的组件id
    const needExcludeCmpId = [
      COMPONENT_FORM_OPERATION_ID,
      COMPONENT_INVOCATION_SERVICE_ID,
      COMPONENT_DATA_VALIDATION_ID,
      COMPONENT_COPY_FORM_DATA_ID,
      COMPONENT_DATASET_ID,
      COMPONENT_STAT_OPERATION_ID,
    ]
    this.tabField.properties.tabs.forEach((tab) => {
      tab.fields.forEach((t) => {
        tabs[t] = 1
      })
    })
    if (mobileFieldsConfig && mobileFieldsConfig.length) {
      mobileFieldsConfig.forEach((id) => {
        const field = fields.find(f => f.id === id)
        if (
          field &&
          field.componentId !== COMPONENT_TAB_ID &&
          !tabs[id] &&
          field.properties.visible &&
          indexOf(needExcludeCmpId, field.componentId) < 0
        ) {
          otherTabs.push(id)
        }
      })
    } else {
      fields.forEach((field) => {
        if (
          field &&
          field.componentId !== COMPONENT_TAB_ID &&
          !tabs[field.id] &&
          field.properties.visible &&
          indexOf(needExcludeCmpId, field.componentId) < 0
        ) {
          otherTabs.push(field.id)
        }
      })
    }
    if (otherTabs.length) {
      const formname = fields.find(f => f.id === COMPONENT_FORM_ID).properties
        .label
      tabts.push({ name: formname, fields: otherTabs })
    }
    tabts = [...tabts, ...this.tabField.properties.tabs]
    this.setState({ tabs: tabts })
  };

  centerText = () => {
    if (this.state.tabs.length) {
      return (
        <View style={tabStyle.centerText}>
          <TouchableOpacity
            style={[
              tabStyle.touchBut,
              tabStyle.touchLeftBut,
              { backgroundColor: this.state.showTabsPage ? '#fff' : '#F8F7F7' },
            ]}
            onPress={() => {
              this.inputBlur(() => {
                this.setState({ showTabsPage: false, borderBottomWidth: 0 })
              })
            }}
            activeOpacity={0.8}
          >
            <Text style={{ fontSize: 12, color: '#41454B' }} numberOfLines={1}>
              {this.state.tabs[this.state.tabsNumber] &&
                this.state.tabs[this.state.tabsNumber].name}
            </Text>
          </TouchableOpacity>
          <View style={{ backgroundColor: '#E5E5E5', width: 1 }} />
          <TouchableOpacity
            style={[
              tabStyle.touchBut,
              tabStyle.touchRightBut,
              { backgroundColor: this.state.showTabsPage ? '#F8F7F7' : '#fff' },
            ]}
            onPress={() => {
              this.inputBlur(() => {
                this.setState({ showTabsPage: true, borderBottomWidth: 1 })
              })
            }}
            activeOpacity={0.8}
          >
            <Text style={{ fontSize: 12, color: '#41454B' }} numberOfLines={1}>
              更多信息
            </Text>
          </TouchableOpacity>
        </View>
      )
    }
    return this.state.centerText
  };

  keyboardDidShow = () => {
    this.keyboardShow = true
  };

  keyboardDidHide = () => {
    this.keyboardShow = false
  };

  inputBlur = (callback, timer = 300) => {
    if (this.keyboardShow) {
      this.searchInput.focus()
      this.searchInput.blur()
      setTimeout(() => {
        if (callback) callback()
      }, timer)
    } else {
      callback()
    }
  };

  handlerBack = () => {
    this.props.doClear()
    DeviceEventEmitter.emit(refreshCurrDatas, true)
    NavigationService.popAndRefresh({ refresh: { formUpdate: Math.random() } })
  };

  androidBack = () => {
    this.handlerBack()
    return true
  };

  showCameraFun = (uploadReques, props) => {
    this.uploadReques = uploadReques
    this.invocationServiceField = props
    GO_MODAL({
      children: (
        <CameraModal
          uploadReques={this.uploadReques}
          invocationServiceField={this.invocationServiceField}
          hideCamera={() => {
            this.setState({ uploadReques: null })
          }}
        />
      ),
    })
  };
  scrollToVerifyCmp = async (currentVerifyCmp) => {
    let fieldsCmp = this.fieldsCmp
    if (this.tabField) {
      const tabIndex = this.state.tabs.findIndex(item =>
        item.fields.find(id => id === currentVerifyCmp.id))
      if (tabIndex >= 0 && tabIndex !== this.state.tabsNumber) {
        await this.setState({
          tabsNumber: tabIndex,
        })
        fieldsCmp = this.tabCmp[`fieldsCmp${tabIndex}`]
        // 当有切换标签页的操作时，延迟执行1秒，以等待组件渲染完毕，获取准确的高度
        setTimeout(() => {
          this.scrollFunc(currentVerifyCmp, fieldsCmp)
        }, 1000)
      } else {
        fieldsCmp = this.tabCmp[`fieldsCmp${this.state.tabsNumber}`]
        this.scrollFunc(currentVerifyCmp, fieldsCmp)
      }
    } else {
      this.scrollFunc(currentVerifyCmp, fieldsCmp)
    }
  };
  scrollFunc = (currentVerifyCmp, fieldsCmp) => {
    if (fieldsCmp && fieldsCmp.scrollViewCmp) {
      let currentVerifyCmpHeight = 0
      // 获取所有的显示组件，并根据每个内部组件的实例的高度属性计算需要滚动的距离
      fieldsCmp.cmpFocusArray.some((item) => {
        if (item.key === currentVerifyCmp.id) {
          return true
        }
        if (item.cmp.refCmp) {
          currentVerifyCmpHeight += item.cmp.refCmp.cmpHeight
        }
        return false
      })
      if (currentVerifyCmpHeight <= fieldsCmp.fieldHeight) {
        // 小于当前滚动视图屏幕高度，则不滚动
        currentVerifyCmpHeight = 0
      } else if (
        currentVerifyCmpHeight >=
        fieldsCmp.scrollHeight - fieldsCmp.fieldHeight
      ) {
        // 大于最大滚动高度，则按照最大滚动距离滚动
        currentVerifyCmpHeight = fieldsCmp.scrollHeight - fieldsCmp.fieldHeight
      }
      fieldsCmp.scrollViewCmp.scrollTo({
        x: 0,
        y: currentVerifyCmpHeight,
        animated: true,
      })
    }
  };
  refFieldsCmp = ref => (this.fieldsCmp = ref);
  refTabCmp = ref => (this.tabCmp = ref);
  refDropdownAlert = ref => (this.dropdown = ref);
  changeTabsNumber = (tabsNumber) => {
    this.setState({ tabsNumber, showTabsPage: false })
  };
  changeBorderBottomWidth = (n) => {
    this.setState({ borderBottomWidth: n })
  };

  renderFunc = (params) => {
    const { formId, dataId, currentkey } = this.props
    if (currentkey && currentkey !== `${formId}_${dataId || 0}`) {
      return <View />
    }
    if (this.tabField) {
      return (
        <TabFields
          ref={this.refTabCmp}
          params={params}
          tabs={this.state.tabs}
          tabsNumber={this.state.tabsNumber}
          showTabsPage={this.state.showTabsPage}
          changeTabsNumber={this.changeTabsNumber}
          changeBorderBottomWidth={this.changeBorderBottomWidth}
        />
      )
    } else if (this.reportField && this.reportField.length) {
      return (
        <ReportFields
          {...params}
          formSubmitHandle={this.formSubmitHandle}
          changeReport={() => this.setState({ isReport: true })}
          mobileFieldsConfig={this.mobileFieldsConfig}
        />
      )
    }
    const userFields = params.items.filter(isNotSystemComponent)
    const wizardFs = userFields.filter(isWizardComponent) || []
    // console.log('ashdkasndlkasdlasdlkj', formId, dataId, currentkey, this.tabField, this.reportField, this.state.tabs, this.reportField && this.reportField.length, userFields, wizardFs)

    if (wizardFs.length !== 0) {
      let fids = []
      wizardFs.forEach((ws) => {
        ws.properties.tabs.forEach((tab) => {
          fids = fids.concat(tab.fields)
        })
      })
      const renderField = params.items.filter(f => fids.indexOf(f.id) === -1)
      return (
        <Fields
          {...params}
          items={renderField}
          formSubmitHandle={this.formSubmitHandle}
          renderWizardFields={this.renderWizardFields}
        />
      )
    }
    return <Fields {...params} formSubmitHandle={this.formSubmitHandle} />
  };
  renderWizardFields = (fids = []) => {
    const allFields = this.props.fields
    let renderFields = fids
    renderFields = renderFields.map(fid =>
      allFields.find(f => f.id === fid))
    return (
      <Fields
        {...this.params}
        items={renderFields}
        formSubmitHandle={this.formSubmitHandle}
      />
    )
  };

  isOnlyWizardComponent = (userFields) => {
    const wizardFs = userFields.filter(isWizardComponent) || []
    if (wizardFs && wizardFs.length !== 1) {
      return false
    }
    const fields = userFields.filter(f =>
      !isWizardComponent(f) &&
      !isFormOperationComponent(f) &&
      !isStatOperationComponent(f))
    const wizartabs = get(wizardFs, '[0].properties.tabs') || []
    let tabsfieldsIds = []
    wizartabs.forEach((t) => {
      tabsfieldsIds = tabsfieldsIds.concat(t.fields)
    })
    if (fields.every(f => tabsfieldsIds.indexOf(f.id) !== -1)) {
      return wizardFs[0]
    }
    return false
  };
  render = () => {
    const {
      fields,
      InvoServiceId,
      updateEveryValue,
      updateValue,
      updateValidation,
      formValidate,
      formId,
      operator,
      dataId,
      appId,
      submittable,
      updateExtraProps,
      fieldConfig,
      updateEditedFlag,
      currEditFieldId,
      viewData,
      formStatus,
      edited,
      isView,
      isWorkFlowForm,
      isWfComplete,
      formType,
      submitInfoBox,
      hideSubmitBox,
      refPropDatas,
      refDisplay,
      isCustomMainNavHeader,
      appName,
    } = this.props
    const userFields = this.props.fields.filter(isNotSystemComponent)
    // console.log('am,sd,andansdkjakdhdkdkjdkjahdjdbkjdkjhdkjhdkjhadkjhkjsdk', this.props, userFields)

    const picTalbeFeilds = userFields.filter(field =>
      isPicTableComponent(field) &&
      field.properties.isSimple !== true &&
      field.properties.visible === true)
    const textListFeilds = userFields.filter(field =>
      isTextListComponent(field) &&
      field.properties.listStyle === '2' &&
      field.properties.visible === true)
    const isOnlyAttendComponent =
      userFields.length === 1 && isAttendanceListComponent(userFields[0])
    const dataIds =
      formType === 1 || formType === '1'
        ? operator === '9'
          ? undefined
          : dataId
        : operator === '6'
          ? undefined
          : dataId
    this.params = {
      ref: this.refFieldsCmp,
      items: fields,
      isRunTime: true,
      updateValue,
      updateEveryValue,
      formValidate,
      useDragHandle: true,
      formId,
      dataId: dataIds,
      appId,
      formDataInited: submittable,
      updateExtraProps,
      updateValidation,
      updateEditedFlag,
      currEditFieldId,
      viewData,
      formStatus,
      edited,
      isView,
      showCameraFun: this.showCameraFun,
      InvoServiceId,
      inputBlur: this.inputBlur,
      copyDataId: operator === '9' ? dataId : undefined,
      refPropDatas,
      operator,
      formType,
      refDisplay,
      isWorkFlowForm,
      isWfComplete,
      fieldConfig,
      formSubmitHandle: this.formSubmitHandle,
      renderWizardFields: this.renderWizardFields,
    }
    if (isOnlyAttendComponent) {
      // console.log('isOnlyAttendComponent', isOnlyAttendComponent)
      return (
        <AttendanceListDetail {...userFields[0].properties} {...this.params} />
      )
    }
    if (picTalbeFeilds.length !== 0) {
      // console.log('talbe', picTalbeFeilds)
      return (
        <PicTalbeDetail {...picTalbeFeilds[0].properties} {...this.params} />
      )
    }
    if (textListFeilds.length > 0) {
      return (
        <TextlistDetail {...textListFeilds[0].properties} {...this.params} />
      )
    }
    const wizrdField = this.isOnlyWizardComponent(userFields)
    if (wizrdField) {
      return (
        <WizardDetail
          {...wizrdField.properties}
          isRunTime
          fieldId={wizrdField.id}
          formId={formId}
          appId={appId}
          changeValue={updateValue}
          renderWizardFields={this.renderWizardFields}
          formDataInited={submittable}
        />
      )
    }
    // console.log('文字', this.props.toggleMenu,isCustomMainNavHeader, this.params)
    return (
      <View style={{ flex: 1, backgroundColor: '#F8F7F7' }}>
        {isCustomMainNavHeader && (
          <FormHeader
            isShowBack={false}
            centerText={appName}
            toggleMenu={this.props.toggleMenu}
            style={{ borderBottomWidth: this.state.borderBottomWidth }}
          />
        )}
        {!this.state.isReport && !isCustomMainNavHeader && (
          <FormHeader
            centerText={this.centerText()}
            rightView={this.state.right}
            onPressLeft={this.handlerBack}
            toggleMenu={this.props.toggleMenu}
            style={{ borderBottomWidth: this.state.borderBottomWidth }}
          />
        )}
        <TextInput
          ref={r => (this.searchInput = r)}
          style={{ display: 'none' }}
        />
        {this.renderFunc(this.params)}
        <DropdownAlert closeInterval={3000} ref={this.refDropdownAlert} />
        {submitInfoBox && submitInfoBox.isShow && (
          <SubmitInfoBox
            hideSubmitBox={hideSubmitBox}
            submitInfoBox={submitInfoBox}
            scrollToVerifyCmp={this.scrollToVerifyCmp}
          />
        )}
      </View>
    )
  };
}

FormReportComponent.defaultProps = {
  isCustomMainNavHeader: false,
}

FormReportComponent.propTypes = {
  fields: PropTypes.arrayOf(PropTypes.object).isRequired,
  initFormData: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired,
  updateValue: PropTypes.func.isRequired,
  formValidate: PropTypes.object.isRequired, // eslint-disable-line
  formId: PropTypes.string.isRequired,
  appId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  fieldId: PropTypes.string,
  updateValidation: PropTypes.func,
  submittable: PropTypes.bool,
  InvoServiceId: PropTypes.string,
  data: PropTypes.object, // eslint-disable-line
  operator: PropTypes.string.isRequired,
  getNavTitle: PropTypes.func,
  query: PropTypes.string,
  title: PropTypes.string,
  formUpdate: PropTypes.any, // eslint-disable-line
  isView: PropTypes.bool,
  doClear: PropTypes.func,
  updateEveryValue: PropTypes.func,
  updateExtraProps: PropTypes.func,
  updateEditedFlag: PropTypes.func,
  currEditFieldId: PropTypes.string,
  viewData: PropTypes.arrayOf(PropTypes.object),
  formStatus: PropTypes.object, // eslint-disable-line
  edited: PropTypes.bool,
  formType: PropTypes.number,
  toggleMenu: PropTypes.func,
  submitInfoBox: PropTypes.shape({
    isLoading: PropTypes.bool,
    isShow: PropTypes.bool,
    fields: PropTypes.array,
  }),
  isCustomMainNavHeader: PropTypes.bool,
}

export default SideMenu(FormReportComponent)
