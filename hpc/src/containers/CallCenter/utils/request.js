import { envParm } from '../../../config/sysPara'
import AliyunOSS from '../../../components/AliyunOSS'

const region = 'oss-cn-hangzhou'
const locationUrls = envParm.URLS
export const serverDocumentPath = `https://${envParm.bucket}.${region}.aliyuncs.com/CallCenter`
export const endPoint = 'https://oss-cn-hangzhou.aliyuncs.com'

let ossToken = null
let isCcOssToken = false

const getOssToken = async (type, token) => {
  const url = `${locationUrls}/captcha/oss/token/mobile/${type}`
  let tokenData
  // 当有osstoken且为呼叫中心模块获得的token的时候
  if (isCcOssToken && ossToken) {
    tokenData = ossToken
  } else {
    const res = await fetch(url,
      { method: 'GET',
        headers: {
          Accept: 'application/json;charset=UTF-8',
          'Content-Type': 'application/json;charset=UTF-8',
          authToken: token,
        } },
      ).then(res => res.json()).catch(err => err)
    if (res && res.errorCode === '0') {
      tokenData = res.data
      isCcOssToken = true
      ossToken = tokenData
    } else {
      isCcOssToken = false
      ossToken = null
      return false
    }
  }
  const config = {
    AccessKey: tokenData.AccessKeyId,
    SecretKey: tokenData.AccessKeySecret,
    SecretToken: tokenData.SecurityToken,
  }
    // 初始化阿里云组件
  AliyunOSS.initWithKey(config, endPoint)
  return true
}

/**
 * 初始化阿里云oss写入权限
 */
function initialWriteOss(token) {
  getOssToken('write', token)
}

/**
 * 上传文件方法
 * @param {*} token
 * @param {*} key
 * @param {*} file
 */
const uploadFile = async (token, key, file) => {
  let initStatus = null
  if (isCcOssToken) {
    initStatus = true
  } else {
    initStatus = await getOssToken('write', token)
  }
  if (initStatus) {
    const uploadConfig = {
      bucketName: envParm.bucket,  // your bucketName
      sourceFile: file, // local file path
      ossFile: key, // the file path uploaded to oss
    }
    return AliyunOSS.uploadObjectAsync(uploadConfig).then(resp => resp).catch((err) => {
      
      console.log('upload is failed', err)
      // 执行失败回调
      return null
    })
  }
  return null
}

const request = (url, options) => { console.log(locationUrls + url); return fetch(locationUrls + url, { ...options }) }

export {
  request,
  uploadFile,
  initialWriteOss,
}
