import React from 'react'
import { View, Text, ScrollView, StyleSheet, TouchableHighlight } from 'react-native'
import { Flex, Icon } from '@ant-design/react-native'
import RNFS from 'react-native-fs'
import { delayLongPress, deviceType } from '../../config/sysPara'
import Breadcrumbs from '../../components/Breadcrumbs'
import AGZListView from '../../components/AGZListView'
import AGZIcon from '../../components/Icon'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import NavigationService from '../../NavigationService'

// 本地文件选择页面
class FileManager extends React.Component {
  state = {
    crumbsDatas: [{
      path: deviceType === 2 ? `${RNFS.DocumentDirectoryPath}/Download` : RNFS.ExternalStorageDirectoryPath,
      name: '文件管理',
    }], // 数组元素为目录Path字符串+'/Download', name:'Download'
    fileDatas: [],
  };

  componentWillMount() {
    this.getFileInfo()
  }

  getFileInfo = (path = deviceType === 2 ? `${RNFS.DocumentDirectoryPath}/Download` : RNFS.ExternalStorageDirectoryPath) => { // +'/Download'
    RNFS.readDir(path) // On Android, use "RNFS.DocumentDirectoryPath" (MainBundlePath is not defined)
      .then((result) => {
        this.setState({ fileDatas: result })
      })
      .catch((err) => {
        console.log(err.message, err.code)
      })
  }

  // path为目录字符串
  _onChange = (fileItem, index = -1) => {
    this.getFileInfo(fileItem.path)
    if (index === -1) {
      if (fileItem.isFile()) {
        this.props.onSelect(fileItem)
      } else {
        this.state.crumbsDatas.push(fileItem)
      }
    } else {
      this.setState({ crumbsDatas: this.state.crumbsDatas.slice(0, index + 1) })
    }
  }

  row = (obj) => {
    let iconName = 'document'
    let flag = false

    if (obj.isDirectory()) {
      iconName = 'folder'
      flag = true
    }
    return (
      <TouchableHighlight
        underlayColor="rgb(210, 230, 255)"
        delayLongPress={Number(delayLongPress)}
        onPress={() => { this._onChange(obj) }}
      >
        <View key={obj.mtime} style={styles.row}>
          <View style={styles.container}>
            <View>
              <AGZIcon name={iconName} />
            </View>
            <View style={styles.textView}>
              <Text style={styles.rowText}>
                {obj.name}
              </Text>
            </View>

          </View>

          {flag && <View style={styles.iconView}>
            <Icon type="right" size="sm" />
          </View>}
        </View>
      </TouchableHighlight>
    )
  };

  render() {
    return (
      <View>
        <FormHeader centerText={'文件列表'} onPressLeft={() => NavigationService.back()} />
        <Breadcrumbs crumbsDatas={this.state.crumbsDatas} changePage={this._onChange} dispalyVol="name" />
        <AGZListView
          style={{ margin: 0, marginTop: 10 }}
          datas={this.state.fileDatas}
          rowRender={this.row}
        />
      </View>
    )
  }
}

let styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    paddingLeft: 8,
    paddingRight: 8,
    height: 50,
    backgroundColor: 'white',
    justifyContent: 'space-between',
  },
  container: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  textView: {
    justifyContent: 'center',
    flex: 1,
    height: 50,
  },
  rowText: {
    fontSize: 16,
    marginLeft: 15,
  },
  iconView: {
    justifyContent: 'center',
  },
})

export default FileManager
