import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import {
  View,
  Text,
  Image,
  Modal,
} from 'react-native'
import { screenWidth, screenHeight } from '../config/sysPara'
import SvgIcon from '../components/SvgIcon'

export default class DownloadProgress extends PureComponent {
  state = {
    visible: this.props.visible,
  }

  close=() => {
    this.setState({
      visible: false,
    })
  }

  render() {
    const { onAnimationEnd, progressValue } = this.props
    return (
      <View>
        <Modal
          isVisible={this.state.visible}
          backdropColor="white"
          backdropOpacity={1}
          onBackButtonPress={() => {}}
          onModalHide={onAnimationEnd}
        >

          <View style={{ flex: 1, alignItems: 'center' }}>
            {/* <SvgIcon name={name[1]} type={name[0]} /> */}
            <Image source={require('../images/logo-loading.gif')} style={{ width: screenWidth * 0.29, height: screenWidth * 0.29, marginTop: screenHeight * 0.3 }} />
            <Text style={{ textAlign: 'center', fontSize: 20, marginTop: screenHeight * 0.07 }}>{ '下载中... ' + '(' + `${progressValue}%` + ')'}</Text>
          </View>

        </Modal>
      </View>
    )
  }
}

DownloadProgress.propTypes = {
  visible: PropTypes.bool,
  progressValue: PropTypes.number,
  onAnimationEnd: PropTypes.func,
}
