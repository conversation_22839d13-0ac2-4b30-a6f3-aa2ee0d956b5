import TextInput from './TextInput'
import Default from './Default'
import Select from './Select'
import List from './List'
import Number from './Number'
import MultiText from './MultiText'
import DateTime from './DateTime'
import Reference from './Reference'
import Option from './Option'
import UserOrGroup from './UserOrGroup'
import Switch from './Switch'
import Attachment from './Attachment'
import Picture from './Picture'
import SinglePicture from './SinglePicture'
import FormNumber from './FormNumber'
import RefProperty from './RefProperty'
import Report from './Report'
import AppUser from './AppUser'
import Split from './Split'
import SubForm from './SubForm'
import InvocationService from './InvocationService'
import RichEditor from './RichEditor'
import MapLocation from './MapLocation'
import AddressLocation from './AddressLocation'
import ComputedValue from './ComputedValue'
import SubFormProduct from './SubFormProduct'
import RefMultiSelect from './RefMultiSelect'
import MultiAppUser from './MultiAppUser'
import RefDataStat from './RefDataStat'
import AttendanceList from './AttendanceList'
import PicList from './PicList'
import LinkCmp from './LinkCmp'
import PicTable from './PicTable'
import DataViewGroup from './DataViewGroup'
import TextList from './TextList'
import CheckingAttendance from './CheckingAttendance'
import Calendar from './Calendar'
import PieChart from './Chart/PieChart'
import BarChart from './Chart/BarChart'
import RadarChart from './Chart/RadarChart'
import SimpleBarChart from './Chart/SimpleBarChart'
import LineChart from './Chart/LineChart'
import HorizontalBarChart from './Chart/HorizontalBarChart'
import Slider from './Slider'
import ScanInput from './ScanInput'
import Expression from './Expression'
import Signatures from './Signatures'
import Wizard from './Wizard'
import Video from './Video'
import Comment from './Comment'

export default {
  TextInput,
  Default,
  Select,
  List,
  Number,
  MultiText,
  DateTime,
  Reference,
  Time: DateTime,
  Option,
  UserOrGroup,
  Switch,
  Address: AddressLocation,
  Attachment,
  FormNumber,
  RefProperty,
  Report,
  AppUser,
  Split,
  SubForm,
  NewSubform: SubForm,
  InvocationService,
  RichEditor,
  Picture,
  SinglePicture,
  MapLocation,
  AddressLocation,
  ComputedValue,
  SubFormProduct,
  RefMultiSelect,
  MultiAppUser,
  RefDataStat,
  AttendanceList,
  PicList,
  LinkCmp,
  PicTable,
  DataViewGroup,
  TextList,
  CheckingAttendance,
  Calendar,
  PieChart,
  BarChart,
  SimpleBarChart,
  LineChart,
  RadarChart,
  HorizontalBarChart,
  Slider,
  ScanInput,
  Expression,
  Signatures,
  Wizard,
  XReactPlayer:Video,
  CommentCmp:Comment
}
