import React, { Component } from 'react'
import PropTypes from 'prop-types'
import {
  View,
  Dimensions,
} from 'react-native'
import {
  MapView,
  Geolocation,
} from 'react-native-baidu-map'
import debounce from 'lodash/debounce'
import { searchPositionService } from './store'
import { bd09togcj02, gcj02towgs84, wgs84togcj02, gcj02tobd09 } from '../../../utils/coordtransform'

const { width: deviceWidth } = Dimensions.get('window')
// 组件高度与宽度的比例9：16
const mapHeight = Math.ceil((deviceWidth * 9) / 16)

class MapLocation extends Component {
  constructor(props) {
    super(props)
    // 定义防抖函数，连续输入时，只取最后一次输入的结果
    this.debounceSearch = debounce(inputValue => this.searchPosition(inputValue), 500)
    this.state = {
      lat: this.props.defaultValue.lat,
      lng: this.props.defaultValue.lng,
      locationText: this.props.defaultValue.locationText,
      locationTitle: this.props.defaultValue.locationTitle,
      value: this.props.defaultValue,
    }
  }

  componentDidMount=() => {
    this.cmpHeight = mapHeight
    const { inputValue } = this.props
    if (inputValue) {
      this.searchPosition(inputValue)
    }
  }

  componentWillReceiveProps = ({ inputValue }) => {
    if (this.props.isRunTime && this.props.inputValue !== inputValue && inputValue !== '') {
      this.debounceSearch(inputValue)
    }
    // if (this.state.value !== defaultValue) {
    //   this.setState({
    //     value: defaultValue,
    //   })
    // }
  }

  changeValue = () => {
    const { changeValue } = this.props
    if (changeValue && this.state.value !== this.props.defaultValue) {
      changeValue({ [this.props.fieldId]: { defaultValue: this.state.value } })
    }
  }
  getCurrentLocationHandle=() => {
    const { changeValue, fieldId } = this.props
    const mapComponent = this
    Geolocation.getCurrentPosition()
        .then((data) => {
          // 保存的时候坐标转换为wgs坐标
          const gcj02Position = bd09togcj02(+data.longitude, +data.latitude)
          const wgs84Position = gcj02towgs84(gcj02Position[0], gcj02Position[1])
          const newState = {
            lat: wgs84Position[1],
            lng: wgs84Position[0],
            locationText: data.address || '',
            locationTitle: '',
          }
          mapComponent.setState(newState)
          changeValue({
            [fieldId]: { defaultValue: newState },
          })
        })
        .catch((e) => {
          console.warn(e, 'error')
        })
  }

  searchPosition=async (inputValue) => {
    const { fieldId, changeValue } = this.props
    const data = await searchPositionService(inputValue)
    if (data.status === 0 && data.results.length) {
      const addressInfo = data.results[0]
      const gcj02Position = bd09togcj02(+addressInfo.location.lng, +addressInfo.location.lat)
      const wgs84Position = gcj02towgs84(gcj02Position[0], gcj02Position[1])
      const newState = {
        lat: wgs84Position[1],
        lng: wgs84Position[0],
        locationText: addressInfo.address || inputValue,
        locationTitle: addressInfo.name || inputValue,
      }
      this.setState(newState)
      changeValue({
        [fieldId]: { defaultValue: newState },
      })
    }
  }
  render() {
    const { lng, lat, locationText } = this.state
    const { required, label, enabled } = this.props
    const gjc02Position = wgs84togcj02(+lng, +lat)
    const bd09Position = gcj02tobd09(gjc02Position[0], gjc02Position[1])
    const center = { latitude: bd09Position[1], longitude: bd09Position[0] }
    const marker = { latitude: bd09Position[1], longitude: bd09Position[0], title: locationText }
    return (
      <View style={{ marginTop: 10 }}>
        <MapView
          style={{ height: mapHeight }}
          center={center}
          marker={marker}
          zoom={20}
        />
      </View>
    )
  }
}
MapLocation.propTypes = {
  isRunTime: PropTypes.bool,
  inputValue: PropTypes.string,
  required: PropTypes.bool,
  label: PropTypes.string,
  enabled: PropTypes.bool,
  fieldId: PropTypes.string,
  changeValue: PropTypes.func,
  defaultValue: PropTypes.shape({
    lng: PropTypes.number, lat: PropTypes.number, locationText: PropTypes.string, locationTitle: PropTypes.string,
  }),

}
MapLocation.defaultProps = {
  required: false,
  label: '',
  enabled: true,
  defaultValue: { lng: 116.402544, lat: 39.928216, locationText: '标题', locationTitle: '内容' },
}
export default MapLocation
