import PropTypes from 'prop-types'
import React from 'react'
import { View } from 'react-native'
import { Toast } from '@ant-design/react-native'
import { SimpleLine } from 'nagz-mobile-lib'

const fn = () => {}
class ComputedValue extends React.PureComponent {
  state ={
    value: this.props.defaultValue,
  }

  componentWillMount = () => {
    const { changeValue, refPropDatas, fieldId } = this.props
    if (changeValue && refPropDatas) {
      changeValue({ [fieldId]: { defaultValue: refPropDatas.value } })
      this.setState({
        value: refPropDatas.value,
      })
    }
  }

  componentDidUpdate(prevProps) {
    const { changeValue, fieldId, refPropDatas } = this.props;

    // 只在 refPropDatas 真正改变时更新计算值
    if (
      prevProps.refPropDatas !== refPropDatas &&
      refPropDatas &&
      (!prevProps.refPropDatas || refPropDatas.value !== prevProps.refPropDatas.value)
    ) {
      changeValue({ [fieldId]: { defaultValue: refPropDatas.value } });
      this.setState({
        value: refPropDatas.value,
      });
    }
  }

  render() {
    const { label, isRunTime, hidelabel, enabled } = this.props

    return (
      <View>
        <SimpleLine
          label={hidelabel && isRunTime ? '' : label}
          placeholder="无数据"
          value={`${this.state.value}`}
          onPress={!enabled ? () => Toast.info('不可更改', 1) : null}
          editable={enabled}
        />
      </View>
    )
  }
}
ComputedValue.propTypes = {
  label: PropTypes.string,
  fieldId: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  isRunTime: PropTypes.bool,
  enabled: PropTypes.bool,
  hidelabel: PropTypes.bool,
  changeValue: PropTypes.func,
  refPropDatas: PropTypes.shape({
    value: PropTypes.any,
  }),
}
ComputedValue.defaultProps = {
  label: '',
  defaultValue: {},
  isRunTime: false,
  fieldId: '',
  enabled: true,
  changeValue: fn,
}
export default ComputedValue
