import React from 'react'
import PropTypes from 'prop-types'
import {
  ActivityIndicator,
  View,
  Image,
  TouchableOpacity,
} from 'react-native'
import {WebView} from 'react-native-webview'
import Orientation from 'react-native-orientation'
import NavigationService from '../../../NavigationService'
import PubSub from '../../../utils/pubsub'
import { screenHeight, screenWidth, deviceType, envParm, API_VERSION } from '../../../config/sysPara'
import { FormHeader } from '../ModalHeader'
import { get } from '../../../request'
import { getToken } from '../../../utils/storage/index'
import { UPDATE_FORMDATA } from '../../../containers/RunTime/Form/constants'

const URLS = envParm.REPORTURLS

class FullReportView extends React.PureComponent {

  state = {
    url: '',
    loading: true,
    screenDirection: 'PORTRAIT',
  }

  componentWillMount = async () => {
      // this.reportInfo = (this.props.item instanceof Array) ? this.props.item[0] : this.props.item
    this.datas = {}
    PubSub.on(UPDATE_FORMDATA, ({ fieldId, dataKey, value }) => {
      if (this.datas[fieldId] && this.datas[fieldId].dataKey === dataKey) {
        this.datas[fieldId].value = value
      }
    })
    this.init()
    const token = await getToken()
    this.reportAddress = `/apps/${this.props.appId}/reports/${this.props.selectReport}/preview?authToken=${token}`
    if (this.props.selectReport) {
      const src = await this.setSrc()
      this.setState({ url: URLS + src })
    }
    console.log('报表链接', this.state.url)
  }

  setSrc = async () => {
    let src = ''
    this.props.reportParameter.forEach((par) => {
      if (par.parameter) {
        const value = this.expressFun(par.value) ? this.getValue(par.value) : par.value
        const parameter = this.expressFun(par.parameter) ? this.getValue(par.parameter) : par.parameter
        src = `${src}&${parameter}=${value}`
      }
    })
    if (this.src === src && src !== '') {
      return this.url
    }
    this.src = src
    const res = await get(this.reportAddress + src)()
    this.url = res.data.url
    if (API_VERSION) {
      this.url += (`&version=${API_VERSION}`)
    }
    return this.url
  }

  setDatas=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    const field = this.props.contextInfo && this.props.contextInfo.fields.find(k => k.id === fieldId)
    const dataKey = field ? field.setting.value : ''
    this.datas[fieldId] = {
      dataKey,
      value: dataKey ? this.props.contextInfo && this.props.contextInfo.formData[fieldId][dataKey] : '',
    }
  }

  getValue=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    return this.datas[fieldId].value
  }

  init=() => {
    this.props.reportParameter.forEach((par) => {
      if (par.parameter) {
        if (this.expressFun(par.value)) { this.setDatas(par.value) }
        if (this.expressFun(par.parameter)) { this.setDatas(par.parameter) }
      }
    })
  }

  expressFun = value => (/^\$\{(.+)\}$/).test(value)

  componentDidMount= async () => {
    Orientation.lockToPortrait()
    const initial = await Orientation.getInitialOrientation()
    this.setState({
      screenDirection: initial,
    })

    Orientation.addOrientationListener(this._orientationDidChange)
  }

  componentWillUnmount() {
    Orientation.getOrientation((err, orientation) => {
      // console.log(`Current Device Orientation: ${orientation}`)
    })

    // Remember to remove listener
    Orientation.lockToPortrait()
    Orientation.removeOrientationListener(this._orientationDidChange)
  }

  rotateScreen = () => {
    if (this.state.screenDirection === 'LANDSCAPE') {
      Orientation.lockToPortrait()
      this.setState({
        screenDirection: 'PORTRAIT',
      })
    } else {
      if (deviceType === 2) {
        Orientation.lockToLandscapeRight()
      } else {
        Orientation.lockToLandscapeLeft()
      }

      this.setState({
        screenDirection: 'LANDSCAPE',
      })
    }
  }

  _orientationDidChange = (orientation) => {
    if (orientation === 'LANDSCAPE') {
      // Orientation.lockToLandscapeRight()
      // this.setState({
      //   screenDirection: orientation,
      // })
    } else {
      // Orientation.lockToPortrait()
      // this.setState({
      //   screenDirection: orientation,
      // })
    }
  }

  webviewLoaded=() => {
    this.setState({
      loading: false,
    })
  }

  renderImage = image => <TouchableOpacity onPress={this.rotateScreen} style={{ width: 45, height: 45, flex: 1, justifyContent: 'center', alignItems: 'center' }}><Image style={{ width: 19, height: 19, resizeMode: 'contain' }} source={image} /></TouchableOpacity>

  render() {
    return (
      <View style={{ flex: 1, backgroundColor: 'white' }}>
        <FormHeader style={{ width: this.state.screenDirection === 'PORTRAIT' ? screenWidth : screenHeight }} centerText={this.props.label} onPressLeft={NavigationService.back} right={this.renderImage(this.state.screenDirection === 'PORTRAIT' ? require('../../../images/icons-rotate-landscape-default.png') : require('../../../images/icons-rotate-longitudinal-default.png'))} />
        <WebView startLoadingState onLoadEnd={this.webviewLoaded} bounces={false} style={{ marginHorizontal: 15, flex: 1, backgroundColor: 'transparent' }} source={{ uri: this.state.url ? this.state.url : this.props.url }} />
        <View style={{ width: screenWidth, position: 'absolute', top: deviceType === 2 ? 84 : 50, justifyContent: 'center' }}>
          <ActivityIndicator
            animating={this.state.loading}
            color="#A0A2A5"
            size="small"
          />
        </View>
      </View>)
  }

}

FullReportView.propTypes = {
  url: PropTypes.string,
  label: PropTypes.string,
  // item: PropTypes.arrayOf(PropTypes.object),
  reportParameter: PropTypes.arrayOf(PropTypes.object),
  selectReport: PropTypes.string,
  formData: PropTypes.object,
  fields: PropTypes.arrayOf(PropTypes.object),
}

export default FullReportView
