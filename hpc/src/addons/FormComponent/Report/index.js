import React from 'react'
import PropTypes from 'prop-types'
import {
  TouchableOpacity,
  Text,
  Image,
  View,
} from 'react-native'
import PubSub from '../../../utils/pubsub'
import { get } from '../../../request'
import { screenHeight, screenWidth, envParm, API_VERSION } from '../../../config/sysPara'
import { getToken } from '../../../utils/storage/index'
import { GO_REPORTDETAIL } from '../../../utils/jump'
import { UPDATE_FORMDATA } from '../../../containers/RunTime/Form/constants'
// import { REPORT_FORM_UPDATE_VALUE } from '../../../containers/RunTime/ReportForm/constants'
import { FormContext } from '../../../utils/reactContext'

const URLS = envParm.REPORTURLS
class Report extends React.PureComponent {
  state = {
    url: '',
  }
  static contextType = FormContext;

  componentDidMount = async () => {
    this.updateType = UPDATE_FORMDATA
    this.datas = {}
    PubSub.on(this.updateType, ({ fieldId, dataKey, value }) => {
      if (this.datas[fieldId] && this.datas[fieldId].dataKey === dataKey) {
        this.datas[fieldId].value = value
      }
    })
    await this.init()
  }

  componentWillReceiveProps = async (props, context) => {
    if (this.context !== context) {
      this.context = { ...context }
      this.datas = {}
      await this.init()
    }
  }

  setSrc = async () => {
    let src = ''
    this.props.reportParameter.forEach((par) => {
      if (par.parameter) {
        const value = this.expressFun(par.value) ? this.getValue(par.value) : par.value
        const parameter = this.expressFun(par.parameter) ? this.getValue(par.parameter) : par.parameter
        src = `${src}&${parameter}=${value}`
      }
    })
    if (this.src === src && src !== '') {
      return this.url
    }
    this.src = src
    const res = await get(this.reportAddress + src)()
    this.url = res.data.url
    if (API_VERSION) {
      this.url += (`&version=${API_VERSION}`)
    }
    return this.url
  }

  setDatas=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    const field = this.context.fields.find(k => k.id === fieldId)
    const dataKey = field ? field.setting.value : ''
    this.datas[fieldId] = {
      dataKey,
      value: dataKey ? this.context.formData[fieldId][dataKey] : '',
    }
  }

  getValue=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    return this.datas[fieldId].value
  }

  init = async () => {
    this.props.reportParameter.forEach((par) => {
      if (par.parameter) {
        if (this.expressFun(par.value)) { this.setDatas(par.value) }
        if (this.expressFun(par.parameter)) { this.setDatas(par.parameter) }
      }
    })
    const token = await getToken()
    this.reportAddress = `/apps/${this.props.appId}/reports/${this.props.selectReport}/preview?authToken=${token}`
    if (this.props.selectReport) {
      const src = await this.setSrc()
      this.setState({ url: URLS + src })
    }
  }

  expressFun = value => (/^\$\{(.+)\}$/).test(value)

  goRootModal = () => {
    GO_REPORTDETAIL({ ...this.props, ...this.state })
  }

  render() {
    const { label } = this.props
    return (
      <TouchableOpacity
        style={{
          width: screenWidth, height: 70, backgroundColor: '#fff', justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row',
        }}
        onPress={() => this.goRootModal()}
      >
        <View style={{ justifyContent: 'center' }}>
          <Text style={{
            fontSize: 14, fontWeight: 'bold', marginLeft: 20, color: '#1b1b1b',
          }}
          >{label}
          </Text>
        </View>
        <Image style={{ marginRight: 15 }} source={require('../../../images/icon-arrow-right-small-default.png')} />
      </TouchableOpacity>
    )
  }
}

Report.propTypes = {
  appId: PropTypes.string,
  label: PropTypes.string,
  reportParameter: PropTypes.arrayOf(PropTypes.object),
  selectReport: PropTypes.string,
  reportArr: PropTypes.arrayOf(PropTypes.object),
}

export default Report
