import PropTypes from 'prop-types'
import React from 'react'
import { View, Dimensions, ActivityIndicator } from 'react-native'
import WebView from 'react-native-webview'
import { SimpleLine } from 'nagz-mobile-lib'
import { GO_FULLEDITORVIEW } from '../../../utils/jump'
import { getUpdateValidationFn } from '../common'

const diviceWitdh = Dimensions.get('window').width
class RichEditor extends React.PureComponent {
  state = {
    value: this.props.defaultValue,
    loading: true,
  }

  componentWillMount = () => {
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, this.props.defaultValue)
  }
  componentDidMount() {
    // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 250
  }
  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.props.defaultValue !== defaultValue) {
      this.updateValidation = this.updateValidation(this.props.required, defaultValue)
    }
    if (this.state.value !== defaultValue) {
      this.setState({
        value: defaultValue,
      })
    }
  }

  goToFullView=() => {
    GO_FULLEDITORVIEW(this.props)
  }
  webviewLoaded=() => {
    this.setState({
      loading: false,
    })
  }
  render() {
    const {
      label, required, enabled, defaultValue,
    } = this.props
    const htmlStr = `<!DOCTYPE html> <html lang="en"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <meta http-equiv="X-UA-Compatible" content="ie=edge"> <title>内容展示</title><style type="text/css">html,body{width:${diviceWitdh - 55}px;height:180px;overflow-x:hidden;overflow-y:hidden;} a{pointer-events:none} img{width:auto;max-width:${diviceWitdh - 50}px;height:auto} </style></head><body scroll="no">${defaultValue}</body></html>`
    console.log(htmlStr)
    const simpleLineProp = {
      isRequire: required, editable: false, enabled, label, placeholder: '显示全部', onPress: this.goToFullView,
    }
    return (
      <View style={{ flex: 1, backgroundColor: 'white', flexDirection: 'column' }}>
        <SimpleLine {...simpleLineProp} />
        <WebView
          originWhiteList={['*']}
          onLoadEnd={this.webviewLoaded}
          bounces={false}
          scalesPageToFit
          style={{ marginHorizontal: 15, height: 200, paddingTop: 4 }}
          source={{ html: htmlStr }}
        />
        <View style={{
 width: diviceWitdh, position: 'absolute', top: 100, justifyContent: 'center',
}}
        >
          <ActivityIndicator
            animating={this.state.loading}
            color="#007AFF"
            size="large"
          />
        </View>
      </View>
    )
  }
}
RichEditor.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.string, // eslint-disable-line
  desc: PropTypes.string,
  required: PropTypes.bool,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  value: PropTypes.any,// eslint-disable-line
  validate: PropTypes.object,// eslint-disable-line
  $$isValidation: PropTypes.bool,
}

RichEditor.defaultProps = {
  label: '',
  defaultValue: {},
  desc: '',
  required: false,
  isRunTime: false,
  fieldId: '',
  value: '',
  validate: {},
  $$isValidation: false,
  enabled: true,
}
export default RichEditor
