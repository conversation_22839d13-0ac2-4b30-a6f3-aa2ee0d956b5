import PropTypes from 'prop-types'
import React from 'react'
import _get from 'lodash/get'

import expression from '../../../utils/expression'
import { View, Image, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native'
import PicTableStyle from './style'
import { getData, getFields, getValueByExp } from './store'
import { FormContext } from '../../../utils/reactContext'

const style = StyleSheet.create(PicTableStyle)
const { isExpression } = expression

class PicTable extends React.PureComponent {
  constructor(props) {
    super(props)
    this.offset = 0
    this.limit = 10
  }
  static contextType = FormContext

  state={ datas: [] }
  componentDidMount= async () => {
    this.loadData()
    const { appId, formId, source } = this.props
    this.fileds = await getFields({ appId, formId: source, srcFormId: formId })
  }
  componentWillReceiveProps=({ filter }) => {
    if (filter !== this.props.filter) {
      this.loadDataByfilterChange(filter)
    }
  }
  getSelectFields=() => {
    const { field1, field2, field3, field4 } = this.props
    return [field1, field2, field3, field4]
  }

  getOffset=() => ({ limit: this.props.rows, offset: 0 })

  loadDataByfilterChange=(filter) => {
    this.loadData(filter)
  }
  clickMore=() => {
    if (this.props.setUrl) {
      const { formId, dataId, formState, filter } = this.props.setUrl
      if (formId && formState && (formId !== this.props.formId || dataId !== this.props.dataId)) {
        if (formState === 'viewPage') {
          this.context.goPage({ appId: this.props.appId, formId, operator: '2', dataId })
        } else {
          this.context.goPage({ appId: this.props.appId, formId, operator: '1', filter })
        }
      }
    }
  }
  rowClick=(row) => {
    const { field1, field2, field3, field4 } = this.props
    const showfields = this.fileds
      .filter(f => [field1, field2, field3, field4].indexOf(f.id) !== -1
        && f.properties.setUrl
        && f.properties.setUrl.supportTable === true)

    if (showfields.length !== 0) {
      this.goLink(showfields[0].properties.setUrl, row.id.value)
    }
  }
  goLink=async (setUrl, rowDataId) => {
    const { appId, source } = this.props
    const { formId, dataId, formState, filter, supportTable } = setUrl
    if (formId && formState && supportTable === true) {
      const condition = formState === 'viewPage' ? dataId : filter
      const isExp = [formId, condition].find(i => isExpression(i))
      let aimForm = formId
      let aimCondt = condition
      if (isExp) {
        const [v1, v2] = await getValueByExp(appId, source, rowDataId, [formId, condition])
        aimForm = v1
        aimCondt = v2
      }
      if (aimForm !== this.props.formId || aimCondt !== this.props.dataId) {
        if (formState === 'viewPage') {
          this.context.goPage({ appId, formId: aimForm, operator: '2', dataId: aimCondt })
        } else {
          this.context.goPage({ appId, formId: aimForm, operator: '1', filter: aimCondt })
        }
      }
    }
  }
  loadData= async (filter = this.props.filter) => {
    const selectField = ['id', ...this.getSelectFields()].join(',')
    const { appId, source, ignoreEmpty, orderby } = this.props
    if (isExpression(filter)) {
      return
    }
    const data = await getData({
      appId,
      formId: source,
      select: selectField,
      ignoreEmpty,
      orderby,
      filter,
      ...this.getOffset(),
    })
    this.setState({ datas: data })
  }
  renderTitle=() => {
    const { label, morelabel } = this.props
    return (
      <View style={style.title}>
        <Text style={style.titleText}>{label}</Text>
        <TouchableOpacity onPress={this.clickMore}>
          <Text style={style.titleMore}>{morelabel}</Text>
        </TouchableOpacity>
      </View>
    )
  }
  renderItem=(row) => {
    const { field1, field2, field3, field4 } = this.props
    return (
      <TouchableOpacity style={{ paddingVertical: 9 }} key={row.id.value} onPress={() => { this.rowClick(row) }}>
        <View style={style.itemContainer}>
          <Image style={style.itemImage} resizeMode="cover" source={{ uri: `${_get(row, `${field1}.display`)}?x-oss-process=image/resize,w_100` }} />
          <View style={style.itemTextWarp}>
            <Text style={{ fontSize: 14, color: 'rgba(0,0,0,.65)' }} numberOfLines={1} ellipsizeMode="tail">{_get(row, `${field2}.display`)}</Text>
            <Text style={{ fontSize: 12, color: 'rgba(0,0,0,.65)', marginTop: 5 }}numberOfLines={1} ellipsizeMode="tail">{_get(row, `${field3}.display`)}</Text>
            <Text style={{ fontSize: 12, color: 'rgba(0,0,0,.45)', marginTop: 10 }} numberOfLines={1} ellipsizeMode="tail">{_get(row, `${field4}.display`)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    )
  }
  render=() => (
    <View style={style.container}>
      {this.renderTitle()}
      <View style={{ marginTop: 7 }}>
        {this.state.datas.map(this.renderItem)}
      </View>
    </View>
  )
}
PicTable.propTypes = {
  label: PropTypes.string,
  morelabel: PropTypes.string,
  rows: PropTypes.number,
  field1: PropTypes.string.isRequired,
  field2: PropTypes.string.isRequired,
  field3: PropTypes.string.isRequired,
  field4: PropTypes.string.isRequired,
  source: PropTypes.string.isRequired,
  formId: PropTypes.string.isRequired,
  ignoreEmpty: PropTypes.string.isRequired,
  filter: PropTypes.string,
  appId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  orderby: PropTypes.string,
  isSimple: PropTypes.bool,
  setUrl: PropTypes.shape({
    formId: PropTypes.string.isRequired,
    formState: PropTypes.string.isRequired,
    dataId: PropTypes.string.isRequired,
    filter: PropTypes.string.isRequired,
  }),
}

PicTable.defaultProps = {
  label: '',
  filter: '',
  dataId: '',
  orderby: '',
  morelabel: 'more',
  rows: 4,
  isSimple: true,
  setUrl: {},
}

export default PicTable
