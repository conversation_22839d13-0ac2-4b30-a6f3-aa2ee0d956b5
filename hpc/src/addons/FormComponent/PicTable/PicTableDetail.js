/* eslint-disable import/no-unresolved */
import PropTypes from 'prop-types'
import React from 'react'
import _get from 'lodash/get'
import { View, FlatList, Image, Text, Animated, StyleSheet, TouchableOpacity, TextInput, ActivityIndicator, Dimensions } from 'react-native'

import NavigationService from '../../../NavigationService'
import expression from '../../../utils/expression'
import { getData, getValueByExp, getFields } from './store'
import SideMenu from '../../../components/SideMenu'
import PicTableStyle, { detailStyle } from './style'
import ModalHeader from '../ModalHeader'
import { isForm } from '../../constants'
import { FormContext } from '../../../utils/reactContext'

const style = StyleSheet.create(detailStyle)
const style2 = StyleSheet.create(PicTableStyle)

const width = Dimensions.get('window').width

const { isExpression } = expression

const prevIcon = require('../../../images/icon-arrow-previous-default.png')
const menuIcon = require('../../../images/icons-burger-outline-line-default.png')
const search = require('../../../images/icons-magnifier-search-gray.png')

class PicTalbeDetail extends React.PureComponent {
  constructor(props) {
    super(props)
    this.page = 0
    this.limit = 10
    this.searchText = ''
    this.hasMore = true
    this.isLoading = false
    this.fileds = []
  }
  static contextType = FormContext

  state={ searchText: '',
    data: [],
    isLoadingMore: false,
    refreshing: false,
    hasMore: true,
    searchLeft: 0,
    showSearchView: false }

  componentWillMount=() => {
    this.setState({
      searchLeft: new Animated.Value(12),
    })
  }
  componentDidMount= async () => {
    this.loadData()
    const { appId, formId, source } = this.props
    this.fileds = await getFields({ appId, formId: source, srcFormId: formId })
  }
  componentWillReceiveProps=({ filter }) => {
    if (filter !== this.props.filter) {
      this.loadDataByfilterChange(filter)
    }
  }
  onEndReached= async () => {
    if (this.isLoading !== true && this.hasMore === true) {
      this.setState({ isLoadingMore: true })
      this.isLoading = true
      this.page = this.page + 1
      const data = await this.getData()
      if (data.length === 0) {
        this.setState({ hasMore: false })
        this.hasMore = false
      } else {
        this.setState({ data: [...this.state.data, ...data] })
      }
      this.setState({ isLoadingMore: false })
      this.isLoading = false
    }
  }

  onRefresh= async () => {
    this.setState({ refreshing: true })
    this.page = 0
    const data = await this.getData()
    this.hasMore = true
    this.setState({ data, hasMore: true, refreshing: false })
  }
  onSearchChange=() => {
    this.search()
  }
  getSelectFields=() => {
    const { field1, field2, field3, field4 } = this.props
    return [field1, field2, field3, field4]
  }

  getQueryString =(param = {}) => {
    const { propFilter = this.props.filter, searchText = this.state.searchText } = param
    if (isExpression(propFilter)) {
      return false
    }
    const { searchcolumn } = this.props
    let filter = ''
    if (searchcolumn && searchcolumn.length !== 0 && searchText) {
      let f = ''
      searchcolumn.forEach((scolumn) => {
        f += ` ${scolumn} like ${searchText} or`
      })
      filter = `(${f.slice(0, f.length - 2)}) `
    }
    const query = !propFilter || isExpression(propFilter) ? '' : propFilter.replace(/ and $/, '').replace(/ or $/, '')
    filter = query ? `( ${query} ) ${filter ? `and  ${filter} ` : ''}` : filter
    return filter
  }
  getOffset=() => ({ limit: this.limit, offset: this.page * 10 })

  getData = async (param = {}) => {
    const { propFilter = this.props.filter, searchText = this.state.searchText } = param
    const filter = this.getQueryString({ propFilter, searchText })
    if (filter === false) {
      return []
    }
    const selectField = ['id', ...this.getSelectFields()].join(',')
    const { appId, source, ignoreEmpty, orderby } = this.props
    const data = await getData({
      appId,
      formId: source,
      select: selectField,
      ignoreEmpty,
      orderby,
      filter,
      ...this.getOffset(),
    })
    return data
  }
  getFormName=() => _get(this.props.items.find(isForm), 'properties.label')

  loadDataByfilterChange= async (filter) => {
    this.page = 0
    const data = await this.getData({ propFilter: filter })
    this.hasMore = true
    this.setState({ data, hasMore: true, refreshing: false })
  }
  rowClick=(row) => {
    const { field1, field2, field3, field4 } = this.props
    const showfields = this.fileds
      .filter(f => [field1, field2, field3, field4].indexOf(f.id) !== -1
        && f.properties.setUrl
        && f.properties.setUrl.supportTable === true)

    if (showfields.length !== 0) {
      this.goLink(showfields[0].properties.setUrl, row.id.value)
    }
  }
  goLink=async (setUrl, rowDataId) => {
    const { appId, source } = this.props
    const { formId, dataId, formState, filter, supportTable } = setUrl
    if (formId && formState && supportTable === true) {
      const condition = formState === 'viewPage' ? dataId : filter
      const isExp = [formId, condition].find(i => isExpression(i))
      let aimForm = formId
      let aimCondt = condition
      if (isExp) {
        const [v1, v2] = await getValueByExp(appId, source, rowDataId, [formId, condition])
        aimForm = v1
        aimCondt = v2
      }
      if (aimForm !== this.props.formId || aimCondt !== this.props.dataId) {
        if (formState === 'viewPage') {
          this.context.goPage({ appId, formId: aimForm, operator: '2', dataId: aimCondt })
        } else {
          this.context.goPage({ appId, formId: aimForm, operator: '1', aimCondt })
        }
      }
    }
  }
  loadData= async () => {
    const data = await this.getData()
    this.setState({ data })
  }

  back=() => {
    NavigationService.popAndRefresh({ refresh: { formUpdate: Math.random() } })
  }
  shouMenu=() => {
    this.props.openMenu()
  }

  search= async (param = {}) => {
    const { searchText = this.state.searchText } = param
    this.page = 0
    const data = await this.getData({ searchText })
    this.setState({ data, hasMore: true })
    this.hasMore = true
  }

  clickSearchBut=(isTrue) => {
    if (isTrue) {
      Animated.timing(this.state.searchLeft, {
        toValue: 60 - width,
        duration: 100,
      }).start()
      this.setState({ showSearchView: true })
    } else {
      Animated.timing(this.state.searchLeft, {
        toValue: 12,
        duration: 100,
      }).start()
      this.setState({
        showSearchView: false,
        searchText: '',
      })
      this.search({ searchText: '' })
    }
  }
  ListFooterComponent=() => {
    if (!this.state.hasMore) {
      return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>没有更多了</Text></View>
    } else if (this.state.isLoadingMore) {
      return <View style={{ paddingVertical: 20 }}><ActivityIndicator size="large" /></View>
    }
    return null
  }
  leftView=() => (
    <View style={{ height: '100%', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
      <TouchableOpacity style={{ }} onPress={this.back}>
        <Image source={prevIcon} />
      </TouchableOpacity>
      <TouchableOpacity style={{ marginLeft: 5 }} onPress={this.shouMenu}>
        <Image source={menuIcon} />
      </TouchableOpacity>
    </View>
  )
  centerText=() => {
    if (this.state.showSearchView) {
      return null
    }
    return this.getFormName()
  }
  rightView=() => {
    if (this.props.searchcolumn && this.props.searchcolumn.length !== 0) {
      return (
        <View style={style.headerRight}>
          <Animated.View
            style={[style.headerSearchBut, {
              borderBottomWidth: this.state.showSearchView ? 1 : 0,
              left: this.state.searchLeft }]}
          >
            <TouchableOpacity onPress={() => { this.clickSearchBut(true) }} >
              <Image source={search} />
            </TouchableOpacity>
            { this.state.showSearchView &&
            <TextInput
              value={this.state.searchText}
              style={style.searchInput}
              placeholder="搜索"
              autoFocus={this.state.showSearchView}
              onChangeText={(value) => { this.setState({ searchText: value }) }}
              onBlur={this.onSearchChange}
              onCancel={() => this.setState({ searchText: '' })}
              underlineColorAndroid="transparent"
            />}
            { this.state.showSearchView && <Text style={{ fontSize: 18 }} onPress={() => { this.clickSearchBut(false) }}>取消</Text> }
          </Animated.View>
        </View>)
    }
    return undefined
  }
  renderItem=({ item }) => {
    const { field1, field2, field3, field4 } = this.props
    return (
      <TouchableOpacity style={{ paddingVertical: 9 }} key={item.id.value} onPress={() => { this.rowClick(item) }}>
        <View style={style2.itemContainer}>
          <Image style={style2.itemImage} resizeMode="cover" source={{ uri: `${_get(item, `${field1}.display`)}?x-oss-process=image/resize,w_100` }} />
          <View style={style2.itemTextWarp}>
            <Text style={{ fontSize: 14, color: 'rgba(0,0,0,.65)' }} numberOfLines={1} ellipsizeMode="tail">{_get(item, `${field2}.display`)}</Text>
            <Text style={{ fontSize: 12, color: 'rgba(0,0,0,.65)', marginTop: 5 }}numberOfLines={1} ellipsizeMode="tail">{_get(item, `${field3}.display`)}</Text>
            <Text style={{ fontSize: 12, color: 'rgba(0,0,0,.45)', marginTop: 10 }} numberOfLines={1} ellipsizeMode="tail">{_get(item, `${field4}.display`)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    )
  }

  renderHeader=() => (
    <ModalHeader header={{ leftView: this.leftView(), centerText: this.centerText(), rightView: this.rightView() }} />
  )
  render=() => (
    <View style={{ flex: 1 }}>
      {this.renderHeader()}
      <FlatList
        data={this.state.data}
        extraData={this.state.hasMore}
        onEndReached={this.onEndReached}
        onEndReachedThreshold={0.1}
        refreshing={this.state.refreshing}
        onRefresh={this.onRefresh}
        keyExtractor={item => item.id.value}
        ListFooterComponent={this.ListFooterComponent}
        removeClippedSubviews={false}
        renderItem={this.renderItem}
        style={style.listWarp}
      />
    </View>
  )
}

PicTalbeDetail.propTypes = {
  filter: PropTypes.string,
  searchcolumn: PropTypes.arrayOf(PropTypes.string),
  ignoreEmpty: PropTypes.bool.isRequired,
  appId: PropTypes.string.isRequired,
  field1: PropTypes.string.isRequired,
  field2: PropTypes.string.isRequired,
  field3: PropTypes.string.isRequired,
  field4: PropTypes.string.isRequired,
  source: PropTypes.string.isRequired,
  formId: PropTypes.string.isRequired,
  orderby: PropTypes.string,
  dataId: PropTypes.string,
  openMenu: PropTypes.func.isRequired,
}

PicTalbeDetail.defaultProps = {
  filter: '',
  searchcolumn: [],
  orderby: '',
  dataId: '',
}
export default SideMenu(PicTalbeDetail)
