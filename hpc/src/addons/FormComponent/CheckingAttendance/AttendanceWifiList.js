import React, { Component } from 'react'
import { Text, View, FlatList } from 'react-native'
import { getVirtualDevice } from './store'
import { FormHeader } from '../ModalHeader'
import NavigationService from '../../../NavigationService'

const ListItem = ({ data }) => (
  <View style={{ padding: 10, marginVertical: 5, backgroundColor: '#fff' }}>
    <View><Text>wifi名：{data.name}</Text></View>
    <View><Text>mac地址：{data.mac}</Text></View>
  </View>

)

class AttendanceWifiList extends Component {
  constructor(props) {
    super(props)

    this.state = {
      wifiListData: [],
    }
  }

  componentDidMount=async () => {
    const { appId } = this.props
    const res = await getVirtualDevice(appId)
    const wifiData = res.datas.find(item => item.type === 'wifi')
    if (wifiData && wifiData.data.length) {
      this.setState({
        wifiListData: wifiData.data,
      })
    }
  }
  keyExtractor=item => item.id
  renderItem=({ item, index }) => <ListItem data={item} />
  goBack=() => {
    NavigationService.popAndRefresh({ refresh: { formUpdate: Math.random() } })
  }
  render() {
    const { wifiListData } = this.state
    return (
      <View>
        <FormHeader centerText="办公WiFi" onPressLeft={this.goBack} />
        <FlatList
          data={wifiListData}
          renderItem={this.renderItem}
          keyExtractor={this.keyExtractor}
        />
      </View>
    )
  }
}
export default AttendanceWifiList
