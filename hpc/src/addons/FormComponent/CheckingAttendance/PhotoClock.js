import PropTypes from 'prop-types'
import React from 'react'
import { BackHandler, View, TouchableOpacity, Text, Image } from 'react-native'
import moment from 'moment'
import { Button } from 'nagz-mobile-lib'
import { FormHeader } from '../../../addons/FormComponent/ModalHeader'
import { isPrivate } from '../../../addons/FormComponent/Attachment/uploadFiles'
import { screenHeight, screenWidth, deviceType } from '../../../config/sysPara'
import NavigationService from '../../../NavigationService'
import { GO_PICTURELIST } from '../../../utils/jump'

const platformFixHeight = deviceType === 1 ? 24 : 0

const AutoRefresh = (time, callback) => {
  const timer = setInterval(() => {
    callback()
  }, time * 1000)
  return timer
}

class PhotoClock extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      displayDetail: this.props.displayDetail,
      currentTime: moment(),
      photos: [],
    }
  }

  componentWillReceiveProps = ({ displayDetail }) => {
    if (this.props.displayDetail !== displayDetail) {
      this.setState({ displayDetail })
    }
  }

  componentDidMount() {
    this.timer = AutoRefresh(1, () => this.setState({
      currentTime: moment((new Date())),
    }))
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
  }

  componentWillUnmount() {
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
  }

  goBack = () => {
    this.props.changeClick()
    NavigationService.back()
    return true
  }

  changePhoto = (photos) => {
    this.setState({ photos })
  }

  onphotoPress = (showActionButton) => {
    GO_PICTURELIST({
      ...this.props,
      existFile: this.state.photos,
      showActionButton: this.state.photos.length > 2 ? false : showActionButton,
      enabled: true,
      changePhoto: this.changePhoto,
      cameraMsg: { compressImageQuality: 0.8, fromAlbum: this.props.fromAlbum, photoclock: true },
    })
  }

  onPressSubmit = async () => {
    const len = this.state.photos.length
    if (len < 1 && this.props.photoclock === '1') return Toast.info('请上传照片', 1)
    await this.props.doCheck(this.state.photos)
    this.goBack()
  }

  renderItem = (file) => {
    if (file.progress % 100 === 0) {
      const uri = isPrivate ? file.url : `${file.url}?x-oss-process=image/resize,m_fixed,h_90,w_90`
      return (
        <TouchableOpacity
          key={file.uid}
          onPress={() => this.onphotoPress(false)}
          style={{ margin: 10, display: 'flex', alignItems: 'center', justifyContent: 'center', width: 90, height: 90, borderColor: '#e5e5e5', borderWidth: 1 }}
          activeOpacity={0.8}
        >
          <Image
            resizeMode="contain"
            source={{ uri }}
            style={{ width: 90, height: 90 }}
          />
        </TouchableOpacity>
      )
    }
  }

  render() {
    const { displayDetail, currentTime, photos } = this.state
    const reverseFiles = []
    if (photos) {
      photos.filter(f => f.version !== '0').forEach((file) => { reverseFiles.unshift(file) })
    }
    return (
      <View style={{ height: screenHeight - platformFixHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
        <FormHeader centerText="考勤打卡" onPressLeft={this.goBack} />
        <View style={{ flex: 1, backgroundColor: '#F8F7F7' }}>
          <View style={{ padding: 18, marginBottom: 10, backgroundColor: '#fff' }}>
            <View>
              <Text style={{ lineHeight: 22, marginTop: -4, fontWeight: 'bold', color: 'rgba(0,0,0,0.65)', fontSize: 14 }}>
                打卡时间 {currentTime.format('HH:mm:ss')}
              </Text>
            </View>
            <View><Text style={{ lineHeight: 22, color: 'rgba(0,0,0,0.85)', fontSize: 12 }}>{displayDetail}</Text></View>
          </View>
          <View style={{ backgroundColor: '#fff' }}>

            <View style={{ paddingLeft: 18, borderBottomWidth: 1, borderBottomColor: '#f2f2f2' }}><Text style={{ lineHeight: 46, color: '#1b1b1b', fontSize: 16, fontWeight: '600' }}>上传照片</Text></View>

            <View style={{ minHeight: 100, display: 'flex', padding: 10, flexDirection: 'row', flexWrap: 'wrap', alignItems: 'flex-end', alignContent: 'flex-end' }}>
              {reverseFiles.map(this.renderItem)}
              <TouchableOpacity onPress={() => this.onphotoPress(true)} style={{ margin: 10, display: 'flex', alignItems: 'center', justifyContent: 'center', width: 90, height: 90, borderColor: '#e5e5e5', borderWidth: 1 }}>
                <Image source={require('../../../images/icon-camera-large.png')} />
              </TouchableOpacity>
            </View>

            <View style={{ paddingHorizontal: 20, paddingVertical: 34, borderTopWidth: 1, borderTopColor: '#f2f2f2' }}>
              <Button
                style={{ container: { height: 40, backgroundColor: '#17A9FF', borderRadius: 2 }, textContent: { fontSize: 14, color: '#fff' } }}
                onPress={this.onPressSubmit}
              >提交
              </Button>
            </View>
          </View>
        </View>
      </View>
    )
  }
}

PhotoClock.propTypes = {
  doCheck: PropTypes.func,
  changeClick: PropTypes.func,
  displayDetail: PropTypes.string,
  currentTime: PropTypes.object,
}

export default PhotoClock

