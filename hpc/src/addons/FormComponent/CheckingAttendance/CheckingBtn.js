import React, { Component } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableWithoutFeedback,
  Platform,
  TouchableOpacity,
  Image,
} from 'react-native'
import PropTypes from 'prop-types'
import moment from 'moment'
import { withNavigationFocus } from 'react-navigation'
import { NetworkInfo } from 'react-native-network-info'
import { Toast, Icon } from '@ant-design/react-native'
import Geo from '@react-native-community/geolocation'
import {
  Geolocation,
} from 'react-native-baidu-map'
import { postAttendanceInfo, getVirtualDevice } from './store'
import { GO_WIFILIST, GO_MODAL } from '../../../utils/jump'
import { getFlatternDistance } from './util'
import { bd09togcj02, gcj02towgs84, wgs84togcj02, gcj02tobd09 } from '../../../utils/coordtransform'
import PhotoClock from './PhotoClock'
import FaceDetector from './FaceDetector'

const onPressImg = require('../../../images/btn_check_in_press.png')
const btnImg = require('../../../images/btn_check_in_default.png')

/**
 * tip 大部分时间用时间戳保存，避免moment对象重复引用
 */
class CheckingBtn extends Component {
  constructor(props) {
    super(props)
    const intialTime = moment()
    this.geores = {}
    this.state = {
      updateFrequency: 10,
      checkFrequency: 60,
      currentTime: intialTime,
      lastCheckTime: 0,
      lastUpdateTime: intialTime.valueOf(),
      checkBtnIsOnPress: false,
      attendanceData: [],
      isAllowCheck: false,
      relativeFormId: null,
      relativeField: null,
      relativeUserId: null,
      isContainsPhoto: false,
      displayDetail: '',
    }
  }
  componentDidMount() {
    console.log(this.timer, 'componentDidMount')
  }
  componentWillMount() {
    console.log(this.beginTimer, 'componentWillMount')
    if (this.props.isFocused) {
      this.beginTimer = setTimeout(() => {
        this.init()
      }, 1000)
    }

    // const { navigation } = this.props
    // this.event2 = navigation.addListener('willBlur', () => {
    //   console.log(this.timer, 'willBlur')
    //   if (this.timer) {
    //     clearInterval(this.timer)
    //     this.timer = null
    //   }
    //   if (this.beginTimer) {
    //     clearTimeout(this.beginTimer)
    //   }
    // })
  }

  componentWillUnmount() {
    console.log(this.timer, 'componentWillUnmount')
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    if (this.beginTimer) {
      clearTimeout(this.beginTimer)
    }
    // if (this.event2) {
    //   this.event2.remove()
    // }
  }
  // 初始化
  init = async () => {
    this.initTimer()
    await this.getUpdateStatus()
  };

  initTimer = async () => {
    this.timer = setInterval(async () => {
      const { lastUpdateTime, updateFrequency, currentTime } = this.state
      const newTime = currentTime.add(1, 's')
      // console.log(newTime.valueOf() - lastUpdateTime)
      // 当前时间超出设置更新频率时，更新状态，
      if (newTime.valueOf() - lastUpdateTime >= updateFrequency * 1000) {
        await this.getUpdateStatus()
      } else {
        this.setState({
          currentTime: newTime,
        })
      }
    }, 1000)
  };
  // 获取当前设备状态
  getUpdateStatus = async () => {
    const { appId } = this.props
    const data = await getVirtualDevice(appId)
    const currentTime = data.currentTime
      ? moment(data.currentTime - 0)
      : moment()
    const momentLastTime = data.lastTime ? moment(data.lastTime - 0)
      : moment(0)
    this.setState({
      lastCheckTime: momentLastTime.valueOf(),
      lastUpdateTime: currentTime.valueOf(),
      currentTime,
      updateFrequency: Number(data.frequency) || 10,
      checkFrequency: Number(data.interval) || 60,
      attendanceData: data.datas || [],
      relativeFormId: data.formId,
      relativeField: data.mobile_fields,
      relativeUserId: data.userId,
      isContainsPhoto: data.isContainsPhoto,
    }, async () => {
      await this.matchAllowCheck()
    })
  };

  getcurrPositionRes = async () => {
    let geoResults = {}
    try {
      geoResults = await Geolocation.getCurrentPosition()
    } catch (error) {
      console.log('Geolocation.getCurrentPosition', error)
    }
    console.log('Geolocation', geoResults)
    if (geoResults.address) this.geores = geoResults
    if (!geoResults.address && this.geores.address) geoResults = this.geores
    const gcj02Position = bd09togcj02(geoResults.longitude, geoResults.latitude)
    const wgs84Position = gcj02towgs84(gcj02Position[0], gcj02Position[1])
    return { geoResults, wgs84Position }
  }

  matchAllowCheck = async (isPostRecord, photos = [], successCallback) => {
    const { appId, relativeOper, formSubmitHandle, photoclock } = this.props
    const { attendanceData, isContainsPhoto } = this.state
    const connectedWifiInfo = await this.getConnectedWifiInfo()
    // debugger
    // Toast.info(`${connectedWifiInfo.ssid}-${connectedWifiInfo.bssid}`, 1)
    const { geoResults, wgs84Position } = await this.getcurrPositionRes()
    let photo = {}
    if (isContainsPhoto && (photoclock === '2' || photoclock === '1') && photos.length) {
      const photoIds = photos.filter(({ id }) => id).map(({ id }) => id)
      photo = { photo: photoIds.join(',') }
    }
    // 迭代处理每一项配置，直到有一项返回结果为true时，停止迭代
    const isAllowCheck = attendanceData.some((item) => {
      if (item.type === 'wifi') {
        return item.data.some((i) => {
          if (
            i.name === connectedWifiInfo.ssid &&
            i.mac.slice(0, 14) === connectedWifiInfo.bssid.slice(0, 14)
          ) {
            this.setState({ displayDetail: connectedWifiInfo.ssid })
            if (isPostRecord) {
              const postRequest = () => {
                postAttendanceInfo(appId, {
                  type: '0',
                  name: connectedWifiInfo.ssid,
                  mac: connectedWifiInfo.bssid,
                  ...photo,
                }, successCallback)
              }
              if (!relativeOper) {
                postRequest()
              } else {
                formSubmitHandle({ id: relativeOper, successCallback: postRequest })
              }
            }
            return true
          }
          return false
        })
      } else if (item.type === 'gps') {
        return item.data.some((i) => {
          const realDistance = getFlatternDistance(wgs84Position[1], wgs84Position[0], Number(i.latitude), Number(i.longitude))
          // Toast.info(`${realDistance}-${wgs84Position[1]}-${wgs84Position[0]}`, 1)
          if (
            realDistance <= Number(i.range) || i.unlimitedscope === 1
          ) {
            this.setState({ displayDetail: geoResults.address })
            if (isPostRecord) {
              const postRequest = () => {
                postAttendanceInfo(appId, {
                  type: '1',
                  longitude: `${wgs84Position[0]}`,
                  latitude: `${wgs84Position[1]}`,
                  address: geoResults.address,
                  ...photo,
                }, successCallback)
              }
              if (!relativeOper) {
                postRequest()
              } else {
                formSubmitHandle({ id: relativeOper, successCallback: postRequest })
              }
            }
            return true
          }
          return false
        })
      }
      return false
    })
    console.log(isAllowCheck)
    this.setState({
      isAllowCheck,
    })
  };
  // 考勤
  doCheck = async (photos, isface) => {
    console.log('doCheck')
    const { lastCheckTime, checkFrequency, relativeFormId, relativeField, relativeUserId } = this.state
    const { loadListData, relativeOper } = this.props
    const currentTime = moment()
    if (!isface && (currentTime.valueOf() - lastCheckTime < checkFrequency * 1000)) {
      return
    }
    await this.matchAllowCheck(true, photos, async () => {
      loadListData(relativeFormId, relativeField, relativeUserId)
      this.setState({
        lastCheckTime: currentTime.valueOf(),
      })
      if (!relativeOper) {
        Toast.success('打卡成功', 1)
      }
    })
    return true
  }

  goToWifiList = () => {
    const { appId } = this.props
    GO_WIFILIST(appId)
  }

  onPressInHandle = () => {
    this.setState({
      checkBtnIsOnPress: true,
    })
  };
  onPressOutHandle = () => {
    this.setState({
      checkBtnIsOnPress: false,
    })
  };
  getConnectedWifiInfo = async () => {
    if (Platform.OS === 'ios') {
      Geo.requestAuthorization()
      const ssid = await NetworkInfo.getSSID()
      const bssid = await NetworkInfo.getBSSID()
      // Toast.info(`${ssid}-${bssid}`, 1)
      console.log('ahdskjadhahsdkjhakjsd', ssid, bssid)
      return {
        ssid,
        bssid,
      }
    }
    const ssid = await NetworkInfo.getSSID()
    const bssid = await NetworkInfo.getBSSID()
    console.log('ahdskjadhahsdkjhakjsd', ssid, bssid)
    return {
      ssid,
      bssid,
    }
  }

  goFaceRecog = () => {
    if (!this.isclick) {
      this.isclick = true
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <FaceDetector
          appId={this.props.appId}
          formId={this.state.relativeFormId}
          displayDetail={this.state.displayDetail}
          doCheck={this.doCheck}
          changeClick={() => { this.isclick = false }}
        />,
        opacity: 0.5,
      })
    }
  }

  goPhoto = () => {
    if (!this.isclick) {
      this.isclick = true
      let attachmentId = ''
      if (this.state.isContainsPhoto) {
        attachmentId = this.state.relativeField.photo
      }
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <PhotoClock
          appId={this.props.appId}
          photoclock={this.props.photoclock}
          fromAlbum={this.props.fromAlbum}
          formId={this.state.relativeFormId}
          fieldId={attachmentId}
          displayDetail={this.state.displayDetail}
          doCheck={this.doCheck}
          changeClick={() => { this.isclick = false }}
        />,
        opacity: 0.5,
      })
    }
  }

  render() {
    const { label, enabled, photoclock } = this.props
    const {
      currentTime,
      checkBtnIsOnPress,
      isAllowCheck,
      lastCheckTime,
      checkFrequency,
      isContainsPhoto,
    } = this.state
    const isLockCheck = enabled ? !isAllowCheck
      ? true
      : (currentTime.valueOf() - lastCheckTime) < checkFrequency * 1000 : true
    const faceRecog = false
    return (
      <View style={{ alignItems: 'center' }}>
        <TouchableWithoutFeedback
          onPress={isLockCheck ? null : faceRecog ? this.goFaceRecog : isContainsPhoto && photoclock === '1' ? this.goPhoto : this.doCheck}
          onPressIn={isLockCheck ? null : this.onPressInHandle}
          onPressOut={isLockCheck ? null : this.onPressOutHandle}
        >
          <View style={styles.btnContainer}>
            <ImageBackground
              source={
                checkBtnIsOnPress ? onPressImg : btnImg
              }
              style={isLockCheck ? styles.btnDisabled : checkBtnIsOnPress ? styles.btnOnPress : styles.btn}
            >
              <Text style={styles.centerText}>{label} </Text>
              <Text style={styles.centerTime}>
                {currentTime.format('HH:mm:ss')}{' '}
              </Text>
            </ImageBackground>
          </View>
        </TouchableWithoutFeedback>
        {!isAllowCheck && (
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ fontSize: 12, color: 'rgba(0,0,0,0.85)' }}>当前不在考勤范围内：</Text>
            <Text onPress={this.goToWifiList} style={{ fontSize: 12, color: '#0091ff' }}>查看办公 WiFi</Text>
          </View>
        )}
        {isContainsPhoto && photoclock === '2' && !faceRecog &&
          <TouchableOpacity style={{ marginVertical: 24, flexDirection: 'row', alignItems: 'center' }} onPress={isLockCheck ? null : this.goPhoto} >
            <Image style={{ width: 14, height: 14 }} source={require('../../../images/icon-camera-small.png')} />
            <Text style={{ paddingLeft: 8, fontSize: 12, color: isLockCheck ? '#7cbefc' : '#0091FF', fontWeight: '600' }}>拍照打卡</Text>
            <Icon name="right" size={12} color="#0091ff" />
          </TouchableOpacity>
        }
      </View>
    )
  }
}

export default withNavigationFocus(CheckingBtn)

CheckingBtn.propTypes = {
  appId: PropTypes.string.isRequired,
  label: PropTypes.string,
  loadListData: PropTypes.func.isRequired,
  relativeOper: PropTypes.string,
}
CheckingBtn.defaultProps = {
  label: '',
  relativeOper: '',
}
const styles = StyleSheet.create({
  btnContainer: {
    width: 200,
    height: 200,
  },
  btnDisabled: {
    opacity: 0.35,
    width: 200,
    height: 200,
    alignItems: 'center',
    paddingTop: 50,
  },
  btn: {
    width: 200,
    height: 200,
    alignItems: 'center',
    paddingTop: 50,
  },
  btnOnPress: {
    width: 200,
    height: 200,
    alignItems: 'center',
    paddingTop: 60,
  },
  centerText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2EA8D9',
  },
  centerTime: {
    fontSize: 18,
    color: '#2EA8D9',
  },
})
