import React, { Component } from 'react'
import { View, Text, FlatList, ActivityIndicator } from 'react-native'
import { DatePicker, List } from '@ant-design/react-native'
import debounce from 'lodash/debounce'
import moment from 'moment'
import { getListData } from './store'
import { FormHeader } from '../ModalHeader'
import NavigationService from '../../../NavigationService'

const minDate = moment('1900-1-1').toDate()
const maxDate = moment('2100-1-1').toDate()

function isJSON(str) {
  console.log('ashdlkadlkasjdlkasjd', str)
  if (typeof str === 'string') {
    try {
      JSON.parse(str)
      return true
    } catch (e) {
      console.log(e)
      return false
    }
  }
  return false
}
const RecordItem = ({ data, timeField, detailField }) => {
  const time = moment(data[timeField].value - 0)
  const detail = data[detailField].value ? JSON.parse(data[detailField].value) : {}
  let displayDetail = ''
  if (detail.type === '0') {
    displayDetail = detail.name
  } else if (detail.type === '1') {
    displayDetail = detail.address
  }
  return (
    <View style={{ marginHorizontal: 24, paddingHorizontal: 14, paddingBottom: 20, borderLeftWidth: 2, borderLeftColor: '#e9e9e9' }}>
      <View><Text style={{ lineHeight: 22, marginTop: -4, fontWeight: 'bold', color: 'rgba(0,0,0,0.65)', fontSize: 14 }}>打卡时间 {time.format('HH:mm:ss')}</Text></View>
      <View><Text style={{ lineHeight: 22, color: 'rgba(0,0,0,0.85)', fontSize: 12 }}>{displayDetail}</Text></View>
    </View>
  )
}

export default class CheckingRecordList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      pageNo: 1,
      pageSize: 10,
      listData: [],
      totalCount: 0,
      isRefresh: true,
      selectTime: moment(),
    }
  }
  componentDidMount() {
    this.refresh()
  }
  refresh = async () => {
    const pageNo = 1
    const pageSize = 10
    this.setState({
      isRefresh: true,
    })
    const res = await this.getData(pageNo, pageSize)
    if (res.flag) {
      this.setState({
        pageNo,
        pageSize,
        listData: res.data,
        totalCount: Number(res.count),
        isRefresh: false,
      })
    }
  }

  isLoadAllData = (total, pageNo, pageSize) => {
    if (pageNo * pageSize >= total) {
      return true
    }
    return false
  }

  loadMoreData = debounce(async () => {
    const { listData, pageNo, pageSize, totalCount } = this.state

    if (this.isLoadAllData(totalCount, pageNo, pageSize)) {
      return
    }
    const res = await this.getData(pageNo + 1, pageSize)
    if (res.flag) {
      this.setState({
        pageNo: pageNo + 1,
        pageSize,
        listData: listData.concat(res.data),
        totalCount: Number(res.count),
      })
    }
  }, 200)
  getData = async (pageNo, pageSize) => {
    const { selectTime } = this.state
    const beginTime = moment(selectTime).startOf('day').valueOf()
    const endTime = moment(selectTime).endOf('day').valueOf()
    const { appId, formId, display } = this.props
    const displayField = `${display.timeField},${display.detailField}`
    let filter = ''
    if (display.userField) {
      filter = `${display.userField} like ${display.currentUserId} and ${display.timeField} ge ${beginTime} and ${display.timeField} le ${endTime}  and ${display.detailField}  ne ${encodeURI('{detail}')}`
    }
    const res = await getListData(appId, formId, pageNo, pageSize, displayField, filter, true)
    const filterData = res.data.filter(i => isJSON(i[display.detailField].value))
    res.data = filterData
    return res
  }
  goBack = () => {
    NavigationService.popAndRefresh({ refresh: { formUpdate: Math.random() } })
  }
  renderItem = ({ item }) => {
    const { display } = this.props
    return <RecordItem data={item} timeField={display.timeField} detailField={display.detailField} />
  }
  changeDateHandle = (value) => {
    this.setState({
      selectTime: moment(value),
    }, () => {
      this.refresh()
    })
  }

  render() {
    const { listData, isRefresh, totalCount, pageNo, pageSize, selectTime } = this.state
    const isNoMore = this.isLoadAllData(totalCount, pageNo, pageSize)
    return (
      <View style={{ flex: 1 }}>
        <FormHeader centerText="打卡记录" onPressLeft={this.goBack} />
        <DatePicker minDate={minDate} maxDate={maxDate} value={selectTime.toDate()} mode="date" onChange={this.changeDateHandle}>
          <List.Item arrow="horizontal">选择日期</List.Item>
        </DatePicker>
        <FlatList
          style={{ flex: 1, marginTop: 40 }}
          data={listData}
          renderItem={this.renderItem}
          onRefresh={this.refresh}
          onEndReached={this.loadMoreData}
          onEndReachedThreshold={0.1}
          refreshing={isRefresh}
          ListFooterComponent={!isNoMore && <ActivityIndicator />}
        />
      </View>
    )
  }
}
