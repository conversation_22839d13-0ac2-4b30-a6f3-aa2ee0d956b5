import PropTypes from 'prop-types'
import React from 'react'
import { BackHandler, View, TouchableOpacity, Text, Image, Animated, Easing, StyleSheet } from 'react-native'
import { Icon, Toast, Portal } from '@ant-design/react-native'
import { RNCamera } from 'react-native-camera'
import moment from 'moment'
import { screenHeight, screenWidth, deviceType } from '../../../config/sysPara'
import NavigationService from '../../../NavigationService'

const platformFixHeight = deviceType === 1 ? 24 : 0

const AutoRefresh = (time, callback) => {
  const timer = setInterval(() => {
    callback()
  }, time * 1000)
  return timer
}

class FaceDetection extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      displayDetail: this.props.displayDetail,
      currentTime: moment(),
      moveAnim: new Animated.Value(-500),
      cameraType: RNCamera.Constants.Type.front,
    }
  }

  componentWillReceiveProps = ({ displayDetail }) => {
    if (this.props.displayDetail !== displayDetail) {
      this.setState({ displayDetail })
    }
  }

  componentDidMount() {
    this.startAnimation()
    this.timer = AutoRefresh(1, () => this.setState({
      currentTime: moment((new Date())),
    }))
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
  }

  startAnimation = () => {
    this.state.moveAnim.setValue(-500)
    Animated.timing(
      this.state.moveAnim,
      {
        toValue: 0,
        duration: 1500,
        easing: Easing.linear,
      },
    ).start(() => this.startAnimation())
  }

  componentWillUnmount() {
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
  }

  goBack = () => {
    this.props.changeClick()
    NavigationService.back()
    return true
  }

  takePicture = (camera) => {
    if (!this.isClick) {
      this.isClick = true
      const tKey = Toast.loading('识别中...', 0)
      camera.takePictureAsync()
        .then(async (data) => {
          // Toast.info(`拍照成功！图片保存地址：\n${data.uri}`)
          // this.setState({ img: data.uri })
          console.log(data, 'imguri')
          const res = await this.props.doCheck([], true)
          if (res) {
            Portal.remove(tKey)
            this.isClick = false
          }
        })
        .catch((err) => {
          console.error(err)
          Portal.remove(tKey)
          this.isClick = false
        })
    }
  }

  switchCamera = () => {
    this.setState({
      cameraType: this.state.cameraType === RNCamera.Constants.Type.front ? RNCamera.Constants.Type.back : RNCamera.Constants.Type.front,
    })
  }

  render() {
    const { displayDetail, currentTime } = this.state
    return (
      <View style={{ height: screenHeight - platformFixHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
        <View style={styles.container}>
          <RNCamera
            ref={(cam) => {
              this.camera = cam
            }}
            style={styles.preview}
            type={this.state.cameraType}
          >
            <View style={{ width: screenWidth, padding: 18, marginBottom: 10, flexDirection: 'row', justifyContent: 'space-between' }}>
              <View>
                <Text style={{ lineHeight: 22, marginTop: -4, fontWeight: 'bold', color: '#fff', fontSize: 14 }}>
                  打卡时间 {currentTime.format('HH:mm:ss')}
                </Text>
                <Text style={{ lineHeight: 22, color: '#fff', fontSize: 12 }}>{displayDetail}</Text>
              </View>
              <TouchableOpacity onPress={this.switchCamera}>
                <Icon name="retweet" size={28} color="#fff" />
              </TouchableOpacity>
              {/* <Image style={{ width: 20, height: 20 }} source={{ uri: this.state.img }} resizeMode="stretch" /> */}
            </View>
            <View style={styles.rectangleContainer}>
              <View style={styles.rectangle} />
              <Animated.View style={[styles.border, { transform: [{ translateY: this.state.moveAnim }] }]} />
            </View>
            <View style={{ width: screenWidth, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 30 }}>
              <TouchableOpacity onPress={this.goBack}><Text style={styles.button}>取消</Text></TouchableOpacity>
              <TouchableOpacity onPress={() => this.takePicture(this.camera)} style={{ backgroundColor: '#fff', width: 60, height: 60, borderRadius: 30, alignItems: 'center', justifyContent: 'center' }}><Icon name="camera" size={32} color="#0091FF" /></TouchableOpacity>
              <TouchableOpacity onPress={this.goBack}><Text style={styles.button}>完成</Text></TouchableOpacity>
            </View>
          </RNCamera>
        </View>
      </View>
    )
  }
}

FaceDetection.propTypes = {
  doCheck: PropTypes.func,
  changeClick: PropTypes.func,
  displayDetail: PropTypes.string,
  currentTime: PropTypes.object,
}

export default FaceDetection

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  preview: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rectangleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  rectangle: {
    height: 500,
    width: 240,
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: 'transparent',
  },
  rectangleText: {
    flex: 0,
    color: '#fff',
    marginTop: 10,
  },
  border: {
    flex: 0,
    width: screenWidth - 40,
    height: 2,
    backgroundColor: '#00FF00',
  },
  button: {
    color: '#fff',
    fontSize: 18,
    lineHeight: 60,
    paddingHorizontal: 20,
    marginHorizontal: 10,
  },
})
