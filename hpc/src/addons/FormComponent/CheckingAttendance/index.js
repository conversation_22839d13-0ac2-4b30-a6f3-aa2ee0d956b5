import React, { Component } from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import { Icon } from '@ant-design/react-native'
import PropTypes from 'prop-types'
import moment from 'moment'
import CheckingBtn from './CheckingBtn'
import { getVirtualDevice, getListData } from './store'
import { GO_ATTENDANCERECORDLIST } from '../../../utils/jump'

function isJSON(str) {
  console.log('ashdlkadlkasjdlkasjd', str)
  if (typeof str === 'string') {
    try {
      JSON.parse(str)
      return true
    } catch (e) {
      console.log(e)
      return false
    }
  }
  return false
}

const RecordItem = ({ data, timeField, detailField }) => {
  const time = moment(data[timeField].value - 0)
  const detail = data[detailField].value ? JSON.parse(data[detailField].value) : {}
  let displayDetail = ''
  if (detail.type === '0') {
    displayDetail = detail.name
  } else if (detail.type === '1') {
    displayDetail = detail.address
  }
  return (
    <View style={{ paddingHorizontal: 14, paddingBottom: 30, borderLeftWidth: 2, borderLeftColor: '#e9e9e9' }}>
      <View><Text style={{ lineHeight: 22, fontWeight: 'bold', color: 'rgba(0,0,0,0.65)', fontSize: 14 }}>打卡时间 {time.format('HH:mm:ss')}</Text></View>
      <View><Text style={{ lineHeight: 22, color: 'rgba(0,0,0,0.85)', fontSize: 12 }}>{displayDetail}</Text></View>
    </View>
  )
}

class CheckingAttendance extends Component {
  constructor(props) {
    super(props)
    this.btnCmpRef = React.createRef()
    this.state = {
      attendanceRecordData: [],
      timeField: '',
      detailField: '',
      userField: '',
      attendanceFormId: '',
    }
  }
  componentWillMount = async () => {
    const { appId, navigation } = this.props
    const data = await getVirtualDevice(appId)
    const { formId, mobile_fields, userId } = data

    this.loadListData(formId, mobile_fields, userId)
  }

  loadListData = async (formId, fields, relativeUserId) => {
    const beginTime = moment().startOf('day').valueOf()
    const endTime = moment().endOf('day').valueOf()
    let display = ''
    let filter = ''
    if (fields) {
      display = `${fields.detail},${fields.log_datetime}`
      filter = `${fields.user_id} like ${relativeUserId}  and ${fields.log_datetime} ge ${beginTime} and ${fields.log_datetime} le ${endTime} and ${fields.detail} ne ${encodeURI('{detail}')}`
    }
    const { appId } = this.props
    const pageSize = 4
    const pageNo = 1
    const res = await getListData(appId, formId, pageNo, pageSize, display, filter)
    const filterData = res.data.filter(i => isJSON(i[fields.detail].value))
    this.setState({
      attendanceRecordData: filterData,
      timeField: fields.log_datetime,
      detailField: fields.detail,
      userField: fields.user_id,
      currentUserId: relativeUserId,
      attendanceFormId: formId,
    })
  }

  toReordList = () => {
    const { appId } = this.props
    const { attendanceFormId, timeField, detailField, userField, currentUserId } = this.state
    const display = {
      timeField, detailField, userField, currentUserId,
    }
    GO_ATTENDANCERECORDLIST(appId, attendanceFormId, display)
  }

  render() {
    const { label, appId, enabled, relativeOper, formSubmitHandle, photoclock, fromAlbum } = this.props
    const { attendanceRecordData, timeField, detailField } = this.state
    return (
      <View style={{ backgroundColor: '#fff' }}>
        {
          !!attendanceRecordData.length &&
          <View style={{ paddingHorizontal: 24, paddingVertical: 20 }}>
            {attendanceRecordData.map((item, i) => <RecordItem key={i} data={item} timeField={timeField} detailField={detailField} />)}
          </View>
        }
        <TouchableOpacity onPress={this.toReordList} style={{ width: 200, paddingHorizontal: 40, paddingBottom: 20, flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{ color: '#0091ff', fontSize: 12, lineHeight: 24 }}>查看打卡记录</Text>
          <Icon name="right" size={12} color="#0091ff" />
        </TouchableOpacity>
        <CheckingBtn ref={this.btnCmpRef} appId={appId} label={label} enabled={enabled} formSubmitHandle={formSubmitHandle} relativeOper={relativeOper} loadListData={this.loadListData} photoclock={photoclock} fromAlbum={fromAlbum} />
      </View>
    )
  }
}

export default CheckingAttendance
CheckingAttendance.propTypes = {
  label: PropTypes.string,
  appId: PropTypes.string,
}
CheckingAttendance.defaultProps = {
  label: '',
  appId: '',
}

