import React from 'react'
import PropTypes from 'prop-types'
import { createForm } from 'rc-form'
import { View } from 'react-native'
import { InputItem, List } from '@ant-design/react-native'

import Label from './Label'

const FormItem = List.Item

const Default = ({ label, desc, required }) => (
  <View />
  )

export const DefaultDisplay = ({ label, defaultValue }) => (
  <View>
    <Label label={label} />
    <InputItem
      placeholder="请输入"
      disabled={true}
      value={String(defaultValue && defaultValue)}
    />
  </View>
  )
Default.propTypes = {
  label: PropTypes.string.isRequired,
}
Default.defaultProps = {
  onSelect: () => {},
  selectId: undefined,
}
export default Default
