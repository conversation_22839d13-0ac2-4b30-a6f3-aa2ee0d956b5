import PropTypes from 'prop-types'
import Orientation from 'react-native-orientation'
import React from 'react'
import debounce from 'lodash/debounce'
// import SortableGrid from 'react-native-sortable-grid'
import * as Animatable from 'react-native-animatable'
import Swipeout from 'react-native-swipeout'
import { Portal, Toast, Modal, Button, Switch } from '@ant-design/react-native'
import { View, Platform, Image, Text, TouchableOpacity, StyleSheet, ScrollView, BackHandler, Dimensions } from 'react-native'
import get from 'lodash/get'
import cloneDeep from 'lodash/cloneDeep'
import { UPDATE_FORMDATA } from '../../../../containers/RunTime/Form/constants'
import PubSub from '../../../../utils/pubsub'

import NavigationService from '../../../../NavigationService'
import Helper from './Helper'

import detailstyle from './style'

const { width, height } = Dimensions.get('window')

const style = StyleSheet.create(detailstyle)

// eslint-disable-next-line import/no-unresolved
const prevIcon = require('../../../../images/icon-arrow-previous-default.png')

class AttendanceDetail extends React.PureComponent {
  static propTypes={
    showFields: PropTypes.arrayOf(PropTypes.string).isRequired,
  }
  constructor(props) {
    super(props)
    this.page = 1
    this.pagesize = 7
  }

  state ={
    modal2: false,
    modal3: false,
    periods: [],
    selectPeriod: '',
    columns: [],
    data: [],
    workbanci: [],
    checked: false,
    swipeOpen: false,
    weekChecked: true,
    selectRow: [],
    selectCol: [],
  }

  componentDidMount= async () => {
    this.tKey = Toast.loading('', 0)
    if (Platform.OS === 'ios') {
      Orientation.lockToLandscapeRight()
    } else {
      Orientation.lockToLandscapeLeft()
    }
    BackHandler.addEventListener('hardwareBackPress', this.handleBack)
    this.helper = new Helper(this.props, this)
    this.initListener = this.debounceListener()
    PubSub.on(UPDATE_FORMDATA, this.initListener)
    this.initListener()
  }
  componentWillUnmount=() => {
    Orientation.lockToPortrait()
    BackHandler.removeEventListener('hardwareBackPress', this.handleBack)
  }
  onPress=(row, key) => {
    if (this.state.checked) {
      if (this.helper.startClickEl) {
        this.end = `${row}_${key}`
        this.helper.changeSelectData(undefined, `${row}_${key}`)
      } else {
        this.end = `${row}_${key}`
        this.start = `${row}_${key}`
        this.helper.changeSelectData(`${row}_${key}`, undefined)
      }
    } else {
      this.editIndex = `${row}_${key}`
      this.setState({ modal2: true })
    }
  }
  onSwitchChange=() => {
    this.endbatch()
    this.setState({ checked: !this.state.checked })
  }
  onSwitchWeekChange=() => {
    this.setState({ weekChecked: !this.state.weekChecked }, () => {
      this.page = 1
      this.setState({ columns: this.getColumns() })
    })
  }
  setWorkbanci=(workbanci = this.helper.workMate) => {
    this.setState({ workbanci })
  }
  setPeriods=() => {
    this.setState({ periods: this.helper.periods })
  }
  getColumns=(page = this.page, weekChecked = this.state.weekChecked) => {
    const { showFields } = this.props
    const dataColumns = this.helper.Columns.filter(c => c.type === 'dayColumn')
    if (weekChecked) {
      const columns = []
      let oneWeek = []
      dataColumns.forEach((d, index) => {
        oneWeek.push(d)
        if (d.extra.m === '星期日' || index === dataColumns.length - 1) {
          columns.push(cloneDeep(oneWeek))
          oneWeek = []
        }
      })
      return [this.helper.Columns.find(c => c.key === showFields[0]), ...columns[page - 1]]
    }
    return [this.helper.Columns.find(c => c.key === showFields[0]), ...dataColumns.slice((page - 1) * 7, page * 7)]
  }
  getMaxPage = () => {
    if (this.state.weekChecked) {
      const columns = []
      let oneWeek = []
      const dataColumns = this.helper.Columns.filter(c => c.type === 'dayColumn')
      dataColumns.forEach((d, index) => {
        oneWeek.push(d)
        if (d.extra.m === '星期日' || index === dataColumns.length - 1) {
          columns.push(cloneDeep(oneWeek))
          oneWeek = []
        }
      })
      return columns.length
    }
    return Math.ceil((this.helper.Columns.filter(c => c.type === 'dayColumn').length - 1) / this.pagesize)
  }

  getSelectSelectStyle=(row, key) => {
    if (this.state.selectRow.findIndex(s => s === row) !== -1) {
      return { backgroundColor: 'rgb(174, 219, 255)' }
    }
    if (this.state.selectCol.findIndex(s => s === key) !== -1) {
      return { backgroundColor: 'rgb(174, 219, 255)' }
    }
    if (this.start && this.end && this.state.checked) {
      const [startRow, startKey] = this.start.split('_')
      const [endRow, endKey] = this.end.split('_')
      if (row >= startRow
        && row <= endRow
        && Number(key) >= Number(startKey)
        && Number(key) <= Number(endKey)) {
        return { backgroundColor: 'rgb(174, 219, 255)' }
      }
    }
    const cell = this.state.columns.find(col => col.key === key)
    if (cell && cell.extra && cell.extra.isholiday) {
      return { backgroundColor: '#ffffe8' }
    }
    return {}
  }
  selectValue=(w) => {
    this.closeModal()
    this.helper.updateValue(w, this.editIndex)
    this.forceUpdate()
  }
  debounceListener = () => debounce(async () => {
    this.helper.props = this.props
    await this.helper.initData()
    Portal.remove(this.tKey)
  }, 200)
  selectPeriods= async (p) => {
    this.closeModal3()
    this.page = 1
    const v = p.id
    if (this.helper.edited) {
      this.showConfirm(async () => {
        this.tKey = Toast.loading('', 0)
        this.startCopy = false
        this.setState({ selectPeriod: v, selectCol: [], selectRow: [] })
        await this.helper.changePeriod(v)
        this.forceUpdate()
        Portal.remove(this.tKey)
      })
    } else {
      this.tKey = Toast.loading('', 0)
      this.startCopy = false
      this.setState({ selectPeriod: v, selectCol: [], selectRow: [] })
      await this.helper.changePeriod(v)
      this.forceUpdate()
      Portal.remove(this.tKey)
    }
  }
  selectRow=(row) => {
    const index = this.state.selectRow.findIndex(s => s === row)
    if (index === -1) {
      this.setState({ selectRow: [...this.state.selectRow, row] })
    } else {
      this.setState({
        selectRow: index === 0 ? this.state.selectRow.slice(1) : [...this.state.selectRow.slice(0, index), this.state.selectRow.slice(index + 1)],
      })
    }
  }
  selectCol =(col) => {
    const index = this.state.selectCol.findIndex(s => s === col)
    if (index === -1) {
      this.setState({ selectCol: [...this.state.selectCol, col] })
    } else {
      this.setState({
        selectCol: index === 0 ? this.state.selectCol.slice(1) : [...this.state.selectCol.slice(0, index), this.state.selectCol.slice(index + 1)],
      })
    }
  }
  showConfirm=(call) => {
    Modal.alert('保存提示', '还有未保存的数据，确定要继续吗?', [
      {
        text: '取消',
        onPress: () => console.log('cancel'),
        style: 'cancel',
      },
      { text: '确定', onPress: call },
    ])
  }
  endbatch=() => {
    this.helper.changeSelectData('', '')
    this.end = ''
    this.start = ''
  }
  bancthSetValue =(value) => {
    if (this.state.checked) {
      this.helper.handleBatchEdit(value)
      this.endbatch()
      this.forceUpdate()
    }
    if (this.state.selectRow.length !== 0 || this.state.selectCol.length !== 0) {
      this.helper.handleBatchEditRowAndCol(this.state.selectRow, this.state.selectCol, value)
      this.forceUpdate()
    }
  }
  closeModal=() => {
    this.setState({ modal2: false })
  }
  closeModal3=() => {
    this.setState({ modal3: false })
  }
  handleBack=() => {
    if (this.helper.edited) {
      this.showConfirm(() => {
        Orientation.lockToPortrait()
        NavigationService.back()
      })
    } else {
      Orientation.lockToPortrait()
      NavigationService.back()
    }
    return true
  }
  submitForm= async () => {
    if (this.loading) {
      Toast.show('数据正在提交')
      return false 
    }
    this.loading = true
    this.tKey = Toast.loading('', 0)
    const res = await this.helper.submitData()
    if (!res) {
      Toast.show('没有需要保存的数据')
    } else {
      this.setState({ selectCol: [], selectRow: [] })
      await this.helper.changePeriod(this.state.selectPeriod)
      this.forceUpdate()
      Toast.success('保存成功')
    }
    this.loading = false
    Portal.remove(this.tKey)
  }
  forceUpdate=() => {
    const columns = this.getColumns()
    this.setState({
      selectPeriod: this.helper.selectPeriod.id,
      columns,
      data: this.helper.showDatas,
      periods: this.helper.periods,
    })
  }
  updateColumn=() => {
    this.setState({ columns: this.getColumns() })
  }

  openSwipe=(sectionID, rowId, direction) => {
    const maxPage = this.getMaxPage()
    if (direction === 'left') {
      if (this.page <= 1) {
        Toast.info('没有上一页啦！')
        this.setState({ swipeOpen: false })
      } else {
        this.page = this.page - 1
        this.setState({ columns: this.getColumns() }, () => {
          this.animatableRef.bounceInLeft(300).then(() => {

          })
        })
      }
    } else if (direction === 'right') {
      if (this.page >= maxPage) {
        Toast.info('没有下一页啦！')
        this.setState({ swipeOpen: false })
      } else {
        this.page = this.page + 1
        this.setState({ columns: this.getColumns() }, () => {
          this.setState({ swipeOpen: false })
          this.animatableRef.bounceInRight(300).then(() => {
          })
        })
      }
    }
  }
  renderAttHeader = (cell) => {
    if (cell.type === 'dayColumn') {
      return (
        <TouchableOpacity
          key={cell.key}
          onPress={() => { this.selectCol(cell.key) }}
        >
          <View key={cell.key} style={[style.headerItem, { backgroundColor: cell.extra.isholiday ? '#ffffe8' : undefined }]}>
            <Text style={{ fontSize: 14 }}>{cell.extra.d}</Text>
            <Text style={{ fontSize: 10, color: '#999' }}>{cell.extra.m}</Text>
          </View>
        </TouchableOpacity>
      )
    }
    return (
      <View key={cell.key} style={style.headerItem}>
        <Text style={{ fontSize: 16 }}>{cell.title}</Text>
      </View>
    )
  }
  renderRowUser=(data, row) => {
    const col = this.state.columns.find(c => c.key === this.props.showFields[0])
    return (
      <TouchableOpacity
        key={row}
        onPress={() => { this.selectRow(row) }}
      >
        <View style={[style.headerItem, { backgroundColor: '#e6f7ff' }]}><Text>{get(data, col.dataIndex)}</Text></View>
      </TouchableOpacity>
    )
  }
  renderRow=(data, row) => (
    <View key={row} style={style.dayrowDatas}>
      {this.state.columns.filter(c => c.type === 'dayColumn').map(col => (
        <TouchableOpacity
          key={row + col.key}
          onPress={() => { this.onPress(row, col.key) }}
        >
          <View style={[style.headerItem, this.getSelectSelectStyle(row, col.key)]}><Text>{get(data, col.dataIndex)}</Text></View>
        </TouchableOpacity>
      ))}
    </View>
  )
  renderHeader =() => (
    <View style={style.headerWarp}>
      <TouchableOpacity onPress={this.handleBack}>
        <View style={style.leftContainer}>
          <Image source={prevIcon} />
          <Text>返回</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity onPress={() => { this.setState({ modal3: true }) }} style={style.leftContainer}>
        <Text>选择期间</Text>
        <Image style={{transform: [{ scale: 0.6 }, { rotate: '-90deg' }] }} source={prevIcon} />
      </TouchableOpacity>
      <View style={style.leftContainer}>
        <Text>星期显示</Text>
        <Switch
          checked={this.state.weekChecked}
          onChange={this.onSwitchWeekChange}
        />
        <TouchableOpacity style={style.subBtn} onPress={this.submitForm} >
          <Text>提交</Text>
        </TouchableOpacity>
      </View>
    </View>)

    renderOperator=() => (
      <View style={style.opWarp}>
        <View style={style.opbanci}>
          <Text>批量录入</Text>
          <Switch
            checked={this.state.checked}
            onChange={this.onSwitchChange}
            trackColor="#e6f7ff"
          />
          {this.state.workbanci.map(w => (
            <TouchableOpacity
              style={{ marginLeft: 10 }}
              key={w}
              onPress={() => { this.bancthSetValue(w) }}
            >
              <Text style={{ color: '#1890ff' }}>{w}</Text>
            </TouchableOpacity>
      ))}
          <TouchableOpacity
            style={{ marginLeft: 10 }}
            onPress={() => { this.bancthSetValue('') }}
          >
            <Text style={{ color: '#999' }}>不排班</Text>
          </TouchableOpacity>
        </View>
        <View style={style.oppage}>
          <TouchableOpacity
            onPress={() => {
            this.openSwipe('', '', 'left')
          }}
            style={style.oppagebtn}
          >
            <Image style={{ transform: [{ scale: 0.6 }], marginTop: 2, opacity: 0.5 }} source={prevIcon} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
            this.openSwipe('', '', 'right')
          }}
            style={style.oppagebtn}
          >
            <Image style={{ transform: [{ scale: 0.6 }, { rotate: '180deg' }], marginTop: 2, opacity: 0.5 }} source={prevIcon} />
          </TouchableOpacity>
        </View>
      </View>
    )
  render = () => (
    <View style={style.warp}>
      {this.renderHeader()}
      <View style={style.warp}>
        {this.renderOperator()}
        <View style={style.headers}>
          {this.state.columns.map(this.renderAttHeader)}
        </View>
        <ScrollView style={style.contentWarp}>
          <View style={style.datas} >
            <View style={style.userDatasWarp}>
              {this.state.data.map(this.renderRowUser)}
            </View>
            <Animatable.View ref={(ref) => { this.animatableRef = ref }} style={style.dayDatasWarp}>
              <Swipeout
                right={[{ component: <Text>下一页</Text> }]}
                left={[{ component: <Text>上一页</Text> }]}
                onOpen={this.openSwipe}
                close={!this.state.swipeOpen}
              >
                <View
                  directionalLockEnabled
                  alwaysBounceHorizontal
                  showsHorizontalScrollIndicator
                  overScrollMode="always"
                  style={style.dayDatas}
                  onScrollEndDrag={this.endScroll}
                >
                  {this.state.data.map(this.renderRow)}
                </View>
              </Swipeout>
            </Animatable.View>
          </View>
        </ScrollView>
      </View>
      <Modal
        transparent
        visible={this.state.modal2}
        onClose={this.closeModal}
        animationType="slide-up"
        maskClosable
        title="选择班次"
      >
        <ScrollView style={{ height: width - 150, paddingHorizontal: 8 }}>
          {this.state.workbanci.map((w, index) => (
            <Button
              style={{ marginTop: 10 }}
              onPress={() => { this.selectValue(w) }}
              key={w}
              type="primary"
            >{`${w}  ${this.helper.workShowData[index]}`}
            </Button>)) }
          <Button
            style={{ marginTop: 10 }}
            onPress={() => { this.selectValue('') }}
            type="ghost"
          >不排班
          </Button>
        </ScrollView>
      </Modal>
      <Modal
        transparent
        visible={this.state.modal3}
        onClose={this.closeModal3}
        animationType="slide-up"
        maskClosable
        title="选择期间"
      >
        <ScrollView style={{ height: width - 150, paddingHorizontal: 8 }}>
          {this.state.periods.map(p => (
            <Button
              style={{ marginTop: 10 }}
              onPress={() => { this.selectPeriods(p) }}
              key={p.id}
              type="primary"
              disabled={p.id === this.state.selectPeriod}
            >{p.period}
            </Button>)) }
        </ScrollView>
      </Modal>
    </View>
  )
}

export default AttendanceDetail
