/* eslint-disable react/prop-types */
import PropTypes from 'prop-types';
import React from 'react';
import {View, Text, Image, TouchableWithoutFeedback} from 'react-native';
import {List} from 'nagz-mobile-lib';
import {Toast} from '@ant-design/react-native';
import {set, get, isEqual, forIn, debounce} from 'lodash-es';
import {GO_MODAL, GO_CREATE} from '../../../utils/jump';
import {getValue} from './store';
import {getUpdateValidationFn} from '../common';
import {isExpression} from '../../../utils/expression/transformExpression';
import DataTable from '../../../components/DataTable';
import EventEmitter from '../../../utils/eventEmitter';

const fn = () => {};

class Reference extends React.PureComponent {
  constructor(props) {
    super(props);
    this.refID = `ref_${Math.random().toString().slice(2)}`;
    this.realValue = '';
    this.loading = false;
  }

  state = {
    value: '',
    titleShowMore: false,
    showValue: '',
  };

  componentDidMount() {
    // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 50;
    const {
      refValue,
      reference,
      refDisplay,
      defaultValue,
      changeValue,
      fieldId,
      updateValidation,
      required,
    } = this.props;

    if (refDisplay !== this.state.showValue) {
      // eslint-disable-next-line react/no-did-mount-set-state
      this.setState({showValue: refDisplay, value: refDisplay});
    }
    this.updateValidation = getUpdateValidationFn(updateValidation, fieldId)(
      required,
      defaultValue,
    );
    if (defaultValue && reference.formId && this.realValue !== defaultValue) {
      if (!refValue) {
        changeValue({[fieldId]: {defaultValue}});
      }
    }
  }
  componentDidUpdate(prevProps) {
    const { refDisplay, defaultValue, required } = this.props;

    // 只在 refDisplay 真正改变时更新显示值
    if (prevProps.refDisplay !== refDisplay && refDisplay !== this.state.showValue) {
      this.setState({showValue: refDisplay, value: refDisplay});
    }

    // 只在 defaultValue 真正改变时更新验证
    if (`${prevProps.defaultValue}`.toLowerCase() !== `${defaultValue}`.toLowerCase()) {
      this.updateValidation = this.updateValidation(required, defaultValue);
    }
  }

  setValue = async (
    defaultValue,
    reference,
    init,
    filter = this.props.filter,
  ) => {
    const {appId, formId, srcFormId, dataId, changeValue, subFormIndex} =
      this.props;
    if (changeValue && !isExpression(defaultValue)) {
      const {data, value, realValue} = await getValue(
        appId,
        srcFormId || formId,
        dataId,
        defaultValue,
        reference,
        undefined,
        filter ? `( ${filter} )` : '',
      );
      if (!data && !value) {
        changeValue({[this.props.fieldId]: {defaultValue: ''}}, subFormIndex);
      }
      if (!init || isEqual(this.props.refValue, data)) {
        changeValue(
          {[this.props.fieldId]: {refValue: {...data}}},
          subFormIndex,
        );
      }
      if (defaultValue !== realValue && realValue && value) {
        changeValue(
          {[this.props.fieldId]: {defaultValue: value}},
          subFormIndex,
        );
      }
      this.setState({value, showValue: value});
    }
  };

  getDisplayColumns = reference => {
    const displayColumns = [...reference.columns];
    if (
      reference.currColumn &&
      displayColumns.indexOf(reference.currColumn) === -1
    ) {
      displayColumns.push(reference.currColumn);
    }
    if (
      reference.valueColumn &&
      displayColumns.indexOf(reference.valueColumn) === -1
    ) {
      displayColumns.push(reference.valueColumn);
    }
    return displayColumns;
  };
  execFilterChange = async filter => {
    const {appId, formId, srcFormId, dataId, defaultValue, reference} =
      this.props;
    if (!isExpression(defaultValue)) {
      const {data, value, realValue} = await getValue(
        appId,
        srcFormId || formId,
        dataId,
        defaultValue,
        reference,
        undefined,
        filter ? `( ${filter} )` : '',
      );
      if (data && value && realValue) {
        return {data, value, realValue};
      }
    }
    return false;
  };
  analyseSubformFields = fields => {
    const subformFieldMap = new Map();
    fields.map(item => {
      const filedIdSplit = item.selectField.split('.');
      if (filedIdSplit.length > 1) {
        if (!subformFieldMap.get(filedIdSplit[0])) {
          subformFieldMap.set(filedIdSplit[0], {});
        }
        subformFieldMap.get(filedIdSplit[0])[filedIdSplit[1]] =
          item.relativeField;
      }
    });
    return subformFieldMap;
  };
  selectRow = () => async rowData => {
    const {reference, changeValue, isPullData, matchFields} = this.props;
    const valueColumn = reference.valueColumn ? reference.valueColumn : 'id';
    if (changeValue) {
      if (isPullData) {
        if (reference.subformColumn) {
          const fieldsMap = this.analyseSubformFields(matchFields);
          fieldsMap.forEach((value, key) => {
            const subFormData = {};
            forIn(value, (oValue, oKey) => {
              set(subFormData, oKey, get(rowData, oValue));
            });
            EventEmitter.emit('referencePullData', key, [subFormData]);
          });
        }
        matchFields.map(item => {
          const fieldData = get(rowData, `${item.relativeField}.value`, '');
          changeValue([
            {
              [item.selectField]: {
                defaultValue: fieldData,
              },
            },
          ]);
        });
      }
      if (reference.subformColumn) {
        const data = rowData[reference.subformColumn];
        const value = data[valueColumn].value;
        await changeValue({[this.props.fieldId]: {defaultValue: value}});
      } else {
        const value = rowData[valueColumn].value;
        await changeValue({[this.props.fieldId]: {defaultValue: value}});
      }
    }
    this.realValue = rowData.id.value;
    if (reference.subformColumn) {
      this.realValue = rowData[reference.subformColumn].id.value;
    }
  };

  clear = () => {
    const {changeValue, subFormIndex} = this.props;
    if (changeValue) {
      changeValue({[this.props.fieldId]: {defaultValue: ''}}, subFormIndex);
      setTimeout(() => {
        changeValue({[this.props.fieldId]: {refValue: ''}}, subFormIndex);
      });
      this.setState({value: '', showValue: ''});
      this.realValue = '';
    }
  };
  showMoreTitle = () => {
    const {label} = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: true,
      });
    }
  };
  hideMoreTitle = () => {
    const {label} = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: false,
      });
    }
  };

  orderbyFun = orderby => {
    const isExp = isExpression(orderby);
    const orders = isExp ? isExp[1] : orderby;
    const datas = {};
    orders.split(',').forEach(data => {
      const value = data.split(' ');
      if (value[1] === 'desc' || value[1] === 'asc') datas[value[0]] = value[1];
    });
    return datas;
  };
  goCreatPage = () => {
    const {appId, reference} = this.props;
    GO_CREATE(() => {}, appId, reference.formId, 0, 'formName');
  };
  toDataTable = debounce(() => {
    // 连续点击的问题
    if (this.loading) {
      return;
    }
    this.loading = true;
    const {
      dataId,
      fieldId,
      formId,
      enabled,
      appId,
      addDefaultValue,
      reference,
      filter,
      orderby,
      operationarea,
      titlecolumn,
      searchcolumn,
      isView,
      inputBlur,
      securityscope,
    } = this.props;
    const disabled = isView ? false : !enabled;
    const data = isExpression(filter)
      ? ''
      : filter.replace(/ and $/, '').replace(/ or $/, '');
    const displayColumns = this.getDisplayColumns(reference);
    const order = this.orderbyFun(orderby);
    const blurCallback = () => {
      if (disabled) {
        Toast.info('不可更改', 1);
      } else if (!this.props.reference.formId) {
        Toast.info('未引用表单', 1);
      } else {
        const params = {
          appId,
          formId: reference.formId,
          newFormId: formId,
          dataId,
          fieldId,
          operationarea,
          titlecolumn,
          searchcolumn,
          displayColumns,
          viewvisiblecolumn: reference.columns,
          addDefaultValue,
          orders: order,
          query: data,
          isSubForm: false,
          selectRow: this.selectRow(false),
          goCreatPage: this.goCreatPage,
          extraParams: securityscope
            ? `srcFormId=${formId}&srcFormFieldId=${fieldId}`
            : '',
        };
        GO_MODAL({
          children: !isExpression(filter) && (
            <DataTable
              {...params}
              pagination={{}}
              colWidth
              showOperators
              showAction
              listType="reference"
            />
          ),
          animationIn: 'slideInRight',
          animationOut: 'slideOutRight',
        });
      }
    };
    if (inputBlur) {
      inputBlur(blurCallback);
    } else {
      blurCallback();
    }
    this.loading = false;
  }, 500);

  render() {
    const {titleShowMore} = this.state;
    const {label, isRunTime, visible, hidelabel} = this.props;
    const inputStyle = {
      fontSize: 14,
      width: 192,
      marginLeft: 12,
      color: this.state.value ? '#1B1B1B' : '#BCBCBC',
      textAlign: 'right',
    };
    if (visible === false) {
      return <View />;
    }
    return (
      <View>
        <List.Item
          thumb={
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 4,
                opacity: this.props.required ? 1 : 0,
              }}>
              <Image
                source={require('../../../images/icon-asterisk-default.png')}
              />
            </View>
          }
          arrow={
            <View
              style={{
                width: 14,
                height: 14,
                marginLeft: 7,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                source={require('../../../images/icon-arrow-right-small-default.png')}
              />
            </View>
          }
          hideBottomLine
          style={{paddingLeft: 6, paddingRight: 0}}
          contentStyle={{paddingRight: 13}}
          extra={
            <Text
              suppressHighlighting
              style={inputStyle}
              onPress={this.toDataTable}>
              {this.state.showValue ? this.state.showValue : '请选择'}
            </Text>
          }>
          {hidelabel && isRunTime ? (
            ''
          ) : (
            <TouchableWithoutFeedback
              onPressIn={this.showMoreTitle}
              onPressOut={this.hideMoreTitle}>
              <View style={{flexDirection: 'row'}}>
                <Text
                  style={{fontSize: 14, fontWeight: 'bold', color: '#1B1B1B'}}
                  numberOfLines={1}>
                  {String(label)}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          )}
        </List.Item>
        {titleShowMore ? (
          <View
            style={{
              height: 44,
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              backgroundColor: '#fff',
              position: 'absolute',
              top: 0,
              overflow: 'hidden',
            }}>
            <View
              style={{
                width: 20,
                paddingLeft: 2,
                alignItems: 'center',
                justifyContent: 'center',
                opacity: this.props.required ? 1 : 0,
              }}>
              <Image
                source={require('../../../images/icon-asterisk-default.png')}
              />
            </View>
            <View style={{flexDirection: 'row'}}>
              <Text
                style={{fontSize: 14, fontWeight: 'bold', color: '#1B1B1B'}}
                numberOfLines={1}>
                {String(label)}
              </Text>
            </View>
          </View>
        ) : null}
      </View>
    );
  }
}

Reference.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  desc: PropTypes.string,
  required: PropTypes.bool,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  value: PropTypes.any, // eslint-disable-line
  validate: PropTypes.object, // eslint-disable-line
  $$isValidation: PropTypes.bool,
  reference: PropTypes.object, // eslint-disable-line
  appId: PropTypes.string.isRequired,
  inputBlur: PropTypes.func,
};

Reference.defaultProps = {
  label: '',
  defaultValue: {},
  desc: '',
  required: false,
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  value: '',
  validate: {},
  $$isValidation: false,
  enabled: true,
  orderby: '',
  filter: '',
};
export default Reference;
