import PropTypes from 'prop-types'
import React from 'react'
import { View, ActivityIndicator, TouchableOpacity } from 'react-native'
import get from 'lodash/get'
import isArray from 'lodash/isArray'
import { Toast, Carousel } from '@ant-design/react-native'
import { SimpleLine } from 'nagz-mobile-lib'
import Image from 'react-native-image-progress'
import load, { getFiles } from './store'
import { isRefComponent } from '../../constants'
import { GO_REFPROPERTYMODAL } from '../../../utils/jump'
import { isExpression } from '../../../utils/expression/transformExpression'
import { FormContext } from '../../../utils/reactContext'

const fn = () => {}

class RefProperty extends React.PureComponent {
  state ={
    value: this.props.defaultValue.display,
  }
  static contextType = FormContext;

  componentWillMount = async () => {
    const {
      isRunTime, defaultValue, isSubForm, joinfieldvalue,
      displaytype, refPropDatas = {}, subFormIndex, fieldId, changeValue,
    } = this.props
    if (isRunTime && !defaultValue.display) {
      if (isSubForm && defaultValue.display) {
        return
      }
      this.loadData(joinfieldvalue)
    } else if (isRunTime && displaytype === 'Picture') {
      this.loadData(joinfieldvalue)
    }
    if (isRunTime && displaytype !== 'Picture' && refPropDatas && refPropDatas.display) {
      changeValue({ [fieldId]: { defaultValue: refPropDatas } }, subFormIndex)
      this.setState({
        value: refPropDatas.display,
      })
    }
  }
  componentDidMount() {
    // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  async componentDidUpdate(prevProps) {
    const { joinfieldvalue, displaytype, refPropDatas, isRunTime, changeValue, fieldId, subFormIndex } = this.props;

    // 只在 joinfieldvalue 真正改变时加载数据
    if (isRunTime && prevProps.joinfieldvalue !== joinfieldvalue && joinfieldvalue !== undefined) {
      await this.loadData(joinfieldvalue);
    }

    // 只在 refPropDatas 真正改变时更新显示值
    if (
      displaytype !== 'Picture' &&
      prevProps.refPropDatas !== refPropDatas &&
      (!prevProps.refPropDatas || !refPropDatas || refPropDatas.display !== prevProps.refPropDatas.display)
    ) {
      changeValue({ [fieldId]: { defaultValue: refPropDatas } }, subFormIndex);
      this.setState({
        value: refPropDatas ? refPropDatas.display : '',
      });
    }
  }

  loadData = async (joinfieldvalue) => {
    const {
      appId, formId, source, joinfield, joinformvaluefield, joinformdisplayfield, changeValue, subFormIndex, displaytype,
    } = this.props
    if (!appId || !source || !joinformvaluefield || !joinformdisplayfield || isExpression(joinfieldvalue)) return
    let data
    // 直接清除
    if (joinfieldvalue) {
      // 当为多图的时候，要根据获取附件的接口来取得value
      if (displaytype === 'Picture') {
        let dataId
        const joinfieldCmp = this.context.fields.find(f => f.id === joinfield)
        if (joinformvaluefield === 'id' && isRefComponent(joinfieldCmp)) {
          if (!joinfieldCmp.properties.reference.valueColumn) {
            dataId = joinfieldvalue
          }
        } else {
          const item = await load(appId, source, joinformvaluefield, joinfieldvalue, joinformdisplayfield, formId)
          dataId = item.id && item.id.value
        }
        const reusult = await getFiles(appId, source, dataId, joinformdisplayfield)

        const resDisplay = []
        const resValue = []
        if (reusult.records && reusult.records.length) {
          reusult.records.forEach((item) => { resDisplay.push(item.ossUrl); resValue.push(item.id) })
          data = {
            [joinformdisplayfield]: {
              display: resDisplay,
              value: resValue,
            },
          }
        }
        if (data !== undefined) {
          const defaultValue = get(data, joinformdisplayfield)
          const value = defaultValue.display
          changeValue({ [this.props.fieldId]: { defaultValue } }, subFormIndex)
          this.setState({
            value,
          })
        } else {
          const defaultValue = {
            display: '',
            value: '',
          }
          changeValue({ [this.props.fieldId]: { defaultValue } }, subFormIndex)
          this.setState({
            value: '',
          })
        }
      }
    }
  }

  toastInfo=() => {
    Toast.info('不可更改', 1)
  }
  showImageModal=(index) => {
    const { value } = this.state
    GO_REFPROPERTYMODAL({ value, index })
  }
  renderChildCmp=() => {
    const {
      isRunTime, enabled, hidelabel, required, label, thousands, displaytype,
    } = this.props
    let { value } = this.state
    if (displaytype === 'SinglePicture') {
      return (
        [<SimpleLine
          label={hidelabel && isRunTime ? '' : label}
          placeholder={value ? '' : '无数据'}
          isRequire={required}
          onPress={null}
          editable={enabled}
        />, value ? <TouchableOpacity onPress={this.showImageModal}><Image
          indicator={ActivityIndicator}
          indicatorProps={{
            color: '#41454B',
          }}
          style={{ height: 250, flex: 1 }}
          source={{ uri: value }}
        />
                    </TouchableOpacity> : null])
    } else if (displaytype === 'Picture') {
      return (
        [<SimpleLine
          label={hidelabel && isRunTime ? '' : label}
          placeholder={value ? '' : '无数据'}
          isRequire={required}
          onPress={null}
          editable={enabled}
        />, isArray(value) ? <Carousel>
          {value.map((item, index) => (<View key={index}><TouchableOpacity onPress={() => this.showImageModal(index)}>
            <Image
              indicator={ActivityIndicator}
              indicatorProps={{
                color: '#41454B',
              }}
              style={{ height: 250, flex: 1 }}
              source={{ uri: item }}
            />
          </TouchableOpacity>
          </View>))}
                             </Carousel> : null]
      )
    } else if (displaytype === 'Number' && thousands) {
      const valueSplit = value.split('.')
      const reg = /(?=(?!(\b))(\d{3})+$)/g
      value = valueSplit[0].replace(reg, ',') + (valueSplit[1] ? `.${valueSplit[1]}` : '')
    }
    return (<SimpleLine
      label={hidelabel && isRunTime ? '' : label}
      placeholder="无数据"
      isRequire={required}
      value={value}
      onPress={!enabled ? this.toastInfo : null}
      editable={enabled}
    />)
  }
  render() {
    const {
      isRunTime, visible,
    } = this.props
    if (isRunTime && visible === false) {
      return <View />
    }

    return (
      <View>
        {this.renderChildCmp()}
      </View>
    )
  }
}

RefProperty.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  formId: PropTypes.string,
  joinfieldvalue: PropTypes.any, // eslint-disable-line
  isRunTime: PropTypes.bool,
  isSubForm: PropTypes.bool,
  required: PropTypes.bool,
  visible: PropTypes.bool,
  appId: PropTypes.string,
  fieldId: PropTypes.string,
  source: PropTypes.string,
  joinfield: PropTypes.string,
  joinformvaluefield: PropTypes.string,
  joinformdisplayfield: PropTypes.string,
  displaytype: PropTypes.string,
  thousands: PropTypes.bool,
  enabled: PropTypes.bool,
  hidelabel: PropTypes.bool,
  changeValue: PropTypes.func,
  subFormIndex: PropTypes.number,
  refPropDatas: PropTypes.object,// eslint-disable-line
}

RefProperty.defaultProps = {
  label: '',
  defaultValue: {},
  isRunTime: false,
  fieldId: '',
  enabled: true,
  changeValue: fn,
}

export default RefProperty
