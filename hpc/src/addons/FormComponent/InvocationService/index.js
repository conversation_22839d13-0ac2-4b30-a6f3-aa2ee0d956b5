import PropTypes from 'prop-types'
import React from 'react'
import { DeviceEventEmitter } from 'react-native'
import { Portal, Toast } from '@ant-design/react-native'
import ImageCropPicker from 'react-native-image-crop-picker'
import { ActionSheet } from 'nagz-mobile-lib'
import { getServiceInfo, getFileInfo } from './store'
import {
  attachmentSetFiles,
  clickInvocationService,
} from '../../../utils/event/DeviceEventEmitter'

const ratios = {
  width: {
    2: 400,
    13: 780,
    17: 770,
  },
  height: {
    2: 260,
    13: 520,
    17: 480,
  },
}

const wrapProps = {
  onTouchStart: e => e.preventDefault(),
}

class InvocationService extends React.PureComponent {
  state = {
    roleinfo: this.props.defaultValue,
  }

  componentDidMount() {
    const { isRunTime, enabled } = this.props
    this.disabled = !isRunTime ? true : !enabled
    this.listener = DeviceEventEmitter.addListener(clickInvocationService, (invocationServiceId) => {
      if (this.props.fieldId === invocationServiceId) {
        if (this.disabled) {
          Toast.info('不可更改', 1)
        } else {
          this.showActionSheet()
        }
      }
    })
    // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 0
  }

  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.state.roleinfo !== defaultValue) {
      this.setState({
        roleinfo: defaultValue,
      })
    }
  }

  componentWillUnmount() {
    this.listener.remove()
  }

  changeValue = () => {
    const { changeValue } = this.props
    if (changeValue && this.state.roleinfo !== this.props.defaultValue) {
      changeValue({ [this.props.fieldId]: { defaultValue: this.state.roleinfo } })
    }
  }
  uploadReques = async (file) => {
    if (!this.disabled) {
      const tKey = Toast.loading('证件识别中', 0)
      const { serviceType, fileid } = this.props
      const image = await getFileInfo(file.path)
      const result = await getServiceInfo(serviceType, image)
      Portal.remove(tKey)
      if (result) {
        // 如果使用image-crop-picker 则file.path.split
        // const filenamearray = file.path.split('/')
        this.setState({
          roleinfo: result,
        }, () => {
          if (fileid) {
            DeviceEventEmitter.emit(attachmentSetFiles, fileid, image)
          }
          this.changeValue()
        })
      }
    }
  }

  openImageLibrary = () => {
    const type = this.props.serviceType
    ImageCropPicker.openPicker({
      width: ratios.width[type],
      height: ratios.height[type],
      cropping: true,
      freeStyleCropEnabled: true,
      cropperChooseText: '选择',
      cropperCancelText: '取消',
    }).then((image) => {
      if (image.path) {
        this.uploadReques(image)
      }
    }).catch((error) => { // 执行失败
      console.error(error)
    })
  }

  openImage = () => {
    const type = this.props.serviceType
    ImageCropPicker.openCamera({
      width: ratios.width[type],
      height: ratios.height[type],
      cropping: true,
      freeStyleCropEnabled: true,
      cropperChooseText: '选择',
      cropperCancelText: '取消',
    }).then((image) => {
      if (image.path) {
        this.uploadReques(image)
      }
    })
  }

  showActionSheet = () => {
    const BUTTONS = ['拍照', '从相册选择文件', '取消']
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        title: '请选择',
        'data-seed': 'logId',
        wrapProps,
      },
      (buttonIndex) => {
        if (buttonIndex === 0) { // 调用拍照
          this.openImage()
        } else if (buttonIndex === 1) {
          this.openImageLibrary()
        }
      },
    )
  }

  render() {
    return null
  }
}

InvocationService.propTypes = {
  defaultValue: PropTypes.object,
  serviceType: PropTypes.string,
  fileid: PropTypes.any,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  enabled: PropTypes.bool,
}

InvocationService.defaultProps = {
  defaultValue: {},
  serviceType: 2,
  changeValue: () => { },
  isRunTime: false,
  fieldId: '',
  enabled: true,
}

export default InvocationService
