import { Toast } from '@ant-design/react-native'
import object from 'lodash/object'
import RNFS from 'react-native-fs'
import { defaultInfoValue, passportArgumentsMap } from './constants'
import { getToken } from '../../../utils/storage'
import { envParm, deviceType } from '../../../config/sysPara'

const checkStatus = (response) => {
  if (response.status === 403) {
    return Promise.reject({
      status: 1001,
    })
  }
  return response
}

export const getFileInfo = async (path, fdir) => {
  let tempfdir = fdir
  if (!fdir) {
    tempfdir = deviceType === 2 ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker` : `${RNFS.PicturesDirectoryPath}`
  }
  let image
  const result = await RNFS.readDir(tempfdir)
  const str = path.substring(path.lastIndexOf('/') + 1, path.length)
  // 遍历找到文件
  result.forEach((v) => {
    if (v.name === str) {
      image = v
    }
  })
  return image
}

export const getServiceInfo = async (type, file) => {
  const authToken = await getToken()
  const url = `${envParm.URLS}/captcha/image/idpsss?type=${type}`
  const formData = new FormData()
  formData.append('file', { uri: `file://${file.path}`, type: 'multipart/form-data', name: file.name })
  const options = {
    method: 'POST',
    headers: {
      authToken,
    },
    body: formData,
  }
  try {
    let data = await fetch(url, options)
    data = await data.json()
    if (data.errorCode === '0') {
      let result = data.data
      // 当证件类型为护照时
      if (type === '13') {
        result = object.mapKeys(result, (value, key) => passportArgumentsMap[key])
      }
      return { ...defaultInfoValue, ...result }
    }
    Toast.fail(data.errorMsg, 1)
    return false
  } catch (error) {
    return null
  }
}
export default getServiceInfo
