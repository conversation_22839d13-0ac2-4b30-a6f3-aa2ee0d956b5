import { get } from '../../request'

export const isTrue = x => x && (x === true || x.toString().toLowerCase() === 'true' || x === 1 || x === '1')

export const isEmpty = v => v === null || v === undefined || (typeof v !== 'number' && v.length === 0)

export const getValidateStatus = (required, value, isEmptyFn = isEmpty, $$isValidation) => $$isValidation && isTrue(required) && isEmptyFn(value) ? 'error' : ''

export const getUpdateValidationFn = (updateValidation = () => {}, fieldId, lastValue, isEmptyFn = isEmpty) => (required, value) => {
  const requiredValue = isEmptyFn(value)
  if (required) {
    if (requiredValue !== lastValue) {
      updateValidation({
        [fieldId]: {
          required: requiredValue,
        },
      })
    }
  }
  return getUpdateValidationFn(updateValidation, fieldId, requiredValue, isEmptyFn)
}

export const getFormatValidateStatus = (format, value, $$isValidation) => (value && format && $$isValidation ? new RegExp(format).test(value) : true) ? '' : 'error'

export const getValidateFormatFn = (updateValidation = () => {}, fieldId, lastValue) => (format, value) => {
  const validateValue = !(value && format ? new RegExp(format).test(value) : true)
  if (validateValue !== lastValue) {
    updateValidation({
      [fieldId]: {
        format: validateValue,
      },
    })
  }
  return getValidateFormatFn(updateValidation, fieldId, validateValue)
}

/*
  return:
    true：props内容没有改变
    false: props内容有改变
*/
export function checkShouldUpdate(preProps, nextProps) {
  if (preProps.label === nextProps.label && preProps.required === nextProps.required
    && preProps.enabled === nextProps.enabled && preProps.$$isValidation === nextProps.$$isValidation && preProps.hidelabel === nextProps.hidelabel) {
    return true
  }
  return false
}

export const validformant = [
  {
    value: '',
    title: '',
    key: '',
  },
  {
    value: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
    title: '电子邮件',
    key: '2',
  },
  {
    value: '^(1)\\d{10}$',
    title: '手机号',
    key: '3',
  },
  {
    value: '^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*([\\?&]\\w+=\\w*)*$',
    title: '网址',
    key: '4',
  },
  {
    value: '^(^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$)|(^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx])$)$',
    title: '身份证',
    key: '5',
  },
]
export const getKeybordtypeByValidformant = (validvalue) => {
  const v = validformant.find(va => va.key === validvalue) || { key: '' }
  switch (v.key) {
    case '2':
      return 'email-address'
    case '3':
      return 'phone-pad'
    case '4':
    case '5':
    default:
      return 'default'
  }
}

export async function getFormFields(appId, formId) { // runtime
  const result = await get(`/apps/${appId}/forms/${formId}/datas/new/0?$force=false`)()
  if (result.errorCode === '0') {
    return result.data.config.form.fields
  }
  return undefined
}

// 防抖的 changeValue 函数，避免频繁的状态更新
export function createDebouncedChangeValue(changeValue, delay = 100) {
  let timeoutId = null;
  let pendingUpdates = {};

  return (updates) => {
    // 合并待处理的更新
    Object.assign(pendingUpdates, updates);

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      if (changeValue && Object.keys(pendingUpdates).length > 0) {
        changeValue(pendingUpdates);
        pendingUpdates = {};
      }
      timeoutId = null;
    }, delay);
  };
}

// 深度比较函数，用于避免不必要的更新
export function deepEqual(obj1, obj2) {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return obj1 === obj2;

  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}
