import PropTypes from 'prop-types'
import React, { PureComponent } from 'react'
import { RNCamera } from 'react-native-camera'
import Sound from 'react-native-sound'
import { get } from 'lodash-es'
import { StyleSheet, Text, Image, ImageBackground, TouchableOpacity, View, Animated, Easing, Dimensions, Platform } from 'react-native'
import { getWithCache } from '../../../request'
import { deviceType } from '../../../config/sysPara'
import NavigationService from '../../../NavigationService'
import AnimateNum from '../../../components/AnimatedNum'

import { CAMPERMISSIONS_CAMERAERA, CAMPERMISSIONS_RECORD_AUDIO } from '../../../utils/AppPermissions'

const audio = require('../../../audio/scantip_ios.aiff')
const anroidaudio = require('../../../audio/scantip_android.mp3')

const { width, height } = Dimensions.get('window')

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  preview: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  capture: {
    flex: 0,
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 15,
    paddingHorizontal: 20,
    alignSelf: 'center',
    margin: 20,
  },
  closeWrap: {
    position: 'absolute',
    top: 31,
    left: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 38,
    height: 38,
    borderRadius: 38,
    backgroundColor: 'rgba(255,255,255,.5)',
  },
  okBtn: {
    position: 'absolute',
    top: 50,
    right: 35,
  },
  bottom: {
    position: 'absolute',
    bottom: 0,
    width,
    height: 60,
    backgroundColor: '#f5f5f5',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentWrap: {
    position: 'absolute',
    flex: 1,
    width,
    bottom: 0,
  },
  contentText: {
    width: width - 42,
    fontSize: 14,
    color: '#c1c1c1',
    marginBottom: 4,
    height: 20,
  },
  firstText: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 14,
  },
  content: {
    position: 'relative',
    paddingHorizontal: 21,
    paddingTop: 50,
    paddingBottom: 13,
    width,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  buttonWrap: {
    width: 94,
    height: 42,
    borderRadius: 21,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(215 ,173, 103 , 07)',
  },
  countWrap: {
    width: width - 42,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#fff',
    position: 'absolute',
    top: -28,
    left: 21,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
  },
})

const qrbg = require('../../../images/qran.png')

const getValue = async (appId, formId, filter, selectColumns) => {
  const res = await getWithCache(`/apps/${appId}/forms/${formId}/datas?$limit=1&$offset=0&$title=false&$operations=false&${filter}&$select=${selectColumns}`)()
  console.log(res, { appId, formId, filter, selectColumns })
  if (res.errorCode !== '0') {
    return []
  }
  return res.data.items
}

class QRModal extends PureComponent {
  static propTypes={
    values: PropTypes.arrayOf(PropTypes.string),
    updateValue: PropTypes.func.isRequired,
    scanLength: PropTypes.number.isRequired,
    scanReg: PropTypes.string.isRequired,
    single: PropTypes.bool,
  }
  static defaultProps={
    values: [],
    single: false,
  }
  constructor(props) {
    super(props)
    this.state = {
      moveAnim: new Animated.Value(0),
      count: 0,
      showData: [],
      showScanDot: false,
      hasError: false,
      errorMsg: '该数据已扫描',
    }
    this.isScanIng = false
    this.values = []
    this.dotorigin = {}
  }

  componentDidMount= async () => {
    if (deviceType === 1) { // Android 获取权限
      const permc = await CAMPERMISSIONS_CAMERAERA()
      const perma = await CAMPERMISSIONS_RECORD_AUDIO()
      if (!permc || !perma) {
        this.close()
        return
      }
    }
    this.values = this.props.values || []
    this.startAnimation()
    if (!this.props.single) {
      this.setState({ showData: this.buildShowData() })
    }
  }
  buildShowData =(data = {}) => {
    const { single, scanProps } = this.props
    if (single) return []
    const { scanColumn, scanShowfield } = scanProps
    if (scanShowfield && scanShowfield.length !== 0) {
      return scanShowfield.map(fid => get(data, `${fid}.display`) || '')
    }
    return ['']
  }
  startAnimation = () => {
    this.state.moveAnim.setValue(0)
    Animated.timing(
      this.state.moveAnim,
      {
        toValue: height - 250,
        duration: 2500,
        easing: Easing.ease,
      },
    ).start(() => { this.startAnimation() })
  };
  close=() => { NavigationService.back() }
  handOk=() => {
    this.props.updateValue(this.values)
    this.close()
  }
  getPos=(bounds) => {
    const { origin, size } = bounds
    if (deviceType === 1) {
      // console.log(width, bounds.width, height, bounds.height, origin)
      return { x: -20, y: -20 }
    }
    return {
      x: Math.floor(Number(origin.x) + Number(size.width) / 2),
      y: Math.floor(Number(origin.y) + Number(size.height) / 2),
    }
  }
  loadScanData = async (value) => {
    const { scanColumn, scanShowfield, scanSource, scanjoinformvaluefield } = this.props.scanProps
    if (scanShowfield && scanSource && scanjoinformvaluefield) {
      const filter = `$filter=${scanjoinformvaluefield} eq '${value}'`
      const res = await getValue(this.props.appId, scanSource, filter, scanShowfield.join(','))
      const data = this.buildShowData(res.length === 0 ? {} : res[0])
      this.setState({ showData: data })
    } else {
      this.setState({ showData: [value] })
    }
  }
  checkError=(value) => {
    const { scanLength, scanReg } = this.props
    if (scanLength && !(value && value.length <= scanLength)) {
      return false
    }
    if (scanReg && !(value && new RegExp(scanReg).test(value))) {
      return false
    }
    return true
  }
  onBarCodeRead = (v) => {
    if (!this.isScanIng) {
      const { updateValue, single } = this.props
      this.isScanIng = true
      this.dotorigin = this.getPos(v.bounds)
      const check = this.checkError(v.data)
      if (check) {
        if (!single && this.values && this.values.indexOf(v.data) === -1) {
          this.values = [...this.values, v.data]
          this.setState({ count: this.state.count + 1, showScanDot: true, hasError: false })
          this.loadScanData(v.data)
        } else if (!single && this.values && this.values.indexOf(v.data) !== -1) {
          this.setState({ hasError: true, showScanDot: true, errorMsg: '该数据已扫描' })
        } else {
          this.setState({ showScanDot: true })
        }
      } else {
        this.setState({ hasError: true, showScanDot: true, errorMsg: '该数据格式不正确' })
      }

      const whoosh = new Sound(deviceType === 1 ? anroidaudio : audio, () => {
        whoosh.play(() => {
          if (single && updateValue && check) {
            updateValue(v.data)
            this.close()
          } else {
            this.isScanIng = false
          }
          this.setState({ showScanDot: false })
          this.dotorigin = {}
        })
      })
    }
  }

  render=() => (
    <View style={styles.container}>
      <RNCamera
        ref={(ref) => {
            this.camera = ref
          }}
        style={styles.preview}
        type={RNCamera.Constants.Type.back}
        autoFocus={RNCamera.Constants.AutoFocus.on}
          // flashMode={RNCamera.Constants.FlashMode.on}
        onBarCodeRead={this.onBarCodeRead}
      />
      <View style={{ flex: 1, height, width, position: 'absolute', borderColor: '#000', borderWidth: 1 }}>
        {this.state.showScanDot && Platform.OS === 'ios' &&
          <View style={{
            position: 'absolute',
            left: this.dotorigin.x || 0,
            top: this.dotorigin.y || 0,
            width: 30,
            height: 30,
            borderRadius: 30,
            backgroundColor: '#1690ff',
            borderWidth: 2,
            borderColor: '#fff' }}
          />
        }
        <Animated.Image
          source={qrbg}
          style={{
              marginLeft: 15,
              width: width - 30,
              height: (width - 30) / 3,
              transform: [{ translateY: this.state.moveAnim }],
            }}
        />
        <TouchableOpacity style={styles.closeWrap} onPress={this.close}>
          <ImageBackground style={{ width: 38, height: 38 }} source={require('../../../images/close_icon.png')} />
        </TouchableOpacity>
        <View style={styles.contentWrap}>
          <View style={styles.content}>
            {!this.state.hasError &&
              <View style={styles.countWrap}>
                <Image style={{ width: 38, height: 38, marginLeft: 25, marginRight: 10 }} source={require('../../../images/success_icon.png')} />
                <Text style={{ fontSize: 18 }}>成功共扫描</Text>
                <AnimateNum num={this.state.count} />
                <Text style={{ fontSize: 18 }}>条数据</Text>
              </View>}
            {
              this.state.hasError &&
              <View style={styles.countWrap}>
                <Image style={{ width: 38, height: 38, marginLeft: 25, marginRight: 10 }} source={require('../../../images/error_icon.png')} />
                <Text style={{ fontSize: 18 }}>{this.state.errorMsg}</Text>
              </View>
            }
            {this.state.showData.map((t, i) => (
              <Text ellipsizeMode="tail" style={[styles.contentText, i === 0 ? styles.firstText : {}]}>{t}</Text>
            ))}
            <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: width - 42, alignItems: 'center' }}>
              <Text style={{ color: '#fff', fontSize: 16 }}>{`当前共扫描数据: ${this.state.count}` }</Text>
              {!this.props.single &&
                <TouchableOpacity style={styles.buttonWrap} onPress={this.handOk}>
                  <Text style={{ fontSize: 18, color: '#fff' }}>结束扫描</Text>
                </TouchableOpacity>
                }
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}

export default QRModal

