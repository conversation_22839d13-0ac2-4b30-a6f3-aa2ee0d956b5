import PropTypes from 'prop-types'
import React from 'react'
import { Carousel } from '@ant-design/react-native'
import { View, Image, TouchableOpacity, Dimensions } from 'react-native'
import _get from 'lodash/get'

import { getData, getPics } from './store'
import { FormContext } from '../../../utils/reactContext'

const { width } = Dimensions.get('window')

class PicList extends React.PureComponent {
  state={ images: [] }
  static contextType = FormContext

  componentDidMount=() => {
    const {
      appId, source, linkfield, picfield, ignoreEmpty, filter, orderby,
    } = this.props

    this.initData({
      appId, source, linkfield, picfield, ignoreEmpty, filter, orderby,
    })
  }
  setIndex=(index) => { this.selectIndex = index }
  selectIndex = 0
  initData= async ({
    appId, source, linkfield, picfield, ignoreEmpty, filter, orderby,
  }) => {
    this.items = await getData({
      appId,
      formId: source,
      select: `id,${linkfield},${picfield}`,
      ignoreEmpty,
      filter,
      orderby,
    })
    if (this.items.length !== 0) {
      const Ids = this.items.map(item => item.id.value)
      const pics = await getPics(appId, source, Ids, picfield, this.items.length === 1)
      const images = this.items.reduce((res, item) => {
        const id = item.id.value
        if (pics[id] && pics[id].length !== 0) {
          const showPic = pics[id].find(pic => pic.version === '1') || pics[id][0]
          return [...res, { id, pic: showPic.ossUrl }]
        }
        return res
      }, [])
      this.setState({ images })
    }
  }
  click=() => {
    // const id = this.state.images[this.selectIndex].id
    const id = _get(this.state.images, `[${this.selectIndex}].id`)
    const link = this.items.find(item => item.id.value === id)
    if (link && link[this.props.linkfield] && link[this.props.linkfield].value) {
      const [, linkurl] = link[this.props.linkfield].value.split('/apps/runtime/')
      if (linkurl) {
        const match = linkurl.match(/(\d+)\/form\/(\d+)\/(\d)\/(\w+)(\/)?(\d*)/)
        if (match && (this.props.formId !== match[2] || this.props.dataId !== match[6])) {
          this.context.goPage({
            appId: match[1], formId: match[2], operator: match[3], dataId: match[6],
          })
        }
      }
    }
  }
  imgaeRender =img => (
    <TouchableOpacity onPress={this.click} key={img.id} >
      <Image
        style={{ width: (width - 20), height: (width - 20) / (this.props.cropSize || 1) }}
        resizeMode="contain"
        source={{ uri: img.pic }}
        key={img.id}
      />
    </TouchableOpacity>
  )

  render =() => {
    const { images } = this.state
    const containerstyle = { width: width - 20, height: (width - 20) / (this.props.cropSize || 1), backgroundColor: '#fff', marginLeft: 10, borderRadius: 8, overflow: 'hidden' }
    const dotStyle = {
      width: images.length > 11 ? ((width * 0.8) / images.length) - 10 : 20,
      height: 2,
      backgroundColor: '#FFFFFF',
      borderRadius: 0,
      opacity: 0.6 }
    return (
      <View style={containerstyle} >
        {images.length !== 1 ?
          <Carousel selectedIndex={0} infinite autoplay pagination={undefined} afterChange={this.setIndex} dotActiveStyle={{ backgroundColor: '#17A9FF' }} dotStyle={dotStyle}>
            { images.map(img => this.imgaeRender(img))}
          </Carousel> :
          this.imgaeRender(images[0])
          }
      </View>
    )
  }
}
PicList.propTypes = {
  appId: PropTypes.string.isRequired,
  source: PropTypes.string.isRequired,
  formId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  linkfield: PropTypes.string.isRequired,
  picfield: PropTypes.string.isRequired,
  ignoreEmpty: PropTypes.bool.isRequired,
  filter: PropTypes.string.isRequired,
  orderby: PropTypes.string.isRequired,
  cropSize: PropTypes.number.isRequired,
}

PicList.defaultProps = {
  dataId: '',
}
export default PicList
