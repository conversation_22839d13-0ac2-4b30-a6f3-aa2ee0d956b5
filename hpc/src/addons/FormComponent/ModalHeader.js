
import React from 'react'
import PropTypes from 'prop-types'
import { View, Text, Platform, TouchableOpacity, Image } from 'react-native'
import NavigationService from '../../NavigationService'
import styles from './styles'
import { width, height } from '../../components/DataTable/style'

const isIos = Platform.OS === 'ios'
export const titleHeight = isIos ? (height === 812 ? 88 : 64) : 44
export const titlePaddingtop = isIos ? (height === 812 ? 44 : 20) : 0
export const headerHeight = titleHeight + titlePaddingtop
const navProps = {
  // shadowColor: '#e5e5e5',
  // shadowOffset: { width: 0, height: 1 },
  // shadowOpacity: 1,
  backgroundColor: '#fff',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  height: titleHeight,
  paddingTop: titlePaddingtop,
  paddingHorizontal: 16,
  borderBottomWidth: 1,
  borderBottomColor: '#e5e5e5',
  elevation: 0,
  width,
}
const leftProps = {
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  width: (width - 182) / 2,
  height: '100%',
}
const rightProps = {
  flexDirection: 'row',
  justifyContent: 'flex-end',
  alignItems: 'center',
  width: (width - 182) / 2,
  height: '100%',
}
const textProps = {
  fontWeight: 'bold',
  color: '#41454b',
  textAlign: 'center',
}

const editIcon = require('../../images/button-switch-edit.png')
const listIcon = require('../../images/button-switch-list.png')

const ModalHeader = ({ header, style = {}, centerTextStyle = {} }) => {
  let number = 1
  if (header.centerText && !React.isValidElement(header.centerText) && header.centerText.length * 18 > 150) {
    number = 2
  }
  const centerText = React.isValidElement(header.centerText) ? header.centerText :
  <Text numberOfLines={number} style={{ fontSize: number === 1 ? 18 : 14, ...textProps, ...centerTextStyle }}>{header.centerText}</Text>
  return (
    <View style={{ ...navProps, ...style }}>
      <View style={{ ...leftProps }}>{header.leftView}</View>
      <View style={{ width: 150, height: '100%', justifyContent: 'center', alignItems: 'center' }}>{centerText}</View>
      <View style={{ ...rightProps }}>{header.rightView}</View>
    </View>
  )
}
ModalHeader.EditBut = ({ onPress = () => {}, isEdit = false, style = {} }) => (
  <TouchableOpacity
    onPress={onPress}
    activeOpacity={1}
    style={style}
  >
    <View style={styles.rightWarp}>
      <View
        style={!isEdit ? styles.rightEditRight : styles.rightEdit}
      />
      <Image
        style={isEdit ? styles.rightEditIconRight : styles.rightEditIcon}
        source={isEdit ? listIcon : editIcon}
      />
    </View>
  </TouchableOpacity>
)
ModalHeader.Gener = ({ toggleMenu, title }) => {
  let number = 1
  if (!React.isValidElement(title) && title.length * 18 > 150) {
    number = 2
  }
  const centerText = React.isValidElement(title) ? title :
  <Text numberOfLines={number} style={{ fontSize: number === 1 ? 18 : 14, ...textProps }}>{title}</Text>
  return (
    <View style={{ ...navProps }}>
      <View style={{ ...leftProps }} >
        <TouchableOpacity style={{ height: '100%', justifyContent: 'center', alignItems: 'center' }} onPress={NavigationService.back}>
          <Image style={{ alignSelf: 'center' }} source={require('../../images/icon-arrow-previous-default.png')} />
        </TouchableOpacity>
        {toggleMenu &&
          <TouchableOpacity style={{ height: '100%', justifyContent: 'center', alignItems: 'center', marginLeft: 10 }} onPress={toggleMenu}>
            <Image style={{ alignSelf: 'center' }} source={require('../../images/icons-burger-outline-line-default.png')} />
          </TouchableOpacity>}
      </View>
      <View style={{ width: 150, height: '100%', justifyContent: 'center', alignItems: 'center' }}>{centerText}</View>
      <View style={{ ...rightProps }} />
    </View>
  )
}
ModalHeader.Gener.propTypes = {
  toggleMenu: PropTypes.func,
  title: PropTypes.string,
}
export const FormHeader = ({
  toggleMenu,
  leftView,
  centerText,
  rightView,
  onPressLeft,
  isShowBack,
  onPressRight,
  style = {},
  right,
  centerTextStyle = {},
}) => {
  let number = 1
  if (!React.isValidElement(centerText) && centerText.length * 18 > 150) {
    number = 2
  }
  const centerView = React.isValidElement(centerText) ? centerText :
  <Text numberOfLines={number} style={{ fontSize: number === 1 ? 18 : 14, ...textProps, ...centerTextStyle }}>{centerText}</Text>
  return (
    <View
      style={{ ...navProps, ...style }}
    >
      <View style={{ ...leftProps }}>
        {leftView ? <View>{leftView}</View> :
        <View style={{ ...leftProps }}>
          {isShowBack &&
          <TouchableOpacity
            hitSlop={{ top: 10, left: 10, bottom: 10, right: 10 }}
            style={{ height: '100%', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}
            onPress={onPressLeft}
          >
            <Image style={{ alignSelf: 'center' }} source={require('../../images/icon-arrow-previous-default.png')} />
          </TouchableOpacity>
          }
          {toggleMenu &&
            <TouchableOpacity hitSlop={{ top: 10, left: 10, bottom: 10, right: 10 }} style={{ height: '100%', justifyContent: 'center', alignItems: 'center', marginLeft: isShowBack ? 10 : 0 }} onPress={toggleMenu}>
              <Image style={{ alignSelf: 'center' }} source={require('../../images/icons-burger-outline-line-default.png')} />
            </TouchableOpacity>}
        </View>
        }
      </View>
      <View style={{ width: 150 }}>{centerView}</View>
      <View
        style={{ ...rightProps }}
      >
        {rightView ? <View>{rightView}</View> :
        <TouchableOpacity onPress={onPressRight}>
          {right}
        </TouchableOpacity>}
      </View>
    </View>
  )
}
FormHeader.defaultProps = {
  isShowBack: true,
}
FormHeader.propTypes = {
  toggleMenu: PropTypes.func,
  leftView: PropTypes.node,
  centerText: PropTypes.any,
  rightView: PropTypes.node,
  right: PropTypes.node,
  onPressLeft: PropTypes.func,
  onPressRight: PropTypes.func,
  style: PropTypes.object,
  centerTextStyle: PropTypes.object,
  isShowBack: PropTypes.bool,
}

export default ModalHeader
