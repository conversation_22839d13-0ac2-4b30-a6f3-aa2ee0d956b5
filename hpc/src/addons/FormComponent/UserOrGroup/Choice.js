import React from 'react'
import { View, Text, Dimensions, StyleSheet, TouchableHighlight, ScrollView, ListView } from 'react-native'
import { Flex, List, Checkbox, Button } from '@ant-design/react-native'
import NavigationService from '../../../NavigationService'

const screenWidth = Dimensions.get('window').width
const screenHeight = Dimensions.get('window').height
const colors = ['#a094ed', '#50dad5', '#fd8f60', '#7dcf64', '#7ad6fd', '#fd90b5', '#20caa0', '#8b9ae3', '#8199af', '#fab9a5', '#31b9f8', '#baac93', '#ef6e64', '#fd9adb', '#aa7845', '#138e53', '#83cbc4', '#1279bd']

export default class Choice extends React.Component {
  state = {
    crumbsDatas: [], // 面包屑数据
    dataSource: '', // listView 数据
    defaultValue: [],  // 被选中默认值
    recordPath: ['back', 'one'], // 记录所走的路径
  };
  componentWillMount = () => {
    NavigationService.refresh({
      onBack: this.onBack,
    })
    this.initPage(this.props.defaultValues)
  }
    // 初始页面显示内容
  initPage = (defaultValues) => {
    const datas = []
    const oneData = this.props.allData.one
    datas.push(this.setInitDatas(oneData, true))
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    })
    this.setState({
      dataSource: dataSource.cloneWithRows(datas),
      defaultValue: [...defaultValues],
    })
  }
    // 点击返回按钮 ‘one’：回到初始页面 ‘back’：退出当前页面 否则返回上一路径
  onBack = () => {
    this.state.recordPath.pop()
    const recordPath = this.state.recordPath.pop()
    if (recordPath === 'one') {
      this.state.recordPath.push('one')
      this.state.crumbsDatas = []
      this.initPage(this.state.defaultValue)
    } else if (recordPath === 'back') {
      NavigationService.popAndRefresh({ refresh: { test: true } })
    } else {
      const { id, name, crumbsDatas } = recordPath
      this.changeInitData(id, name, crumbsDatas, true)
    }
  }
    // 设置页面展示的人员或组数据 （展示的数据 ， 是不是组类）
  setInitDatas = (data, isOrg) => {
    const name = isOrg ? data.name : data.nickname
    const childrenNum = isOrg ? data.children.length + data.users.length : 0
    return {
      id: data.id,
      name,
      isOrg,
      children: data.children,
      users: data.users,
      childrenNum,
      mobile: data.mobile,
    }
  }
    // 改变页面所展示的人员或组的数据 （id ，name，面包屑数据，是不是从onBack传来的数据）
  changeInitData =(id, name, crumbsDatas, isOnBack) => {
    const datas = []
    this.props.allData[id].children.map((children) => {
      datas.push(this.setInitDatas(children, true))
    })
    this.props.allData[id].users.map((user) => {
      datas.push(this.setInitDatas(user, false))
    })
    if (!isOnBack) {
      crumbsDatas = crumbsDatas || [...this.state.crumbsDatas]
      crumbsDatas.push({
        id,
        name,
      })
    }
    this.state.recordPath.push({
      id,
      name,
      crumbsDatas,
    })
    this.setState({
      dataSource: this.state.dataSource.cloneWithRows(datas),
      crumbsDatas,
    })
  }
    // 点击面包屑改变页面 （id，name，所点击面包屑数据的下标）
  changePage = (id, name, index) => {
    const crumbsDatas = this.state.crumbsDatas
    this.changeInitData(id, name, crumbsDatas.slice(0, index))
  }
    // 改变被选中的默认值
  changeDefaultValue = (id) => {
    let defaultValue = [...this.state.defaultValue]
    const index = defaultValue.indexOf(id)
    if (this.props.multiselect) {
      if (index !== -1) {
        defaultValue.splice(index, 1)
      } else {
        defaultValue.push(id)
      }
    } else {
      defaultValue = index !== -1 ? [] : [id]
    }
    this.setState({
      defaultValue,
    })
  }
    // 统计选中组类和人员的个数
  totalBox = () => {
    let orgsNum = 0
    let usersNum = 0
    const defaultValue = {
      orgs: [],
      users: [],
    }
    this.state.defaultValue.map((value) => {
      if (this.props.allData[value].isOrg) {
        orgsNum++
        defaultValue.orgs.push(value)
      } else {
        usersNum++
        defaultValue.users.push(value)
      }
    })
    return {
      orgsNum,
      usersNum,
      defaultValue,
    }
  }
  render = () => {
    const height = this.state.crumbsDatas.length ? 40 : 0
    const totalBox = this.totalBox()
    return (
      <View style={styles.page}>
        {/* 面包屑区域*/}
        <Flex
          style={{
            backgroundColor: '#fff',
            width: screenWidth,
            height,
            paddingLeft: 10,
            paddingRight: 10,
            justifyContent: 'center',
          }}
        >
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {
                            this.state.crumbsDatas.map((data, index) => {
                              const isLast = this.state.crumbsDatas.length - 1 === index
                              return (
                                <Text
                                  key={data.id + Math.random()}
                                  style={{ color: isLast ? '#333' : '#0790fa' }}
                                  onPress={() => { if (!isLast) this.changePage(data.id, data.name, index) }}
                                >{data.name}<Text style={{ color: '#333' }}>{isLast ? '' : '>'}</Text></Text>
                              )
                            })
                        }
          </ScrollView>
        </Flex>
        {/* 展示人员或组数据区域*/}
        <List style={styles.choiceBox}>
          <ListView
            ref="lv"
            dataSource={this.state.dataSource}
            pageSize={5}
            scrollRenderAheadDistance={10}
            renderRow={(data) => {
              const childrenNum = data.childrenNum
              const checked = this.state.defaultValue.indexOf(data.id) !== -1
              if (data.isOrg) {
                return (
                  <List.Item
                    arrow={childrenNum ? 'horizontal' : 'empty'}
                    multipleLine
                    onClick={() => { childrenNum ? this.changeInitData(data.id, data.name) : null }}
                    key={data.id}
                    thumb={
                      <Checkbox
                          onChange={(e) => { e.target.checked = !e.target.checked; this.changeDefaultValue(data.id) }}
                          checked={checked}
                          style={{ marginRight: 10 }}
                        />
                                        }
                  >
                    {data.name}
                  </List.Item>
                )
              } else {
                                // 名字首字符为汉字的显示名字的最后一位 否则显示第一位
                const charCodeNum = data.name.substring(0, 1).charCodeAt() < 128 ? 0 : data.name.length - 1
                                // 根据id选取指定背景色
                const bgColor = colors[data.id % colors.length]
                return (
                  <List.Item arrow="empty" multipleLine key={data.id}>
                    <Checkbox
                      onChange={(e) => { e.target.checked = !e.target.checked; this.changeDefaultValue(data.id) }}
                      checked={checked}
                    >
                      <TouchableHighlight
                          style={{
                              backgroundColor: bgColor,
                              width: 30,
                              height: 30,
                              borderRadius: 15,
                              justifyContent: 'center',
                              alignItems: 'center',
                              marginLeft: 10,
                              marginRight: 10,
                            }}
                        >
                          <Text style={{ color: '#fff' }}>{data.name.substring(charCodeNum, charCodeNum + 1)}</Text>
                        </TouchableHighlight>
                      <Flex style={{ height: 40 }} direction="column" align="start" justify="center">
                          <Text>{data.name}</Text>
                          <Text style={{ color: '#8a8a8a' }}>{data.mobile}</Text>
                        </Flex>
                    </Checkbox>
                  </List.Item>
                )
              }
            }}
          />
        </List>
        {/* 统计总数区域*/}
        <Flex style={styles.totalBox} wrap="wrap">
          <Text style={{ color: '#2192ee' }}>已选择：{totalBox.orgsNum}个群组 {totalBox.usersNum}个成员</Text>
          <Button
            size="small" type="primary" inline
            onPress={() => {
              this.props.changeValue(totalBox.defaultValue, true)
            }}
            style={{ paddingLeft: 30, paddingRight: 30, position: 'absolute', right: 10, top: 18 }}
          >
                        确定
                    </Button>
        </Flex>
      </View>
    )
  }
}

let styles = StyleSheet.create({
  page: {
    marginTop: 5,
    backgroundColor: '#f5f4f9',
    minHeight: screenHeight - 58,
  },
  choiceBox: {
    marginTop: 10,
    backgroundColor: '#fff',
    maxHeight: screenHeight - 158,
  },
  totalBox: {
    position: 'absolute',
    bottom: 0,
    height: 60,
    width: screenWidth,
    backgroundColor: '#f0eff5',
    paddingLeft: 10,
    paddingRight: 10,
  },
})
