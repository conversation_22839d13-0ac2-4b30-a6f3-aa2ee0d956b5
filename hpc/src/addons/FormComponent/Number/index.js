import PropTypes from 'prop-types'
import React from 'react'
import { View, Platform, Keyboard } from 'react-native'
import { SimpleLine } from 'nagz-mobile-lib'
import { Toast } from '@ant-design/react-native'
import isBoolean from 'lodash/isBoolean'
import { getDisplayValue, thousandsFun } from './displayValue'

import { isTrue, getUpdateValidationFn } from '../common'

const fn = () => {}

const isInvalidValue = (value) => {
  if (!value) return false
  const valueArr = value.toString().split('.')
  if (valueArr[0].length > 16 || (valueArr[1] && (valueArr[1].length > 9 || valueArr[0].length + valueArr[1].length > 16))) {
    return true
  }
  if (value !== '-' && isNaN(Number(value))) {
    return true
  }
  return false
}
const getDisplayFormant = (value, displayFormat) => {
  if (value && !isNaN(value)) {
    if (displayFormat === '1') {
      return thousandsFun(value)
    } else if (displayFormat === '2') {
      return `${Number(value)}`
    }
  }
  return value
}
class NumberInput extends React.PureComponent {
  state = {
    value: getDisplayFormant(this.props.defaultValue, this.props.displayFormat),
    isMaxLength: false,
    isText: true,
  }

  componentWillMount = () => {
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, this.state.value)
    if (this.props.storageMode && this.props.defaultValue !== '') {
      this.changeValue(this.props.defaultValue, 1)
    }
  }
  componentDidMount() {
    // 因为高度为固定的，所以不通过onlayout计算高度
    this.cmpHeight = 50
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
  }

  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.props.defaultValue !== defaultValue) {
      if (isNaN(this.props.defaultValue) && isNaN(defaultValue)) {
        return
      }
      if (this.updateValidation) {
        this.updateValidation = this.updateValidation(this.props.required, defaultValue)
      }
      if (this.props.storageMode) {
        this.changeValue(defaultValue)
      }
    }
    if ((isNaN(Number(defaultValue)) || defaultValue === '') && this.props.defaultValue !== defaultValue) {
      this.setState({
        value: '',
      })
    } else if (this.state.value !== Number(defaultValue) && this.props.defaultValue !== defaultValue) {
      const { decimalLength, storageMode } = this.props
      const value = decimalLength > 0 && storageMode ? (Number(defaultValue)).toFixed(decimalLength) : defaultValue
      this.setState({
        value: getDisplayFormant(value, this.props.displayFormat),
      })
    }
  }
  componentWillUnmount() {
    this.keyboardDidHideListener.remove()
  }
  keyboardDidHide=() => {
    this.simpleLineref.closeKeyboard()
  }
  onBlur = () => {
    if (this.props.defaultValue !== this.state.value) {
      this.changeValue(this.state.value === undefined ? null : (`${this.state.value}`).replace(/,/g, ''))
    }
    this.setState({
      isText: true,
    })
  }

  updateValue=(value) => {
    const val = (`${value}`).replace(/,/g, '')
    if (isInvalidValue(val)) {
      this.setState({
        isMaxLength: true,
      })
      return
    }
    this.setState({
      value: val,
      isMaxLength: false,
    })
    if (this.props.isChangeUpdate) {
      if (this.props.defaultValue !== val) {
        this.changeValue(val === undefined ? null : (`${val}`).replace(/,/g, ''))
      }
    }
  }

  changeValue = (val) => {
    const {
      updateEveryValue, storageMode, displayMode, decimalLength,
    } = this.props
    const value = val !== '' && storageMode ? getDisplayValue(val, displayMode, decimalLength) : val
    if (updateEveryValue) {
      updateEveryValue({
        [this.props.fieldId]: {
          defaultValue: value === '' || value === null || value === undefined ? '' : Number(value),
        },
      })
    }
  }

  changeFormsFocus = () => {
    this.onBlur()
  }

  onPress=() => {
    this.setState({
      value: (`${this.state.value}`).replace(/,/g, ''),
      isText: false,
    })
  }

  render() {
    const {
      label, required, isRunTime, enabled, isView, hidelabel,
    } = this.props
    const editable = isView ? false : (isBoolean(enabled) ? enabled : false)
    return (
      <View>
        <SimpleLine
          ref={(ref) => { this.simpleLineref = ref }}
          label={hidelabel && isRunTime ? '' : label}
          inputType={Platform.OS === 'ios' ? 'numbers-and-punctuation' : 'numeric'}
          isText={this.state.isText}
          placeholder="请输入"
          isRequire={isTrue(required)}
          editable={editable}
          value={String(this.state.value)}
          onChange={this.updateValue}
          onBlur={this.onBlur}
          onPress={!editable ? () => Toast.info('不可更改', 1) : this.onPress}
          onSubmitEditing={this.changeFormsFocus}
        />
      </View>
    )
  }
}
NumberInput.propTypes = {
  label: PropTypes.string,
  fieldId: PropTypes.string,
  defaultValue: PropTypes.any,// eslint-disable-line
  displayMode: PropTypes.string,
  required: PropTypes.bool,
  enabled: PropTypes.bool,
  isView: PropTypes.bool,
  hidelabel: PropTypes.bool,
  isRunTime: PropTypes.bool,
  storageMode: PropTypes.bool,
  changeValue: PropTypes.func,// eslint-disable-line
  updateValidation: PropTypes.func,
  updateEveryValue: PropTypes.func,
  decimalLength: PropTypes.number,
  thousands: PropTypes.bool,
}

NumberInput.defaultProps = {
  label: '',
  defaultValue: '',
  required: false,
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  enabled: true,
}
export default NumberInput
