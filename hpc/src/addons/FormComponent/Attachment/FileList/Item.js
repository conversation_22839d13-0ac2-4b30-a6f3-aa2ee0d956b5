import React from 'react'
import PropTypes from 'prop-types'
import { Progress } from '@ant-design/react-native'
import RNFS from 'react-native-fs'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
  NativeModules,
  Dimensions,
} from 'react-native'

import Icon from 'react-native-vector-icons/FontAwesome'

import { deviceType } from '../../../../config/sysPara'
import { GO_FILEDETAIL } from '../../../../utils/jump'

import AGZIcon from '../../../../components/Icon'
import ImageLoader from '../../../../components/ImageLoader'
import { itemStyle } from './style'
import { getFont } from '../util'

const { width, height } = Dimensions.get('window')

const checkedimg = require('../../../../images/icon-checkbox-checked-default.png')
const uncheckedimg = require('../../../../images/icon-checkbox-un_checked-default.png')

const styles = StyleSheet.create(itemStyle)

class Item extends React.Component {
  // 显示附件图标或图片预览
  showPic = (file) => {
    console.log('asdasdasd', file)
    if (getFont(file.type) === 'file-picture-o') {
      // 如果是图片，那么显示图片预览
      const src = this.props.isPrivate ? file.url : `${file.url}?x-oss-process=image/resize,m_fixed,h_40,w_40`
      // console.log('asdasdhalsdjljdklaskdljlkajdklad', file.url, src)
      return <ImageLoader src={src} />
    }
    // 不是图片，显示标准图标
    return (
      <View style={{ height: 48, width: 48, alignItems: 'center', justifyContent: 'center' }}>
        <Icon name={getFont(file.type)} size={34} />
      </View>)
  }

  checkedItem=() => {
    const { checked, file, onCheck } = this.props
    const index = checked.findIndex(c => c === file.uid)
    if (index === -1) {
      onCheck([...checked, file.uid])
    } else {
      onCheck([...checked.slice(0, index), ...checked.slice(index + 1, checked.lenght)])
    }
  }
  openFile=() => {
    const { file, appId, fieldId, formId, dataId, isPrivate, isDownload } = this.props
    GO_FILEDETAIL({ file, appId, fieldId, formId, dataId, isPrivate, isDownload })
  }
  render=() => {
    const { isEdit, file, showAction, checked = [], isDownload, isDelete } = this.props
    return (
      <TouchableOpacity
        activeOpacity={isEdit ? 0.8 : 1}
        onPress={isEdit ? this.checkedItem : this.openFile}
      >

        <View style={styles.row} key={file.uid}>
          <View
            style={{ flex: 1,
              flexDirection: 'row',
              alignItems: 'center' }}
          >
            {isEdit && <Image
              source={checked.indexOf(file.uid) !== -1 ? checkedimg : uncheckedimg}
              style={{
                width: 21,
                height: 21,
                marginRight: 20,
              }}
            />}
            { this.showPic(file) }
            {/* <Progress style={{ marginLeft: 10 }} percent={40 % 100} position="normal" appearTransition /> */}
            { file.progress % 100 !== 0 &&
            <View style={{ position: 'relative', marginLeft: 10, width: width - 120, height: 4, backgroundColor: '#ccc' }}>
              <View style={{ position: 'absolute', left: 0, top: 0, width: (width - 120) * ((file.progress % 100) / 100), height: 4, backgroundColor: 'blue' }} />
            </View>}
            {file.progress % 100 === 0 && <Text numberOfLines={1} style={styles.rowText}>{file.name}</Text> }
          </View>
          {
          file.progress % 100 === 0 && !isEdit && (isDownload || isDelete) &&
          <View style={{ flexDirection: 'row-reverse', width: 40 }}>
            <TouchableOpacity onPress={() => showAction(file)} ctiveOpacity={0.8}>
              <View>
                <Image style={{ width: 20, height: 20 }} source={require('../../../../images/icons-more-dot-default.png')} />
              </View>
            </TouchableOpacity>
          </View>
        }
        </View>
      </TouchableOpacity>
    )
  }
}
Item.propTypes = {
  file: PropTypes.shape({
    uid: PropTypes.string,
  }),
  checked: PropTypes.arrayOf(PropTypes.string),
  onCheck: PropTypes.func,
  showAction: PropTypes.func,
  isEdit: PropTypes.bool,
  dataId: PropTypes.string,
  appId: PropTypes.string,
  formId: PropTypes.string,
  fieldId: PropTypes.string,
}
export default Item
