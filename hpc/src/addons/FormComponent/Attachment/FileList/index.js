import React from 'react'
import PropTypes from 'prop-types'
import { View, StyleSheet, ScrollView } from 'react-native'
import { ActionSheet } from 'nagz-mobile-lib'

import Item from './Item'
import { listItemStyle } from './style'

const styles = StyleSheet.create(listItemStyle)

const wrapProps = {
  onTouchStart: e => e.preventDefault(),
}
class FileList extends React.PureComponent {
  state = {
    selectedFile: {},
    clicked: '',
    checkfiles: [],
  }
  getbtns = (file) => {
    const btns = []
    const { enabled } = this.props
    if (this.hasDownload(file.operations)) {
      btns.push('下载')
    }
    if (enabled && this.hasDelete(file.operations)) {
      btns.push('删除')
    }
    btns.push('取消')
    return btns
  }
  showActionSheet = (file) => {
    const { enabled } = this.props
    // const BUTTONS = !this.props.enabled ? ['下载', '取消'] : ['下载', '删除', '取消']
    const BUTTONS = this.getbtns(file)
    const isDelete = this.hasDelete(file.operations)
    const isDownload = this.hasDownload(file.operations)
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        destructiveButtonIndex: BUTTONS.length !== 3 ? undefined : BUTTONS.length - 2,
        message: file.name,
        wrapProps,
      },
      (buttonIndex) => {
        if (buttonIndex === 0) {
          if (isDownload) {
            this.props.download(file.uid, file.name)
          } else if (enabled && isDelete) {
            this.props.deleteFile(file.uid, file.id)
          }
        } else if (buttonIndex === 1 && enabled && isDelete && isDownload) {
          this.props.deleteFile(file.uid, file.id)
        }
      },
    )
  }
  handlePress = async () => {
    await this.props.deleteFile(this.state.checkfiles)
    this.setState({ checkfiles: [] })
  }
  hasDownload = opts => opts && opts.indexOf('4') !== -1
  hasDelete = opts => opts && opts.indexOf('5') !== -1
  hasPriview = opts => opts && opts.indexOf('6') !== -1
  render = () => {
    const { appId, fieldId, formId, dataId, isPrivate } = this.props
    return (
      <View style={this.props.isEdit && this.state.checkfiles && this.state.checkfiles.length !== 0 ? styles.warpcontainerEdit : styles.warpcontainer}>
        <ScrollView style={styles.container}>
          {
            this.props.files && this.props.files.map(file => (
              <Item
                key={file.uid}
                isEdit={this.props.isEdit}
                showAction={() => { this.showActionSheet(file) }}
                file={file}
                checked={this.state.checkfiles}
                appId={appId}
                fieldId={fieldId}
                formId={formId}
                dataId={dataId}
                isPrivate={isPrivate}
                isDownload={this.hasDownload(file.operations)}
                isDelete={this.hasDelete(file.operations)}
                onCheck={(fileIds) => { this.setState({ checkfiles: fileIds }) }}
              />
            ))
          }
        </ScrollView>
      </View>
    )
  }
}
FileList.propTypes = {
  files: PropTypes.arrayOf(PropTypes.object),
  isEdit: PropTypes.bool,
  download: PropTypes.func,
  deleteFile: PropTypes.func,
  dataId: PropTypes.string,
  appId: PropTypes.string,
  formId: PropTypes.string,
  fieldId: PropTypes.string,
  enabled: PropTypes.bool,
  isPrivate: PropTypes.bool,
}

export default FileList
