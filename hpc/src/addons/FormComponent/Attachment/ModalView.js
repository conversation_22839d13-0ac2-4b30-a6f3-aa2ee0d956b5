import React from 'react'
import { View, Dimensions, TouchableOpacity, StyleSheet, Image, Text, AsyncStorage } from 'react-native'
import isArray from 'lodash/isArray'
import PropTypes from 'prop-types'
import { Toast, Modal } from '@ant-design/react-native'
import NavigationService from '../../../NavigationService'
import ModalHeader from '../ModalHeader'
import { headerStyle } from './style'
import SelectFile from './SelectFile'
import FileList from './FileList'
import { ArrayEqual } from './util'
import { getUpdateValidationFn } from '../common'
import { getFiles, addFile, removeFile, getFilesById, getInitValue } from './store'
import { uploadfile, deletefile, downloadfile, getFileUrl, isPrivate } from './uploadFiles'
import PictrueView from './Picture/PictrueView'

const { width, height } = Dimensions.get('window')
const style = StyleSheet.create(headerStyle)
const prevIcon = require('../../../images/icon-arrow-previous-default.png')

const random = () => Math.floor((Math.random() + (Math.floor(Math.random() * 9) + 1)) * Math.pow(10, 9))

class ModalView extends React.PureComponent {
  state = {
    isEdit: false,
    fileList: [],
  }
  componentDidMount = () => {
    this.initFields()
  }
  componentWillReceiveProps = async ({ defaultValue, picture, joinfieldvalue }) => {
    if (picture && !ArrayEqual(defaultValue, this.props.defaultValue)) {
      if (!defaultValue || defaultValue.length === 0) {
        this.setState({ fileList: [] })
        this.updateValue([])
      } else {
        const { records: files } = await getFilesById(this.props.appId, defaultValue)
        this.setFields(files)
      }
    }
  }
  initFields = async () => {
    const { dataId, appId, formId, fieldId, isRunTime, updateValidation, defaultValue } = this.props
    this.updateValidation = getUpdateValidationFn(updateValidation, fieldId)(this.props.required, this.state.fileList)
    if (isRunTime && dataId) {
      const { records: files } = await getFiles(appId, formId, dataId, fieldId)
      this.setFields(files)
    } else if (isArray(defaultValue) && defaultValue.length !== 0) {
      const { records: files } = await getFilesById(this.props.appId, defaultValue)
      this.setFields(files)
    }
  }
  setFields = (files) => {
    const fileList = files.map(file => ({
      id: file.id,
      uid: file.ossFileName,
      name: file.fileName,
      url: file.ossUrl,
      type: file.fileType,
      size: file.fileSize,
      progress: 100,
      status: 'success',
      version: file.version || '',
      operations: file.operations || [],
    }))
    this.setState({ fileList })
    this.updateValue(fileList)
  }
  deleteFile = async (fileId, id) => {
    if (fileId instanceof Array) {
      await this.deleteFiles(fileId)
      return
    }
    const { appId, fieldId, formId, dataId } = this.props
    const { fileList } = this.state
    const index = fileList.findIndex((file => file.uid === fileId))
    if (index !== -1) {
      const res = await deletefile(fileId, { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0 }, true)
      if (res) {
        await removeFile(appId, id)
        const fs = [...fileList.slice(0, index),
        ...fileList.slice(index + 1),
        ]
        this.setState({
          fileList: fs,
        })
        this.updateValue(fs)
        Toast.success('删除成功')
      }
    }
  }
  deleteFiles = async (fids = [], showtoast = true, isCover = false) => {
    const { appId, fieldId, formId, dataId } = this.props
    const files = this.state.fileList.filter(f => fids.indexOf(f.uid) === -1)
    const deletefs = this.state.fileList.filter(f => fids.indexOf(f.uid) !== -1)
    for (let i = 0; i < deletefs.length; i += 1) {
      const dfile = deletefs[i]
      const res = await deletefile(dfile.uid, { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0, attachDataId: dfile.id }, isCover)
      if (!res) {
        return false
      }
      if (res && !isCover) await removeFile(appId, dfile.id)
    }
    this.updateValue(files)
    this.setState({ fileList: files })
    showtoast && Toast.success('删除成功')
    return true
  }
  download = async (fileId, fileName) => {
    const { appId, fieldId, formId, dataId } = this.props
    await downloadfile(fileId, fileName, (f) => {
      this.uploadFileCallBack(f ? f.currentSize / f.totalSize : 1, { uid: fileId, name: fileName })
    }, { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0 })
  }
  findfile = file => this.state.fileList.filter(f => f.name.toLowerCase() === file.name.toLowerCase())
  setFiles = (file) => {
    const cf = this.findfile(file)
    const doUpload = () => {
      const uid = `${random()}`
      Object.assign(file, { uid: `${uid}` })
      this.beforeUpload(file)
    }
    const ok = async () => {
      const res = await this.deleteFiles(cf.map(f => f.uid), false, true)
      if (res) {
        doUpload()
      }
    }
    if (cf && cf.length !== 0) {
      Modal.alert('附件上传提醒', '有重复的附件，需要覆盖吗?', [
        {
          text: '取消',
          onPress: () => console.log('cancel'),
          style: 'cancel',
        },
        {
          text: '确定',
          onPress: () => {
            ok()
          },
        },
      ])
    } else {
      doUpload()
    }
  }

  beforeUpload = async (file) => {
    const { appId, formId, fieldId, dataId, single } = this.props
    const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')
    if (single) {
      const uids = []
      for (let i = 0; i < this.state.fileList.length; i += 1) {
        const tmpfile = this.state.fileList[i]
        uids.push(tmpfile.uid)
        await removeFile(appId, tmpfile.id)
      }
      uids.length !== 0 && await deletefile(uids[0], { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0 })

      this.setState({
        fileList: [
          {
            uid: file.uid,
            name: file.name,
            url: '',
            progress: 0,
            type: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
            size: file.size,
          },
        ],
      })
    } else {
      this.setState({
        fileList: [
          ...this.state.fileList,
          {
            uid: file.uid,
            name: file.name,
            url: '',
            progress: 0,
            type: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
            size: file.size,
          },
        ],
      })
    }
    uploadfile(file.uid, file, (f) => {
      this.uploadFileCallBack(f ? f.currentSize / f.totalSize : 1, file)
    }, { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: file.size }).then((f) => {
      this.uploadfiledone(f, file)
    })
    return false
  }
  uploadfiledone = async (f, file) => {
    if (f) {
      const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')
      const { appId, formId, fieldId, dataId } = this.props
      // const ossUrl = `https://${bucket}.${region}.aliyuncs.com/${file.uid}`
      let user = await AsyncStorage.getItem('result')
      var path = appId === '0' ?
        `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
        : `${appId}/${formId}/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
      const ossUrl = getFileUrl(path, fileNameSplit[fileNameSplit.length - 1].toLowerCase())
      this.uploadFileCallBack(1, { uid: file.uid, url: ossUrl }, 'done')
      addFile(appId, {
        appId,
        attachmentId: fieldId,
        boRecordId: dataId || '',
        fileName: file.name,
        fileSize: file.size,
        formId,
        ossFileName: file.uid,
        ossUrl,
        remark: '',
        fileType: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
      }).then((res) => {
        const { fileList } = this.state
        const currFile = fileList.find(item => item.uid === file.uid)
        const index = fileList.findIndex(item => item.uid === file.uid)
        const fs = [...fileList.slice(0, index),
        { ...currFile, id: res.fileId, operations: res.operations ? res.operations : ['4', '5', '6'] },
        ...fileList.slice(index + 1),
        ]
        this.setState({
          fileList: fs,
        })
        this.updateValue(fs)
      })
    } else {
      this.uploadFileCallBack(0, file, 'error')
    }
  }
  updateValue = (fileList) => {
    const { changeValue, fieldId } = this.props
    const fileIds = fileList.filter(({ id }) => id).map(({ id }) => id)
    if (changeValue) changeValue({ [fieldId]: { defaultValue: fileIds } })
    this.updateValidation = this.updateValidation(this.props.required, fileIds)
  }
  uploadFileCallBack = (progress, f, type) => {
    const { fileList } = this.state
    const currFile = fileList.find(file => file.uid === f.uid || file.uid === f.name)
    const index = fileList.findIndex(file => file.uid === f.uid || file.uid === f.name)
    let url = currFile.url
    if (type === 'done' && currFile.url === '') {
      url = f.url
    }
    if (index !== -1) {
      this.setState({
        fileList: [...fileList.slice(0, index),
        {
          ...currFile,
          progress: Math.floor((progress * 100) % 100),
          url,
          status: type === 'error' ? 'exception' : (progress === 1 ? 'success' : 'active'),
        },
        ...fileList.slice(index + 1),
        ],
      })
    }
  }

  selectFileCallBack = (file) => {
    this.setFiles(file)
  }

  selectPicCallBack = (file) => {
    this.setFiles(file)
  }
  deleteSelect = () => {
    const selectfIds = this.fileListRef.state.checkfiles
    if (selectfIds) {
      const selectFiles = this.state.fileList.filter(f => selectfIds.indexOf(f.uid) !== -1 && f.operations && f.operations.indexOf('5') !== -1)
      if (selectFiles && selectFiles.length !== 0) {
        this.deleteFiles(selectFiles.map(f => f.uid))
      } else {
        Toast.info('没有删除权限')
      }
    }
  }
  downloadSelect = async () => {
    const selectfIds = this.fileListRef.state.checkfiles
    if (selectfIds) {
      const selectFiles = this.state.fileList.filter(f => selectfIds.indexOf(f.uid) !== -1 && f.operations && f.operations.indexOf('4') !== -1)
      if (selectFiles && selectFiles.length !== 0) {
        this.setState({
          fileList: this.state.fileList.map((f) => {
            if (selectfIds.indexOf(f.uid) !== -1) {
              return { ...f, progress: 1 }
            }
            return f
          })
        })
        for (let i = 0; i < selectFiles.length; i += 1) {
          const f = selectFiles[i]
          await this.download(f.uid, f.name)
        }
      } else {
        Toast.info('没有下载权限')
      }
    }
  }
  modalHeaderRender = () => ({
    centerText: '文件列表',
    leftView: (
      <TouchableOpacity onPress={NavigationService.back}>
        <View style={style.leftContainer}>
          <Image source={prevIcon} />
          <Text style={style.leftText}>返回</Text>
        </View>
      </TouchableOpacity>),
    rightView: (
      <View
        style={style.rightContainer}
      >
        {this.state.fileList && this.state.fileList.length !== 0 &&
          ModalHeader.EditBut({
            onPress: () => { this.setState({ isEdit: !this.state.isEdit }) },
            isEdit: this.state.isEdit,
          })
        }
        <SelectFile
          isEdit={this.state.isEdit}
          onSelectPic={this.selectPicCallBack}
          onSelectFile={this.selectFileCallBack}
          deleteSelect={this.deleteSelect}
          downloadSelect={this.downloadSelect}
          enabled={this.props.enabled}
          isPrivate={isPrivate}
        />
      </View>
    ),
  })

  attachmentRender = () => {
    const { appId, fieldId, formId, dataId } = this.props
    return (
      <View style={{ width, height, backgroundColor: '#fff' }}>
        <ModalHeader header={this.modalHeaderRender()} />
        <FileList
          ref={(ref) => { this.fileListRef = ref }}
          isEdit={this.state.isEdit}
          files={this.state.fileList}
          deleteFile={this.deleteFile}
          download={this.download}
          appId={appId}
          fieldId={fieldId}
          formId={formId}
          dataId={dataId}
          enabled
          isPrivate={isPrivate}
        />
      </View>
    )
  }
  pictureRender = () => (
    <PictrueView
      files={this.state.fileList}
      onSelectFile={this.selectFileCallBack}
      deleteFile={this.deleteFile}
      download={this.download}
      isPrivate={isPrivate}
      {...this.props}
    />
  )

  render = () => {
    if (this.props.picture) {
      return this.pictureRender()
    }
    return this.attachmentRender()
  }
}

ModalView.propTypes = {
  defaultValue: PropTypes.arrayOf(PropTypes.string),
  dataId: PropTypes.string,
  appId: PropTypes.string,
  formId: PropTypes.string,
  fieldId: PropTypes.string,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  updateValidation: PropTypes.func,
  required: PropTypes.bool,
  single: PropTypes.bool,
  picture: PropTypes.bool,
  enabled: PropTypes.bool,
}

export default ModalView
