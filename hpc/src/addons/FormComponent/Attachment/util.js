import RNFS from 'react-native-fs'
import { deviceType } from '../../../config/sysPara'

export const getFileInfo = async (path, callBack, fdir) => {
  let tempfdir = fdir
  if (!fdir) {
    tempfdir = deviceType === 2 ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker` : `${RNFS.PicturesDirectoryPath}`
  }

  const result = await RNFS.readDir(tempfdir)
  const str = path.substring(path.lastIndexOf('/') + 1, path.length)
  // 遍历找到文件
  result.forEach((v) => {
    if (v.name === str) {
      callBack(v)
    }
  })
}

export const getFont = (fileType) => {
  switch (fileType) {
    case 'doc':
    case 'docx':
      return 'file-word-o'
    case 'xls':
    case 'xlsx':
      return 'file-excel-o'
    case 'ppt':
    case 'pptx':
      return 'file-powerpoint-o'
    case 'rar':
    case 'zip':
      return 'file-archive-o'
    case 'pdf':
      return 'file-pdf-o'
    case 'txt':
      return 'file-text'
    case 'js':
    case 'html':
    case 'css':
      return 'file-code-o'
    case 'mp4':
    case 'avi':
      return 'file-audio-o'
    case 'rm':
    case 'asf':
    case 'wmv':
      return 'file-movie-o'
    case 'jpg':
    case 'gif':
    case 'png':
      return 'file-picture-o'
    default:
      return 'file'
  }
}

export const ArrayEqual = (arr = [], arr2 = []) =>
  arr instanceof Array && arr2 instanceof Array
&& (arr === arr2
|| (arr.length === 0 && arr2.length === 0)
|| (arr.length === arr2.length
  && arr.every(a => arr2.indexOf(a) !== -1)))

/**
*
* @param fn {Function}   实际要执行的函数
* @param delay {Number}  延迟时间，也就是阈值，单位是毫秒（ms）
*
* @return {Function}     返回一个“去弹跳”了的函数
*/
export const debounce = (fn, delay) => {
  // 定时器，用来 setTimeout
  let timer
  // 返回一个函数，这个函数会在一个时间区间结束后的 delay 毫秒时执行 fn 函数
  return () => {
    // 保存函数调用时的上下文和参数，传递给 fn
    const context = this
    const args = arguments
    // 每次这个返回的函数被调用，就清除定时器，以保证不执行 fn
    clearTimeout(timer)
    // 当返回的函数被最后一次调用后（也就是用户停止了某个连续的操作），
    // 再过 delay 毫秒就执行 fn
    timer = setTimeout(() => {
      fn.apply(context, args)
    }, delay)
  }
}
