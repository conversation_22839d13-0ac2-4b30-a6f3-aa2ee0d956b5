/* eslint-disable func-names */
import { AsyncStorage } from 'react-native'
import RNFS from 'react-native-fs'
import { get } from '../../../request'
import { envParm, deviceType } from '../../../config/sysPara'
import AliyunOSS from '../../../components/AliyunOSS'

import { uploadfile as ftpUpload, downFile as ftpDownFile, deleteFile as ftpDeleteFile } from './ftpFileUtil'

export const region = 'oss-cn-hangzhou'
export const endPoint = 'https://oss-cn-hangzhou.aliyuncs.com'
export const bucket = envParm.bucket

export const isPrivate = envParm.isPrivate

// AliyunOSS.enableOSSLog();
const getCliean = async (api, param, isDelete = false, isCover = false) => {
  // 授权地址
  // 获取签名信息
  const tokendata = await get(`/captcha/oss/token/${api}`)({ data: { ...param, isDelOpr: isDelete, isCover } })
  if (!tokendata || !tokendata.data || tokendata.errorCode !== '0') {
    Toast.fail('获取授权失败')
    return false
  }
  const creds = tokendata.data
  const config = {
    AccessKey: creds.AccessKeyId,
    SecretKey: creds.AccessKeySecret,
    SecretToken: creds.SecurityToken,
  }
  // 初始化阿里云组件
  AliyunOSS.initWithKey(config, endPoint)
  return true
}
export const uploadfile = async (key, file, callBack, param) => {
  if (isPrivate) {
    return ftpUpload(key, file)
  }
  const aliyunStatus = await getCliean('write', param)
  if (aliyunStatus) {
    // upload config
    const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')
    let user = await AsyncStorage.getItem('result')
    var path = param.appId === '0' ?
      `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${key}`
      : `${param.appId}/${param.formId}/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${key}`
    const uploadConfig = {
      bucketName: bucket, // your bucketName
      sourceFile: file.path, // local file path
      ossFile: path, // the file path uploaded to oss
    }
    // console.log('选择文件并暂存本地', uploadConfig, key, file, callBack, param, AliyunOSS.addEventListener('uploadProgress', callBack))
    AliyunOSS.addEventListener('uploadProgress', callBack)
    // 执行上传
    return AliyunOSS.uploadObjectAsync(uploadConfig).then((resp) => {
      // 去除事件监听
      AliyunOSS.removeEventListener('uploadProgress', callBack)
      // 此处可以执行回调
      return resp
    }).catch((err) => {
      console.log('upload is failed', err)
      // 执行失败回调
      return null
    })
  }
  return null
}
export const downloadfile = async function (key, filename, callBack, param) {
  const localPath = deviceType === 2 ?
    `${RNFS.DocumentDirectoryPath}/Download/`
    : `${RNFS.ExternalStorageDirectoryPath}/HuaPuC/`
  if (isPrivate) {
    // eslint-disable-next-line no-return-await
    return await ftpDownFile(key, filename, localPath, callBack)
  }
  const aliyunStatus = await getCliean('read', param)
  if (aliyunStatus) {
    // download
    const downloadConfig = {
      bucketName: bucket,
      ossFile: key, // the file path on the oss
      fileName: filename,
    }
    // const downloadProgress = p => console.log(p.currentSize / p.totalSize);
    AliyunOSS.addEventListener('downloadProgress', callBack)
    return AliyunOSS.downloadObjectAsync(downloadConfig).then((localFile) => {
      AliyunOSS.removeEventListener('downloadProgress', callBack)
      return localFile
    }).catch((error) => {
      console.error(error)
      return null
    })
  }
  return null
}
export const getOSSToken = async () => {
  // 授权地址
  // 获取签名信息
  const tokendata = await get('/captcha/oss/token/mobile/read')()
  if (!tokendata || !tokendata.data || tokendata.errorCode !== '0') {
    Toast.fail('获取授权失败')
    return false
  }
  const creds = tokendata.data
  const config = {
    accessKey: creds.AccessKeyId,
    secretKey: creds.AccessKeySecret,
    secretToken: creds.SecurityToken,
  }
  return config
}
export const deletefile = async function (key, param, isCover = false) {
  if (isPrivate) {
    // eslint-disable-next-line no-return-await
    return await ftpDeleteFile(key)
  }
  const aliyunStatus = await getCliean('write', param, true, isCover)
  if (aliyunStatus) {
    // delete
    const deleteConfig = {
      bucketName: bucket,
      ossFile: key, // the file path on the oss
    }
    return AliyunOSS.deleteObjectAsync(deleteConfig).then((resp) => {
      console.log(resp) // the local file path downloaded from oss
      return true
    }).catch((error) => {
      console.error(error)
      return false
    })
  }
  return false
}

export const getFileUrl = (key, type) => {
  if (isPrivate) {
    return `${envParm.URLS}/captcha/preview/${key}?type=${type}`
  }
  return `https://${bucket}.${region}.aliyuncs.com/${key}`
}

