import PropTypes, { object } from 'prop-types'
import React from 'react'
import { View, Text, Dimensions, TouchableHighlight, Image, Alert, StyleSheet } from 'react-native'
import { Modal, Button, Carousel, ActionSheet, Progress } from '@ant-design/react-native'
import ImagePicker from 'react-native-image-crop-picker'
import AGZIcon from '../../../../components/Icon'
import { GO_FILEMANAGER } from '../../../../utils/jump'
import RNFS from 'react-native-fs'
import { screenWidth, deviceType } from '../../../../config/sysPara'
import { fields } from '../../../../containers/FormDesign/reducer'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  container: {
    width,
    height,
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: '#d6d7da',
    flex: 1,
  },
  resizeMode: {
    width, height: height / 2,
  },
  resizeModePreview: {
    width, height: height - 50,
  },
  resizeModeMT: {
    width,
    height: height / 2,
    marginTop: (height - 50) / 4,
  },
})

class PicList extends React.Component {
  state={
    picvisible: false,
    currentUrl: '',
    files: [],
  }

  componentWillReceiveProps = ({ files, single }) => {
    if (files.length !== this.state.files.length ||
      files.some(f =>
        this.state.files.find(file => file.uid === f.uid && (file.progress !== f.progress || file.url !== f.url))) ||
      (single
        && files.length === this.state.files.length
        && files.length === 1
        && (this.state.files[0].uid !== files[0].uid
          || this.state.files[0].url !== files[0].url))) {
      this.setState({ picvisible: false, files: files.map(newF => ({ ...newF })) })
    }
  }
  chooseFile = () => {
    GO_FILEMANAGER(this.props.onSelectFile)
  }
  getFileInfo = (path = `${deviceType === 2 ? RNFS.TemporaryDirectoryPath : `${RNFS.CachesDirectoryPath}/`}react-native-image-crop-picker`) => {
    const that = this
    RNFS.readDir(path) // On Android, use "RNFS.DocumentDirectoryPath" (MainBundlePath is not defined)
      .then((result) => {
        // 根据 字符 查找result数组
        const str = that.state.filePath.substring(that.state.filePath.lastIndexOf('/') + 1, that.state.filePath.length)
        // 遍历找到文件
        result.map((v) => {
          if (v.name === str) {
            this.props.onSelectPic(v)
          }
        })
      }).catch((err) => {
      })
  }
  showActionSheet = () => {
    const that = this
    const BUTTONS = ['拍照', '从相册选择文件', '取消']
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        // destructiveButtonIndex: BUTTONS.length - 2,
        // title: '标题',
        message: '请选择您的附件',
        'data-seed': 'logId',
      },
      (buttonIndex) => {
        this.setState({ clicked: BUTTONS[buttonIndex] })
        // console.log(buttonIndex);
        if (buttonIndex == 0) { // 调用拍照
          ImagePicker.openCamera({
            width: 300,
            height: 400,
            cropping: true,
          }).then((image) => {
            this.setState({
              filePath: image.path,
            })
            if (image.path) {
              that.getFileInfo()
            }
          })
        } else if (buttonIndex == 1) { // 调用选择图片
          ImagePicker.openPicker({
            width: 300,
            height: 400,
            cropping: true,
          }).then((image) => {
            this.setState({
              filePath: image.path,
            })
            if (image.path) {
              that.getFileInfo()
            }

          //  this._onChange(image)
          }).catch((error) => { // 执行失败
            console.error(error)
          })
        }
      },
    )
  }
  currentUid = ''
  currentId = ''
  render =() => {
    const { files } = this.state
    return (
      <View>
        <View style={styles.resizeMode}>
          {
            files && files.length !== 0 ?
              <Carousel
                dots={!this.props.single}
                selectedIndex={0}
                infinite={files.length > 1}
              >
                {
            files.map((img) => {
              if (img.progress === 100) {
                return (
                  <TouchableHighlight
                    onPress={(e) => {
                      this.setState({ picvisible: true, currentUrl: img.url })
                      this.currentUid = img.uid
                      this.currentId = img.id
                    }}
                    key={img.uid}
                  >
                    <Image
                      style={styles.resizeMode}
                      resizeMode="contain"
                      source={{ uri: this.props.isPrivate ? img.url : `${img.url}?x-oss-process=image/resize,h_${Math.floor(height / 2)}` }}
                      key={img.uid}
                    />
                  </TouchableHighlight>
                )
              }
                return <Progress percent={img.progress} position="normal" unfilled="hide" appearTransition />
            })
        }

              </Carousel> :
              <TouchableHighlight onPress={() => this.showActionSheet()}>
                <View style={styles.resizeModeMT}>
                  <AGZIcon name="add-circle-outline" height={50} width={50} color="black" backcolor="white" iconSize={40} />
                </View>
              </TouchableHighlight>
        }
        </View>
        <Modal
          visible={this.state.picvisible}
          onClose={() => { this.setState({ picvisible: false }) }}
          closable
        >
          <View style={{ width, height }}>
            <TouchableHighlight
              onPress={(e) => {
                    this.setState({ picvisible: false })
                  }}
            >
              <Image
                style={styles.resizeModePreview}
                resizeMode="contain"
                source={{ uri: this.state.currentUrl }}
              />
            </TouchableHighlight>
            <View style={{ flexDirection: 'row', width: 50, marginLeft: (width - 50) / 2 }}>
              <TouchableHighlight onPress={() => this.props.deleteFile(this.currentUid, this.currentId)}>
                <View >
                  <AGZIcon name="trash" height={23} width={23} color="gray" backcolor="white" iconSize={21} />
                </View>
              </TouchableHighlight>
              <TouchableHighlight onPress={() => this.showActionSheet()}>
                <View >
                  <AGZIcon name="add-circle-outline" height={23} width={23} color="gray" backcolor="white" iconSize={21} />
                </View>
              </TouchableHighlight>
            </View>

          </View>
        </Modal>
      </View>
    )
  }
}

export default PicList
