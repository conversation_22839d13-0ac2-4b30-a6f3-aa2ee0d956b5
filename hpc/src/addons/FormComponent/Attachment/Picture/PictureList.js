/* eslint-disable no-unused-expressions */
/* eslint-disable no-await-in-loop */
import React from 'react'
import PropTypes from 'prop-types'
import { View, Image, StyleSheet, Alert, BackHandler, Platform, Animated, Easing, Text, TouchableOpacity, ToastAndroid, Dimensions, StatusBar, AsyncStorage } from 'react-native'
import { Carousel, Toast } from '@ant-design/react-native'
import NavigationService from '../../../../NavigationService'

import { getUpdateValidationFn } from '../../common'
import SelectFile from './SelectFile'
import { pictureListStyles } from './style'
import { addFile, removeFile } from '../store'
import { uploadfile, deletefile, downloadfile, getFileUrl, isPrivate } from '../uploadFiles'
import ImageLoader from '../../../../components/ImageLoader'
import { debounce } from '../util'

const { width, height } = Dimensions.get('window')
const random = () => Math.floor((Math.random() + (Math.floor(Math.random() * 9) + 1)) * Math.pow(10, 9))

const ToastInfo = (msg, duration = 1) => {
  if (Platform.OS === 'android') {
    ToastAndroid.show(msg, ToastAndroid.SHORT)
  } else {
    Toast.info(msg, duration)
  }
}

const style = StyleSheet.create(pictureListStyles)
const border = {
  borderWidth: 2,
  borderColor: '#4CBDFF',
}

class PictureList extends React.PureComponent {
  state = {
    selectPic: this.props.existFile && this.props.existFile.length !== 0 ? this.props.existFile[this.props.existFile.length - 1] : {},
    fileList: this.props.existFile,
    rotateValue: new Animated.Value(0),
  }
  componentWillMount = () => {
    const {
      fieldId, updateValidation, showActionButton, single, cameraMsg = {},
    } = this.props
    if (showActionButton) {
      SelectFile(this.selectFileCallBack, { multiple: !single, ...cameraMsg })
    }
    this.updateValidation = getUpdateValidationFn(updateValidation, fieldId)
  }
  componentDidMount() {
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', async () => {
      if (this.props.changePhoto) await this.props.changePhoto(this.state.fileList)
      NavigationService.back()
      return true
    })
  }

  componentWillUnmount() {
    this.backHandler.remove()
  }

  // eslint-disable-next-line react/sort-comp
  deleteFile = async (fileId, id, name) => {
    if (fileId instanceof Array) {
      await this.deleteFiles(fileId)
      return
    }
    const {
      appId, fieldId, formId, dataId,
    } = this.props
    const { fileList } = this.state
    this.fileList = fileList
    const toDeltefs = this.fileList.filter(f => f.name === name).map(f => f)
    for (let i = 0; i < toDeltefs.length; i += 1) {
      const { uid: fid, id: dId } = toDeltefs[i]

      const index = this.fileList.findIndex((file => file.uid === fid))
      if (index !== -1) {
        // eslint-disable-next-line no-await-in-loop
        await deletefile(fid, {
          appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0,
        })
        // eslint-disable-next-line no-await-in-loop
        await removeFile(appId, dId)
        this.fileList = [...this.fileList.slice(0, index),
        ...this.fileList.slice(index + 1),
        ]
      }
    }
    this.updateValue()
    Toast.success('删除成功')
  }
  download = async (fileId, fileName) => {
    const {
      appId, fieldId, formId, dataId,
    } = this.props
    await downloadfile(fileId, fileName, (f) => {
      this.uploadFileCallBack(f ? f.currentSize / f.totalSize : 1, { uid: fileId, name: fileName })
    }, {
      appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0,
    })
  }
  beforeUpload = async (file) => {
    const {
      appId, formId, fieldId, dataId, single,
    } = this.props
    const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')

    if (single) {
      const uids = []
      for (let i = 0; i < this.state.fileList.length; i += 1) {
        const tmpfile = this.state.fileList[i]
        uids.push(tmpfile.uid)
        await removeFile(appId, tmpfile.id)
      }
      uids.length !== 0 && await deletefile(uids[0], {
        appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0,
      })
      const tempFile = {
        uid: file.uid,
        name: file.name,
        url: '',
        progress: 0,
        type: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
        size: file.size,
      }
      this.fileList = [tempFile]
      this.setState({
        fileList: this.fileList,
        selectPic: tempFile,
      })
    } else {
      this.fileList = [
        {
          uid: file.uid,
          name: file.name,
          url: '',
          progress: 0,
          type: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
          size: file.size,
        },
        ...this.state.fileList,
      ]
      this.setState({
        fileList: this.fileList,
      })
    }

    uploadfile(file.uid, file, (f) => {
      this.uploadFileCallBack(f ? f.currentSize / f.totalSize : 1, file)
    }, {
      appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: file.size,
    }).then(async (f) => {
      if (f) {
        // const ossUrl = `https://${bucket}.${region}.aliyuncs.com/${file.uid}`
        let user = await AsyncStorage.getItem('result')
        var path = appId === '0' ?
          `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
          : `${appId}/${formId}/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
        const ossUrl = getFileUrl(path, fileNameSplit[fileNameSplit.length - 1].toLowerCase())
        // const ossUrl = getFileUrl(file.uid, fileNameSplit[fileNameSplit.length - 1].toLowerCase())
        this.uploadFileCallBack(1, { uid: file.uid, url: ossUrl }, 'done')
        addFile(appId, {
          appId,
          attachmentId: fieldId,
          boRecordId: dataId || '',
          fileName: file.name,
          fileSize: file.size,
          formId,
          ossFileName: file.uid,
          ossUrl,
          remark: '',
          fileType: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
        }).then((res) => {
          const fileList = this.fileList
          const currFile = fileList.find(item => item.uid === file.uid)
          const index = fileList.findIndex(item => item.uid === file.uid)
          this.fileList = [
            ...fileList.slice(0, index),
            { ...currFile, id: res.fileId },
            ...fileList.slice(index + 1),
          ]
          this.updateValue()
        })
      } else {
        this.uploadFileCallBack(0, file, 'error')
      }
    })
    return false
  }

  setFiles = (files) => {
    const uid = `${random()}`
    if (files instanceof Array) {
      let datas = files
      if (this.props.cameraMsg && this.props.cameraMsg.photoclock) {
        const dif = 3 - this.state.fileList.length
        if (files.length > dif) ToastInfo('最多上传三张照片')
        datas = files.slice(0, dif)
      }
      datas.forEach((file, index) => {
        Object.assign(file, { uid: `${uid}-${index + 1}` })
        this.beforeUpload(file)
      })
    } else {
      Object.assign(files, { uid: `${uid}-${1}` })
      this.beforeUpload(files)
    }
  }
  updateValue = debounce(() => {
    const { changeValue, fieldId } = this.props
    const fileIds = this.fileList.filter(({ id }) => id).map(({ id }) => id)
    this.setState({ fileList: this.fileList })
    if (changeValue) changeValue({ [fieldId]: { defaultValue: fileIds } })
    this.updateValidation = this.updateValidation(this.props.required, fileIds)
  }, 500)

  uploadFileCallBack = (progress, f, type) => {
    const fileList = this.fileList
    const currFile = fileList.find(file => file.uid === f.uid || file.uid === f.name)
    const index = fileList.findIndex(file => file.uid === f.uid || file.uid === f.name)
    let url = currFile.url
    if (type === 'done' && currFile.url === '') {
      url = f.url
    } else return
    if (index !== -1) {
      this.fileList = [
        ...fileList.slice(0, index),
        {
          ...currFile,
          progress: Math.floor((progress * 100) % 100),
          url,
          status: type === 'error' ? 'exception' : (progress === 1 ? 'success' : 'active'),
        },
        ...fileList.slice(index + 1),
      ]
      this.setState({
        fileList: this.fileList,
      })
    }
  }

  selectFileCallBack = async (file) => {
    this.state.rotateValue.setValue(0)
    Animated.timing(this.state.rotateValue, {
      toValue: 360 * 1000,
      duration: 800 * 1000,
      easing: Easing.linear,
    }).start()// 开始spring动画
    this.setFiles(file)
  }
  handleDeletefile = () => {
    if (!this.props.enabled) {
      ToastInfo('没有权限！！')
      return
    }
    Alert.alert('确定删除?', '', [
      { text: '否', onPress: () => { } },
      {
        text: '是',
        onPress: () => {
          if (this.state.selectPic && this.state.selectPic.uid) {
            const reverseFiles = []
            if (this.state.fileList) {
              this.state.fileList.filter(f => f.version !== '1').forEach((file) => { reverseFiles.unshift(file) })
            }
            const { uid, id, name } = this.state.selectPic
            if (this.state.fileList.length > 1) {
              const index = reverseFiles.findIndex(f => f.uid === this.state.selectPic.uid)
              if (index === reverseFiles.length - 1) {
                this.setState({ selectPic: reverseFiles[0] })
              } else {
                this.setState({ selectPic: reverseFiles[index + 1] })
              }
            } else {
              this.setState({ selectPic: {} })
            }
            this.deleteFile(uid, id, name)
          }
        },
      },
    ])
  }
  titleRender = () => {
    const reverseFiles = []
    if (this.state.fileList) {
      this.state.fileList.filter(f => f.version !== '1').forEach((file) => { reverseFiles.unshift(file) })
    }
    return (
      <View style={style.headerContainer} >
        <TouchableOpacity
          style={style.headerleft}
          onPress={async () => {
            if (this.props.changePhoto) await this.props.changePhoto(this.state.fileList)
            NavigationService.back({ refresh: ({ selectFiles: this.state.fileList }) })
          }}
          activeOpacity={0.6}
        >
          <View >
            <Image source={require('../../../../images/icon-arrow-previous-light-default.png')} />
          </View>
        </TouchableOpacity>
        <View style={style.headerCenter}>
          <Text style={style.headerRightText}>
            {reverseFiles && reverseFiles.length !== 0 ? `${reverseFiles.findIndex(f => f.uid === this.state.selectPic.uid) + 1}/${reverseFiles.length}` : ''}
          </Text>
        </View>
        <TouchableOpacity
          style={style.headerRight}
          onPress={this.props.enabled && this.handleDeletefile}
          activeOpacity={0.6}
        >
          <View >
            {
              (this.state.fileList && this.state.fileList.length !== 0 && this.props.enabled) ?
                <Image source={require('../../../../images/icon-trash-outline-light-default.png')} />
                : undefined
            }
          </View>
        </TouchableOpacity>
      </View>)
  }
  imageLoaderRender = f => (
    <ImageLoader
      src={isPrivate ? f.url : `${f.url}?x-oss-process=image/resize,m_fixed,h_50,w_50`}
      textStyle={{ color: '#fff' }}
      cententStyle={(() => {
        if (this.state.selectPic && f.uid === this.state.selectPic.uid) {
          return {
            width: 50, height: 50, backgroundColor: '#000', ...border,
          }
        }
        return { width: 50, height: 50, backgroundColor: '#000' }
      })()}
      style={(() => {
        if (this.state.selectPic && f.uid === this.state.selectPic.uid) {
          return { width: 50, height: 50, ...border }
        }
        return { width: 50, height: 50 }
      })()
      }
    />
  )
  render = () => {
    const { fileList } = this.state
    const { cameraMsg = {} } = this.props
    const reverseFiles = []
    if (fileList) {
      fileList.filter(f => f.version !== '1').forEach((file) => { reverseFiles.unshift(file) })
    }
    const disable = cameraMsg.photoclock && reverseFiles.length > 2
    const bottomHieght = (Math.ceil(((reverseFiles ? reverseFiles.length : 0) + 1) / 6) * 60) + 10
    return (
      <View style={style.container}>
        <StatusBar
          backgroundColor="#1B1B1B"
          barStyle="light-content"
          translucent
        />
        {this.titleRender()}
        <View style={[style.contentContainer, { height: height - bottomHieght - 60 }]}>
          {
            reverseFiles && reverseFiles.length !== 0 ?
              <Carousel
                ref={(rf) => { this.carouseref = rf }}
                selectedIndex={0}
                pagination={undefined}
                afterChange={(index) => {
                  this.setState({ selectPic: reverseFiles[index] })
                }}
                dotStyle={{ opacity: 0 }}
              >
                {
                  reverseFiles.map((f) => {
                    if (f.url && f.url.indexOf('http') !== -1) {
                      return (
                        <View
                          key={f.uid}
                          style={{
                            backgroundColor: '#000', width, height: height - bottomHieght - 60, alignItems: 'center', justifyContent: 'center',
                          }}
                        >
                          <ImageLoader
                            key={f.uid}
                            resizeMode="contain"
                            src={isPrivate ? f.url : `${f.url}?x-oss-process=image/resize,h_${Math.floor((height - bottomHieght - 60) * 1.2)}`}
                            style={{ width, height }}
                            textStyle={{ color: '#fff' }}
                            cententStyle={{ backgroundColor: '#000', width, height: height - bottomHieght - 60 }}
                          />
                        </View>
                      )
                    }
                    return (
                      <View
                        key={f.uid}
                        style={{
                          backgroundColor: '#000', width, height: height - bottomHieght - 60, alignItems: 'center', justifyContent: 'center',
                        }}
                      >
                        <Animated.Image
                          style={{
                            width: 50,
                            height: 50,
                            transform: [{ rotate: this.state.rotateValue.interpolate({ inputRange: [0, 360], outputRange: ['0deg', '360deg'] }) }],
                          }}
                          resizeMode="contain"
                          source={require('../../../../images/icon-loading.png')}
                        />
                      </View>
                    )
                  })
                }
              </Carousel>
              :
              <View style={{
                width, height: height - bottomHieght - 60, alignItems: 'center', justifyContent: 'center',
              }}
              >
                <Image
                  source={require('../../../../images/placeholder-image-outline.png')}
                />
              </View>
          }

        </View>
        <View style={[style.plist, { height: bottomHieght }]}>

          {
            reverseFiles && reverseFiles.map((f, index) => {
              if (f.progress % 100 === 0 && f.url) {
                return (
                  <TouchableOpacity
                    key={f.uid}
                    onPress={() => {
                      if (!this.state.selectPic || (this.state.selectPic && f.uid !== this.state.selectPic.uid)) {
                        this.setState({ selectPic: f })
                        if (Platform.OS === 'android') {
                          this.carouseref.scrollviewRef.scrollTo({ y: 0, x: width * index }, false)
                        }
                        this.carouseref.updateIndex({ x: width * index, y: 0 })
                      }
                    }}
                  >
                    <View style={style.plistItem}>
                      {f.url && this.imageLoaderRender(f)}
                    </View>
                  </TouchableOpacity>
                )
              }
              return (
                <View style={style.plistItem} key={(f && f.uid) || index}>
                  <Animated.Image
                    style={{
                      width: 50,
                      height: 50,
                      transform: [{ rotate: this.state.rotateValue.interpolate({ inputRange: [0, 360], outputRange: ['0deg', '360deg'] }) }],
                    }}
                    resizeMode="contain"
                    source={require('../../../../images/icon-loading.png')}
                  />
                </View>)
            })
          }
          <TouchableOpacity
            onPress={() => {
              if ((reverseFiles && reverseFiles.length === 1 && this.props.single) || !this.props.enabled || disable) {
                ToastInfo('不能添加！！')
                return
              }
              SelectFile(this.selectFileCallBack, { multiple: !this.props.single, ...cameraMsg })
            }}
            activeOpacity={0.6}
          >
            <View style={style.plistItem}>
              <View
                style={{
                  width: 50,
                  height: 50,
                  flexDirection: 'row',
                  backgroundColor: '#fff',
                  justifyContent: 'center',
                  alignItems: 'center',
                  opacity: reverseFiles && reverseFiles.length === 1 && this.props.single ? 0.6 : 1,
                }}
              >
                <Image
                  source={reverseFiles && reverseFiles.length === 1 && this.props.single
                    ? require('../../../../images/icon-add-large-fill-press.png')
                    : require('../../../../images/icon-add-large-fill-default.png')}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    )
  }
}

PictureList.propTypes = {
  dataId: PropTypes.string,
  appId: PropTypes.string,
  formId: PropTypes.string,
  fieldId: PropTypes.string,
  changeValue: PropTypes.func,
  required: PropTypes.bool,
  showActionButton: PropTypes.bool,
  single: PropTypes.bool,
  existFile: PropTypes.arrayOf(PropTypes.object),
  enabled: PropTypes.bool,
  updateValidation: PropTypes.func,
  changePhoto: PropTypes.func,
  cameraMsg: PropTypes.object,
}
export default PictureList
