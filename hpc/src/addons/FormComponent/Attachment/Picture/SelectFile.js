import PropTypes from 'prop-types'
import React from 'react'
import { Platform } from 'react-native'
import { ActionSheet, Toast } from 'nagz-mobile-lib'
import ImagePicker from 'react-native-image-crop-picker'
import RNFS from 'react-native-fs'

import { deviceType } from '../../../../config/sysPara'

const getFileInfo = async (path, fdir) => {
  const fileName = path.substring(path.lastIndexOf('/') + 1, path.length)
  let tempfdir = fdir
  if (!fdir) {
    tempfdir = deviceType === 2 ? `${RNFS.TemporaryDirectoryPath}react-native-image-crop-picker` : path.replace('file://', '').replace(fileName, '')
  }
  const result = await RNFS.readDir(tempfdir)
  // 遍历找到文件
  return result.find(v => v.name === fileName)
}

const setImages = async (images, config = {}) => {
  if (config.multiple) {
    const files = []
    for (let i = 0; i < images.length; i += 1) {
      const image = images[i]
      if (image.path) {
        files.push(await getFileInfo(image.path))
      }
    }
    return files
  } else if (images.path) {
    return await getFileInfo(images.path)
  }
}
export default (onSelectFile, config = {}) => {
  let BUTTONS = ['拍照', '从相册选择文件', '取消']
  if (!config.fromAlbum && config.photoclock) {
    BUTTONS = ['拍照', '取消']
  }
  ActionSheet.showActionSheetWithOptions(
    {
      options: BUTTONS,
      cancelButtonIndex: BUTTONS.length - 1,
      title: '请选择您的附件',
      'data-seed': 'logId',
    },
    async (buttonIndex) => {
      if (buttonIndex === 0) { // 调用拍照
        const image = await ImagePicker.openCamera(config)
        const realfs = await setImages(config.multiple ? [image] : image, config)
        if (realfs) {
          onSelectFile(realfs)
        }
      } else if (!(!config.fromAlbum && config.photoclock) && buttonIndex === 1) {
        // 调用选择图片
        const images = await ImagePicker.openPicker(config)
        const realfs = await setImages(images, config)
        if (realfs) {
          onSelectFile(realfs)
        }
      }
    },
  )
}

