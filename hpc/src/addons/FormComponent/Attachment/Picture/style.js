import { Dimensions, Platform } from 'react-native'
import { deviceType } from '../../../../config/sysPara'

const { width, height } = Dimensions.get('window')

export const pictureViewStyles = {
  container: {
    width, height: (width * (9 / 16)) + 50,
  },
  imageItem: {
    width,
    height: width * (9 / 16),
  },
  nofileContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width,
    height: (width * (9 / 16)),
  },
  progress: {
    alignItems: 'center',
    justifyContent: 'center',
    width,
    height: (width * (9 / 16)),
    backgroundColor: '#FFFFFF',
    paddingTop: (width * (9 / 16)) / 2,
  },
}
export const pictureListStyles = {
  container: {
    width,
    height,
    backgroundColor: '#000',
  },
  headerContainer: {
    shadowColor: 'black',
    shadowOffset: { height: 0.38 },
    shadowRadius: 0.38,
    shadowOpacity: 0.1,
    elevation: 4,
    flexDirection: 'row',
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#1B1B1B',
    opacity: 0.8,
    height: Platform.OS === 'android' && Platform.Version < 21 ? 40 : 60,
    paddingTop: Platform.OS === 'android' && Platform.Version < 21 ? 0 : 20,
    width,
  },
  headerleft: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  headerCenter: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  headerRightText: {
    fontSize: 16,
    color: '#fff',
  },
  contentContainer: {
    width,
    backgroundColor: '#000',
  },
  plist: {
    bottom: 0,
    flexWrap: 'wrap',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1B1B1B',
    flex: 1,
    width,
    paddingLeft: (width % 60) / 2,
    paddingTop: 10,
  },
  plistItem: {
    width: 60,
    height: 50,
    marginBottom: 10,
    alignItems: 'center',
    alignSelf: 'center',
  },
  progress: {
    alignItems: 'center',
    justifyContent: 'center',
    width,
    height,
    backgroundColor: '#FFFFFF',
    paddingTop: height / 2,
  },
}
