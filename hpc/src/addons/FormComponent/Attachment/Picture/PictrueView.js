import PropTypes from 'prop-types'
import React from 'react'
import { View, Image, TouchableOpacity, StyleSheet, Platform, ToastAndroid, Dimensions } from 'react-native'
import { Toast, Carousel, Progress } from '@ant-design/react-native'
import { SimpleLine } from 'nagz-mobile-lib'
import { pictureViewStyles } from './style'
import { GO_PICTURELIST } from '../../../../utils/jump'

const style = StyleSheet.create(pictureViewStyles)

const { width } = Dimensions.get('window')
const ToastInfo = (msg, duration = 1) => {
  if (Platform.OS === 'android') {
    ToastAndroid.show(msg, ToastAndroid.SHORT)
  } else {
    Toast.info(msg, duration)
  }
}
class PictureView extends React.PureComponent {
  state = {
    currIndex: 0,
  }
  onPress = () => {
    GO_PICTURELIST({ ...this.props, existFile: this.props.files })
  }

  onPressFun = () => {
    const { files, enabled = true, inputBlur } = this.props
    const blurCallback = () => {
      if (enabled && ((files && files.length === 0) || !files)) {
        GO_PICTURELIST({ ...this.props, existFile: files, showActionButton: true })
      } else if (files && files.length !== 0) {
        GO_PICTURELIST({ ...this.props, existFile: files })
      } else {
        ToastInfo('不可更改')
      }
    }
    if (inputBlur) {
      inputBlur(blurCallback)
    } else {
      blurCallback()
    }
  }

  onPressFunc = () => {
    const { inputBlur } = this.props
    const blurCallback = () => {
      if (!this.props.enabled) {
        ToastInfo('不可更改')
        return
      }
      GO_PICTURELIST({ ...this.props, existFile: this.props.files, showActionButton: true })
    }
    if (inputBlur) {
      inputBlur(blurCallback)
    } else {
      blurCallback()
    }
  }

  renderDot = () => <View />

  render = () => {
    const { files, label, required, isPrivate } = this.props
    const reverseFiles = []
    if (files) {
      files.filter(f => f.version !== '0').forEach((file) => { reverseFiles.unshift(file) })
    }
    return (
      <View style={style.container}>
        <SimpleLine
          value={`${reverseFiles.length === 0 ? 0 : this.state.currIndex + 1}/${reverseFiles.length}`}
          label={label}
          editable={false}
          enabled={false}
          isRequire={required}
          onPress={this.onPressFun}
        />
        {reverseFiles && reverseFiles.length > 1 ?
          <Carousel
            selectedIndex={0}
            pagination={reverseFiles && reverseFiles.length === 1 ? () => <View /> : undefined}
            afterChange={index => this.setState({ currIndex: index })}
            dotActiveStyle={{ backgroundColor: '#17A9FF' }}
            dotStyle={{
              width: reverseFiles.length > 11 ? ((width * 0.8) / reverseFiles.length) - 10 : 20,
              height: 2,
              backgroundColor: '#FFFFFF',
              borderRadius: 0,
              opacity: 0.6,
            }}
          >
            {
              reverseFiles.map((file) => {
                if (file.progress % 100 === 0) {
                  const uri = isPrivate ? file.url : `${file.url}?x-oss-process=image/resize,h_${Math.floor(width * (9 / 16) * 1.2)}`
                  return (
                    <TouchableOpacity
                      key={file.uid}
                      onPress={this.onPress}
                      activeOpacity={1}
                    >
                      <Image
                        resizeMode="contain"
                        source={{ uri }}
                        style={style.imageItem}
                      />
                    </TouchableOpacity>
                  )
                }
                return (
                  <View style={style.progress} key={file.uid}>
                    <Progress
                      style={{ height: 5, backgroundColor: '#FFFFFF', width: width * 0.8 }}
                      percent={file.progress % 100}
                      position="normal"
                      appearTransition
                    />
                  </View>
                )
              })
            }
          </Carousel>
          :
          <View style={style.nofileContainer}>
            {reverseFiles && reverseFiles.length === 1 ?
              <TouchableOpacity
                onPress={this.onPress}
                activeOpacity={1}
              >
                <Image
                  resizeMode="contain"
                  source={{ uri: `${isPrivate ? reverseFiles[0].url : `${reverseFiles[0].url}?x-oss-process=image/resize,h_${Math.floor(width * (9 / 16) * 1.2)}`}` }}
                  style={style.imageItem}
                />
              </TouchableOpacity>
              :
              <TouchableOpacity
                onPress={this.onPressFunc}
                activeOpacity={0.6}
              >
                <Image
                  resizeMode="contain"
                  source={require('../../../../images/img-add-large-image_placeholder-default.png')}
                />
              </TouchableOpacity>}
          </View>
        }
      </View>
    )
  }
}
PictureView.propTypes = {
  files: PropTypes.arrayOf(PropTypes.object),
  label: PropTypes.string,
  required: PropTypes.bool,
  enabled: PropTypes.bool,
  isPrivate: PropTypes.bool,
  inputBlur: PropTypes.func,
}
export default PictureView
