import PropTypes from 'prop-types'
import React from 'react'
import { View, Platform, ToastAndroid, Image, Text, DeviceEventEmitter, AsyncStorage } from 'react-native'
import { SimpleLine } from 'nagz-mobile-lib'
import { Toast } from '@ant-design/react-native'
import { GO_FILELIST } from '../../../utils/jump'
import ModalView from './ModalView'
import { attachmentSetFiles } from '../../../utils/event/DeviceEventEmitter'
import { addFile, getFiles, removeFile, getInitValue } from './store'
import { uploadfile, deletefile, getFileUrl } from './uploadFiles'
import { isExpression } from '../../../utils/expression/transformExpression'

const random = () => Math.floor((Math.random() + (Math.floor(Math.random() * 9) + 1)) * Math.pow(10, 9))

const ToastInfo = (msg, duration = 1) => {
  if (Platform.OS === 'android') {
    ToastAndroid.show(msg, ToastAndroid.SHORT)
  } else {
    Toast.info(msg, duration)
  }
}
class Attachment extends React.PureComponent {
  componentDidMount = () => {
    const { operator, joinfieldvalue } = this.props
    const attachmentCmp = this
    this.subscription = DeviceEventEmitter.addListener(attachmentSetFiles, (fileid, file) => {
      if (fileid === attachmentCmp.props.fieldId) {
        this.setFiles(file)
      }
    })
    if (operator === '10' && joinfieldvalue && !isExpression(joinfieldvalue)) {
      this.changeFileWithJoinField(joinfieldvalue)
    }
  }
  componentWillUnmount() {
    this.subscription.remove()
  }
  componentWillReceiveProps = async ({ joinfieldvalue }) => {
    if (joinfieldvalue !== this.props.joinfieldvalue) {
      this.changeFileWithJoinField(joinfieldvalue)
    }
  }
  changeFileWithJoinField = async (joinfieldvalue) => {
    const { appId, source, joinformvaluefield, joinfield, joinformdisplayfield, formId, fieldId } = this.props
    await this.removeAllFiles()
    this.copyFilesFromOtherField(appId, { source, joinformvaluefield, joinfield, joinformdisplayfield, formId, fieldId, joinfieldvalue })
  }
  removeAllFiles = async () => {
    const { appId, formId, dataId, fieldId } = this.props
    const { records: fileList } = await getFiles(appId, formId, dataId, fieldId)
    if (fileList && fileList.length !== 0) {
      const uIds = []
      fileList.forEach((f) => { f.uid && uIds.push(f.uid) })
      this.deleteAllFiles(uIds, fileList)
    }
  }
  deleteAllFiles = async (fileList) => {
    const { appId, fieldId, formId, dataId, changeValue } = this.props
    for (let i = 0; i < fileList.length; i += 1) {
      const dfile = fileList[i]
      await deletefile(dfile.uid, { appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0 })
      await removeFile(appId, dfile.id)
    }
    if (changeValue) changeValue({ [fieldId]: { defaultValue: [] } })
  }
  copyFilesFromOtherField = async (appId, copyProps) => {
    const resData = await getInitValue(appId, copyProps)
    if (resData && resData.length !== 0) {
      const promis = []
      for (let i = 0; i < resData.length; i += 1) {
        const { fileName: name, fileSize, ossFileName: uid, version } = resData[i]
        promis.push(this.uploadfiledone(true, { name, uid, size: fileSize, version }))
      }
      Promise.all(promis).then((allres) => {
        this.props.changeValue({ [this.props.fieldId]: { defaultValue: allres.filter(r => r) } })
      })
    }
  }
  uploadfiledone = async (f, file) => {
    if (f) {
      const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')
      const { appId, formId, fieldId, dataId, changeValue, defaultValue } = this.props
      let user = await AsyncStorage.getItem('result')
      var path = appId === '0' ?
        `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
        : `${appId}/${formId}/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
      const ossUrl = getFileUrl(path, fileNameSplit[fileNameSplit.length - 1].toLowerCase())
      const res = await addFile(appId, {
        appId,
        attachmentId: fieldId,
        boRecordId: dataId || '',
        fileName: file.name,
        fileSize: file.size,
        formId,
        ossFileName: file.uid,
        ossUrl,
        remark: '',
        fileType: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
      })
      return res.fileId
    }
    this.uploadFileCallBack(0, file, 'error')
    return false
  }
  onPress = () => {
    const { defaultValue, enabled, inputBlur } = this.props
    const blurCallback = () => {
      if (!enabled && defaultValue.length === 0) {
        ToastInfo('不可更改')
      } else {
        this.handlePress()
      }
    }
    if (inputBlur) {
      inputBlur(blurCallback)
    } else {
      blurCallback()
    }
  }

  onLayout = (e) => {
    this.cmpHeight = e.nativeEvent.layout.height
  }

  setFiles = async (file) => {
    const { appId, formId, fieldId, dataId } = this.props
    const uid = `${random()}`
    Object.assign(file, { uid: `${uid}-${file.name}` })
    uploadfile(file.uid, file, () => { }, {
      appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: file.size,
    }).then(async (f) => {
      if (f) {
        const fileNameSplit = file instanceof Array ? file[0].name.split('.') : file.name.split('.')
        // const ossUrl = `https://${bucket}.${region}.aliyuncs.com/${file.uid}`

        let user = await AsyncStorage.getItem('result')
        var path = appId === '0' ?
          `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
          : `${appId}/${formId}/${JSON.parse(user).userId}/${fileNameSplit[fileNameSplit.length - 1].toLowerCase()}/${file.uid}`
        const ossUrl = getFileUrl(path, fileNameSplit[fileNameSplit.length - 1].toLowerCase())
        addFile(appId, {
          appId,
          attachmentId: fieldId,
          boRecordId: dataId || '',
          fileName: file.name,
          fileSize: file.size,
          formId,
          ossFileName: file.uid,
          ossUrl,
          remark: '',
          fileType: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
        }).then((res) => {
          this.updateValue(res.fileId)
        })
      }
    })
  }

  updateValue = (fileId) => {
    const { changeValue, defaultValue, fieldId } = this.props
    const value = [fileId, ...defaultValue]
    if (changeValue) changeValue({ [fieldId]: { defaultValue: value } })
  }

  handlePress = () => {
    GO_FILELIST({ ...this.props, isDownload: this.props.download, isDelete: this.props.delete })
  }
  render = () => {
    const { label, defaultValue, picture = false, required } = this.props
    return (
      <View onLayout={this.onLayout}>
        {picture ?
          <ModalView {...this.props} />
          :
          <SimpleLine
            label={label}
            onPress={this.onPress}
            isRequire={required}
            rightIconView={
              <Image
                resizeMode="center"
                source={require('../../../images/icon-arrow-right-small-default.png')}
              />
            }
          >
            <Text>{`共 ${defaultValue.length} 个`}</Text>
          </SimpleLine>}
      </View>
    )
  }
}

Attachment.propTypes = {
  defaultValue: PropTypes.arrayOf(PropTypes.string),
  label: PropTypes.string,
  picture: PropTypes.bool,
  required: PropTypes.bool,
  enabled: PropTypes.bool,
  inputBlur: PropTypes.func,
  changeValue: PropTypes.func,
  fieldId: PropTypes.string,
}

export default Attachment
