/* eslint-disable no-unused-expressions */
import { Toast } from '@ant-design/react-native'
import RNFS from 'react-native-fs'
import { Platform } from 'react-native'
import { envParm } from '../../../config/sysPara'
import { upload, del } from '../../../request'

export const uploadfile = async (key1, file) => {
  const key = `${key1}`
  const result = await upload(`/captcha/upload/${key}`)({ data: { file: { ...file, uri: Platform.OS === 'android' ? `file://${file.path}` : file.path, type: 'multipart/form-data', name: encodeURI(file.name) } } })
  if (result.errorCode === '0') {
    return { name: key, url: '' }
  }
  Toast.fail(result.errorMsg)
  return ''
}

export const tt = () => {}

export const downFile = async (key, filename, localPath, callBack) => {
  const ress = await RNFS.exists(localPath)
  if (ress !== true) {
    await RNFS.mkdir(localPath)
  }
  const toFile = Platform.OS === 'android' ? `file://${localPath}/${filename}` : `${localPath}/${filename}`
  console.log(toFile)
  const options = {
    fromUrl: `${envParm.URLS}/captcha/download/${key}`,
    toFile,
    background: true,
    progressDivider: 5,
    begin: (res) => {
      // console.log('begin', res)
      callBack && callBack({ currentSize: 1, totalSize: 10 })
    },
    progress: (res) => {
      // console.log('progress', res)
      // console.log(res)
    },
  }
  const ret = RNFS.downloadFile(options)
  ret.promise.then((res) => {
    // 下载完成时执行
    callBack && callBack({ currentSize: 10, totalSize: 10 })
    return res
  }).catch((err) => {
    // 下载出错时执行
    console.log(err)
    return err
  })
}

// eslint-disable-next-line no-return-await
export const deleteFile = async key => await del(`/captcha/delete/${key}`)()
