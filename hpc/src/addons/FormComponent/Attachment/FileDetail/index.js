import PropTypes from 'prop-types'
import React from 'react'
import { View, Text, TouchableOpacity, Dimensions, StyleSheet, Image, NativeModules } from 'react-native'
import Icon from 'react-native-vector-icons/FontAwesome'
import RNFS from 'react-native-fs'
import { Progress } from '@ant-design/react-native'
import NavigationService from '../../../../NavigationService'
import { headerStyle } from '../style'
import { getFont } from '../util'
import ModalHeader from '../../ModalHeader'
import { downloadfile } from '../uploadFiles'
import { deviceType } from '../../../../config/sysPara'

const prevIcon = require('../../../../images/icon-arrow-previous-default.png')
const closeIcon = require('../../../../images/icon-close-light-fill-default.png')

const { width, height } = Dimensions.get('window')
const style = StyleSheet.create(headerStyle)

class FileDetail extends React.PureComponent {
  state={
    exist: false,
    progress: 100,
  }
  componentDidMount=() => {
    this.initFile()
  }
  uploadFileCallBack = (progress, f, type) => {
    if (f.uid === this.props.file.uid) {
      if (type === 'done' || progress * 100 === 100) {
        this.setState({ progress: 100, exist: true })
      } else {
        this.setState({ progress: progress * 100 })
      }
    }
  }

  download = (fileId, fileName) => {
    const {
      appId, fieldId, formId, dataId,
    } = this.props
    downloadfile(fileId, fileName, (f) => {
      this.uploadFileCallBack(f ? f.currentSize / f.totalSize : 1, { uid: fileId, name: fileName })
    }, {
      appId, formId, attachmentId: fieldId, recordId: dataId || '', fileSize: 0,
    })
  }

  initFile = () => {
    const { file } = this.props
    if (getFont(file.type) !== 'file-picture-o') {
      const path = deviceType === 2 ?
        `${RNFS.DocumentDirectoryPath}/Download/${file.name}`
        : `${RNFS.ExternalStorageDirectoryPath}/HuaPuC/${file.name}`
      console.log(path)
      RNFS.exists(path).then((res) => {
        if (res) {
          this.setState({ exist: true })
        }
      })
    }
  }
  openFile=() => {
    const { file, isDownload } = this.props
    console.log(NativeModules)
    if (this.state.exist) {
      if (NativeModules.OpenFile) {
        NativeModules.OpenFile.openFile(file.name, `.${file.type}`)
      } else if (NativeModules.FileOpener) {
        NativeModules.FileOpener.open(
          `${RNFS.DocumentDirectoryPath}/Download/${file.name}`,
          '*/*',
        ).then(() => { console.log(11) }).then(() => { console.log(22) })
      }
    } else {
      isDownload && this.download(file.uid, file.name)
    }
  }
  modalHeaderRender=() => {
    const { file } = this.props
    const isImage = getFont(file.type) === 'file-picture-o'
    return ({
      centerText: isImage ? file.name : '附件预览',
      leftView: (
        <TouchableOpacity onPress={NavigationService.back}>
          <View style={style.leftContainer}>
            <Image source={isImage ? closeIcon : prevIcon} />
            {!isImage && <Text style={style.leftText}>返回</Text>}
          </View>
        </TouchableOpacity>),
      rightView: (
        <View style={style.rightContainer} />
      ),
    })
  }
  render=() => {
    const { file, isDownload } = this.props
    const isImage = getFont(file.type) === 'file-picture-o'
    return (
      <View>
        <ModalHeader
          header={this.modalHeaderRender()}
          style={{ backgroundColor: isImage ? '#000' : '#fff' }}
          centerTextStyle={{ color: isImage ? '#fff' : '#41454b' }}
        />
        {isImage ?
          <View style={{ height: height - 44, width, backgroundColor: '#1B1B1B' }}>
            <Image
              style={{ width, height: height - 50 }}
              resizeMode="contain"
              source={{ uri: file.url }}
            />
          </View>
          :
          <View style={{
 height: (height - 44) * 0.7, width, alignItems: 'center', justifyContent: 'center',
}}
          >
            <Icon name={getFont(file.type)} size={100} />
            <Text style={{ marginTop: 26, fontSize: 16, color: '#41454B' }}>{file.name}</Text>

            {this.state.progress === 100 ? <TouchableOpacity onPress={this.openFile}>
              {
                this.state.exist ?
                  <Text style={{ fontSize: 20, marginTop: 20, color: '#17A9FF' }}>打开...</Text>
                :
                  <Text style={{ fontSize: 20, marginTop: 20, color: isDownload ? '#17A9FF' : '#999' }}>下载 ({(file.size / 1024 / 1024).toFixed(2)}M)</Text>
              }
            </TouchableOpacity>
            :
            <View style={{ width: width * 0.8, height: 5, marginTop: 20 }}>
              <Progress percent={this.state.progress} position="normal" appearTransition />
            </View>
            }
          </View>}
      </View>
    )
  }
}

FileDetail.propTypes = {
  file: PropTypes.shape({
    type: PropTypes.string,
    size: PropTypes.number,
    uid: PropTypes.string,
  }),
  appId: PropTypes.string,
  dataId: PropTypes.string,
  formId: PropTypes.string,
  fieldId: PropTypes.string,
}
export default FileDetail
