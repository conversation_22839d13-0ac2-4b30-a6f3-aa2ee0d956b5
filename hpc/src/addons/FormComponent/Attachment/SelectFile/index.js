import PropTypes from 'prop-types'
import React from 'react'
import { Image, TouchableOpacity, View, Platform, ToastAndroid, Text } from 'react-native'
import { ActionSheet } from 'nagz-mobile-lib'
import { Toast } from '@ant-design/react-native'
import RNFS from 'react-native-fs'
import ImagePicker from 'react-native-image-crop-picker'
import NavigationService from '../../../../NavigationService'
import { deviceType } from '../../../../config/sysPara'
import { GO_FILEMANAGER } from '../../../../utils/jump'
// import FileManager from '../../../../components/FileManager'
import { getFileInfo } from '../util'

const addIcon = require('../../../../images/icon-add-large-fill-default.png')

const ToastInfo = (msg, duration = 1) => {
  if (Platform.OS === 'android') {
    ToastAndroid.show(msg, ToastAndroid.SHORT)
  } else {
    Toast.info(msg, duration)
  }
}
class SelectFile extends React.PureComponent {
  state = {
    fileUid: '',
    isVisible: false,
  }

  onPress = () => {
    if (this.props.isEdit) {
      this.showEditAciontSheet()
    } else {
      this.showActionSheet()
    }
  }

  openPic = (width, height) => {
    ImagePicker.openPicker({
      width,
      height,
      cropping: true,
      cropperCircleOverlay: false,
      showCropGuidelines: true,
      cropperChooseText: '选择',
      cropperCancelText: '取消',
    }).then((image) => {
      if (image.path) {
        getFileInfo(
          image.path,
          this.props.onSelectFile,
          `${deviceType === 2 ? RNFS.TemporaryDirectoryPath
            : `${RNFS.CachesDirectoryPath}/`}react-native-image-crop-picker`,
        )
      }
    })
  }
  showActionSheet = () => {
    if (!this.props.enabled) {
      ToastInfo('不可更改')
      return
    }
    const BUTTONS = ['拍照', '从相册选择文件', '本地文件', '取消']
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        // destructiveButtonIndex: BUTTONS.length - 2,
        // title: '标题',
        title: '请选择您的附件',
        'data-seed': 'logId',
      },
      (buttonIndex) => {
        if (buttonIndex === 0) { // 调用拍照
          ImagePicker.openCamera({
            width: 300,
            height: 400,
            cropping: true,
            cropperChooseText: '选择',
            cropperCancelText: '取消',
          }).then((image) => {
            if (image.path) {
              getFileInfo(
                image.path,
                this.props.onSelectFile,
                `${deviceType === 2 ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker` : RNFS.PicturesDirectoryPath}`,
              )
            }
          })
        } else if (buttonIndex === 1) {
          // 调用选择图片
          console.log('sdhkjadhlkajsdlajdlasd')
          ImagePicker.openPicker({
            width: 300,
            height: 400,
            cropping: true,
            cropperChooseText: '选择',
            cropperCancelText: '取消',
          }).then((image) => {
            if (image.path) {
              getFileInfo(
                image.path,
                this.props.onSelectFile,
                `${deviceType === 2 ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker` : RNFS.PicturesDirectoryPath}`,
              )
            }

            //  this._onChange(image)
          }).catch((error) => { // 执行失败
            console.error(error)
          })
        } else if (buttonIndex === 2) { // 上传本地文件
          // 选择文件
          this.chooseFile()
        }
      },
    )
  }

  showEditAciontSheet = () => {
    const BUTTONS = !this.props.enabled ? ['下载', '取消'] : ['下载', '删除', '取消']
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        destructiveButtonIndex: !this.props.enabled ? undefined : 1,
        cancelButtonIndex: BUTTONS.length - 1,
        'data-seed': 'logId',
      },
      (buttonIndex) => {
        if (buttonIndex === 0) {
          this.props.downloadSelect()
        } else if (buttonIndex === 1 && this.props.enabled) {
          this.props.deleteSelect()
        }
      },
    )
  }

  chooseFile = () => {
    GO_FILEMANAGER((f) => {
      this.props.onSelectFile(f)
      NavigationService.back()
    })
  }

  render = () => (
    <View
      style={{ alignItems: 'center', justifyContent: 'center' }}
    >
      <TouchableOpacity onPress={this.onPress}>
        {this.props.isEdit ? <Text style={{ fontSize: 18, color: '#41454B' }}>操作</Text> : <Image style={{ opacity: !this.props.enabled ? 0.6 : 1 }} source={addIcon} />}
      </TouchableOpacity>
    </View>
  )
}

SelectFile.propTypes = {
  isEdit: PropTypes.bool,
  onSelectFile: PropTypes.func,
  downloadSelect: PropTypes.func,
  deleteSelect: PropTypes.func,
  enabled: PropTypes.bool,
}
export default SelectFile
