import PropTypes from 'prop-types';
import React from 'react';
import { Text,View } from 'react-native';
import { screenWidth } from '../../config/sysPara'
import styles from './styles'

export default function Label({ label,enabled, desc, style ,isRequired}) {
  // 这里是为了符合其他使用了label组件的组件 如果没有传入enable属性，那么默认为颜色不更改
  if (enabled === undefined) {
    enabled = true
  }
  return (
    <View style={{flexDirection:'row'}}>
      <Text style={{color:'red',opacity:isRequired ? 1:0}}>*</Text> 
      <Text style={ enabled ? style : [style, {color:'rgba(0,0,0,0.4)'}]} numberOfLines={1}>
        {String(label)}
      </Text>
    </View>
  )
}

Label.propTypes = {
  label: PropTypes.string.isRequired,
  desc: PropTypes.string,
  isRequired:PropTypes.bool,
}
