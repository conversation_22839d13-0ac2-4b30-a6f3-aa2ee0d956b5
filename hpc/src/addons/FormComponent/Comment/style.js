import { StyleSheet, Dimensions } from "react-native";

const { width, height } = Dimensions.get("window");

export const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    backgroundColor: "#fff",
  },
  commentsList: {},
  commentItem: {
    padding: 20,
    borderBottomColor: "rgba(0, 0, 0, 0.12);",
    borderBottomWidth: 1,
  },
  commentImg: {
    width: 20,
    height: 20,
  },

  commentName: {
    color: "#1890FF",
    fontSize: 14,
    fontWeight: "500",
  },
  commentTime: {
    marginRight: 7,

    color: "rgba(0,0,0,0.45)",
    fontSize: 14,
    fontWeight: "400",
  },
  commentContent: {

  
    color: "rgba(0,0,0,0.75)",
    fontSize: 14,
    fontWeight: "400",
  },
  
  subItemContainer: {
    marginTop:12,
    padding: 10,
    backgroundColor: "rgba(78,119,224,0.09)",
  },
  flexRow: {
    display: "flex",
    flexDirection: "row",
  },
  flexCol: {
    display: "flex",
    flexDirection: "column",
  },

  flexJc: {
    justifyContent: "center",
  },
  flexJcsb: {
    justifyContent: "space-between",
  },
  flexAc: {
    alignItems: "center",
  },
});
