import { View, Text, Image, TouchableOpacity } from "react-native";
import React, { useEffect } from "react";
import { useRequest, useSetState, useToggle, usePagination } from "ahooks";
import {
  Pagination,
  Button,
  ActivityIndicator,
  TextareaItem,
  Modal,
  Icon,
} from "@ant-design/react-native";
import {
  getAllcommentsServer,
  delCommentServer,
  sendCommentServer,
  getUserInfoByIdsServer,
} from "@request/api";
import dayjs from "dayjs";
import { uniqBy as _uniqBy } from "lodash";
import replyImg from "../../../images/icon-comment-default.png";
import { getAppUserId } from "../../../utils/storage";
import { styles } from "./style";
const defaultPageSize = 5;
const SubCommnetItem = ({
  subItem,
  parentItem,
  showReplyHandle,
  deleteReplyHandle,
  userId,
}) => {
  const addReply = () => {
    showReplyHandle(subItem.id);
  };
  const deleteReply = () => {
    deleteReplyHandle(parentItem.id, subItem.id);
  };
  const isVisitor = subItem.creator === "111";
  const leftName = subItem.creator === "111" ? "游客" : subItem.userName;
  let rightName = "";
  const isReplyParent = subItem.refId === parentItem.id;
  if (isReplyParent) {
    rightName = parentItem.userName;
  } else {
    const targetItem = parentItem.subs.find((i) => i.id === subItem.refId);
    rightName = targetItem.creator === "111" ? "游客" : subItem.userName;
  }
  return (
    <View style={[styles.flexRow, styles.flexJcsb, { marginTop: 6 }]}>
      <View style={[styles.flexRow, { flex: 1 }]}>
        <Text style={styles.commentName}>{leftName}</Text>
        <Text style={[styles.commentContent, { marginHorizontal: 4 }]}>
          回复
        </Text>
        <Text style={styles.commentName}>{rightName}: </Text>
        <Text style={[styles.commentContent, { flex: 1 }]}>
          {subItem.content}
        </Text>
      </View>

      {userId === subItem.creator && !isVisitor ? (
        <Icon
          onPress={deleteReply}
          style={{ fontSize: 16 }}
          color="red"
          name="delete"
        ></Icon>
      ) : (
        <TouchableOpacity onPress={addReply}>
          <Image source={replyImg} style={{ width: 18, height: 18 }}></Image>
        </TouchableOpacity>
      )}
    </View>
  );
};
const CommentItem = ({ item, userId, showReplyHandle, deleteReplyHandle }) => {
  const addReply = () => {
    showReplyHandle(item.id);
  };
  const deleteReply = () => {
    deleteReplyHandle(item.id);
  };
  const isVisitor = item.creator === "111";
  return (
    <View style={styles.commentItem}>
      <View style={[styles.flexRow, styles.flexJcsb, styles.flexAc]}>
        <View style={styles.flexRow}>
          {/* <Image source={styles.commentImg}></Image> */}
          <Text style={[styles.commentName, { marginRight: 7 }]}>
            {item.userName}
          </Text>
          <Text style={styles.commentTime}>
            {dayjs(Number(item.gmt_create)).format("HH:mm")}
          </Text>
        </View>
        {userId === item.creator && !isVisitor ? (
          <Icon
            onPress={deleteReply}
            style={{ fontSize: 16 }}
            color="red"
            name="delete"
          ></Icon>
        ) : (
          <TouchableOpacity onPress={addReply}>
            <Image source={replyImg} style={{ width: 18, height: 18 }}></Image>
          </TouchableOpacity>
        )}
      </View>
      <Text style={[styles.commentContent, { marginTop: 12, flex: 1 }]}>
        {item.content}
      </Text>
      {item?.subs?.length && (
        <View style={styles.subItemContainer}>
          {item?.subs?.map((sub) => (
            <SubCommnetItem
              userId={userId}
              subItem={sub}
              parentItem={item}
              showReplyHandle={showReplyHandle}
              deleteReplyHandle={deleteReplyHandle}
            ></SubCommnetItem>
          ))}
        </View>
      )}
    </View>
  );
};

const index = (props) => {
  const { appId, formId, dataId, fieldId } = props;
  const [state, setState] = useSetState({
    textAreaValue: "",
    userId: "",
  });
  const { data: userInfoData, runAsync: runGetUserInfo } = useRequest(
    getUserInfoByIdsServer
  );
  const {
    data,
    loading,
    pagination,
    runAsync: runGetComments,
    params,
    mutate,
  } = usePagination(
    ({ current }, pageSize) =>
      getAllcommentsServer(current, pageSize, appId, formId, dataId, fieldId),
    {
      manual: true,
      defaultPageSize: defaultPageSize,
    }
  );
  useEffect(() => {
    const loadData = async () => {
      const data = await runGetComments({ current: 1 }, defaultPageSize);
      console.log(data);
      const ids = _uniqBy(data.list, (i) => i.creator);
      console.log(ids.map((i) => i.userId).join(","));
      const info = await runGetUserInfo(ids.map((i) => i.userId).join(","));
      console.log(info);
      const res = await getAppUserId();
      setState({ userId: res });
    };
    loadData();
  }, []);
  function setTextAreaValue(str) {
    setState({
      textAreaValue: str,
    });
  }
  function prevPage() {
    if (pagination.current <= 1) {
      return;
    }
    runGetComments(
      {
        current: pagination.current - 1,
        // pageSize:defaultPageSize
      },
      defaultPageSize
    );
    // pagination.onChange(pagination.current - 1,defaultPageSize);
  }
  function nextPage() {
    if (pagination.current >= pagination.totalPage) {
      return;
    }

    const cpageSize =
      pagination.current + 1 === pagination.totalPage
        ? pagination.total - defaultPageSize * pagination.current
        : defaultPageSize;
    runGetComments(
      {
        current: pagination.current + 1,
        // pageSize:cpageSize
      },
      cpageSize
    );
    // pagination.onChange(pagination.current + 1,cpageSize);
  }
  async function addCommentHandle(v) {
    const data = {
      content: v || state.textAreaValue,
      formId,
      dataId,
      fieldId,
      refId: "0",
      type: 0,
    };
    const res = await sendCommentServer(appId, data);
    if (res?.id) {
      runGetComments(
        {
          current: 1,
        },
        defaultPageSize
      );
    }
  }
  function showAddCommentHandle() {
    Modal.prompt("新增回复", "", (v) => addCommentHandle(v), "default", "", [
      "请输入回复内容",
    ]);
  }
  function showReplyHandle(id) {
    Modal.prompt("新增回复", "", (v) => addReplyHandle(id, v), "default", "", [
      "请输入回复内容",
    ]);
  }
  async function deleteReplyHandle(id, subId) {
    const res = await delCommentServer(appId, subId || id);
    if (res) {
     const editParent= data.list.find(i=>i.id===id)
      // runGetComments(
      //   {
      //     current: pagination.current,
      //   },
      //   defaultPageSize
      // );
    }
  }

  async function addReplyHandle(id, v) {
    const data = {
      content: v,
      formId,
      dataId,
      fieldId,
      refId: id || "0",
      type: 1,
    };
    const res = await sendCommentServer(appId, data);
    console.log(res);
    if (res?.id) {
      runGetComments(
        {
          current: pagination.current,
        },
        defaultPageSize
      );
    }
  }
  console.log(state, data, loading, pagination);
  return (
    <View style={styles.container}>
      <View style={styles.flexCol}>
        {loading ? (
          <ActivityIndicator size="large" />
        ) : (
          data?.list.map((item) => (
            <CommentItem
              key={item.id}
              item={item}
              userId={state.userId}
              showReplyHandle={showReplyHandle}
              deleteReplyHandle={deleteReplyHandle}
            ></CommentItem>
          ))
        )}
        {data?.list?.length === 0 && (
          <View>
            <TextareaItem
              style={{ fontSize: 14 }}
              onChange={setTextAreaValue}
              value={state.textAreaValue}
              placeholder="暂时没有任何评论，请添加评论"
              rows={4}
              clear
            ></TextareaItem>
          </View>
        )}
      </View>
      <View
        style={[
          styles.flexRow,
          styles.flexJcsb,
          { paddingVertical: 10, paddingHorizontal: 20 },
        ]}
      >
        {!!data?.list?.length === 0 ? (
          <Button
            disabled={!state.textAreaValue}
            size="small"
            onPress={addCommentHandle}
          >
            回复
          </Button>
        ) : (
          [
            <View style={[styles.flexRow, styles.flexAc]}>
              <Button
                disabled={pagination.current <= 1}
                size="small"
                onPress={prevPage}
              >
                上一页
              </Button>
              <Pagination
                style={{ marginHorizontal: 10 }}
                mode="number"
                total={pagination.totalPage}
                current={pagination.current}
              />
              <Button
                disabled={pagination.current >= pagination.totalPage}
                size="small"
                onPress={nextPage}
              >
                下一页
              </Button>
            </View>,
            <TouchableOpacity onPress={showAddCommentHandle}>
              <Image
                source={replyImg}
                style={{ width: 18, height: 18 }}
              ></Image>
            </TouchableOpacity>,
          ]
        )}
      </View>
    </View>
  );
};

export default index;
