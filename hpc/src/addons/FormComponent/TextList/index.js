import PropTypes from 'prop-types'
import React from 'react'
import { Toast, Portal } from '@ant-design/react-native'
import { View, Text, TouchableOpacity } from 'react-native'
import Store from './store'
import styles from './style'
import { FormContext } from '../../../utils/reactContext'

class TextList extends React.PureComponent {
  constructor(props) {
    super(props)
    this.filterQuery = ''
  }
  state = {
    loading: false,
    dataSource: [],
    offset: 0,
  }
  static contextType = FormContext;

  componentWillMount = async () => {
    const tKey = Toast.loading('加载中...', 0)
    const { source } = this.props
    if (source) {
      await this.init()
      await this.reload()
    }
    Portal.remove(tKey)
    this.setState({ loading: true })
  }

  componentWillReceiveProps = async ({ filter, orderby, ignoreEmpty }) => {
    if (this.props.filter !== filter || this.props.orderby !== orderby || this.props.ignoreEmpty !== ignoreEmpty) {
      this.filterQuery = filter
      this.store.setIgnoreEmpty(ignoreEmpty)
      this.store.setOrders(orderby)
      this.store.setPage(0)
      this.reload()
    }
  }

  init = () => {
    this.filterQuery = this.props.filter
    this.initStore()
  }

  initStore = () => {
    const { appId, source, filter, ignoreEmpty, orderby, viewvisiblecolumn, maxRows } = this.props
    this.store = new Store({
      source: `/apps/${appId}/forms/${source}/datas`,
      filter,
      ignoreEmpty,
      orderby,
      page: this.state.offset,
      maxRows,
      viewvisiblecolumn,
    })
  }

  reload = async () => {
    this.store.setFilter('').addFilter(this.filterQuery)
    const data = await this.store.load()
    if (Object.keys(data).length !== 0) {
      const dataSource = data.items
      this.setState({ dataSource })
    } else {
      this.setState({ dataSource: [] })
    }
  }

  rowClick = (row) => {
    const { appId, source } = this.props
    this.context.goPage({ appId, formId: source, operator: '2', dataId: row.id.value })
  }

  clickMore = () => {
    if (this.props.setUrl) {
      const { formId, dataId, formState, filter } = this.props.setUrl
      if (formId !== this.props.formId || dataId !== this.props.dataId) {
        if (!formId) return Toast.info('目标表单不存在！', 1)
        if (formState === 'viewPage') {
          if (!dataId) return Toast.info('数据不存在！', 1)
          this.context.goPage({ appId: this.props.appId, formId, operator: '2', dataId })
        } else {
          this.context.goPage({ appId: this.props.appId, formId, operator: '1', filter })
        }
      }
    }
  }

  renderTitle=() => {
    const { label, morelabel, hidelabel, setUrl } = this.props
    return (
      <View style={styles.title}>
        <Text style={styles.titleText}>{hidelabel ? morelabel : label}</Text>
        {setUrl.formState ?
          <TouchableOpacity activeOpacity={0.8} onPress={this.clickMore}>
            <Text style={styles.titleMore}>查看更多</Text>
          </TouchableOpacity> : <Text />}
      </View>
    )
  }

  renderItem = (row) => {
    const { viewvisiblecolumn } = this.props
    const cols = viewvisiblecolumn.length
    return (
      <TouchableOpacity activeOpacity={0.8} key={row.id.value} onPress={() => { this.rowClick(row) }}>
        <View style={styles.itemLine}>
          <Text style={{ fontWeight: 'bold' }}> · </Text>
          {viewvisiblecolumn.map((field, i) => {
            const value = row[field].display
            if (i === 0) {
              return cols === 3 ?
                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', color: 'rgba(0,0,0,.65)' }}>
                  <Text>[</Text>
                  <Text style={{ maxWidth: 84 }} numberOfLines={1} ellipsizeMode="tail">{value}</Text>
                  <Text>]&nbsp;</Text>
                </View> : <Text style={{ flex: 1, color: 'rgba(0,0,0,.85)' }} numberOfLines={1} ellipsizeMode="tail">{value}</Text>
            }
            if (i === 1) {
              return <Text style={{ flex: 1, color: 'rgba(0,0,0,.85)', textAlign: cols === 2 ? 'right' : 'left' }} numberOfLines={1} ellipsizeMode="tail">{value}</Text>
            }
            if (i === 2) {
              return <Text style={{ color: 'rgba(0,0,0,.35)', textAlign: 'right' }} numberOfLines={1} ellipsizeMode="tail">{value}</Text>
            }
          })}
        </View>
      </TouchableOpacity>
    )
  }

  render() {
    const { minRows, maxRows } = this.props
    return (
      <View style={styles.container}>
        {this.renderTitle()}
        {this.state.loading ?
          <View style={{ minHeight: minRows * 28, maxHeight: maxRows * 28, overflow: 'hidden' }}>
            {this.state.dataSource.map(this.renderItem)}
          </View> :
          <View style={{ minHeight: minRows * 28, maxHeight: maxRows * 28, overflow: 'hidden' }} />
        }
      </View>
    )
  }
}

TextList.propTypes = {
  appId: PropTypes.string.isRequired,
  formId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  fieldId: PropTypes.string,
  source: PropTypes.string,
  label: PropTypes.string,
  morelabel: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  desc: PropTypes.string,
  isRunTime: PropTypes.bool,
  orderby: PropTypes.string,
  filter: PropTypes.string,
  searchcolumn: PropTypes.array, // eslint-disable-line
  viewvisiblecolumn: PropTypes.array, // eslint-disable-line
  hidelabel: PropTypes.bool,
  ignoreEmpty: PropTypes.bool,
  minRows: PropTypes.number,
  maxRows: PropTypes.number,
  setUrl: PropTypes.shape({
    formId: PropTypes.string.isRequired,
    formState: PropTypes.string.isRequired,
    dataId: PropTypes.string.isRequired,
    filter: PropTypes.string.isRequired,
  }),
}

TextList.defaultProps = {
  label: '',
  defaultValue: {},
  desc: '',
  isRunTime: false,
  fieldId: '',
  setUrl: {},
}


export default TextList
