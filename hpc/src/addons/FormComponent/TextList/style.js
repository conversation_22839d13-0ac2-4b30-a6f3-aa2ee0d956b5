import { StyleSheet, Platform } from 'react-native'
import { screenWidth } from '../../../config/sysPara'

const width = screenWidth - 40

export default styles = StyleSheet.create({
  container: {
    width: width + 20,
    margin: 10,
    paddingVertical: 20,
    paddingHorizontal: 10,
    borderRadius: 8,
    backgroundColor: '#fff',
    fontSize: 14,
    overflow: 'hidden',
  },
  title: {
    paddingHorizontal: 10,
    paddingTop: 10,
    paddingBottom: 7,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'rgba(0,0,0,0.85)',
  },
  titleMore: {
    fontSize: 12,
    color: '#868E96',
  },
  itemLine: {
    width: '100%',
    height: 28,
    lineHeight: 28,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  listWarp: {
    width: width + 20,
    margin: 10,
    borderRadius: 8,
    display: 'flex',
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: Platform.OS === 'ios' ? 64 : 44,
    paddingTop: Platform.OS === 'ios' ? 20 : 0,
    paddingRight: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
    elevation: 0,
    width: screenWidth,
    position: 'relative',
  },
  searchIcon: {
    position: 'absolute',
    left: (screenWidth) / 2,
    top: Platform.OS === 'ios' ? 32 : 12,
  },
  searchText: {
    flex: 1,
    textAlign: 'center',
    borderColor: '#F2F2F2',
    borderWidth: 1,
    borderRadius: 4,
    height: 28,
    padding: 0,
  },
  headerRight: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    width: 50,
    alignItems: 'center',
    position: 'relative',
  },
  headerSearchBut: {
    margin: 0,
    width: screenWidth - 40,
    height: 30,
    marginLeft: 20,
    marginRight: 20,
    position: 'absolute',
    backgroundColor: '#fff',
    borderColor: '#E5E5E5',
    zIndex: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    padding: 0,
    paddingLeft: 10,
    paddingRight: 10,
    fontSize: 16,
  },
})
