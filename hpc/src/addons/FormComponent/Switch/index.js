import PropTypes from 'prop-types'
import React from 'react'
import { View, StyleSheet, Text, TouchableWithoutFeedback } from 'react-native'
import { Switch as AntdSwitch } from 'nagz-mobile-lib'
import { screenWidth } from '../../../config/sysPara'

const fn = () => {}

const styles = StyleSheet.create({
  switch: {
    flex: 1,
    height: 50,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 20,
    paddingRight: 20,
    flexDirection: 'row',
  },
  title: {
    width: (screenWidth - 74) * 0.3,
    fontSize: 14,
    color: '#1b1b1b',
    fontWeight: 'bold',
  },
  fullTitle: {
    paddingLeft: 20,
    fontSize: 14,
    color: '#1b1b1b',
    fontWeight: 'bold',
  },
})

class Switch extends React.PureComponent {
  state={
    titleShowMore: false,
  }
  componentDidMount() {
      // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  changeValue = (checked) => {
    const { changeValue, inputBlur } = this.props
    const blurCallback = () => {
      if (changeValue) {
        changeValue({ [this.props.fieldId]: { defaultValue: checked } })
      }
    }
    if (inputBlur) {
      inputBlur(blurCallback)
    } else {
      blurCallback()
    }
  }
  showMoreTitle=() => {
    const { label } = this.props
    if (label.length > 5) {
      this.setState({
        titleShowMore: true,
      })
    }
  }
  hideMoreTitle=() => {
    const { label } = this.props
    if (label.length > 5) {
      this.setState({
        titleShowMore: false,
      })
    }
  }
  render() {
    const { label, isRunTime, enabled, hidelabel, isView } = this.props

    const disabled = isView ? true : (!isRunTime ? true : !enabled)

    return (
      <View style={styles.switch}>
        <TouchableWithoutFeedback onPressIn={this.showMoreTitle} onPressOut={this.hideMoreTitle}>
          <View>
            <Text numberOfLines={1} style={styles.title}>{hidelabel && isRunTime ? '' : label}</Text>
          </View>
        </TouchableWithoutFeedback>
        <AntdSwitch
          disabled={disabled}
          checked={this.props.defaultValue}
          onChange={this.changeValue}
        />
        {this.state.titleShowMore ? <View style={[{ height: 50, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff', flex: 1, position: 'absolute', top: 0, overflow: 'hidden' }]}>
          <Text numberOfLines={1} style={styles.fullTitle}>{hidelabel && isRunTime ? '' : label}</Text>
        </View> : null}
      </View>
    )
  }
}

Switch.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  hidelabel: PropTypes.bool,
  isView: PropTypes.bool,
  fieldId: PropTypes.string,
  enabled: PropTypes.bool,
  inputBlur: PropTypes.func,
}

Switch.defaultProps = {
  label: '',
  defaultValue: {},
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  enabled: true,
}

export default Switch
