import PropTypes from 'prop-types'
import React from 'react'
import { Toast } from '@ant-design/react-native'
import { View, StyleSheet, TouchableOpacity, Text, Linking } from 'react-native'

import SvgIcon from '../../../components/SvgIcon'
import { FormContext } from '../../../utils/reactContext'

import linkCmpStyles from './style'

const style = StyleSheet.create(linkCmpStyles)

// eslint-disable-next-line react/prop-types
const LinkItem = ({ disabled, data, goLink }) => {
  console.log('sadhjadlkajdjalksjdljd', disabled, data, goLink)

  const iconSplit = data.icon.split('|')
  const type = iconSplit[0]
  const name = iconSplit[1]
  if (disabled) {
    return (
      <TouchableOpacity onPress={() => { Toast.info('不可跳转') }}>
        <View style={style.linkItem}>
          <View style={style.linkItemImg} >
            <SvgIcon name={name} type={type} />
          </View>
          <Text numberOfLines={1} ellipsizeMode="tail" style={style.linkItemText}>{data.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }
  if (data.isElink) {
    return (
      <TouchableOpacity onPress={() => {
        // Toast.info('外部链接,不能跳转')
        var baiduURL = data.address
        Linking.canOpenURL(baiduURL).then(supported => {
          if (!supported) {
            console.warn('Can\'t handle url: ' + baiduURL);
          } else {
            return Linking.openURL(baiduURL);
          }
        }).catch(err => console.error('An error occurred', baiduURL));
      }}>
        <View style={style.linkItem}>
          <View style={style.linkItemImg} >
            <SvgIcon name={name} type={type} />
          </View>
          <Text numberOfLines={1} ellipsizeMode="tail" style={style.linkItemText}>{data.name}</Text>
        </View>
      </TouchableOpacity >
    )
  }
  return (
    <TouchableOpacity onPress={goLink}>
      <View style={style.linkItem}>
        <View style={style.linkItemImg} >
          <SvgIcon name={name} type={type} />
        </View>
        <Text numberOfLines={1} ellipsizeMode="tail" style={style.linkItemText}>{data.name}</Text>
      </View>
    </TouchableOpacity>
  )
}

class LinkCmp extends React.PureComponent {
  static contextType = FormContext

  goLink = async (setUrl) => {
    const { appId } = this.props
    const { formId, dataId, formState, filter } = setUrl
    if (this.props.formId !== formId || dataId !== this.props.dataId) {
      if (formState === 'viewPage') {
        this.context.goPage({ appId, formId, operator: '2', dataId })
      } else {
        this.context.goPage({ appId, formId, operator: '1', filter })
      }
    }
  }

  render = () => {
    const { linkSource, enabled, isLeftAlign } = this.props
    return (
      <View style={[style.container, { justifyContent: isLeftAlign ? 'flex-start' : 'center' }]}>
        {linkSource.map(item => <LinkItem disabled={!enabled} data={item} goLink={() => this.goLink(item.setUrl)} />)}
      </View>
    )
  }
}

LinkCmp.propTypes = {
  // eslint-disable-next-line react/require-default-props
  linkSource: PropTypes.arrayOf(PropTypes.object),
  enabled: PropTypes.bool.isRequired,
  isLeftAlign: PropTypes.bool.isRequired,
  appId: PropTypes.string.isRequired,
  formId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
}

LinkCmp.defaultProps = {
  dataId: '',
}

export default LinkCmp
