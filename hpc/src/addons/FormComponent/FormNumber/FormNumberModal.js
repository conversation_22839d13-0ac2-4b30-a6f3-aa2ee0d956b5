import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { View, Text, TextInput, Image, TouchableOpacity } from 'react-native'
import { <PERSON><PERSON>, Button } from '@ant-design/react-native'
import Style from './style'
import { deviceType } from '../../../config/sysPara'

const closeImg = require('../../../images/icon-close-outline-default.png')

class FormNumberModal extends Component {
  static propTypes = {
    isView: PropTypes.bool,
    enabled: PropTypes.bool,
    fieldId: PropTypes.string,
    numberflagdisplay: PropTypes.string,
    changeValue: PropTypes.func,
    closeModal: PropTypes.func,
    getFillnumber: PropTypes.func,
  }
  state={
    numberflagdisplay: this.props.numberflagdisplay,
  }
  getFillnumber = () => {
    this.props.getFillnumber()
    this.props.closeModal()
  }
  changeDisplay = (text) => {
    this.setState({
      numberflagdisplay: text,
    })
  }
  changeDisplayConfirm = async () => {
    const { changeValue, closeModal } = this.props
    if (changeValue) {
      changeValue('numberflagdisplay', this.state.numberflagdisplay)
    }
    // 先让当前输入框失去焦点，收回键盘，再执行关闭遮罩
    if (this.inputRef) {
      await this.inputRef.blur()
    }
    closeModal()
  }
  render() {
    const { enabled, isView, visible, mumbermark } = this.props
    return (
      <Modal
        visible={visible}
        transparent
        maskClosable={false}
        onRequestClose={this.props.closeModal}
        footer={null}
        className={Style.modalClass}
        wrapClassName={Style.modalClass}
      >
        <TouchableOpacity
          onPress={this.props.closeModal}
          style={{ height: 20, position: 'relative' }}
        >
          <Image style={{ width: 12, height: 12, alignSelf: 'center', position: 'absolute', top: 0, right: 0 }} source={closeImg} />
        </TouchableOpacity>
        {mumbermark &&
          <View style={Style.btnView}>
            <Button type="primary" style={{ height: 40, borderWidth: 0 }} onPress={this.getFillnumber}>补号</Button>
          </View>}

        <View style={Style.subView}>
          <Text style={Style.displayText}>设置前缀</Text>
          <TextInput
            ref={el => this.inputRef = el}
            placeholder="请输入"
            placeholderTextColor="#BCBCBB"
            underlineColorAndroid="transparent"
            style={Style.displayInput}
            editable={isView ? false : enabled}
            value={this.state.numberflagdisplay}
            onChangeText={this.changeDisplay}
            autoFocus={enabled}
            onBlur={this.changeDisplayConfirm}
          />
        </View>
        <View style={{ height: mumbermark && deviceType === 2 ? 40 : 0 }} />
      </Modal>
    )
  }
}

export default FormNumberModal

