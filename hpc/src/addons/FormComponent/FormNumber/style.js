import { StyleSheet } from 'react-native'
import { screenWidth, screenHeight } from '../../../config/sysPara'

export default Style = StyleSheet.create({
  childrenWrap: {
    width: (screenWidth - 74) * 0.7,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  textContent: {
    flex: 1,
    color: '#BCBCBB',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    textAlign: 'right',
    marginRight: 10,
  },
  inputContent: {
    width: '64%',
    // height: 50,
    padding: 0,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    textAlign: 'right',
    color: '#1B1B1B',
  },
  modalClass: {
    padding: 0,
  },
  btnView: {
    height: 80,
    paddingHorizontal: 10,
    paddingVertical: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#EDEDED',
  },
  subView: {
    height: 100,
    paddingTop: 20,
    paddingHorizontal: 10,
    paddingBottom: 10,
    flexDirection: 'column',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  displayText: {
    color: '#41454B',
    fontWeight: 'bold',
  },
  displayInput: {
    height: 40,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: '#D7D7D7',
    paddingLeft: 14,
    fontSize: 12,
    color: '#1B1B1B',
  },
})
