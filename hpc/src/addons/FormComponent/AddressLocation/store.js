import { serviceKey } from './constants'

const geocoderLocationService = async (lat, lng, range = 500) => {
  const url = `https://api.map.baidu.com/geocoder/v2/?location=${lat},${lng}+&radius=${range}&output=json&pois=1&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}

const searchPositionService = async (keyword, region = null) => {
  const url = `https://api.map.baidu.com/place/v2/search?query=${keyword}&region=${region}&scope=2&page_size=20&output=json&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}

const searchLocationService = async (keyword, location, radius) => {
  const url = `https://api.map.baidu.com/place/v2/search?query=${keyword}&location=${location}&radius=${radius}&scope=2&page_size=20&output=json&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}

module.exports = {
  geocoderLocationService,
  searchPositionService,
  searchLocationService,
}
