import PropTypes from 'prop-types'
import React from 'react'
import {
  View,
  TextInput,
  Image,
  TouchableOpacity,
} from 'react-native'
// import {
//   Picker,
// } from 'nagz-mobile-lib'
import { Picker } from '@ant-design/react-native'
import {
  Toast,
} from '@ant-design/react-native'
import {
  GO_MODAL,
} from '../../../utils/jump'
import {
  isEmpty,
  getUpdateValidationFn,
  createDebouncedChangeValue,
  deepEqual,
} from '../common'
import {
  getAddress,
} from '../../../utils/storage'
import ModalView from './ModalView'
import cloneDeep from 'lodash/cloneDeep'
import {
  addressData,
} from '../../addressData'

const isEmptyFn = address => (isEmpty(address.country) && isEmpty(address.province) && isEmpty(address.city) && isEmpty(address.area)) || isEmpty(address.room)
class AddressLocation extends React.PureComponent {
  state = {
    room: this.props.defaultValue.room,
    options: null,
  }

  componentWillMount() {
    // this.options = await this._getAddrData()
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId, undefined, isEmptyFn)(this.props.required, this.props.defaultValue)

    // 创建防抖的 changeValue 函数
    this.debouncedChangeValue = createDebouncedChangeValue(this.props.changeValue, 150);

    const {
      // eslint-disable-next-line react/prop-types
      cascade, allowCountry,
    } = this.props
    let addData = addressData
    if (allowCountry === false) {
      addData = addressData[0].children
    }
    this.options = addressData

    this.setState({
      options: this.showCascadeData(addData, cascade),
    })
  }
  componentDidMount() {
    // 因为高度为固定，所以不通过onlayout计算高度
    this.cmpHeight = 143
  }
  componentDidUpdate(prevProps) {
    const { defaultValue, required, changeValue } = this.props;

    // 更新防抖的 changeValue 函数
    if (prevProps.changeValue !== changeValue) {
      this.debouncedChangeValue = createDebouncedChangeValue(changeValue, 150);
    }

    // 只在 defaultValue 真正改变时更新验证
    if (!deepEqual(prevProps.defaultValue, defaultValue)) {
      if (this.updateValidation) {
        this.updateValidation = this.updateValidation(required, defaultValue);
      }
    }

    // 只在 room 值真正改变时更新状态，避免无限循环
    if (prevProps.defaultValue.room !== defaultValue.room && this.state.room !== defaultValue.room) {
      this.setState({
        room: defaultValue.room,
      });
    }
  }

  onChange = (value) => {
    const {
      changeValue,
      fieldId,
      defaultValue,
      cascade,
      allowCountry,
    } = this.props
    let [country = '', province = '', city = '', area = ''] = value
    let room = defaultValue.room
    if (allowCountry === false) {
      [province = '', city = '', area = ''] = value
      country = ''
      //  if (+cascade < 4) { room = '' }
    }
    if (+cascade < 5) { room = '' }
    if (changeValue) {
      changeValue({
        [fieldId]: {
          defaultValue: {
            ...defaultValue,
            country,
            province,
            city,
            area,
            room,
          },
        },
      })
    }
  }

  showCascadeData(addressData, cascade) {
    if (+cascade < 4) {
      const addressDataTmp = cloneDeep(addressData)
      let addressDataResult
      if (cascade === '1') {
        addressDataResult = addressDataTmp.map((item) => {
          item = {
            value: item.value,
            label: item.label,
          }
          return item
        })
      }
      if (cascade === '2') {
        addressDataResult = addressDataTmp.map((item) => {
          item.children = item.children.map(childrenData => ({
            value: childrenData.value,
            label: childrenData.label,
          }))
          return item
        })
      }
      if (cascade === '3') {
        addressDataResult = addressDataTmp.map((item) => {
          item.children.map((childrenItem) => {
            childrenItem.children = childrenItem.children.map(childrenData => ({
              value: childrenData.value,
              label: childrenData.label,
            }))
          })
          return item
        })
      }
      return addressDataResult
    }
    return addressData
  }

  updateRoom = (text) => {
    this.setState({
      room: text,
    })

    const { fieldId, defaultValue } = this.props
    if (text !== defaultValue.room) {
      const value = {
        ...defaultValue,
        room: text,
      }
      // 使用防抖的 changeValue 函数
      this.debouncedChangeValue({
        [fieldId]: {
          defaultValue: value,
        },
      })
    }
  }

  changeValue = () => {
    const {
      changeValue,
      fieldId,
      defaultValue,
    } = this.props
    if (changeValue && this.state.room !== defaultValue.room) {
      const value = {
        ...defaultValue,
        room: this.state.room,
      }
      changeValue({
        [fieldId]: {
          defaultValue: value,
        },
      })
      // this.timer = null
    }
  }

  showModal = () => {
    const {
      fieldId,
      defaultValue,
      searchWord,
      isAllowModify,
      allowDistance,
      changeValue,
    } = this.props
    const {
      room,
    } = defaultValue
    let mapSearchWord = ''
    if (searchWord !== '' && searchWord !== null) {
      mapSearchWord = searchWord
    } else if (room !== '' && room !== null) {
      mapSearchWord = room
    }
    const modalParams = {
      children: <ModalView
        searchWord={
          mapSearchWord
        }
        fieldId={
          fieldId
        }
        defaultValue={
          defaultValue
        }
        changeValue={
          changeValue
        }
        isAllowModify={
          isAllowModify
        }
        allowDistance={
          allowDistance
        }
      />,
      animationIn: 'slideInUp',
      animationOut: 'slideOutDown',
    }
    GO_MODAL(modalParams)
  }

  formatAddress = values => values.join('')

  disabledToast = () => Toast.info('不可更改', 1)

  render = () => {
    const {
      label,
      defaultValue,
      isRunTime,
      enabled,
      required,
      isView,
      cascade,
      allowCountry,
    } = this.props
    const {
      country,
      province,
      city,
      area,
    } = defaultValue
    const disabled = isView ? true : (!isRunTime ? true : !enabled)
    let disValue = [country, province, city, area]
    let cols = cascade === '5' ? 4 : +cascade
    switch (+cascade) {
      case 1:
        if (country === '') {
          disValue = []
        }
        break
      case 2:
        if (country === '' || province === '') {
          disValue = []
        }
        break
      case 3:
        if (country === '' || province === '' || city === '') {
          disValue = []
        }
        break
      case 4:
      case 5:
        if (country === '' || province === '' || city === '' || area === '') {
          disValue = []
        }
        break
      default:
        break
    }
    if (allowCountry === false) {
      disValue = [province, city, area]
      switch (+cascade) {
        case 1:
          if (province === '') {
            disValue = []
          }
          break
        case 2:
          if (province === '' || city === '') {
            disValue = []
          }
          break
        case 3:
        case 4:
          if (province === '' || city === '' || area === '') {
            disValue = []
          }
          break
        default:
          break
      }
      cols = (cascade === '5' || cascade === '4') ? 3 : +cascade
    }
    console.log(cols, cascade, allowCountry)
    return (
      <View style={{ flex: 1, flexDirection: 'column', backgroundColor: 'white', borderBottomWidth: 1, borderColor: '#e5e5e5', borderTopWidth: 1, marginTop: 10 }}>
        <Picker cols={cols} isRequire={required} extra="请选择" format={this.formatAddress} label={label} customClick={disabled ? this.disabledToast : null} data={this.state.options} onChange={this.onChange} value={disValue} disabled={disabled} />
        <View style={{ borderTopColor: '#e5e5e5', borderTopWidth: 1, marginHorizontal: 20, paddingVertical: 13, flexDirection: 'row' }}>
          {
            (cascade === '5' || (allowCountry === false && cascade === '4')) && <TextInput
              value={this.state.room}
              editable={!disabled}
              underlineColorAndroid="transparent"
              onChangeText={this.updateRoom}
              placeholder="请输入详细地址"
              placeholderTextColor="#bbbbbb"
              multiline
              style={{
                flex: 1,
                color: '#1b1b1b',
                fontSize: 14,
                height: 54,
                lineHeight: 21,
                paddingTop: 4,
                backgroundColor: '#fff',
                textAlignVertical: 'top'
              }}
            />
          }
          {disabled || (cascade !== '5' || (allowCountry === false && cascade === '4')) ? null : <TouchableOpacity onPress={this.showModal}><Image source={require('../../../images/icons-maps.png')} style={{ marginLeft: 32, width: 32, height: 32 }} /></TouchableOpacity>}
        </View>
      </View>
    )
  }
}

AddressLocation.propTypes = {
  hidelabel: PropTypes.bool,
  isView: PropTypes.bool,
  changeValue: PropTypes.func,
  updateValidation: PropTypes.func,
  required: PropTypes.bool,
  fieldId: PropTypes.string,
  label: PropTypes.string,
  isRunTime: PropTypes.bool,
  $$isValidation: PropTypes.bool,
  enabled: PropTypes.bool,
  defaultValue: PropTypes.shape({
    country: PropTypes.string,
    province: PropTypes.string,
    city: PropTypes.string,
    area: PropTypes.string,
    room: PropTypes.string,
    place: PropTypes.string,
    lng: PropTypes.string,
    lat: PropTypes.string,
  }),
  searchWord: PropTypes.string,
  isAllowModify: PropTypes.bool,
  allowDistance: PropTypes.string,
}
AddressLocation.defaultProps = {
  defaultValue: {
    country: '',
    province: '',
    city: '',
    area: '',
    room: '',
    place: '',
    lng: '',
    lat: '',
  },
  searchWord: '',
  isAllowModify: false,
  allowDistance: '0',
}
export default AddressLocation
