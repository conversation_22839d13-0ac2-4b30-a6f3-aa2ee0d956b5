import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import {
  Text,
  View,
  Dimensions,
  TextInput,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  BackHandler,
} from "react-native";
import { MapView} from "react-native-baidu-map";
import Geolocation from '@react-native-community/geolocation'
import {
  initializeRegistryWithDefinitions,
  View as AnimateView,
} from "react-native-animatable";
import Icon from "react-native-vector-icons/Ionicons";
import NavigationService from "../../../NavigationService";
import ModalHeader,{titlePaddingtop,titleHeight} from "../ModalHeader";
import {
  geocoderLocationService,
  searchPositionService,
  searchLocationService,
} from "./store";
import {
  bd09togcj02,
  gcj02towgs84,
  wgs84togcj02,
  gcj02tobd09,
} from "../../../utils/coordtransform";

const isIos = Platform.OS === "ios";
const { height: screenHeight, width: screenWidth } = Dimensions.get("window");

// 定义searchbar的动画
initializeRegistryWithDefinitions({
  searchBarIn: {
    0: {
      left: 0,
      height: isIos ? 44 : 44,
    },
    0.5: {
      left: -screenWidth,
      height: isIos ? 44 : 44,
    },
    1: {
      left: -screenWidth,
      height: isIos ? 44 : 44,
    },
  },
  searchBarOut: {
    0: {
      left: -screenWidth,
      height: isIos ? 44 : 54,
    },
    0.5: {
      left: -screenWidth,
      height: isIos ? 44 : 44,
    },
    1: {
      left: 0,
      height: isIos ? 44 : 44,
    },
  },
});

// 匹配是否为直辖市
function matchMunicipality(province) {
  let flag = false;
  const MunicipalityCityArray = [
    "北京市",
    "上海市",
    "天津市",
    "重庆市",
    "台湾省",
    "香港特别行政区",
    "澳门特别行政区",
  ];
  MunicipalityCityArray.map((i) => {
    if (province === i) {
      flag = true;
    }
  });
  return flag;
}

class ResItem extends PureComponent {
  savePositionInfo = () => {
    const { item, savePositionInfo } = this.props;
    savePositionInfo(item);
  };
  render = () => {
    const { item, index } = this.props;
    return (
      <TouchableOpacity
        key={`searchResults${index}`}
        onPress={this.savePositionInfo}
      >
        <View
          style={{
            marginHorizontal: 20,
            paddingVertical: 10,
            borderBottomWidth: 1,
            borderBottomColor: "#E5E5E5",
          }}
        >
          <Text style={{ color: "#1b1b1b", paddingBottom: 4 }}>
            {item.name}
          </Text>
          <Text style={{ color: "#A0A2A5", fontSize: 12 }}>
            {item.addr || item.address}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
}

class ModalView extends PureComponent {
  state = {
    searchWord: this.props.searchWord,
    searchResults: [],
    center: { latitude: 39.928216, longitude: 116.402544 },
    marker: { latitude: 39.928216, longitude: 116.402544, title: "" },
    location: null,
    zoom: 10,
    //  isOnlyShowSearchResults: true,
  };
  componentDidMount() {
    this.createMap();
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.goBack);
    }
  }
  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.goBack);
    }
  }

  goBack = () => {
    NavigationService.back();
    return true;
  };
  createMap = () => {
    const { address, searchWord, isAllowModify, allowDistance } = this.props;
    const mapComponent = this;
    // 因百度地图搜索区域始终要有范围限制，所以当不允许修改的时候，搜索范围限制为50
    let allowRange = 50;
    mapComponent.setState({
      searchWord,
    });
    if (isAllowModify) {
      allowRange = +allowDistance;
    }
    if (address.lng !== "" && address.lat !== "") {
      // 只要有经纬度,按照经纬度地图周边查询
      // string转换为number
      const lng = +address.lng;
      const lat = +address.lat;
      const gjc02Position = wgs84togcj02(lng, lat);
      const bd09Position = gcj02tobd09(gjc02Position[0], gjc02Position[1]);
      // params: lat ,lng,range
      geocoderLocationService(bd09Position[1], bd09Position[0], allowRange)
        .then((results) => {
          if (results.status === 0) {
            const poiData = results.result.pois;
            mapComponent.setState({
              //  isOnlyShowSearchResults: false,
              searchResults: poiData,
              location: { lat: bd09Position[1], lng: bd09Position[0] },
              center: { latitude: bd09Position[1], longitude: bd09Position[0] },
              marker: {
                latitude: bd09Position[1],
                longitude: bd09Position[0],
                title: address.place || "",
              },
            });
          }
        })
        .catch((e) => {
          console.warn(e, "error");
        });
    } else if (isAllowModify && allowDistance === "null") {
      // 当默认有搜索字段切搜索范围无限制的时候
      this.geoSearch(searchWord);
    } else {
      // 当无经纬度，也无查询字段，获取当前定位
      this.getCurrentLocationHandle(searchWord, allowRange);
    }
  };
  // 获取当前定位后的处理
  getCurrentLocationHandle = async (searchWord, allowRange) => {
    console.log('getCurrentLocationHandle')
    const mapComponent = this;
    Geolocation.getCurrentPosition(
      async (position) => {
        console.log(geoResults);
        const geoResults= position.coords
        if (searchWord !== null && searchWord !== "") {
          const location = `${geoResults.latitude},${geoResults.longitude}`;
          const data = await searchLocationService(
            searchWord,
            location,
            allowRange
          );
          if (data.status === 0 && data.results.length) {
            mapComponent.setState({
              searchResults:
                Object.keys(data.results[0]).length > 2 ? data.results : [],
              center: {
                latitude: geoResults.latitude,
                longitude: geoResults.longitude,
              },
              marker: {
                latitude: geoResults.latitude,
                longitude: geoResults.longitude,
                title: geoResults.address || "",
              },
            });
          } else {
            mapComponent.setState({
              searchResults: [],
              center: {
                latitude: geoResults.latitude,
                longitude: geoResults.longitude,
              },
              marker: {
                latitude: geoResults.latitude,
                longitude: geoResults.longitude,
                title: geoResults.address || "",
              },
            });
          }
        } else {
          const data = await geocoderLocationService(
            geoResults.latitude,
            geoResults.longitude,
            allowRange
          );
          if (data.status === 0) {
            const { location } = data.result;
            const poiData = data.result.pois;
            mapComponent.setState({
              // isOnlyShowSearchResults: false,
              searchResults: poiData,
              center: { latitude: location.lat, longitude: location.lng },
              marker: {
                latitude: location.lat,
                longitude: location.lng,
                title: data.sematic_description || "",
              },
            });
          } else {
            mapComponent.setState({
              // isOnlyShowSearchResults: true,
              searchResults: [],
            });
          }
        }
      },
      (error) => {
        // See error code charts below.
        console.log(error.code, error.message);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
  );
 
  };
  // 搜索方法
  geoSearch = async (searchWord) => {
    const { address, isAllowModify, allowDistance } = this.props;
    const { province, city } = address;
    const mapComponent = this;
    // 当搜索距离为无限制的时候，按照区域搜索
    if (isAllowModify && allowDistance === "null") {
      let region;
      // 当选择了省市区的时候，搜索范围变为在省市区内查找
      if (province === "") {
        region = null;
      } else if (matchMunicipality(province)) {
        region = province;
      } else {
        region = city;
      }
      searchPositionService(searchWord, region)
        .then((results) => {
          if (results.status === 0) {
            if (results.results.length) {
              mapComponent.setState({
                // Object.keys(results.results[0]).length > 2 =>因为百度searchapi 某些搜索结果只包含市的name和num，无法做到详细定位，所以排除这些搜索结果
                searchResults:
                  Object.keys(results.results[0]).length > 2
                    ? results.results
                    : [],
              });
            } else {
              Promise.reject("搜索结果为空");
            }
          } else {
            Promise.reject(results.status);
          }
        })
        .catch((e) => {
          console.warn(e);
          mapComponent.setState({
            searchResults: [],
          });
        });
    } else {
      // 当搜索距离有限制的时候，按照当前定位或内部经纬度范围内搜索
      const { center } = this.state;
      let allowRange = 50;
      if (isAllowModify) {
        allowRange = +allowDistance;
      }
      const lat = +center.latitude;
      const lng = +center.longitude;
      // const gjc02Position = wgs84togcj02(lng, lat)
      // const bd09Position = gcj02tobd09(gjc02Position[0], gjc02Position[1])
      const location = `${lat},${lng}`;
      const data = await searchLocationService(
        searchWord,
        location,
        allowRange
      );
      if (data.status === 0 && data.results.length) {
        mapComponent.setState({
          searchResults:
            Object.keys(data.results[0]).length > 2 ? data.results : [],
        });
      } else {
        mapComponent.setState({
          searchResults: [],
        });
      }
    }
  };
  closeModal = () => {
    this.props.closeModal();
  };
  savePositionInfo = async (item) => {
    const { changeValue, fieldId } = this.props;
    const addressLocationCmp = this;
    let formatLocation;
    let formatAddress;
    // 因为百度不同api返回的poi结果参数名不同，所以需要判断情况转换参数
    if (item.point) {
      formatLocation = {
        lat: item.point.y,
        lng: item.point.x,
      };
      formatAddress = item.addr;
    } else {
      formatLocation = {
        lat: item.location.lat,
        lng: item.location.lng,
      };
      formatAddress = item.address;
    }
    if (changeValue) {
      // 因为地址组件需要 省市区信息，而只有geocoder能解析出addressComponents
      // x =>lng  , y =>lat
      const data = await geocoderLocationService(
        formatLocation.lat,
        formatLocation.lng
      );
      if (data.status === 0) {
        const { addressComponent } = data.result;
        let city = addressComponent.city;
        const gcj02Position = bd09togcj02(
          formatLocation.lng,
          formatLocation.lat
        );
        const wgs84Position = gcj02towgs84(gcj02Position[0], gcj02Position[1]);
        // 当为直辖市时，city类始终未市辖区
        if (matchMunicipality(addressComponent.province)) {
          city = "市辖区";
        }
        const value = {
          country: "中国",
          province: addressComponent.province,
          city,
          area: addressComponent.district,
          room: formatAddress,
          place: item.name,
          lng: `${wgs84Position[0]}`,
          lat: `${wgs84Position[1]}`,
        };
        changeValue({ [fieldId]: { address: value } });
        addressLocationCmp.closeModal();
      }
    }
  };

  changeSearchWord = (e) => {
    this.setState({
      searchWord: e.nativeEvent.text,
    });
  };

  submitEditingHandle = (e) => {
    this.geoSearch(e.nativeEvent.text);
  };

  showSearchBar = () => {
    this.animateView.searchBarIn(300);
    this.searchInput.focus();
    this.animateScrollView.transitionTo(
      { height: screenHeight - (isIos ?88 : 44), top: isIos ?88 : 44 },
      300,
      "ease"
    );
    //  this.setState({ isOnlyShowSearchResults: true })
  };

  hideSearchBar = () => {
    this.searchInput.blur();
    this.animateView.searchBarOut(300);
    this.animateScrollView.transitionTo(
      {
        height: screenHeight - 400 - 44,
        top: isIos ? 44 + 400 + 20 : 44 + 400,
      },
      300,
      "ease"
    );
    // this.setState({ isOnlyShowSearchResults: false })
  };
  handleAnimateViewRef = (ref) => (this.animateView = ref);
  handleAnimateScrollViewRef = (ref) => (this.animateScrollView = ref);
  handleSearchinputRef = (ref) => (this.searchInput = ref);

  searchHeader = () => {
    const { searchWord } = this.state;

    return (
      <View
        transition="left"
        style={{
          width: screenWidth,
          flexDirection: "row",
          paddingHorizontal: 20,
          paddingTop:titlePaddingtop,
          height:  titleHeight,
          backgroundColor: "#fff",
          alignItems: "center",
    
        }}
      >
        <View
          style={{
            flexDirection: "row",
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            height: 40,
            borderBottomWidth: 0.5,
            borderColor: "#e5e5e5",
            marginRight: 13,
          }}
        >
          <Image
            source={require("../../../images/icons-magnifier-search-default.png")}
          />
          <TextInput
            ref={this.handleSearchinputRef}
            style={{ padding: 0,paddingLeft: 6, flex: 1, fontSize: 16, height: 40 }}
            blurOnSubmit
            placeholder="搜索"
            defaultValue={searchWord}
            selectTextOnFocus
            returnKeyType="search"
            onSubmitEditing={this.submitEditingHandle}
          />
        </View>
        <View>
          <Text onPress={this.hideSearchBar} style={{ fontSize: 18 }}>
            取消
          </Text>
        </View>
      </View>
    );
  };
  render = () => {
    const { center, marker, searchResults } = this.state;
    const { currentHeight } = this.props;
console.log('sceen',screenHeight)

    const headerProps = {
      leftView: (
        <Text
          onPress={this.closeModal}
          style={{ fontSize: 18, color: "#41454b" }}
        >
          关闭
        </Text>
      ),
      centerText: "位置",
      rightView: (
        <TouchableOpacity onPress={this.showSearchBar}>
          <Image
            source={require("../../../images/icons-magnifier-search-default.png")}
          />
        </TouchableOpacity>
      ),
    };
    return (
      <AnimateView
        transition="height"
        style={{ flex: 1, height: currentHeight }}
      >
        <AnimateView
          ref={this.handleAnimateViewRef}
          style={{
            backgroundColor: "#fff",
            width: 2 * screenWidth,
            flexDirection: "row",
            left: 0,
            height:  44,
          }}
        >
          <ModalHeader style={{ width: screenWidth }} header={headerProps} />
          {this.searchHeader()}
        </AnimateView>
        <View style={{marginTop:44, height: 400 }}>
          <MapView
            style={{ height: 400 }}
            center={center}
            marker={marker}
            zoom={20}
          />
        </View>
        <AnimateView
          ref={this.handleAnimateScrollViewRef}
          style={{
            borderTopWidth: 1,
            borderTopColor: "#e5e5e5",
            backgroundColor: "#fff",
            flex: 1,
            position: "absolute",
            top: 44+44 + 400,
            left: 0,
            width: screenWidth,
            height: screenHeight - 400 - 44-44,
          }}
        >
          <ScrollView
            style={{ backgroundColor: "#fff", flex: 1 }}
            keyboardDismissMode="on-drag"
          >
            {searchResults.length ? (
              <View style={{ paddingBottom: 20 }}>
                {searchResults.map((item, index) => (
                  <ResItem
                    item={item}
                    key={index}
                    index={index}
                    savePositionInfo={this.savePositionInfo}
                  />
                ))}
              </View>
            ) : (
              <Text
                style={{ fontSize: 16, textAlign: "center", lineHeight: 24 }}
              >
                暂无数据
              </Text>
            )}
          </ScrollView>
        </AnimateView>
      </AnimateView>
    );
  };
}

ModalView.propTypes = {
  address: PropTypes.shape({
    province: PropTypes.string,
    city: PropTypes.string,
    area: PropTypes.string,
    room: PropTypes.string,
    place: PropTypes.string,
    lng: PropTypes.string,
    lat: PropTypes.string,
  }),
  fieldId: PropTypes.string,
  searchWord: PropTypes.string,
  isAllowModify: PropTypes.bool,
  allowDistance: PropTypes.string,
  closeModal: PropTypes.func,
  changeValue: PropTypes.func.isRequired,
  currentHeight: PropTypes.number,
};
ModalView.defaultProps = {
  address: {
    province: "",
    city: "",
    area: "",
    room: "",
    place: "",
    lng: "",
    lat: "",
  },
  fieldId: "",
  searchWord: "",
  isAllowModify: false,
  allowDistance: "0",
};
export default ModalView;
