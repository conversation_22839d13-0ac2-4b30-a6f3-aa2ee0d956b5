import PropTypes from 'prop-types'
import React from 'react'
import { View, DeviceEventEmitter } from 'react-native'
import { SimpleLine } from 'nagz-mobile-lib'
import { AnimatedImage } from '../../lib/AnimatedImage'
import { Toast } from '@ant-design/react-native'
import isBoolean from 'lodash/isBoolean'
import { isTrue, getUpdateValidationFn, getValidateFormatFn, getKeybordtypeByValidformant } from './common'
import { clickInvocationService } from '../../utils/event/DeviceEventEmitter'

const fn = () => { }
class TextInput extends React.Component {
  state = {
    value: this.props.defaultValue,
    isText: true,
  }

  componentWillMount = () => {
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, this.state.value)
    this.formatValidation = getValidateFormatFn(this.props.updateValidation, this.props.fieldId)(this.props.validformat, this.state.value)
  }
  componentDidMount() {
    // 因为单行文本高度为固定的50，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.props.defaultValue !== defaultValue) {
      if (this.updateValidation) {
        this.updateValidation = this.updateValidation(this.props.required, defaultValue)
      }
      this.formatValidation = this.formatValidation(this.props.validformat, defaultValue)
    }
    if (this.state.value !== defaultValue) {
      this.setState({
        value: defaultValue,
      })
    }
  }
  onRightIconPress = () => {
    if (this.props.invocationService) {
      DeviceEventEmitter.emit(clickInvocationService, this.props.invocationService)
    }
  }
  updateValue = (text) => {
    this.setState({
      value: text,
    })
    if (this.props.isChangeUpdate) {
      const { changeValue } = this.props
      if (changeValue && text !== this.props.defaultValue) {
        changeValue({ [this.props.fieldId]: { defaultValue: text } })
      }
    }
  }
  changeValue = () => {
    const { changeValue } = this.props
    if (changeValue && this.state.value !== this.props.defaultValue) {
      changeValue({ [this.props.fieldId]: { defaultValue: this.state.value } })
    }
    this.setState({
      isText: true,
    })
  }

  changeFormsFocus = () => {
    const { fieldId, changeFormsFocus } = this.props
    if (changeFormsFocus) {
      changeFormsFocus(fieldId)
    }
    this.changeValue()
  }

  rightIconView = () => {
    if (this.props.invocationService) {
      return (<AnimatedImage
        style={{ justifyContent: 'center', alignItems: 'center', width: 34, height: 50 }}
        source={require('../../images/icon-awsome-button.png')}
        onPress={() => {
          if (this.props.inputBlur) {
            this.props.inputBlur(this.onRightIconPress)
          } else {
            this.onRightIconPress()
          }
        }}
      />)
    }
    return null
  }
  // 定义组件的获取焦点事件
  cmpFocus = () => {
    this.setState({
      isText: false,
    })
    // this.refCmp.focus()
  }
  onPress = () => {
    Toast.info('不可更改', 1)
  }
  // eslint-disable-next-line no-return-assign
  refThisCmp = ref => this.refCmp = ref

  render() {
    const { label, required, isRunTime, enabled, isView, hidelabel, validformat } = this.props
    const editable = isView ? false : (isBoolean(enabled) ? enabled : false)
    const inputType = getKeybordtypeByValidformant(validformat)
    return (
      <View>
        <SimpleLine
          inputType={inputType}
          ref={this.refThisCmp}
          isText={this.state.isText}
          label={hidelabel && isRunTime ? '' : label}
          placeholder="请输入"
          isRequire={isTrue(required)}
          value={`${this.state.value}`}
          onChange={this.updateValue}
          onBlur={this.changeValue}
          onPress={!editable ? this.onPress : this.cmpFocus}
          editable={editable}
          rightIconView={this.rightIconView()}
          // returnKeyType="next"
          onSubmitEditing={this.changeFormsFocus}
        />
      </View>
    )
  }
}
TextInput.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  required: PropTypes.bool,
  enabled: PropTypes.bool,
  isView: PropTypes.bool,
  hidelabel: PropTypes.bool,
  validformat: PropTypes.string,
  changeValue: PropTypes.func,
  updateValidation: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  invocationService: PropTypes.any, // eslint-disable-line
  inputBlur: PropTypes.func,
}
TextInput.defaultProps = {
  label: '',
  defaultValue: '',
  required: false,
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  enabled: true,
}
export default TextInput
