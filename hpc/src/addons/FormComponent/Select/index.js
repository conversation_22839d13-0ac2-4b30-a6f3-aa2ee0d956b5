import PropTypes from 'prop-types'
import React from 'react'
import { View, TouchableOpacity, TextInput, Text } from 'react-native'
// import { Picker } from 'nagz-mobile-lib'
// import { Picker } from '../../../nagzMobileLib/lib/index'
import { Toast } from '@ant-design/react-native'
import get from 'lodash/get'
import getList, { getTreeData, generateList } from './store'
import { isTrue, getUpdateValidationFn } from '../common'
import { FormContext } from '../../../utils/reactContext'
import { Picker } from '../../../lib/Picker'
// import { Button, CheckBox } from '../../../lib/nagz-mobile-lib'

const fn = () => { }

export default class Select extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      value: '',
      options: this.props.option.options || [],
      isText: true,
      defaultValue: '',
    }
  }
  static contextType = FormContext;

  componentWillMount = async () => {
    const { isRunTime, option, sourcefield, orderby, filter, treeField, isSubForm, treefieldCmp } = this.props
    if (isRunTime) {
      if (sourcefield) {
        await this.getData(orderby, filter)
      } else if (treeField && !isSubForm) {
        const treefield = this.context.fields.find(({ id }) => id === treeField)
        if (treefield) {
          this.treeFieldProps = treefield.properties
          this.refreshTree()
        }
      } else if (treefieldCmp) {
        this.treeFieldProps = treefieldCmp
        await this.refreshTree()
      } else {
        await this.displayOptions(option.options)
      }
      this.confirmDefaultVal()
    }
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, option.defaultValue)
  }
  componentDidMount() {
    // 因为高度为固定的，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  componentWillReceiveProps = async ({ option, orderby, filter }, context) => {
    const { isRunTime, sourcefield, changeValue, intostore, treeField, isSubForm } = this.props
    if (isRunTime && sourcefield) {
      if (this.props.orderby !== orderby || this.props.filter !== filter) {
        await this.getData(orderby, filter)
        this.confirmDefaultVal()
      }
    }
    if (option.defaultValue !== this.state.defaultValue) {
      // changeValue({ [this.props.fieldId]: { 'option.defaultValue': option.defaultValue } })
      if (treeField && !sourcefield) {
        const item = (this.options.find(i => i.value === option.defaultValue) || {}).label || ''
        this.setState({ defaultValue: option.defaultValue, value: intostore && !item ? option.defaultValue : item })
      } else {
        this.setState({ defaultValue: option.defaultValue, value: option.defaultValue })
      }
    }
    if (this.updateValidation) {
      this.updateValidation = this.updateValidation(this.props.required, option.defaultValue)
    }
    if (this.context !== context && !sourcefield && treeField && isRunTime && !isSubForm) {
      const treefield = context.fields.find(({ id }) => id === treeField)
      if (treefield && this.treeFieldProps !== treefield.properties) {
        this.treeFieldProps = treefield.properties
        this.refreshTree()
      }
    }
  }

  setDefaultValue = (value, options) => {
    const uninopt = options.filter(val => val.value === value).length === 0
    const defaultValue = uninopt && this.props.intostore === false ? '' : value
    return defaultValue
  }

  confirmDefaultVal = () => {
    const { sourcefield, changeValue, intostore, treeField } = this.props
    const value = this.setDefaultValue(this.props.option.defaultValue, this.options)
    if (treeField && !sourcefield) {
      const item = (this.options.find(i => i.value === value) || {}).label || ''
      this.setState({ defaultValue: value, value: intostore && !item ? value : item })
    } else {
      this.setState({ defaultValue: value, value })
    }
    if (value !== this.props.option.defaultValue) {
      changeValue({ [this.props.fieldId]: { 'option.defaultValue': value } })
    }
  }

  displayOptions = (options) => {
    this.options = options.map(opt => ({ label: opt.value, value: opt.value })).filter(o => o.value !== '')
    if (!isTrue(this.props.required)) {
      this.options.unshift({ label: '请选择', value: '' })
    }
  }

  getData = async (orderby, filter) => {
    const { appId, source, maxValue, sourcefield } = this.props
    const thisorderby = orderby ? `&$orderby=${orderby}` : ''
    const thisfilter = filter ? `&$filter=(${filter})` : ''
    const result = await getList(appId, source, `$limit=${maxValue}&$select=${sourcefield}${thisorderby}${thisfilter}`)
    let options = []
    options = [...options, ...result.map(item => ({
      value: get(item, sourcefield).display,
      title: '',
      checked: false,
    }))]
    this.displayOptions(options)
    this.setState({
      options,
    })
  }

  refreshTree = async () => {
    const { appId, formId } = this.props
    const { filter, orderby, fieldid, valueColumn } = this.treeFieldProps
    const data = {}
    if (filter) data.filter = filter
    if (orderby) data.orderby = orderby
    const treeData = await getTreeData(appId, formId, fieldid, data)
    const dataList = await generateList(treeData)
    const options = dataList.map(item => ({ label: item.display, value: valueColumn ? item.value : item.id }))
    this.options = options.filter(o => o.value !== '')
    if (!isTrue(this.props.required)) {
      this.options.unshift({ label: '请选择', value: '' })
    }
    this.setState({ options })
  }

  onSelectClick = () => {
    if (!this.props.enabled) {
      return Toast.info('不可更改', 1)
    }
    if (this.state.options.length === 0) {
      return Toast.info('没有内容', 1)
    }
    return null
  }

  onClick = () => {
    if (!this.props.enabled) {
      return Toast.info('不可更改', 1)
    } else if (this.state.options.length === 0) {
      if (this.props.intostore) {
        return this.setState({ isText: false })
      }
      return Toast.info('没有内容', 1)
    }
    return null
  }

  onTextFocus = () => {
    if (this.props.intostore) {
      this.setState({ isText: false })
    }
  }

  onChangeText = v => this.setState({ value: v, defaultValue: v })

  onBlur = () => {
    const { changeValue } = this.props
    if (this.state.defaultValue !== this.props.option.defaultValue) {
      changeValue({ [this.props.fieldId]: { 'option.defaultValue': this.state.defaultValue } })
    }
    this.setState({
      isText: true,
    })
  }

  updateValue = (value) => {
    if (value === this.state.defaultValue) {
      return
    }
    const { changeValue, treeField, sourcefield } = this.props
    if (changeValue) {
      let val = value[0]
      if (this.options.length !== 0 && this.options.findIndex(v => v.value === value[0]) === -1) {
        val = this.options[0].value
      }
      changeValue({ [this.props.fieldId]: { 'option.defaultValue': val } })
    }
    if (treeField && !sourcefield) {
      const item = (this.state.options.find(i => i.value === value[0]) || {}).label || ''
      this.setState({ defaultValue: value, value: item })
    } else {
      this.setState({ defaultValue: value, value })
    }
  }

  render() {
    const { label, required, isRunTime, enabled, hidelabel, isView } = this.props
    console.log('asdasdasdasd测试', this.state.options, this.state.options, this.props, this.state.defaultValue)
    return (
      <View>
        <Picker
          data={this.options}
          customClick={this.state.options.length === 0 || !enabled ? this.onSelectClick : null}
          cols={1}
          label={hidelabel && isRunTime ? '' : label}
          isRequire={isTrue(required)}
          onOk={this.updateValue}
          disabled={isView ? true : !enabled}
          value={[this.state.defaultValue]}
        >
          <TouchableOpacity
            style={{ width: '100%' }}
            activeOpacity={0.8}
            onPress={this.state.options.length === 0 || !enabled ? this.onClick : this.onTextFocus}
          >
            {this.state.isText ?
              <Text
                numberOfLines={1}
                style={{ width: '100%', fontSize: 14, color: this.state.value ? '#1B1B1B' : '#BCBCBB', textAlign: 'right' }}
              >{this.state.value || '请选择'}
              </Text> :
              <TextInput
                placeholder="请选择"
                placeholderTextColor="#BCBCBB"
                underlineColorAndroid="transparent"
                style={{ width: '100%', fontSize: 14, color: '#1B1B1B', textAlign: 'right' }}
                editable={isView ? false : enabled}
                value={this.state.value}
                onChangeText={this.onChangeText}
                onBlur={this.onBlur}
                onSubmitEditing={this.onBlur}
                autoFocus
              />
            }
          </TouchableOpacity>
        </Picker>
      </View>
    )
  }
}

Select.propTypes = {
  label: PropTypes.string,
  required: PropTypes.bool,
  isRunTime: PropTypes.bool,
  hidelabel: PropTypes.bool,
  enabled: PropTypes.bool,
  isView: PropTypes.bool,
  intostore: PropTypes.bool,
  appId: PropTypes.string,
  fieldId: PropTypes.string,
  source: PropTypes.string,
  sourcefield: PropTypes.string,
  maxValue: PropTypes.string,
  orderby: PropTypes.string,
  filter: PropTypes.string,
  option: PropTypes.shape({
    defaultValue: PropTypes.any,
    options: PropTypes.array,
  }),
  changeValue: PropTypes.func,
  updateValidation: PropTypes.func,
}

Select.defaultProps = {
  label: '',
  required: false,
  changeValue: fn,
  fieldId: '',
  option: {
    defaultValue: {},
    options: [],
  },
  enabled: true,
}
