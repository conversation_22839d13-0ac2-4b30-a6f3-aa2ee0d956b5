import PropTypes from 'prop-types'
import React from 'react'
import { View } from 'react-native'
// import { DatePicker } from 'nagz-mobile-lib'
import { Toast, DatePicker } from '@ant-design/react-native'
import moment from 'moment'
import isString from 'lodash/isString'
import { getUpdateValidationFn } from '../common'
import DateModal from './DateModal'
import { DatePicker } from '../../../lib/DatePicker'
// import { GO_MODAL } from '../../../utils/jump'

const fn = () => { }

// DatePicker value用的值为Date对象，不能用moment对象
const replaceFormat = format => format.replace('yyyy', 'YYYY')
  .replace('dd', 'DD')
  .replace('hh', 'HH')
  .replace('EEEE', 'dddd')

class TimePeriod extends React.PureComponent {
  state = {
    type: 'start',
    value: this.props.defaultValue ? new Date(Number(this.props.defaultValue)) : null,
    startDate:
      this.props.defaultValue.start === ''
        ? ''
        : isString(this.props.defaultValue.start)
          ? new Date(Number(this.props.defaultValue.start))
          : new Date(this.props.defaultValue.start),
    endDate:
      this.props.defaultValue.end === ''
        ? ''
        : isString(this.props.defaultValue.end)
          ? new Date(Number(this.props.defaultValue.end))
          : new Date(this.props.defaultValue.end),
  }

  componentWillMount = () => {
    this.isDefaultEmpty = !!this.props.defaultValue
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, this.props.defaultValue)
  }
  componentDidMount() {
    // 因为高度为固定的，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.props.defaultValue !== defaultValue) {
      this.setState({
        startDate:
          defaultValue.start === ''
            ? ''
            : isString(defaultValue.start)
              ? new Date(Number(defaultValue.start))
              : new Date(defaultValue.start),
        endDate:
          defaultValue.end === ''
            ? ''
            : isString(defaultValue.end)
              ? new Date(Number(defaultValue.end))
              : new Date(defaultValue.end),
      })
      if (this.updateValidation) {
        this.updateValidation = this.updateValidation(this.props.required, defaultValue)
      }
    }
    if (defaultValue && defaultValue !== '') {
      this.isDefaultEmpty = true
    }
  }

  formatDate = (date) => {
    const { format } = this.props
    const feFormat = replaceFormat(format)
    return moment(date).format(feFormat)
  }
  // datepick 选出来的时间为 Date()对象
  updateStartValue = (date) => {
    const {
      changeValue,
      fieldId,
    } = this.props
    const { startDate, endDate } = this.state
    if (changeValue) {
      changeValue({
        [fieldId]: {
          defaultValue: {
            start: date ? date.getTime().toString() : '',
            end: endDate ? endDate.valueOf().toString() : '',
          },
        },
      })
      this.setState({ startDate: date })
    }
  }
  // datepick 选出来的时间为 Date()对象
  updateEndValue = (date) => {
    const {
      changeValue,
      fieldId,
    } = this.props
    const { startDate, endDate } = this.state
    if (changeValue) {
      changeValue({
        [fieldId]: {
          defaultValue: {
            start: startDate ? startDate.valueOf().toString() : '',
            end: date ? date.getTime().toString() : '',
          },
        },
      })
      this.setState({ endDate: date })
    }
  }
  updateModalDate = (timestamp) => {
    const {
      changeValue,
      fieldId,
    } = this.props
    const { type, startDate, endDate } = this.state
    if (changeValue) {
      if (type === 'start') {
        changeValue({
          [fieldId]: {
            defaultValue: {
              start: timestamp.toString(),
              end: endDate ? endDate.getTime().toString() : '',
            },
          },
        })
        this.setState({ startDate: new Date(timestamp) })
      } else if (type === 'end') {
        changeValue({
          [fieldId]: {
            defaultValue: {
              start: startDate ? startDate.getTime().toString() : '',
              end: timestamp.toString(),
            },
          },
        })
        this.setState({ endDate: new Date(timestamp) })
      }
    }
  }
  contentOnClick = () => {
    const { enabled } = this.props
    if (!enabled) {
      Toast.info('不可更改', 1)
      return
    }
    this.setState({ visible: true })
  }
  startContentOnClick = () => {
    const { enabled } = this.props
    if (!enabled) {
      Toast.info('不可更改', 1)
      return
    }
    this.setState({ visible: true, type: 'start' })
  }
  endContentOnClick = () => {
    const { enabled } = this.props
    if (!enabled) {
      Toast.info('不可更改', 1)
      return
    }
    this.setState({ visible: true, type: 'end' })
  }
  closeModal = () => { this.setState({ visible: false }) }
  render() {
    const {
      label,
      required,
      isRunTime,
      enabled,
      format,
      isView,
    } = this.props
    const { startDate, endDate, type } = this.state
    console.log(startDate, endDate)
    const disabled = isView ? true : (!isRunTime ? true : !enabled)
    const inputStyle = {
      backgroundColor: '#fff',
      width: '100%',
    }
    let mode = 'year'
    if (format === 'HH:mm' || format === 'HH:mm:ss') {
      mode = 'time'
    } else if (format === 'yyyy-MM-dd HH:mm:ss' || format === 'yyyy-MM-dd HH:mm' || format === 'yyyy-MM-dd HH') {
      mode = 'datetime'
    } else if (format === 'yyyy-MM-dd' || format === 'MM-dd' || format === 'yyyy-MM-dd EEEE') {
      mode = 'date'
    } else if (format === 'yyyy-MM') {
      mode = 'month'
    }

    return (
      <View>
        <DatePicker
          style={inputStyle}
          mode={mode}
          format={this.formatDate}
          onChange={this.updateStartValue}
          disabled={disabled}
          extra={format}
          value={startDate}
          minDate={new Date(1900, 1, 1, 0, 0, 0)}
          maxDate={new Date(2050, 1, 1, 23, 59, 59)}
          label={`${label}开始`}
          isRequire={required}
          contentOnClick={this.startContentOnClick}
        />
        <DatePicker
          style={inputStyle}
          mode={mode}
          format={this.formatDate}
          onChange={this.updateEndValue}
          disabled={disabled}
          extra={format}
          value={endDate}
          minDate={new Date(1900, 1, 1, 0, 0, 0)}
          maxDate={new Date(2050, 1, 1, 23, 59, 59)}
          label={`${label}结束`}
          isRequire={required}
          contentOnClick={this.endContentOnClick}
        />
        {this.state.visible && <DateModal closeModal={this.closeModal} visible={this.state.visible} format={replaceFormat(format)} value={type === 'start' ? startDate : endDate} updateValue={this.updateModalDate} />}
      </View>
    )
  }
}

TimePeriod.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  isView: PropTypes.bool,
  enabled: PropTypes.bool,
  desc: PropTypes.string,// eslint-disable-line
  required: PropTypes.bool,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  format: PropTypes.string,
  validate: PropTypes.object,// eslint-disable-line
  updateValidation: PropTypes.func,
}

TimePeriod.defaultProps = {
  label: '',
  defaultValue: {},
  desc: '',
  required: false,
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  validate: {},
  enabled: true,
  height: 1,
  format: 'YYYY-MM-DD HH:mm:ss',
}

export default TimePeriod
