import {
  Text,
  View,
  TouchableHighlight,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  Modal as RnMoadl,
} from "react-native";
import Video from "react-native-video";
import ImagePicker from "react-native-image-crop-picker";
import Orientation from "react-native-orientation";
import React, { useState, useEffect, useRef } from "react";
import { useRequest, useSetState, useToggle } from "ahooks";
import { getAttachmentsFilesServer, addFileServer } from "@request/api";
import { get as _get, isNumber } from "lodash-es";
import { SimpleLine, TextInput } from "nagz-mobile-lib";
import { nanoid } from "nanoid";
import dayjs from "dayjs";
import {
  Button,
  Icon,
  Modal,
  ActionSheet,
  InputItem,
  Toast,
} from "@ant-design/react-native";
import { GO_FILEMANAGER } from "../../../utils/jump";
import NavigationService from "../../../NavigationService";

import { getFileInfo, ossUpload, getFileType } from "./util";
import style from "./style";
import videoImg from "../../../images/icon-multmedia_video_default.png";
import audioImg from "../../../images/icon-multmedia_audio_default.png";
import unknownImg from "../../../images/icon-multmedia_unknown_default.png";

import addImg from "../../../images/icon-multmedia_add_file-small.png";
import linkImg from "../../../images/icon-multmedia_link_file_small.png";
import addFileImg from "../../../images/icon-multmedia_add_file_large.png";
import addLinkImg from "../../../images/icon-multmedia_link_file_large.png";

const defaultVideoHeight = 210;

const VideoSourceItem = ({ item, index, changeVideoIndexHandle }) => {
  function pressHandle() {
    changeVideoIndexHandle(index);
  }
  let imgSource = null;
  if (item.videoType === "1") {
    if (
      item.videoUrl.indexOf("mp4") > 0 ||
      item.videoUrl.indexOf("mpg") > 0 ||
      item.videoUrl.indexOf("avi") > 0 ||
      item.videoUrl.indexOf("mov") > 0
    ) {
      imgSource = videoImg;
    } else if (item.videoUrl.indexOf("mp3") > 0) {
      imgSource = audioImg;
    } else {
      imgSource = unknownImg;
    }
  } else {
    imgSource = unknownImg;
  }
  return (
    <TouchableOpacity onPress={pressHandle}>
      <View style={style.sourceItem}>
        <View style={[style.sourceItemImgWrapper]}>
          <Image
            style={style.sourceItemImg}
            source={imgSource}
            resizeMode="contain"
          ></Image>
        </View>
        <View style={[style.flexCol, { flex: 1 }]}>
          <View style={{ height: 43 }}>
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              style={style.sourceItemTitle}
            >
              {item.title}
            </Text>
          </View>
          <View>
            {item.videoType === "1" && isNumber(item.fileSize) && (
              <Text style={style.sourceItemFileSize}>{`${(
                item.fileSize /
                (1024 * 1024)
              ).toFixed(2)}M`}</Text>
            )}
            {item.videoType === "2" && (
              <Text style={style.sourceItemFileSize}>链接</Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const index = (props) => {
  const { width: windowWidth, height: windowHeight } = Dimensions.get("window");
  console.log(windowWidth, windowHeight);
  const playerRef = useRef(null);
  const {
    label,
    required,
    appId,
    formId,
    fieldId,
    dataId,
    changeValue,
    defaultValue,
  } = props;

  const [showSelectSource, { toggle: toggleSelectSourceVisible }] =
    useToggle(false);

  const [showAddLink, { toggle: toggleAddLinkVisible, setLeft: closeAddLink }] =
    useToggle(false);
  const [state, setState] = useSetState({
    videoSource: [],
    selectVideoIndex: 0,
    orientationWidth: windowWidth,
    orientationHeight: defaultVideoHeight,
    fullScreen: false,
  });
  const [inputState, setinputState] = useSetState({
    link: "",
    name: "",
  });
  const { loading, runAsync: runGetAttachmentsFiles } = useRequest(
    getAttachmentsFilesServer,
    {
      manual: true,
    }
  );
  useEffect(() => {
    async function loadData() {
      const { appId, formId, dataId, fieldId } = props;
      if (!dataId) {
        return;
      }
      const res = await runGetAttachmentsFiles(appId, formId, dataId, fieldId);
      console.log(res);
      if (res) {
        setState({
          videoSource: res.records,
        });
      }
    }
    loadData();
  }, []);
  function doFullscreenhandle() {
    playerRef.current.presentFullscreenPlayer();
  }
  async function selectFile(file) {
    const tempId = nanoid();
    file.tempId = tempId;
    console.log(file);
    const params = {
      appId,
      formId,
      attachmentId: fieldId,
      recordId: dataId || "",
      fileSize: file.size,
    };
    const url = await ossUpload(file, params);
    console.log(url);
    if (url) {
      const fileType = getFileType(file);
      const videoItemInfo = {
        tempId: nanoid(),
        title: file.name,
        videoUrl: url,
        videoType: "1",
        gmt_create: dayjs(file.mtime).format(),
        fileType: fileType,
        imageUrl: "",
        fileSize: file.size,
        remark: "",
      };
      const res = await addFileServer(appId, {
        appId,
        attachmentId: fieldId,
        formId,
        boRecordId: dataId || "",
        ...videoItemInfo,
      });
      console.log("addFileServer", res);
      if (res.fileId) {
        const newVideoSource = [
          ...state.videoSource,
          { id: res.fileId, ...videoItemInfo },
        ];
        setState({
          videoSource: newVideoSource,
        });
        const value = [res.fileId, ...defaultValue];
        if (changeValue) changeValue({ [fieldId]: { defaultValue: value } });
      }
    } else {
      Toast.fail("获取链接失败");
    }
  }
  function resizeView() {
    // const dirction = Orientation.getInitialOrientation();
    // console.log(dirction, windowWidth, windowHeight);
    // if (dirction === "PORTRAIT") {
    //   setState({
    //     orientationWidth: windowWidth,
    //     orientationHeight: defaultVideoHeight,
    //   });
    // } else {
    //   setState({
    //     orientationHeight: windowWidth,
    //     orientationWidth: windowHeight,
    //   });
    // }
  }
  function addLinkAction() {
    toggleAddLinkVisible();
  }
  function selectFileAction() {
    if (!props.enabled) {
      ToastInfo("不可更改");
      return;
    }
    const BUTTONS = ["拍照", "从相册选择文件", "本地文件", "取消"];
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        // destructiveButtonIndex: BUTTONS.length - 2,
        // title: '标题',
        title: "请选择您的附件",
        "data-seed": "logId",
      },
      (buttonIndex) => {
        if (buttonIndex === 0) {
          // 调用拍照
          ImagePicker.openCamera({
            width: 300,
            height: 400,
            cropping: true,
            cropperChooseText: "选择",
            cropperCancelText: "取消",
          }).then(async (image) => {
            if (image.path) {
              const res = await getFileInfo(
                image.path,
                `${
                  deviceType === 2
                    ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker`
                    : RNFS.PicturesDirectoryPath
                }`
              );
              selectFile(res);
            }
          });
        } else if (buttonIndex === 1) {
          // 调用选择图片
          console.log("sdhkjadhlkajsdlajdlasd");
          ImagePicker.openPicker({
            width: 300,
            height: 400,
            cropping: true,
            cropperChooseText: "选择",
            cropperCancelText: "取消",
          })
            .then(async (image) => {
              debugger;
              if (image.path) {
                const res = await getFileInfo(
                  image.path,
                  `${
                    deviceType === 2
                      ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker`
                      : RNFS.PicturesDirectoryPath
                  }`
                );
                selectFile(res);
              }

              //  this._onChange(image)
            })
            .catch((error) => {
              // 执行失败
              console.error(error);
            });
        } else if (buttonIndex === 2) {
          // 上传本地文件
          // 选择文件
          GO_FILEMANAGER((f) => {
            selectFile(f);
            NavigationService.back();
          });
        }
      }
    );
  }
  function changeVideoIndexHandle(index) {
    setState({
      selectVideoIndex: index,
    });
    toggleSelectSourceVisible();
  }
  const footerButtons = [
    { text: "取消", onPress: () => closeInnput() },
    { text: "提交", onPress: () => postLink() },
  ];
  function setInputValue(key, value) {
    setinputState({
      [key]: value,
    });
  }
  function closeInnput() {
    console.log("closeInnput");
    closeAddLink();
    clearInput();
  }
  function clearInput() {
    setinputState({
      name: "",
      link: "",
    });
  }
  async function postLink() {
    console.log("postLink");
    if (!inputState.name || !inputState.link) {
      Toast.info("请填写内容");
      return;
    }
    const videoItemInfo = {
      tempId: nanoid(),
      title: inputState.name,
      videoUrl: inputState.link,
      videoType: "2",
      gmt_create: dayjs().format(),
      fileType: "",
      imageUrl: "",
      fileSize: 0,
      remark: "",
    };
    const res = await addFileServer(appId, {
      appId,
      attachmentId: fieldId,
      formId,
      boRecordId: dataId || "",
      ...videoItemInfo,
    });
    console.log(res);
    if (res.fileId) {
      const newVideoSource = [
        ...state.videoSource,
        { id: res.fileId, ...videoItemInfo },
      ];
      setState({
        videoSource: newVideoSource,
      });
      const value = [res.fileId, ...defaultValue];
      if (changeValue) changeValue({ [fieldId]: { defaultValue: value } });
    }
    closeInnput();
  }
  function fullScreenHandle() {
    playerRef.current.presentFullscreenPlayer();
    Orientation.lockToLandscape();
    setState({ fullScreen: true });
    // resizeView();
  }
  const { videoSource, selectVideoIndex } = state;
  console.log(
    props,
    showSelectSource,
    videoSource,
    selectVideoIndex,
    showAddLink
  );
  return (
    <View
      style={{
        width: windowWidth,
        height: 260,
      }}
    >
      <SimpleLine label={label} isRequire={required}>
        <View style={style.flexRow}>
          {!!videoSource.length && (
            <TouchableOpacity onPress={fullScreenHandle}>
              <Icon color="#000" name="fullscreen"></Icon>
            </TouchableOpacity>
          )}
          {!!videoSource.length && (
            <TouchableOpacity onPress={toggleSelectSourceVisible}>
              <Icon color="#000" name="menu"></Icon>
            </TouchableOpacity>
          )}
        </View>
      </SimpleLine>
      {!!videoSource.length ? (
        <Video
          resizeMode="cover"
          fullscreenAutorotate={true}
          fullscreenOrientation={"all"}
          paused={true}
          ref={playerRef}
          style={state.fullScreen?{
            width: windowHeight,
            height: windowWidth,
          }:{
            width: windowWidth,
            height: 210,
          }}
          controls
          source={{
            uri: _get(videoSource, [selectVideoIndex, "videoUrl"], ""),
          }} // 可以是一个 URL 或者 本地文件
        />
      ) : (
        <View
          style={[
            style.videoContainer,
            style.flexRow,
            { justifyContent: "center" },
          ]}
        >
          <TouchableOpacity onPress={selectFileAction}>
            <View
              style={[style.flexCol, { alignItems: "center", marginRight: 42 }]}
            >
              <Image source={addFileImg}></Image>
              <Text>添加文件</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={addLinkAction}>
            <View style={[style.flexCol, { alignItems: "center" }]}>
              <Image source={addLinkImg}></Image>
              <Text>链接文件</Text>
            </View>
          </TouchableOpacity>
        </View>
      )}
      <Modal
        visible={showSelectSource}
        popup
        onClose={toggleSelectSourceVisible}
        maskClosable
        animationType="slide-up"
      >
        <ScrollView style={style.sourceList}>
          <View style={[style.operRow, style.flexRow]}>
            <View style={[style.flexRow]}>
              <TouchableOpacity onPress={selectFileAction}>
                <View style={[style.flexRow, { marginRight: 24 }]}>
                  <Image style={style.operRowImg} source={addImg}></Image>
                  <Text style={style.operRowText}>添加文件</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={addLinkAction}>
                <View style={style.flexRow}>
                  <Image style={style.operRowImg} source={linkImg}></Image>
                  <Text style={[style.operRowText, { color: "#F2994A" }]}>
                    链接文件
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPress={toggleSelectSourceVisible}>
              <View style={style.flexRow}>
                <Text style={[style.operRowText, { color: "#000000" }]}>
                  关闭
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          {videoSource.map((item, index) => (
            <VideoSourceItem
              key={item.id}
              item={item}
              index={index}
              changeVideoIndexHandle={changeVideoIndexHandle}
            ></VideoSourceItem>
          ))}
        </ScrollView>
      </Modal>
      <Modal
        title="链接文件"
        presentationStyle="formSheet"
        animationType="slide-up"
        maskClosable
        popup
        visible={showAddLink}
        transparent
        footer={footerButtons}
      >
        <View style={{}}>
          <InputItem
            value={inputState.name}
            onChange={(v) => setInputValue("name", v)}
            style={{ marginTop: 20, fontSize: 14, paddingBottom: 10 }}
            placeholder="请输入网址"
          ></InputItem>
          <InputItem
            value={inputState.link}
            onChange={(v) => setInputValue("link", v)}
            style={{ marginTop: 20, fontSize: 14, paddingBottom: 10 }}
            placeholder="请输入名称"
          ></InputItem>
        </View>
      </Modal>
    
    </View>
  );
};

export default index;
