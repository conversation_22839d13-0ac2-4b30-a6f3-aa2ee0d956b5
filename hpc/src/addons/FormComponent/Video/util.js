import RNFS from "react-native-fs";
import { AsyncStorage } from "react-native";
import { uploadfile, getFileUrl } from "../Attachment/uploadFiles";

export const selectFile = (file) => {};
export const ossUpload = async (file, params, callBack = () => {}) => {
  const fileType = getFileType(file);
  const res = await uploadfile(file.tempId, file, callBack, { ...params }).then(
    async (f) => {
      console.log(f);
      return f;
      if (f) {
        const fileNameSplit =
          file instanceof Array
            ? file[0].name.split(".")
            : file.name.split(".");
        // const ossUrl = `https://${bucket}.${region}.aliyuncs.com/${file.uid}`

        let user = await AsyncStorage.getItem("result");
        var path =
          appId === "0"
            ? `user/avatar/${JSON.parse(user).userId}/${fileNameSplit[
                fileNameSplit.length - 1
              ].toLowerCase()}/${file.uid}`
            : `${appId}/${formId}/${JSON.parse(user).userId}/${fileNameSplit[
                fileNameSplit.length - 1
              ].toLowerCase()}/${file.uid}`;
        const ossUrl = getFileUrl(
          path,
          fileNameSplit[fileNameSplit.length - 1].toLowerCase()
        );
        addFile(appId, {
          appId,
          attachmentId: fieldId,
          boRecordId: dataId || "",
          fileName: file.name,
          fileSize: file.size,
          formId,
          ossFileName: file.uid,
          ossUrl,
          remark: "",
          fileType: fileNameSplit[fileNameSplit.length - 1].toLowerCase(),
        }).then((res) => {
          this.updateValue(res.fileId);
        });
      }
    }
  );
  if (res) {
    const path = await getFilePath(file, params);
    console.log("path", path);
    const ossUrl = getFileUrl(path, fileType);
    return ossUrl;
  }
  return null;
};
export const getFileInfo = async (path, fdir) => {
  let tempfdir = fdir;
  if (!fdir) {
    tempfdir =
      deviceType === 2
        ? `${RNFS.TemporaryDirectoryPath}/react-native-image-crop-picker`
        : `${RNFS.PicturesDirectoryPath}`;
  }

  const result = await RNFS.readDir(tempfdir);
  const str = path.substring(path.lastIndexOf("/") + 1, path.length);
  // 遍历找到文件
  let files = [];
  result.forEach((v) => {
    if (v.name === str) {
      files.push(v);
    }
  });
  return files;
};

export const getFilePath = async (file, param) => {
  const fileType = getFileType(file);
  let user = await AsyncStorage.getItem("result");
  let path = "";
  if (param.appId === "0") {
    path = `user/avatar/${JSON.parse(user).userId}/${fileType}/${file.tempId}`;
  } else {
    path = `${param.appId}/${param.formId}/${
      JSON.parse(user).userId
    }/${fileType}/${file.tempId}`;
  }
  return path;
};
export const getFileType = (file) => {
  const fileNameSplit = file.name.split(".");
  let fileType = null;
  if (fileNameSplit[fileNameSplit.length - 1]) {
    fileType = fileNameSplit[fileNameSplit.length - 1].toLowerCase();
  }
  return fileType;
};
