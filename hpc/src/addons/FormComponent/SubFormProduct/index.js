/* eslint-disable react/require-default-props */
import PropTypes from 'prop-types'
import React from 'react'
import isEqual from 'lodash/isEqual'
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash/debounce'

import { View, Image, Dimensions, TouchableOpacity, Text } from 'react-native'
import { Button, WhiteSpace } from 'nagz-mobile-lib'
import omit from 'lodash/omit'
import { Toast, Portal } from '@ant-design/react-native'
import * as Animatable from 'react-native-animatable'
import ViewRow from './ViewRow'
import Helper from '../SubForm/NewHelper'
import { GO_ROOTMODAL } from '../../../utils/jump'
import { screenWidth } from '../../../config/sysPara'
import SubFormTotal from './SubFormTotal'
import RefModal from './RefModal'
import {
  thousandsFun,
  getDisplayValue,
} from '../../FormComponent/Number/displayValue'
import { isFormOperationComponent, isStatOperationComponent } from '../../constants'
import { isExpression } from '../../../utils/expression/transformExpression'
import { UPDATE_FORMDATA } from '../../../containers/RunTime/Form/constants'
import PubSub from '../../../utils/pubsub'
import { FormContext } from '../../../utils/reactContext'

const { width } = Dimensions.get('window')

// const random = () => Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, 9))

class SubFormProduct extends React.Component {
  constructor(props) {
    super(props)
    this.updateValuesQueue = []

    this.fns = {
      forceUpdate: this.forceUpdate,
      startEdit: this.startEdit,
      isNotHideColumn: this.isNotHideColumn,
    }
  }
  static contextType = FormContext;

  state = {
    columns: [],
    fieldsRows: [],
    rowDatas: [],
    editIndex: -1,
    totalRow: null,
    showItems: false,
    refPropDatas: [],
    isLoaded: false,
  }

  componentDidMount = async () => {
    if (this.props.isRunTime) {
      this.cloneRelations = cloneDeep(this.props.formsource.relations)
      this.helper = new Helper(this.props, this.context, this.fns, 2)
      this.initListener = this.debounceListener()
      PubSub.on(UPDATE_FORMDATA, this.initListener)
      this.initListener()
    }
  }

  componentWillReceiveProps = (nextProps, nextContext) => {
    // if (this.props.isRunTime && this.context !== nextContext) {
    //   this.helper.checkContextChange(nextContext)
    // }
    if (nextProps.productFilter !== this.props.productFilter
      && !isExpression(nextProps.productFilter)
      && !isExpression(this.props.productFilter)
      && this.helper.rowDatas.length !== 0) {
      this.clearDatas()
    }
    const relations = nextProps.formsource.relations
    if (relations !== this.cloneRelations) {
      for (let i = 0; i < relations.length; i += 1) {
        if (!isEqual(relations[i].value, this.cloneRelations[i].value)) {
          this.helper.relationChange(relations[i])
        }
      }
      this.cloneRelations = cloneDeep(this.props.formsource.relations)
      this.helper.props.formsource.relations = this.cloneRelations
    }
  }

  shouldComponentUpdate = (nextProps, nextState) => {
    const shouldUpdate = Object.keys(nextProps)
      .some((key) => {
        if (key === 'defaultValue' || key === 'extraProps' || key === 'formsource') return false
        if (nextProps[key] !== this.props[key]) {
          return true
        }
        return false
      }) ||
    Object.keys(nextState)
      .some((key) => {
        if (key === 'totalRow') {
          if (nextState.totalRow === null || this.state.totalRow === null) return true
          return Object.keys(nextState.totalRow)
            .some((fieldId) => {
              if (fieldId === '$$id') {
                return false
              }
              if (nextState.totalRow[fieldId] === null || this.state.totalRow[fieldId] === null) {
                return true
              }
              return nextState.totalRow[fieldId].display !== this.state.totalRow[fieldId].display
            })
        }
        if (nextState[key] !== this.state[key]) {
          return true
        }
        return false
      })
    return shouldUpdate
  }
  onPress=() => {
    if (this.opening) return
    Toast.loading('', 0)
    this.opening = true
    const { inputBlur } = this.props
    if (inputBlur) {
      inputBlur(() => { this.addRow(this.state.rowDatas.length) })
    } else {
      this.addRow(this.state.rowDatas.length)
    }
  }

  getTableColumns = (originFields) => {
    const c = {}
    for (let i = 0; i < originFields.length; i++) {
      if (this.isNotHideColumn(originFields[i])) {
        c[originFields[i].id] = i
      }
    }
    return c
  }
    getNumberDisplay = (value, { displayMode, decimalLength, thousands }) => {
      if (displayMode === undefined || decimalLength === undefined || thousands === undefined) {
        return value
      }
      let display = getDisplayValue(value, displayMode, decimalLength)
      if (thousands) {
        display = thousandsFun(display)
      }
      return display
    }

  getRowKey = record => record.id ? record.id.value : record.$$id
  genTotalRow = () => {
    let {
      sum = [], avg = [], count = [], min = [], max = [],
    } = this.props.formsource.total
    sum = sum.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    avg = avg.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    max = max.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    min = min.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    count = count.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    if (sum.length === 0 && avg.length === 0 && max.length === 0 && min.length === 0 && count.length === 0) return

    const colSum = fieldId => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : 0
      const n = Number(origin)
      return total + (isNaN(n) ? origin : n)
    }

    const colMin = (fieldId, type) => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : ''
      if (origin === '') return total
      const n = Number(origin)
      const value = isNaN(n) ? origin : n
      const maxValue = total[0] > total[1] ? total[0] : total[1]
      const minValue = total[0] > total[1] ? total[1] : total[0]
      if (type === 'max') {
        return [
          (total[0] === total[1] && total[0] === null) ? value : maxValue > value ? maxValue : value,
          minValue < value ? minValue : value,
        ]
      }
      return [
        maxValue > value ? maxValue : value,
        (total[0] === total[1] && total[0] === null) ? value : minValue < value ? minValue : value,
      ]
    }

    const colCount = fieldId => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : ''
      if (origin === '') return total
      return total + 1
    }

    const noBlankRows = this.helper.rowDatas.filter(({ $$emptyId }) => !$$emptyId).filter(this.helper.checkIsNotBlankRow)
    const totalSum = fieldId => noBlankRows.reduce(colSum(fieldId), 0)
    const totalMin = (fieldId, type) => noBlankRows.reduce(colMin(fieldId, type), [null, null])
    const totalCount = fieldId => noBlankRows.reduce(colCount(fieldId), 0)
    const resultSum = sum.reduce((result, fieldId) => ({ ...result, [fieldId]: totalSum(fieldId) }), {})
    const resultAvg = avg.reduce((result, fieldId) => ({ ...result, [fieldId]: totalSum(fieldId) / noBlankRows.length }), {})
    const resultMax = max.reduce((result, fieldId) => ({ ...result, [fieldId]: totalMin(fieldId, 'max')[0] }), {})
    const resultMin = min.reduce((result, fieldId) => ({ ...result, [fieldId]: totalMin(fieldId, 'min')[1] }), {})
    const resultCount = count.reduce((result, fieldId) => ({ ...result, [fieldId]: totalCount(fieldId) }), {})
    const total = type => (result, fieldId) => {
      const field = this.helper.fields.find(({ id }) => id === fieldId)
      return ({ ...result, [fieldId]: { display: this.getNumberDisplay(type[fieldId], field.properties) } })
    }
    const totalRow = {
      ...sum.reduce(total(resultSum), {}),
      ...avg.reduce(total(resultAvg), {}),
      ...max.reduce(total(resultMax), {}),
      ...min.reduce(total(resultMin), {}),
      ...count.reduce(total(resultCount), {}),
    }
    this.setState({
      totalRow: {
        $$id: 'TOTAL_ROW',
        ...totalRow,
      },
    })
    this.pushUpdateValuesQueue({
      [this.props.fieldId]: {
        formsource: {
          ...this.props.formsource,
          total: {
            ...this.props.formsource.total,
            value: {
              sum: resultSum,
              avg: resultAvg,
              max: resultMax,
              min: resultMin,
              count: resultCount,
            },
          },
        },
      },
    })
  }
  debounceListener = () => debounce(this.init, 300)

  init = async () => {
    PubSub.off(UPDATE_FORMDATA, this.initListener)
    await this.helper.loadFields()
    this.afterLoadFields()
  }
  afterLoadFields = async () => {
    const columns = this.getTableColumns(this.helper.originFields)
    this.setState({
      rowDatas: this.helper.rowDatas,
      fieldsRows: this.helper.fieldsRows,
      columns,
      refPropDatas: this.helper.refPropDatas,
      editIndex: this.helper.editIndex,
    })
    this.initDefaultValue(this.helper.rowDatas)
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
    this.setState({ isLoaded: true })
  }
  forceUpdate = () => {
    this.setState({
      rowDatas: this.helper.rowDatas,
      fieldsRows: this.helper.fieldsRows,
      columns: this.getTableColumns(this.helper.originFields),
      refPropDatas: this.helper.refPropDatas,
      editIndex: this.helper.editIndex,
    })
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
  }

  initDefaultValue = (rowDatas) => {
    if (this.props.dataId && rowDatas.length > 0) {
      this.updatePropsToMainForm()
      this.commitUpdateValue()
    }
  }

  isNotHideColumn = field => this.props.mobileStyleFields &&
  this.props.mobileStyleFields.indexOf(field.id) !== -1 &&
   !isFormOperationComponent(field) &&
   !isStatOperationComponent(field) &&
   field.properties.visible !== false

  startEdit = async (record, index) => {
    if (!this.props.enabled || this.helper.forceUpdateFlag) return
    this.helper.setEditIndex(index)
    if (record.$$emptyId) {
      await this.helper.startEdit()
      this.updatePropsToMainForm()
      this.genTotalRow()
      this.commitUpdateValue()
    } else if (!record.$$loaded) {
      await this.helper.loadData()
    }
  }
  updateValue = async (value, index) => {
    // const tKey = Toast.loading('', 0)
    await this.helper.formUpdateValue(value, index)
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
    // Portal.remove(tKey)
  }

  updatePropsToMainForm = () => {
    const willStoredDatas = this.helper.rowDatas
      .filter(({ id, $$updated }) => id || $$updated) // 已有数据或已经更新
      .filter(this.helper.checkIsNotBlankRow)
      .map((rowData) => {
        // 没有改动的数据只传id
        if (rowData.id && !rowData.$$loaded) {
          return { id: rowData.id }
        }
        // 有改动或者新增的数据
        return omit(rowData, ['$$id', '$$updated', '$$loaded'])
      })
    this.pushUpdateValuesQueue({
      [this.props.fieldId]: {
        defaultValue: [...willStoredDatas, ...this.helper.removedDatas],
      },
    })
  }

  pushUpdateValuesQueue = (value) => {
    this.updateValuesQueue.push(value)
  }

  commitUpdateValue = () => {
    if (this.updateValuesQueue.length > 0) {
      this.props.changeValue(this.updateValuesQueue)
      this.updateValuesQueue = []
    }
  }
  addRow = async (index) => {
    await this.helper.addRow(index)
    await this.startEdit(this.helper.rowDatas[index], index)
  }

  addMutiRow= async (fieldId, newRowDatas) => {
    await this.helper.addMutiRow(fieldId, newRowDatas)
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      refPropDatas: this.helper.refPropDatas,
      rowDatas: this.helper.rowDatas,
    })
    this.genTotalRow()
    this.props.updateEditedFlag(this.props.fieldId)
    this.updatePropsToMainForm()
    this.commitUpdateValue()
  }

  clearDatas=() => {
    this.props.updateEditedFlag(this.props.fieldId)
    while (this.helper.rowDatas.length !== 0) {
      this.helper.removeRow(0)
    }
    this.updatePropsToMainForm()
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      refPropDatas: this.helper.refPropDatas,
      rowDatas: this.helper.rowDatas,
      editIndex: -1,
    })
    this.genTotalRow()
    this.commitUpdateValue()
  }

  removeRow = (index) => {
    this.props.updateEditedFlag(this.props.fieldId)
    this.helper.removeRow(index)
    this.updatePropsToMainForm()
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      refPropDatas: this.helper.refPropDatas,
      rowDatas: this.helper.rowDatas,
      editIndex: -1,
    })
    this.genTotalRow()
    this.commitUpdateValue()
    // e.stopPropagation()
  }

  openModal = () => {
    if (this.isGoRefModal === true) return
    this.isGoRefModal = true
    this.helper.backup()
    const colFields = this.helper && this.helper.fields ? this.helper.fields.filter(field => this.state.columns[field.id]) : []
    GO_ROOTMODAL({
      children: <RefModal
        {...this.props}
        helper={this.helper}
        updateValue={this.updateValue}
        close={this.forceUpdate}
        cb={() => { this.isGoRefModal = false; this.opening = false }}
        colFields={colFields}
        rowDatas={this.state.rowDatas}
        startEdit={this.startEdit}
        remove={this.removeRow}
        refPropDatas={this.state.refPropDatas}
      />,
      isCustomGoBack: true,
    })
  }

  veiwRowRemove =() => this.removeRow(this.helper.editIndex)
  changeLine=() => this.setState({ showItems: !this.state.showItems })
  render() {
    const { label, required, enabled } = this.props
    const { columns } = this.state
    const colFields = this.helper && this.helper.fields ? this.helper.fields.filter(field => columns[field.id]) : []
    return (
      <View>
        <WhiteSpace style={{ backgroundColor: '#F8F7F7' }} />
        <View style={{ backgroundColor: 'white', width: screenWidth }}>
          {this.helper &&
            <SubFormTotal
              fields={this.helper.fields}
              total={this.state.totalRow}
              totalRow={this.state.rowDatas.length}
              label={label}
              required={required}
              showItems={this.state.showItems}
              changeLine={this.changeLine}
            />
          }
        </View>
        <Animatable.View
          transition="height"
          style={{
            backgroundColor: 'white',
            overflow: 'hidden',
            height: this.state.showItems ? this.state.rowDatas.length * 66 : 0,
          }}
        >
          {
            this.state.rowDatas
            // .filter(row => Number(this.getRowValue(this.props.productprops.quantity, row)) !== 0)
            .map((row, index) => (<ViewRow
              first={index === 0}
              key={this.getRowKey(row)}
              colFields={colFields}
              row={row}
              index={index}
              propductProps={this.props.productprops}
              startEdit={this.startEdit}
              remove={this.veiwRowRemove}
              updateValue={this.updateValue}
              refPropData={this.state.refPropDatas[index]}
              disabled={!enabled}
            />))
          }
        </Animatable.View>
        {this.props.enabled && (
          this.state.rowDatas.length === 0 ?
            <View
              style={{
                height: 110,
                backgroundColor: 'white',
                paddingHorizontal: 20,
                alignItems: 'center',
              }}
            >
              <View
                style={{
                  width: width - 40,
                  height: 1,
                  backgroundColor: '#E5E5E5',
                  marginBottom: 34,
                }}
              />
              <Button
                disabled={!this.state.isLoaded}
                style={this.state.rowDatas.length === 0 ? {} : {
                  container: {
                    shadowRadius: 0,
                    borderTopWidth: 1,
                    borderTopColor: '#E5E5E5',
                  },
                }}
                inline
                onPress={this.openModal}
                prefixIcon={<Image source={require('../../../images/icon-add-outline-default.png')} />}
              >
                新增
              </Button>
            </View>
         :
            <TouchableOpacity disabled={!this.state.isLoaded} onPress={this.openModal} activeOpacity={1}>
              <View
                style={{
               borderTopWidth: 1,
               borderTopColor: '#E5E5E5',
               flexDirection: 'row',
               justifyContent: 'space-between',
               width,
               height: 50,
               backgroundColor: '#fff',
               alignItems: 'center',
               paddingHorizontal: 20,
             }}
              >
                <Text style={{ fontSize: 14, color: '#BCBCBB' }}>修改</Text>
                <Image source={require('../../../images/icon-arrow-right-small-default.png')} />
              </View>
            </TouchableOpacity>
          )
        }
        <WhiteSpace style={{ backgroundColor: '#F8F7F7' }} />
      </View>
    )
  }
}

SubFormProduct.propTypes = {
  dataId: PropTypes.string,
  productFilter: PropTypes.string,
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  enabled: PropTypes.bool,
  updateEditedFlag: PropTypes.func,
  formsource: PropTypes.object, // eslint-disable-line
  extraProps: PropTypes.object, // eslint-disable-line
  inputBlur: PropTypes.func,
  required: PropTypes.bool,
  mobileStyleFields: PropTypes.arrayOf(PropTypes.string),
  productprops:PropTypes.object, // eslint-disable-line
}

export default SubFormProduct
