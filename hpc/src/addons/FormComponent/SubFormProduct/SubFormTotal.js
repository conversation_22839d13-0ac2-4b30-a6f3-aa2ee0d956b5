import React from 'react'
import { View, Text, Image, Dimensions } from 'react-native'
import { SimpleLine } from 'nagz-mobile-lib/lib/SimpleLine'
import * as Animatable from 'react-native-animatable'

const { width } = Dimensions.get('window')

const arrowdIcon = require('../../../images/icons-arrow-down-small-fill-default.png')

class SubFormTotal extends React.Component {
  state = {
    rotate: '0deg',
  }
  componentWillReceiveProps=({ showItems }) => {
    if (showItems !== this.props.showItems) {
      this.setState({ rotate: !showItems ? '180deg' : '0deg' })
    }
  }
  render() {
    const {
      fields = [], total, totalRow, label, required, changeLine,
    } = this.props
    return (
      <View>
        <SimpleLine
          onPress={changeLine}
          onRightIconPress={changeLine}
          rightIconView={
            <Animatable.Image
              transition="rotate"
              source={arrowdIcon}
              style={{ width: 10, height: 10, transform: [{ rotate: this.state.rotate }] }}
            />
          }
          isRequire={required}
          label={label}
          editable={false}
          value={`共 ${totalRow} 条`}
        />
        {totalRow !== 0 && total !== null &&
        <View style={{
            backgroundColor: '#F8F7F7', paddingTop: 20, paddingLeft: 20, paddingBottom: 15,
          }}
        >
          <View style={{ width: 150 }}>
            {fields.filter(field => total[field.id])
              .map(field => (
                <View
                  key={field.id}
                  style={{ flexDirection: 'row', marginBottom: 5, justifyContent: 'space-between' }}
                >
                  <Text style={{ fontSize: 14, color: '#FFC219' }}>{field.properties.label}: </Text>
                  <Text style={{ fontSize: 14, color: '#FFC219' }}>{total[field.id] && String(total[field.id].display)}</Text>
                </View>
              ))
            }
          </View>
          <View style={{
              paddingRight: 30, position: 'absolute', right: 0, bottom: 10,
            }}
          >
            <Image source={require('../../../images/img-quote.png')} />
          </View>
        </View>}
      </View>
    )
  }
}
export default SubFormTotal
