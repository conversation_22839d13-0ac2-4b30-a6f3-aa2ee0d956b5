import React from 'react'
import PropTypes from 'prop-types'
import debounce from 'lodash/debounce'
import _get from 'lodash/get'
import { Modal, Toast } from '@ant-design/react-native'
import { ActionSheet } from 'nagz-mobile-lib'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'

import { Text, View, TouchableOpacity, BackHandler, Dimensions, Platform, Keyboard, TextInput } from 'react-native'
import NavigationService from '../../../NavigationService'
import ModalHeader from '../../FormComponent/ModalHeader'
import SubmitInfoBox from '../../../containers/RunTime/Form/component/SubmitInfoBox'
import Field from './Field'
import { isRefComponent, isSelectComponent } from '../../constants'

const { height } = Dimensions.get('window')

export default class EditModal extends React.PureComponent {
  static propTypes = {
    helper: PropTypes.shape({
      editIndex: PropTypes.number,
      getEditFieldsRow: PropTypes.func,
      getEditRowData: PropTypes.func,
      validate: PropTypes.func,
      addRow: PropTypes.func,
      setEditIndex: PropTypes.func,
      startEdit: PropTypes.func,
      resetBackupFlag: PropTypes.func,
      updateValidation: PropTypes.func,
      backupFlag: PropTypes.bool,
      restore: PropTypes.func,
      refPropDatas: PropTypes.object,
      fields: PropTypes.array,
    }),
    cb: PropTypes.func,
    close: PropTypes.func,
    updateValue: PropTypes.func,
    remove: PropTypes.func,
    columns: PropTypes.array,
    mobileStyleFields: PropTypes.array,
    colFields: PropTypes.array,
    appId: PropTypes.string,
    formsource: PropTypes.string,
    viewData: PropTypes.array,

  }
  constructor(props) {
    super(props)
    this.cmpFocusArray = []
    this.update = debounce((callback) => {
      const editRow = this.props.helper.getEditFieldsRow() || []
      const fieldsRow = [...editRow]
      const record = this.props.helper.getEditRowData()
      this.setState({
        editIndex: this.props.helper.editIndex,
        fieldsRow,
        record,
        refPropDatas: this.props.helper.refPropDatas[this.props.helper.editIndex],
      }, () => {
        callback && callback()
      })
    }, 100)
  }

  state = {
    editIndex: -1,
    fieldsRow: [],
    record: {},
    keyboardHeight: 0,
    submitInfoBoxHeight: 120,
    submitInfoBox: {
      isLoading: false,
      isShow: false,
      fields: [],
    },
    refPropDatas: [],
  }

  componentWillMount = () => {
    this.update()
    this.props.helper.setUpdateEditModal(this.update)
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this.keyboardDidShow)
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.handlerCancel)
    }
  }

  componentDidMount() {
    this.fieldsCmp = this
  }

  componentWillUnmount = () => {
    if (this.keyboardDidShowListener) this.keyboardDidShowListener.remove()
    if (this.keyboardDidHideListener) this.keyboardDidHideListener.remove()
    if (Platform.OS === 'android') {
      BackHandler.removeEventListener('hardwareBackPress', this.handlerCancel)
    }
    if (this.props.cb) {
      this.props.cb()
    }
    if (this.actionSheet) {
      this.actionSheet.close()
    }
  }

  filterForceUpdateCol = field => this.props.formsource.relations
    .find(relation => relation.force && relation.name === field.id)

  handlerCancel = () => {
    this.cancel()
    return true
  }
  closeModal = () => {
    this.remove()
  }

  keyboardDidShow = (e) => {
    this.keyboardShow = true
    this.setState({
      submitInfoBoxHeight: 80,
      keyboardHeight: e.endCoordinates.height,
    })
  }

  keyboardDidHide = () => {
    this.keyboardShow = false
    this.setState({
      submitInfoBoxHeight: 120,
      keyboardHeight: 0,
    })
  }

  updateValue = async (v) => {
    this.isupdateIng = true
    debugger
    await this.props.updateValue(v)
    this.update(() => {
      this.isupdateIng = false
    })
  }
  close = () => {
    const invalidFields = this.props.helper.validate()
    if (invalidFields.length) {
      this.invalidHandle(invalidFields)
      return
    }
    this.props.close()
    this.props.helper.setEditIndex(-1)
    this.props.helper.resetBackupFlag()
    NavigationService.back()
  }
  remove = () => {
    this.props.remove()
    this.props.helper.resetBackupFlag()
    NavigationService.back()
  }
  closeAndAdd = async () => {
    const invalidFields = this.props.helper.validate()
    if (invalidFields.length) {
      this.invalidHandle(invalidFields)
      return
    }
    const nextIndex = this.props.helper.editIndex + 1
    await this.props.helper.addRow(nextIndex)
    this.props.helper.setEditIndex(nextIndex)
    await this.props.helper.startEdit()
    this.update()
  }
  updateValidation = (validations) => {
    this.props.helper.updateValidation(validations)
  }
  invalidHandle = (invalidFields) => {
    this.setState({
      submitInfoBox: {
        isLoading: false,
        isShow: true,
        fields: invalidFields,
      },
    })
  }
  hideSubmitBox = () => {
    this.setState({
      submitInfoBox: {
        isLoading: false,
        isShow: false,
        fields: [],
      },
    })
  }
  inputBlur = (callback) => {
    if (this.keyboardShow) {
      this.searchInput.focus()
      this.searchInput.blur()
      setTimeout(() => {
        callback()
      }, 300)
    } else {
      callback()
    }
  }

  scrollToVerifyCmp = (currentVerifyCmp) => {
    const fieldsCmp = this.fieldsCmp
    if (fieldsCmp && fieldsCmp.scrollViewCmp) {
      let currentVerifyCmpHeight = 0
      // 获取所有的显示组件，并根据每个内部组件的实例的高度属性计算需要滚动的距离
      fieldsCmp.cmpFocusArray.some((item) => { if (item.key === currentVerifyCmp.id) { return true } if (item.cmp.cmpHeight) { currentVerifyCmpHeight += item.cmp.cmpHeight } return false })
      if (currentVerifyCmpHeight <= fieldsCmp.fieldHeight) {
        currentVerifyCmpHeight = 0
      } else if (currentVerifyCmpHeight >= fieldsCmp.scrollHeight - fieldsCmp.fieldHeight) {
        currentVerifyCmpHeight = fieldsCmp.scrollHeight - fieldsCmp.fieldHeight
      }
      fieldsCmp.scrollViewCmp.scrollTo({ x: 0, y: currentVerifyCmpHeight, animated: true })
    }
  }

  operation = () => {
    this.inputBlur(this.showActionSheet)
  }

  showActionSheet = () => {
    const BUTTONS = ['确定', '确定并新增', '删除', '取消']
    this.actionSheet = ActionSheet
    this.actionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        destructiveButtonIndex: BUTTONS.length - 2,
        wrapProps: {
          onTouchStart: e => e.preventDefault(),
        },
      },
      (buttonIndex) => {
        if (buttonIndex === 0) {
          this.close()
        } else if (buttonIndex === 1) {
          this.closeAndAdd()
        } else if (buttonIndex === 2) {
          if (this.props.hansRemove) {
            if (this.props.helper.backupFlag !== 'new') {
              Modal.alert('删除', '确定删除?', [
                { text: '取消', onPress: () => console.log('cancel') },
                { text: '确定', onPress: () => this.remove(), style: { fontWeight: 'bold' } },
              ])
            } else {
              this.remove()
            }
          } else {
            Toast.info('没有删除权限')
          }
        }
      },
    )
  }

  cancel = () => {
    if (!this.isupdateIng) {
      this.props.helper.restore()
      this.props.helper.resetBackupFlag()
      this.props.close()
      NavigationService.back()
    }
  }
  sizeChange = (width, h) => {
    if (h === 0) {
      return
    }
    this.scrollHeight = h
  }
  viewSizeChange = (e) => {
    if (e.nativeEvent.layout.height === 0) {
      return
    }
    this.fieldHeight = e.nativeEvent.layout.height
  }
  refScrollView = (ref) => { this.scrollViewCmp = ref }
  render() {
    const { submitInfoBox, submitInfoBoxHeight, keyboardHeight } = this.state
    const { columns } = this.props
    const { isShow } = submitInfoBox
    const Header = (<ModalHeader
      header={{
        leftView: (<TouchableOpacity onPress={this.cancel}><Text style={{ fontSize: 18 }}>取消</Text></TouchableOpacity>),
        centerText: <View style={{ flexDirection: 'row', justifyContent: 'center' }} ><Text style={{ fontSize: 18 }}>详情</Text></View>,
        rightView: (<TouchableOpacity onPress={this.operation}><Text style={{ fontSize: 18 }}>操作</Text></TouchableOpacity>),
      }}
    />)
    let viewHeight = height - 74 - keyboardHeight
    if (isShow) {
      viewHeight = height - 74 - keyboardHeight - submitInfoBoxHeight
    }
    this.fieldHeight = viewHeight
    return (
      <View style={{ height }}>
        {Header}
        <TextInput ref={r => this.searchInput = r} style={{ display: 'none' }} />
        <View style={{ height: viewHeight, marginTop: 10 }}>
          {
            this.state.editIndex !== -1 &&
            <KeyboardAwareScrollView
              onContentSizeChange={this.sizeChange}
              innerRef={this.refScrollView}
              style={{ flex: 1 }}
            >
              {
                this.props.mobileStyleFields.map((fieldId, index) => {
                  const fIndex = columns[fieldId]
                  if (fIndex || fIndex === 0) {
                    const field = this.props.helper.fields[fIndex]
                    let refdisplayValue
                    let treefieldCmp
                    if (isRefComponent(field)) {
                      refdisplayValue = _get(this.state.record, `${field.id}.display`)
                    }
                    if (isSelectComponent(field)) {
                      const { treeField } = field.properties
                      if (treeField) {
                        const fieldsRows = this.state.fieldsRow
                        treefieldCmp = fieldsRows.find(f => f.fieldid === treeField)
                      }
                    }
                    return (<Field
                      enabled={!this.filterForceUpdateCol(field) && field.properties.enabled}
                      key={fieldId}
                      field={field}
                      appId={this.props.appId}
                      fieldRow={{ ...this.state.fieldsRow[fIndex] }}
                      record={this.state.record}
                      formsource={this.props.formsource}
                      updateValue={this.updateValue}
                      index={index}
                      viewData={this.props.viewData}
                      cmpFocusArray={this.cmpFocusArray}
                      updateValidation={this.updateValidation}
                      inputBlur={this.inputBlur}
                      refPropDatas={this.state.refPropDatas[field.id] ? this.state.refPropDatas[field.id] : undefined}
                      refDisplay={refdisplayValue}
                      treefieldCmp={treefieldCmp}
                    />)
                  }
                  return undefined
                })
              }
            </KeyboardAwareScrollView>
          }
        </View>
        {submitInfoBox && submitInfoBox.isShow && <SubmitInfoBox hideSubmitBox={this.hideSubmitBox} submitInfoBox={submitInfoBox} scrollToVerifyCmp={this.scrollToVerifyCmp} />}
      </View>
    )
  }
}
