/* eslint-disable  */
import { Toast } from '@ant-design/react-native'
import _get from 'lodash/get'
import _uniq from 'lodash/uniq'
import has from 'lodash/has'
import pick from 'lodash/pick'
import isEmpty from 'lodash/isEmpty'
import set from 'lodash/set'
import isObject from 'lodash/isObject'
import isArray from 'lodash/isArray'
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash/debounce'
import omit from 'lodash/omit'
import moment from 'moment'

import { get, getWithCache, put } from '../../../request'
import { getValue, getFormListData } from '../Reference/store'
import { getComponents } from '../../../utils/storage/global'
import { handleFields } from '../../../containers/RunTime/Form/sagas'
import expression from '../../../utils/expression'
import expFunc from '../../../utils/expression/expressionfunc'
import { getFormFields } from './store'
import { batchNumbyPrefix, batchNumbyPrefixs } from '../FormNumber/store'
import { FORMATREPLACE } from '../../../utils/expression/expressionfunc/date'
import getList from '../Select/store'

import {
  isOptionComponent,
  isDateComponent,
  isTimeComponent,
  isRefComponent,
  isRefPropComponent,
  isSwitchComponent,
  isAppUserComponent,
  isRefStat,
  isComputedComponent,
  isFormOperationComponent,
  isFormNumberComponent,
  isNumberComponent,
  isTextComponent,
  isSelectComponent,

} from '../../constants'
import { isEqual } from '../../../utils/common'
import { union } from 'lodash-es'

const {
  getExpressionKeys,
  exec,
  getExpressionDataKeys,
  isExpression,
  getFieldxpressionKeys,
  ONLY_PORP_REG,
  EXPCONTEXT_NAMESPACE,
  findExp,
  getRefPropsRelate,
  findRefColumns,
} = expression

export const randomId = () => Math.random().toString().slice(2)

export const getRefPropsData = (appId, formId, relateData, outFields) => getWithCache(`/apps/${appId}/forms/${formId}/datas/relateFields`)({ data: { relateData, outFields } })
export const getRefPropsDataBatch = (appId, formId, relateArrData, outFields, srcFormId) => getWithCache(`/apps/${appId}/forms/${formId}/datas/relateFields/batch`)({ data: { relateArrData, outFields, srcFormId } })

const getProp = (commonProps, ownProps) => key => _get(ownProps, key) || _get(commonProps, key)

export default class Helper {
  constructor(props, context, fns, type = 1) { // type  = 1 子表单 ，2 子表单商品类
    this.props = props
    this.context = context
    this.editIndex = -1
    this.propertiesExp = {}
    this.expressionProps = {}
    this.expressionDatas = {}
    this.fieldExpressionProps = {}
    this.refPropsRelate = {}
    this.refRelateColumns = []
    this.refPropDatas = []
    this.defaultRefPropDatas = {}
    this.refComponentneedUpdate = {}
    this.fns = fns
    // 子表单所有字段
    this.fields = []
    // 新行的fields
    this._newRowFields = []
    // 新行的data
    this._newRowData = {}
    // 所有行的fields
    this.fieldsRows = []
    // 所有行的数据
    this.rowDatas = []
    // 删除的数据
    this.removedDatas = []
    // 原始的fields数据
    this.originFields = []
    // 去当前行的时候是否取新行
    this.getNew = false
    // 相关联的context中的字段是否变化
    this.refContextHasChange = true
    // 记录了关联的哪些context变化了
    this.changedContext = new Set()

    this.triggleFieldId = null
    this.triggerPropKey = null
    this._fieldsMap = {}
    this.cacheRefFields = {}
    this.enabled = props.enabled
    this.idField = {}
    this.initDataed = false
    this.cacheNeedUpdate = []
    this.validations = {}
    this.updateRefPropsDataTimer = null
    this.subFormType = type
    this.isQRadd = false
  }

  get emptyRowData() {
    return { $$emptyId: randomId() }
  }

  get newRowData() {
    return {
      $$id: randomId(), $$loaded: true, $$updated: true, ...cloneDeep(this._newRowData),
    }
  }

  get newRowFields() {
    return cloneDeep(this._newRowFields)
  }

  get emptyRowFields() {
    return []
  }

  get editFormDatas() {
    return this.rowDatas[this.editIndex]
  }

  get editFields() {
    return this.fieldsRows[this.editIndex]
  }

  setEditIndex(editIndex) {
    this.editIndex = editIndex
  }

  setEditRowData(value) {
    this.rowDatas[this.editIndex] = value
  }

  getEditRowData(specialIndex = this.editIndex) {
    if (this.getNew) return this._newRowData
    return this.rowDatas[specialIndex]
  }

  setEditFieldsRow(value) {
    this.fieldsRows[this.editIndex] = value
  }

  getEditFieldsRow(specialIndex = this.editIndex) {
    if (this.getNew) return this._newRowFields
    return this.fieldsRows[specialIndex]
  }

  openGetNewFlag() {
    this.getNew = true
  }

  closeGetNewFlag() {
    this.getNew = false
  }

  refContextChanged(fieldId) {
    this.refContextHasChange = true
    this.changedContext.add(fieldId)
  }

  restoreContextChangeFlag() {
    this.refContextHasChange = false
    this.changedContext.clear()
  }

  async initAppUsers() {
    const result = await getWithCache(`/apps/${this.props.appId}/users`)()
    if (result.errorCode === '0') {
      this.appUsers = result.data.users
    } else {
      Toast.fail(result.errorMsg)
    }
  }

  static hasAppUsersComponent(fields) {
    return fields.some(isAppUserComponent)
  }

  async loadFields() {
    const {
      appId, formId: srcFormId, formsource: { formId }, dataId, loadFieldResult,
    } = this.props
    const fieldPromise = loadFieldResult
      ? new Promise((resovle) => { resovle(loadFieldResult) })
      : get(`/apps/${appId}/forms/${formId}/datas/new/0?srcFormId=${srcFormId}${dataId ? `&srcFormDataId=${dataId}` : ''}`)()
    const [component, result] = await Promise.all([getComponents(), fieldPromise])
    if (result.errorCode === '0' && result.data && result.data.config && result.data.config.form && result.data.config.form.fields) {
      this.idField = result.data.config.form.fields.filter(field => field.id === 'id')
      const fields = result.data.config.form.fields.filter(field => field.setting && field.id !== 'id')
      this.originFields = fields
      this.components = component
      if (Helper.hasAppUsersComponent(fields)) {
        await this.initAppUsers()
      }
      await this.initFields(fields)
      if (dataId || this.props.copyDataId) {
        await this.loadDisplayDatas()
      }
      await this.initRowDatasAndFieldsRows()
      return fields
    }
    Toast.fail(result.errorMsg || '子表单没有字段')
    return []
  }

  async forceUpdate(relation) {
    if (!this.enabled) return
    const value = relation.value
    const { field } = this.findFieldByFieldId(relation.name)
    if (isFormOperationComponent(field)) {
      return
    }
    const property = isExpression(field.data[field.setting.value].value)[1]
    const originEditIndex = this.editIndex
    for (let i = 0; i < this.rowDatas.length; i += 1) {
      if (this.rowDatas[i].$$emptyId) continue
      this.forceUpdateFlag = true
      this.setEditIndex(i)
      if (!this.rowDatas[i].$$loaded) {
        await this.loadData()
      }
      if (isRefComponent(field)) {
        const fieldProps = (this.fieldsRows[i] ? this.fieldsRows[i][this.findFieldByFieldId(field.id).fieldIndex] : field.properties) || field.properties
        const refFields = await this.getRefFields(this.props.appId, fieldProps.reference.formId)
        getValue(this.props.appId, this.props.formId, this.props.dataId, value, fieldProps.reference, refFields, fieldProps.filter, this.refRelateColumns[field.id])
          .then(({ data: refValue, realValue: defaultValue } = { data: '', value: '' }) => {
            this.formUpdateValue([{
              [relation.name]: {
                refValue,
              },
            }, {
              [relation.name]: {
                defaultValue,
              },
            }], i)
          })
      } else {
        this.formUpdateValue({
          [relation.name]: {
            [property]: value,
          },
        }, i)
      }
    }
    this.setEditIndex(originEditIndex)
    if (this.forceUpdateFlag) {
      // this.fns.forceUpdate()
      this.forceUpdateFlag = false
    }
  }

  relationChange(newRelation) {
    if (newRelation.force && this.fields.length > 0) {
      this.forceUpdate(newRelation)
    }
    this.refContextChanged(newRelation.name)
  }

  initRowDataByDefaultRefField = async () => {
    const fieldInfo = this.findFieldByFieldId(this.props.formsource.defaultRefField)
    const filter = this.props.defaultFilter
    const orderby = this.props.defaultOrderby
    const refField = fieldInfo.field
    if (refField) {
      const { appId } = this.props
      const {
        formId, valueColumn, columns, subformColumn,
      } = refField.properties.reference
      let $filter = ''
      let $orderby = ''
      if (filter) $filter = `&$filter=${filter}`
      if (orderby) $orderby = `&$orderby=${orderby}`
      const searchColumn = union([...this.refRelateColumns[this.props.formsource.defaultRefField], ...columns])
      const url = `/apps/${appId}/forms/${formId}/datas?$operations=false&$title=false&$count=false${$filter}${$orderby}&$select=${searchColumn.join(',')}`
      const result = await get(url)()
      if (result.errorCode === '0') {
        const rows = result.data.items.map((item, i) => {
          if (!this.rowDatas[i]) {
            this.rowDatas.push(this.emptyRowData)
            this.fieldsRows[i] = this.emptyRowFields
          }
          if (!this.refPropDatas[i]) {
            this.refPropDatas.push({})
          }
          const refValue = subformColumn ? item[subformColumn] : item
          return {
            defaultValue: valueColumn ? refValue[valueColumn].value : refValue.id.value,
            refValue,
          }
        })
        this.setEditIndex(0)
        await this.addMutiRow(this.props.formsource.defaultRefField, rows)
        this.setEditIndex(-1)
      }
    }
  }
  async initRowDatasAndFieldsRows() {
    // for (let i = this.rowDatas.length; i < this.props.defaultRows; i += 1) {
    //   this.rowDatas.push(this.emptyRowData)
    //   this.refPropDatas.push({})
    //   this.fieldsRows[i] = this.emptyRowFields
    // }
    if (!this.props.copyDataId && !this.props.dataId) {
      await this.initRowDataByDefaultRefField()
    }
    // if (!this.props.copyDataId) {
    //   for (let i = 0; i < this.rowDatas.length; i += 1) {
    //     this.fieldsRows[i] = []
    //   }
    //   if (!this.props.dataId) {
    //     await this.initRowDataByDefaultRefField()
    //   }
    // }
  }

  async getFk(appId, formId, fieldId) {
    const result = await get(`/apps/${appId}/forms/${formId}/subformfields/${fieldId}/fk`)()
    if (result.errorCode === '0' && result.data) {
      return result.data.field
    }
    Toast.fail(result.errorMsg)

    return ''
  }

  getDefaultValueProp = field => isExpression(field.data[field.setting.value].value)[1]

  /**
   * 子表单插入多行多列数据方法
   *
   * @param {*} matrixDatas
   * @param {*} editColFieldId
   */
  async insertMatrix(matrixDatas, editColFieldId) {
    for (let i = 0; i < matrixDatas.length; i += 1) {
      const lineData = matrixDatas[i]
      const currentEditIndex = this.editIndex + i
      if (currentEditIndex >= this.rowDatas.length) {
        this.addRow(currentEditIndex)
      }
      if (i !== 0 && this.rowDatas[currentEditIndex] && this.rowDatas[currentEditIndex].$$emptyId) {
        await this.startEdit(currentEditIndex)
      }
      for (let j = 0; j < lineData.length; j += 1) {
        const fieldId = editColFieldId[j]
        this.setFieldData(fieldId, lineData[j], currentEditIndex)
      }
    }
  }
  /**
   * 子表单根据id和对应的行，插入数据方法
   *
   * @param {*} fieldId 字段id
   * @param {*} fieldData 字段数据
   * @param {*} editRowIndex 要编辑子表单的行
   */
  async setFieldData(fieldId, fieldData, editRowIndex) {
    const fieldInfo = this.findFieldByFieldId(fieldId)
    const field = { ...fieldInfo.field, properties: this.fieldsRows[editRowIndex][fieldInfo.fieldIndex] }
    if (field.properties.enabled === true) {
      const defaultValueProps = this.getDefaultValueProp(field)
      if (isTextComponent(field) || field.type === 'TextInput') {
        this.formUpdateValue({
          [fieldId]: {
            [defaultValueProps]: fieldData,
          },
        }, editRowIndex).then(this.fns.forceUpdate)
      } else if (isDateComponent(field)) {
        const feFormat = FORMATREPLACE(field.properties.format)
        let dateValue = ''
        if (feFormat === 'YYYY' && fieldData.length > 4) {
          dateValue = moment(Number(fieldData))
        } else {
          dateValue = moment(Number(fieldData), this.feFormat)
        }
        if (dateValue.toString() !== 'Invalid date') {
          this.formUpdateValue({
            [fieldId]: {
              [defaultValueProps]: dateValue.toDate().getTime(),
            },
          }, editRowIndex).then(this.fns.forceUpdate)
        }
      } else if (isSelectComponent(field)) {
        const { intostore, option, orderby, filter, source, maxValue, sourcefield } = field.properties
        if (intostore) {
          this.formUpdateValue({
            [fieldId]: {
              [defaultValueProps]: fieldData,
            },
          }, editRowIndex).then(this.fns.forceUpdate)
        } else {
          let values = []
          if (sourcefield) {
            const thisorderby = orderby ? `&$orderby=${orderby}` : ''
            const thisfilter = filter ? `&$filter=(${filter})` : ''
            const result = await getList(this.props.appId, source, `$limit=${maxValue}&$select=${sourcefield}${thisorderby}${thisfilter}`)
            values = result.map(item => _get(item, sourcefield).display)
          } else {
            values = option.options.map(item => item.value)
          }
          if (values.indexOf(fieldData) !== -1) {
            this.formUpdateValue({
              [fieldId]: {
                [defaultValueProps]: fieldData,
              },
            }, editRowIndex).then(this.fns.forceUpdate)
          }
        }
      } else if (isNumberComponent(field)) {
        if (Number.isNaN(fieldData) && fieldData.indexOf(',') !== -1) {
          const upvalue = fieldData.replace(new RegExp(',', 'g'), '')
          this.formUpdateValue({
            [fieldId]: {
              [defaultValueProps]: Number(upvalue),
            },
          }, editRowIndex).then(this.fns.forceUpdate)
        } else if (!Number.isNaN(fieldData)) {
          this.formUpdateValue({
            [fieldId]: {
              [defaultValueProps]: Number(fieldData),
            },
          }, editRowIndex).then(this.fns.forceUpdate)
        }
      } else if (isRefComponent(field) && field.properties.searchcolumn) {
        const { appId, formId, dataId } = this.props
        // 当为引用的时候，先根据引用的值字段查询引用那条记录的数据refValue
        const res = await getValue(
          appId, formId, dataId, fieldData, field.properties.reference, null,
          field.properties.filter,
          this.refRelateColumns[field.id],
        )
        if (_get(res, 'data.id.value', null)) {
          const { reference } = field.properties
          const refValue = reference.valueColumn ? res.data[reference.valueColumn].value : res.data.id.value
          this.formUpdateValue([
            { [fieldId]: { defaultValue: refValue } },
            { [fieldId]: { refValue: { ...res.data } } },
          ], editRowIndex).then(this.fns.forceUpdate)
        } else {
          const refData = await this.loadRefDataBySearchValue(field, fieldData)
          if (refData) {
            const { reference } = field.properties
            const value = reference.valueColumn ? refData[reference.valueColumn].value : refData.id.value
            this.formUpdateValue([
              { [fieldId]: { defaultValue: value } },
              { [fieldId]: { refValue: { ...refData } } },
            ], editRowIndex).then(this.fns.forceUpdate)
          }
        }
      } else if (field.type === 'BarcodeParsing') {
        const searchText = fieldData.slice(0, fieldData.length - field.properties.rules[field.properties.shibiema])
        const barRefData = await this.loadRefDataBySearchValue(field, searchText)
        if (barRefData) {
          const { reference } = field.properties
          const barValue = reference.valueColumn ? barRefData[reference.valueColumn].value : barRefData.id.value
          this.formUpdateValue([
            { [fieldId]: { defaultValue: barValue } },
            { [fieldId]: { refValue: { ...barRefData } } },
            { [fieldId]: { tiaoma: fieldData } },
          ], editRowIndex).then(this.fns.forceUpdate)
        } else {
          this.formUpdateValue([
            { [fieldId]: { tiaoma: fieldData } },
          ], editRowIndex).then(this.fns.forceUpdate)
        }
      }
    }
  }

  async loadDisplayDatas() {
    const { copyDataId, classifyProps } = this.props
    const defaultValue = this.props.defaultValue || []
    this.rowDatas = defaultValue.map(rowData => ({ ...rowData, $$loaded: true }))
    for (let i = 0; i < this.rowDatas.length; i += 1) {
      this.setEditIndex(i)

      this.setEditFieldsRow([])
      const fieldsRow = await this.updateFieldPropsByFromData()
      this.setEditFieldsRow(fieldsRow)
      for (let index = 0; index < this.fields.length; index += 1) {
        await this.setDisplayData(index)
      }
      if (copyDataId) {
        let classifyRowIds = []
        if (classifyProps) {
          classifyRowIds = classifyProps.getclassifyRowIds(this.rowDatas[i])
        }
        await this.loadData(this.rowDatas[i].id.value, classifyRowIds)
        this.rowDatas[i].$$id = randomId()
        this.rowDatas[i].$$updated = true
        delete this.rowDatas[i].id
      }
      if (!this.refPropDatas[i]) {
        this.refPropDatas.push({})
      }
      await this.initRefRelatData(i, this.rowDatas[i])
    }
    this.setEditIndex(-1)
  }
  initRefRelatData = async (editIndex = this.editIndex, formData) => {
    const data = formData || this.getEditRowData(editIndex)
    const refPropsRelates = this.refPropsRelate
    const outFields = _uniq(refPropsRelates.reduce((res, r) => ([...res, ...r.refId]), []))
    if (refPropsRelates && refPropsRelates.length !== 0 && outFields && outFields.length !== 0) {
      const result = pick(formData, outFields)
      if (result.errorCode === '0') {
        const currentRefPropDatas = this.refPropDatas[editIndex] || {}
        this.refPropDatas = [...this.refPropDatas.slice(0, editIndex), { ...currentRefPropDatas, ...result.data }, ...this.refPropDatas.slice(editIndex + 1)]
      }
    }
  }
  async loadData(copyDataId, classifyRowIds) {
    const {
      appId, formId, formsource: { formId: subFormId }, dataId, classifyProps,
    } = this.props
    const rowDatas = this.getEditRowData()
    let result = {}
    const getFunc = async id => await get(`/apps/${appId}/forms/${subFormId}/datas/new/${id}?srcFormId=${formId}${copyDataId ? '' : `&srcFormDataId=${dataId}`}&$config=true`)()
    if (copyDataId && classifyRowIds && classifyRowIds.length) {
      result.errorCode = '0'
      result.data = []
      for (let i = 0; i < classifyRowIds.length; i += 1) {
        const res = await getFunc(classifyRowIds[i])
        if (res.errorCode !== '0') {
          result.errorCode = res.errorCode
        }
        result.data.push(res)
      }
    } else {
      result = await getFunc(rowDatas.id.value)
    }
    if (result.errorCode === '0' && result.data) {
      if (classifyProps) result = classifyProps.classifyLoaddata(result, classifyRowIds)
      this.setEditRowData(Object.keys(rowDatas)
        .concat(Object.keys(omit(result.data, 'config')))
        .reduce((prev, fieldId) => {
          const { field } = this.findFieldByFieldId(fieldId)
          if (!field) {
            return prev
          }
          // 引用属性保持不变
          if (isRefPropComponent(field) || isRefStat(field) || isComputedComponent(field)) {
            return {
              ...prev,
              [fieldId]: rowDatas[fieldId],
            }
          }
          if (typeof rowDatas[fieldId] === 'object') {
            return {
              ...prev,
              [fieldId]: {
                ...rowDatas[fieldId],
                ...result.data[fieldId],
              },
            }
          }
          return {
            ...prev,
            [fieldId]: result.data[fieldId],
          }
        }, { $$loaded: true, id: rowDatas.id }))
      let fieldsRow = []
      const rowFields = this.fields
        .map(f => result.data.config.form.fields.find(({ id }) => id === f.id) || { $$emptyCol: true })
        .filter(f => f)
      fieldsRow = await this.updateFieldPropsByFromData(
        undefined,
        handleFields(rowFields, this.components),
      )
      this.setEditFieldsRow(fieldsRow)
    }
  }

  async initFields(fields) {
    this.fields = handleFields(fields, this.components, 'subform') // 当前子表单中的字段
    this.initPropertiesExp()
    this._newRowData = await this.initRowData()
    this._newRowFields = await this.initProperties()
    // 延后调用
    // await this.getInitData(true)

    for (let index = 0; index < this.fields.length; index += 1) {
      await this.setDisplayData(index, this._newRowFields, this._newRowData)
    }
    if (this.props.classifyProps) this.props.classifyProps.recordNewRowData(cloneDeep(this._newRowData))
  }

  // 子表单初始化显示值
  async getInitData(updateAll = false, editIndex = this.editIndex) {
    if (!this.refContextHasChange) return
    const relations = this.props.formsource.relations
    for (let i = 0; i < relations.length; i += 1) {
      const name = relations[i].name
      const value = relations[i].value
      const currfield = this.fields.filter(({ id }) => id === name)[0]
      if (!currfield) {
        continue
      }
      const propKey = isExpression(currfield.data[currfield.setting.value].value)[1]
      if ((this.initDataed && !updateAll && this.changedContext.size > 0 && !this.changedContext.has(name))) {
        continue
      }
      await this.updateRefFields(value, currfield, propKey, editIndex)
    }
    this.initDataed = true
    this.openGetNewFlag()
    await this.handleSpecialComponents(this._newRowData, this._newRowFields)
    this.closeGetNewFlag()
    this.restoreContextChangeFlag()
  }

  async updateRefFields(value, currfield, propKey, editIndex) {
    this.openGetNewFlag()
    if (isRefPropComponent || isComputedComponent) {
      this.defaultRefPropDatas = { ...this.defaultRefPropDatas, [currfield.id]: { value, display: value } }
    }
    await this.formUpdateValue({
      [currfield.id]: {
        [propKey]: value,
      },
    }, editIndex)

    this.closeGetNewFlag()
  }

  // 初始化表达式-属性对应关系
  initPropertiesExp(fields = this.fields) {
    for (let i = 0; i < fields.length; i += 1) {
      const field = fields[i]
      const { propertiesExp, expressionProp } = getExpressionKeys(field.properties, fields)
      this.propertiesExp[field.id] = propertiesExp
      this.expressionProps[field.id] = expressionProp
      this.expressionDatas[field.id] = getExpressionDataKeys(field.data, field.properties)
    }
    this.fieldExpressionProps = getFieldxpressionKeys(fields)
    this.refPropsRelate = getRefPropsRelate([...fields, ...this.idField])
    this.refRelateColumns = findRefColumns(fields, this.fieldExpressionProps)
  }

  // 初始化行数据
  async initRowData(fields = this.fields) {
    const handled = []
    const handler = async (formData, field, fieldkey) => {
      if (field.data && field.data.length !== 0) {
        if (formData[field.id] === undefined) {
          formData[field.id] = {} // eslint-disable-line
        }
        let value
        if (isComputedComponent(field)) {
          const noneKeyCr = this.refPropsRelate.find(cr => cr.refId.indexOf(field.id) !== -1)
          if (noneKeyCr) {
            const outFields = noneKeyCr.refId.join(',')
            const relateFileds = noneKeyCr.key.reduce((res, k) => {
              const [fid] = k.split('.')
              return { ...res, [fid]: _get(formData, k) }
            }, {})
            const res = await getRefPropsData(this.props.appId, this.props.formsource.formId, JSON.stringify(relateFileds), outFields)
            value = _get(res.data, `${field.id}.value`)
            this.defaultRefPropDatas = { ...this.defaultRefPropDatas, ...res.data }
          }
        } else {
          value = await exec(field.data[fieldkey].value, { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...formData } })
          if (value && fieldkey === 'f1' && isRefComponent(field)) {
            const { appId, formId, dataId } = this.props
            const fieldProps = field.properties
            const refFields = await this.getRefFields(this.props.appId, fieldProps.reference.formId)
            const { data } = await getValue(
              appId, formId, dataId, value, fieldProps.reference, refFields,
              isExpression(fieldProps.filter) ? '' : fieldProps.filter,
              [...this.refRelateColumns[field.id], fieldProps.reference.currColumn],
            )
            formData[field.id]['ref'] = data === undefined ? null : data// eslint-disable-line
          }
        }
        if (!(isRefComponent(field) && fieldkey === 'ref')) {
          formData[field.id][fieldkey] = value === undefined ? null : value// eslint-disable-line
        }
        if (value) {
          const res = await this.updateRefPropsData(this.editIndex, field.id, fieldkey, formData)
          if (res) {
            this.defaultRefPropDatas = { ...this.defaultRefPropDatas, ...res }
          }
        }
      }
    }
    const check = async (formData, field) => {
      for (let i = 0; i < Object.keys(field.data).length; i += 1) {
        const fieldkey = Object.keys(field.data)[i]
        if (handled.indexOf(field.id + fieldkey) !== -1) continue
        handled.push(field.id + fieldkey)
        const match1 = isExpression(field.data[fieldkey].value)
        if (match1) {
          const value = _get(field.properties, match1[1])
          const match = isExpression(value)
          if (match) {
            const expKeywords = findExp(match[1]).map(keyword => keyword.split('.')[0])
            for (let j = 0; j < expKeywords.length; j += 1) {
              const parent = fields.find(f => f.id === expKeywords[j])
              if (parent) {
                await check(formData, parent)
              }
            }
          }
          await handler(formData, field, fieldkey)
        }
      }
    }
    const formData = {}
    for (let j = 0; j < fields.length; j += 1) {
      const field = fields[j]
      await check(formData, field)
    }
    return formData
  }

  async initProperties(formData = this.newRowData, fields = this.fields) {
    const initProperty = async (properties, keys = '', property, field) => {
      const props = keys ? _get(properties, keys) : properties
      for (let i = 0; i < Object.keys(props).length; i += 1) {
        const propkeys = Object.keys(props)[i]
        const currkeys = keys ? `${keys}.${propkeys}` : propkeys
        const currProp = _get(properties, currkeys)
        if (isObject(currProp)) {
          await initProperty(properties, currkeys, property, field)
        } else if (isExpression(currProp)) {
          let data
          if (!(field && isComputedComponent(field) && currkeys === 'defaultValue')) {
            data = await exec(currProp, { [EXPCONTEXT_NAMESPACE]: { ...properties, ...formData } })
          }
          property.push({
            currkeys,
            data: data === undefined ? '' : data,
          })
        }
      }
      return property
    }
    const newFields = []
    for (let i = 0; i < fields.length; i += 1) {
      const field = fields[i]
      const property = await initProperty(field.properties, '', [], field)
      const newField = cloneDeep(field.properties)
      property.forEach((p) => {
        set(newField, p.currkeys, p.data)
      })
      newFields.push(newField)
    }
    return newFields
  }

  async updateFieldPropsByFromData(formData = this.getEditRowData(), fields = this.fields) {
    const props = []
    fields.forEach((field) => {
      const prop = {}
      props.push(prop)
      if (!field.data) return
      Object.keys(field.data).forEach((fieldKey) => {
        const exp = field.data[fieldKey].value
        if (typeof exp === 'string' && exp.match(ONLY_PORP_REG)) {
          const propKye = exp.match(ONLY_PORP_REG)[1]
          if (formData[field.id] && typeof formData[field.id][fieldKey] !== 'undefined') {
            set(prop, propKye, formData[field.id][fieldKey])
          }
        }
      })
    })
    const newProps = await this.initPropertiesData(props, fields)
    return newProps
  }

  async initPropertiesData(props, fields = this.fields) {
    const newFields = []
    for (let i = 0; i < fields.length; i += 1) {
      const field = fields[i]
      const prop = await this.initProperty({ ...field.properties, ...props[i] }, '', field)
      newFields.push(prop)
    }
    return newFields
  }

  async initProperty(properties, keys = '', field, formData = this.editFormDatas) {
    const props = keys ? _get(properties, keys) : properties
    for (let i = 0; i < Object.keys(props).length; i += 1) {
      const propkeys = Object.keys(props)[i]
      const currkeys = keys ? `${keys}.${propkeys}` : propkeys
      const currProp = _get(properties, currkeys)
      if (isObject(currProp)) {
        await this.initProperty(properties, currkeys, field)
      } else if (isExpression(currProp)) {
        let data
        if (!(field && isComputedComponent(field) && currkeys === 'defaultValue')) {
          data = await exec(currProp, { [EXPCONTEXT_NAMESPACE]: { ...properties, ...formData } })
        }
        set(properties, currkeys, data === undefined ? '' : data)
      }
    }
    return properties
  }
  forceUpdateSubformDate = debounce(() => { this.fns.forceUpdate() }, 500)

  async formUpdateValue(originValue, editIndex = this.editIndex) {
    let fieldIndex = -1
    let field
    console.log(originValue, editIndex = this.editIndex)
    const updateValue = async (value) => {
      let fields = this.getEditFieldsRow(editIndex)
      if ((!fields || fields.length === 0) && editIndex === 0) {
        fields = this._newRowFields
      }
      if (fields) {
        const fieldInfo = this.findFieldByFieldId(Object.keys(value)[0])
        fieldIndex = fieldInfo.fieldIndex
        field = fieldInfo.field
        if (!fields[fieldIndex]) {
          fields[fieldIndex] = {}
        }
        const valueKey = Object.keys(value[field.id])[0]
        const valueData = value[field.id][valueKey]
        this.triggleFieldId = field.id
        this.triggerPropKey = valueKey
        const props = set(fields[fieldIndex], valueKey, valueData)
        await this.updateProps(editIndex, props, valueKey, field.id)
        this.triggleFieldId = null
        this.triggerPropKey = null
      }
    }
    if (isArray(originValue)) {
      for (let i = 0; i < originValue.length; i += 1) {
        await updateValue(originValue[i])
      }
    } else {
      await updateValue(originValue)
    }
    // this.fns.forceUpdate()
    this.forceUpdateSubformDate()
  }

  getRefFields = async (appId, formId) => {
    if (this.cacheRefFields && this.cacheRefFields[formId]) {
      return this.cacheRefFields[formId]
    }
    const fields = await getFormFields(appId, formId)
    this.cacheRefFields[formId] = fields
    return this.cacheRefFields[formId]
  }

  async setDisplayData(fieldIndex, fieldsRow = this.getEditFieldsRow(), dataRow = this.getEditRowData()) {
    const getDisplay = async (value, commonProps, ownProps, field) => {
      const getOneProp = getProp(commonProps, ownProps)
      let data
      if (isOptionComponent(field)) {
        return value && value.join(',')
      } else if (isDateComponent(field) || isTimeComponent(field)) {
        return expFunc.DATEFORMAT([value, getOneProp('format')])
      } else if (isRefComponent(field)) {
        const reference = getOneProp('reference')
        data = getOneProp('refValue')[reference.currColumn]
        if (data && data.display) {
          return data && data.display
        } else if (dataRow[field.id] && dataRow[field.id].ref) {
          return _get(dataRow[field.id].ref, `${reference.currColumn}.value`) || ''
        }
        return ''
      } else if (isRefPropComponent(field)) {
        data = getOneProp('defaultValue')
        return data && data.display
      } else if (isAppUserComponent(field)) {
        const user = this.appUsers && this.appUsers.find(({ id }) => id === value)
        return user && user.name
      } else if (isSwitchComponent(field)) {
        return value ? getOneProp('open') : getOneProp('close')
      }
      return value
    }
    const field = this.fields[fieldIndex]
    if (dataRow[field.id]) {
      dataRow[field.id].display = await getDisplay(dataRow[field.id][field.setting.value], field.properties, fieldsRow[fieldIndex], field) // eslint-disable-line
    }
  }

  refupdateBatchFormNumber = async (newupdate) => {
    const ids = []
    const rdatas = newupdate.reduce((res, { formData, editIndex, fieldId, propertykey, getNew }) => {
      const data = formData || this.getEditRowData(editIndex)
      const { field } = this.findFieldByFieldId(fieldId)
      if (isFormNumberComponent(field) && editIndex > -1 && propertykey === 'prefix') {
        if (ids.indexOf(fieldId) < 0) ids.push(fieldId)
        const r = _get(data, `${fieldId}.${propertykey}`) || ''
        return [...res, { editIndex: getNew ? -100 : editIndex, [fieldId]: r }]
      }
      return res
    }, [])
    if (ids.length > 0) {
      for (let i = 0; i < ids.length; i += 1) {
        const ableData = rdatas.filter(r => r[ids[i]] && r.editIndex !== this.editIndex)
        const prefixs = ableData.map(r => r[ids[i]])
        if (prefixs.join(',')) {
          const result = await batchNumbyPrefixs(this.props.appId, this.props.formsource.formId, ids[i], prefixs.join(','))
          ableData.forEach((rd, j) => {
            this.updateOtherProps({ editIndex: rd.editIndex, fieldId: ids[i], value: result[j].serialNumber })
          })
        }
      }
    }
  }
  updateBatchFormNumber = async (newupdate, rdatas) => {
    const formnumberIds = newupdate.map(({ editIndex, fieldId }) => {
      const { field: Field } = this.findFieldByFieldId(fieldId)
      if (isFormNumberComponent(Field) && editIndex < 0) {
        return Field
      }
      return null
    }).filter(f => f)
    this.formnumberIds = _uniq([...(this.formnumberIds || []), ...formnumberIds])
    let newdatas = rdatas
    if (rdatas.length < 1) {
      newdatas = newupdate.reduce((res, { formData, editIndex, fieldId, propertykey }) => {
        const data = formData || this.getEditRowData(editIndex)
        const { field } = this.findFieldByFieldId(fieldId)
        if ((this.props.formsource.defaultRefField === fieldId || isRefComponent(field)) && editIndex > -1 && propertykey === 'f1') {
          const resIndex = res.findIndex(r => r.editIndex === editIndex)
          if (resIndex > -1) {
            res[resIndex] = { editIndex, data }
            return res
          }
          return [...res, { editIndex, data }]
        }
        return res
      }, [])
    }
    if (this.formnumberIds.length > 0 && newdatas.length > 0) {
      for (let i = 0; i < this.formnumberIds.length; i += 1) {
        const formnumberId = this.formnumberIds[i].id
        const ableData = newdatas.filter(r => r.editIndex !== this.editIndex && !r.data[formnumberId].number)
        const prefixs = ableData.reduce((res, { data }) => {
          const r = _get(data, `${formnumberId}.prefix`) || ''
          return [...res, r]
        }, [])
        if (prefixs.join(',')) {
          const { numberflag } = this.formnumberIds[i].properties
          const result = isExpression(numberflag) ? await batchNumbyPrefixs(this.props.appId, this.props.formsource.formId, formnumberId, prefixs.join(','))
            : await batchNumbyPrefix(this.props.appId, this.props.formsource.formId, formnumberId, prefixs[0], prefixs.length)
          ableData.forEach((rd, j) => {
            this.updateOtherProps({ editIndex: rd.editIndex, fieldId: formnumberId, value: result[j].serialNumber })
          })
        }
      }
    }
  }
  findRelateDataAndRelatedate = ({
    formData, editIndex, fieldId, propertykey,
  }) => {
    const data = formData || this.getEditRowData(editIndex)
    const { field: Field } = this.findFieldByFieldId(fieldId)
    const refPropsRelates = this.refPropsRelate.filter(r => r.key.indexOf(`${fieldId}.${propertykey}`) !== -1 && !isComputedComponent(Field) && !isRefPropComponent(Field))
    if (refPropsRelates && refPropsRelates.length !== 0) {
      const relateData = refPropsRelates.reduce((res, r) => ({
        ...res,
        ...r.key.reduce((tr, k) => {
          const { field } = this.findFieldByFieldId(k.split('.')[0])
          let d = _get(data, k) || ''
          if (isRefComponent(field)) {
            d = _get(data, `${field.id}.${field.setting.value}`) || ''
          }
          return ({ ...tr, [`${field.id}${isRefComponent(field) ? '.value' : ''}`]: d })
        }, {}),
      }), {})
      const outFields = _uniq(refPropsRelates.reduce((res, r) => ([...res, ...r.refId]), []))
      return { relateData, outFields, data }
    }
    return false
  }
  updateOtherProps = async ({ editIndex = this.editIndex, fieldId, value }) => {
    const { field: Field } = this.findFieldByFieldId(fieldId)
    if (!this.fns.isNotHideColumn(Field) || this.editIndex !== editIndex || this.subFormType === 2) {
      if (isRefPropComponent(Field)) {
        await this.formUpdateValue({ [fieldId]: { defaultValue: value } }, editIndex)
      } else if (isComputedComponent(Field)) {
        await this.formUpdateValue({ [fieldId]: { defaultValue: value.value } }, editIndex)
      }
    }
    if (isFormNumberComponent(Field)) {
      await this.formUpdateValue({ [fieldId]: { serialnumber: value } }, editIndex)
    }
  }
  updateBatchRefData = async () => {
    const newupdate = cloneDeep(this.cacheNeedUpdate)
    this.cacheNeedUpdate = []
    const rdatas = newupdate.reduce((res, data) => {
      const r = this.findRelateDataAndRelatedate(data)
      if (r) {
        return [...res, { editIndex: data.editIndex, ...r }]
      }
      return res
    }, [])
    const paramData = rdatas.reduce((res, d) => {
      const { outFields = [], poutFields = {}, relateArrData = {} } = res
      const { editIndex: idx, outFields: of, relateData: rd } = d
      if (relateArrData[idx]) {
        return {
          outFields: [...outFields, ...of],
          poutFields: { ...poutFields, [idx]: [...poutFields[idx], ...of] },
          relateArrData: { ...relateArrData, [idx]: { ...relateArrData[idx], ...rd } },
        }
      }
      return {
        outFields: [...outFields, ...of],
        poutFields: { ...poutFields, [idx]: of },
        relateArrData: { ...relateArrData, [idx]: rd },
      }
    }, {})
    if (paramData && paramData.relateArrData && paramData.outFields) {
      const relatearrayDataParm = Object.keys(paramData.relateArrData).map(key => paramData.relateArrData[key])
      const result = await getRefPropsDataBatch(this.props.appId, this.props.formsource.formId, JSON.stringify(relatearrayDataParm), _uniq(paramData.outFields).join(','), this.props.formId)
      if (result.errorCode !== 0) {
        Object.keys(paramData.relateArrData).forEach((key, i) => {
          const idx = Number(key)
          const currentRefPropDatas = this.refPropDatas[idx] || {}
          // const resData = result.data.items[i]
          const resData = pick(result.data.items[i], paramData.poutFields[key])
          this.refPropDatas = [...this.refPropDatas.slice(0, idx), { ...currentRefPropDatas, ...resData }, ...this.refPropDatas.slice(idx + 1)]
          Object.keys(resData).forEach((fid) => {
            this.updateOtherProps({ editIndex: idx, fieldId: fid, value: resData[fid] || '' })
          })
        })
      }
    }
    this.updateBatchFormNumber(newupdate, rdatas)
    this.refupdateBatchFormNumber(newupdate)
    this.fns.forceUpdate()
    if (this.openEditModal && this.updateEditModal) {
      this.updateEditModal()
    }
    this.updateRefPropsDataTimer = null
  }

  updateRefPropsDataAsync = (editIndex, fieldId, propertykey) => {
    this.cacheNeedUpdate.push({ editIndex, fieldId, propertykey })
    if (this.cacheNeedUpdate.length > 25) {
      this.updateBatchRefData()
    }
    if (this.updateRefPropsDataTimer) {
      clearTimeout(this.updateRefPropsDataTimer)
    }
    this.updateRefPropsDataTimer = setTimeout(this.updateBatchRefData, 800)
  }

  async updateRefPropsData(editIndex, fieldId, propertykey) {
    this.updateRefPropsDataAsync(editIndex, fieldId, propertykey)
  }
  async updateField({
    editIndex, fieldId, properties, propertykey,
  }) {
    const { field, fieldIndex } = this.findFieldByFieldId(fieldId)
    if (isRefComponent(field) && propertykey === 'defaultValue') {
      const { appId, formId, dataId } = this.props
      const editFields = this.getEditFieldsRow(editIndex)
      const fieldProps = properties || (editFields ? editFields[fieldIndex] : field.properties)
      const refFields = await this.getRefFields(this.props.appId, fieldProps.reference.formId)
      if (fieldProps.defaultValue) {
        const { data, value, realValue } = await getValue(
          appId, formId, dataId, fieldProps.defaultValue, fieldProps.reference, refFields,
          fieldProps.filter,
          this.refRelateColumns[field.id],
        )
        const rowData = this.getEditRowData(editIndex)
        rowData[field.id].display = value
        const props = set(fieldProps, 'refValue', data)
        await this.updateProps(editIndex, props, 'refValue', fieldId)
      } else {
        const rowData = this.getEditRowData(editIndex)
        rowData[field.id].display = ''
        const props = set(fieldProps, 'refValue', {})
        await this.updateProps(editIndex, props, 'refValue', fieldId)
      }
    } else if (isRefComponent(field) && propertykey === 'filter') {
      const value = _get(properties, propertykey)
      debugger
      await this.updateRefPropsFilter(editIndex, fieldId, value, field)
    }
  }
  async updateProps(editIndex, properties, propertykey, fieldId) {
    const { propertiesExp, expressionProps, expressionDatas } = this
    // 更新这个属性相关的data
    await this.updateFormDataByProps(editIndex, {
      expressionDatas,
      fieldId,
      propertykey,
      properties,
    })
    await this.manualClearRef(fieldId, propertykey, editIndex, properties)
    // 更新某个属性, 去查找有哪些其他属性引用了这个属性, 更新这些属性
    const expressionPropsFieldId = expressionProps[fieldId]
    // 找到 表达式-属性 的map中以这个属性为开头的值, 比如更新了defaultValue, 可能map关系是 defaultValue.f1: ['label']
    const willUpdatePropertyKeys = Object.keys(expressionPropsFieldId)
      .filter(key => key.startsWith(propertykey))
    for (let i = 0; i < willUpdatePropertyKeys.length; i += 1) {
      const willUpdatePropertyKey = willUpdatePropertyKeys[i]
      // 有可能一个属性被多个属性引用,所以expressionProps的值是个数组
      for (let j = 0; j < expressionPropsFieldId[willUpdatePropertyKey].length; j += 1) {
        const propKey = expressionPropsFieldId[willUpdatePropertyKey][j]
        // 拿到属性原始的表达式
        const orgValue = propertiesExp[fieldId][propKey]
        // 计算表达式
        const value = await exec(orgValue, { [EXPCONTEXT_NAMESPACE]: { ...properties, ...this.getEditRowData(editIndex) } })
        set(properties, propKey, value === undefined ? '' : value)
        // 更新关联的属性
        if (expressionPropsFieldId[propKey]) {
          await this.updateProps(editIndex, properties, propKey, fieldId)
        }
        // 更新关联属性的data
        await this.updateFormDataByProps(editIndex, {
          expressionDatas,
          fieldId,
          propertykey: propKey,
          properties,
        })
      }
    }
    await this.updateField({
      editIndex, fieldId, propertykey, properties,
    })
  }
  execFilterChange = async (filter, parperties) => {
    const { defaultValue, reference } = parperties
    const { appId, formId, dataId } = this.props
    if (!isExpression(defaultValue)) {
      const { data, value, realValue } = await getValue(appId, formId, dataId, defaultValue, reference, undefined, filter ? `( ${filter} )` : '')
      if (data && value && realValue) {
        return true
      }
    }
    return false
  }
  async manualClearRef(fieldId, propertykey, editIndex, properties) {
    const { field } = this.findFieldByFieldId(fieldId)
    const { defaultValue, filter } = properties
    if (isRefComponent(field) && propertykey === 'filter' && defaultValue) {
      const flag = await this.execFilterChange(filter, properties)
      if (!flag) {
        await this.formUpdateValue([{
          [fieldId]: {
            defaultValue: '',
          },
        }, {
          [fieldId]: {
            refValue: '',
          },
        }], editIndex)
      }
    }
  }
  async updateFormDataByProps(editIndex, {
    expressionDatas, fieldId, propertykey, properties,
  }) {
    if (!propertykey) return
    const willUpdatePropertyKeys = Object.keys(expressionDatas[fieldId])
      .filter(key => key.startsWith(propertykey))
    for (let i = 0; i < willUpdatePropertyKeys.length; i += 1) {
      const willUpdatePropertyKey = willUpdatePropertyKeys[i]
      for (let j = 0; j < expressionDatas[fieldId][willUpdatePropertyKey].length; j += 1) {
        const datakey = expressionDatas[fieldId][willUpdatePropertyKey][j]
        await this.updateFormData(editIndex, properties, datakey, fieldId)
      }
    }
  }

  async updateFormData(editIndex, properties, datakey, fieldId) {
    const formDatas = this.getEditRowData(editIndex)
    const { fieldIndex, field } = this.findFieldByFieldId(fieldId)
    if (formDatas[fieldId] === undefined) {
      formDatas[fieldId] = {}
    }
    const formData = formDatas[fieldId]
    const dataKeys = datakey.split('.')
    if (formData && dataKeys[1] === 'value') {
      const value = await exec(_get(field.data, datakey), { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...properties, ...formDatas } })
      // 不是新增而且相等才不继续计算
      if (isEqual(value, formData[dataKeys[0]])) {
        return
      }
      formData[dataKeys[0]] = value
      await this.updateRefPropsData(editIndex, fieldId, dataKeys[0], formDatas)
    }

    await this.setDisplayData(fieldIndex, this.getEditFieldsRow(editIndex), this.getEditRowData(editIndex))
    // 合并的时候，如果需要更改合并字段的属性，禁止更改
    if (this.ismergeing === true && this.props.mergeprops && this.props.mergeprops.mergeField && this.props.mergeprops.mergeField.indexOf(fieldId) !== -1) {
      return
    }
    await this.updatePropsByFormData(editIndex, properties, `${fieldId}.${dataKeys[0]}`)
  }

  static findKeys(dataKeys, fieldExps) {
    let datakey = Object.keys(fieldExps).find(key => key.startsWith(dataKeys))
    if (!datakey) {
      datakey = Object.keys(fieldExps).reduce((res, item) => {
        if (item.indexOf(dataKeys) === 0) {
          res.push(item)
        }
        return res
      }, [])
    }
    if (datakey) {
      if (typeof datakey === 'string') {
        datakey = [datakey]
      }
      return datakey
    }
    return []
  }
  static expDatakeySort = (a, b) => {
    const srotarray = ['filter', 'desc', 'defaultValue']
    return srotarray.indexOf(a) - srotarray.indexOf(b)
  }

  static selctRefDatasByFieldIdAndDataKey = (dataKey, fieldExpressionProps) => Object.keys(fieldExpressionProps)
    .filter(key => key.startsWith(dataKey))
    .reduce((result, key) => {
      const res = result
      Object.keys(fieldExpressionProps[key]).forEach((k2) => {
        if (has(result, k2)) {
          res[k2] = _uniq([...result[k2], ...fieldExpressionProps[key][k2]].sort(Helper.expDatakeySort))
        } else {
          res[k2] = fieldExpressionProps[key][k2]
        }
      })
      return res
    }, {})

  async updateRefPropsFilter(editIndex, fieldId, value, field) {
    if (field.properties.isDefaultValue === true) {
      const { securityscope, orderby, reference } = (this.fieldsRows[editIndex] ? this.fieldsRows[editIndex][this.findFieldByFieldId(field.id).fieldIndex] : field.properties) || field.properties
      const { appId } = this.props
      const extraParams = securityscope ? `&srcFormId=${this.props.formsource.formId}&srcFormFieldId=${fieldId}` : ''
      const result = await getFormListData(appId, reference.formId, undefined, value, orderby, extraParams)
      if (result.errorCode === '0') {
        if (result.data.items.length > 0) {
          const rowData = result.data.items[0]
          let data = { ...rowData }
          let updatevalue = reference.valueColumn ? rowData[reference.valueColumn].value : rowData.id.value
          if (reference.subformColumn) {
            data = rowData[reference.subformColumn]
            updatevalue = reference.valueColumn ? data[reference.valueColumn].value : data.id.value
          }
          await this.formUpdateValue([
            { [fieldId]: { defaultValue: updatevalue } },
            { [fieldId]: { refValue: { ...rowData } } },
          ], editIndex)
        } else {
          await this.formUpdateValue([
            { [fieldId]: { defaultValue: '' } },
            { [fieldId]: { refValue: {} } },
          ], editIndex)
        }
      }
    }
  }

  async updatePropsByFormData(editIndex, properties, dataKeys) {
    const fields = this.getEditFieldsRow(editIndex)
    // let datakey = Object.keys(this.fieldExpressionProps).find(key => key === dataKeys)
    const willUpdateKeys = Helper.selctRefDatasByFieldIdAndDataKey(dataKeys, this.fieldExpressionProps)
    const formDatas = this.getEditRowData(editIndex)
    for (let i = 0; i < Object.keys(willUpdateKeys).length; i += 1) {
      const fieldId = Object.keys(willUpdateKeys)[i]
      const { fieldIndex, field } = this.findFieldByFieldId(fieldId)
      const propKeys = willUpdateKeys[fieldId]
      for (let j = 0; j < propKeys.length; j += 1) {
        const propkey = propKeys[j]
        if (this.triggleFieldId === fieldId && this.triggerPropKey === propkey) {
          continue
        }
        if (!(isComputedComponent(field) && propkey === 'defaultValue')) {
          const exp = this.propertiesExp[fieldId][propkey]
          const value = await exec(exp, { [EXPCONTEXT_NAMESPACE]: { ...field.properties, ...properties, ...formDatas } })
          if (!fields[fieldIndex]) {
            fields[fieldIndex] = {}
          }
          const props = set(fields[fieldIndex], propkey, value)
          await this.updateProps(editIndex, props, propkey, fieldId) // eslint-disable-line
          // 如果引用属性被强制隐藏了， 手动去取
          if (field.type === 'RefProperty' && propkey === 'joinfieldvalue' && this.refPropDatas[editIndex]) {
            const updatevalues = { [field.id]: { defaultValue: this.refPropDatas[editIndex][field.id] } }
            await this.formUpdateValue(updatevalues, editIndex)
          }
        }
      }
    }
  }

  async startEdit(index = this.editIndex) {
    await this.getInitData(false, index)
    this.fieldsRows = [...this.fieldsRows.slice(0, index), this.newRowFields, ...this.fieldsRows.slice(index + 1)]
    this.rowDatas = [...this.rowDatas.slice(0, index), this.newRowData, ...this.rowDatas.slice(index + 1)]
    this.refPropDatas = [...this.refPropDatas.slice(0, index), this.defaultRefPropDatas, ...this.refPropDatas.slice(index + 1)]
    await this.updateFormNumber()
  }
  addRow(index) {
    this.fieldsRows = [...this.fieldsRows.slice(0, index + 1), this.emptyRowFields, ...this.fieldsRows.slice(index + 1)]
    this.rowDatas = [...this.rowDatas.slice(0, index + 1), this.emptyRowData, ...this.rowDatas.slice(index + 1)]
    this.refPropDatas = [...this.refPropDatas.slice(0, index + 1), {}, ...this.refPropDatas.slice(index + 1)]
    this.backupFlag = 'new'
  }

  async handleSpecialComponents(rowData, fieldsRow, editIndex = this.editIndex) {
    const refPropFields = this.fields.filter(isRefPropComponent)
    let updateValues
    for (let i = 0; i < refPropFields.length; i += 1) {
      if (this.refPropDatas[editIndex] && this.refPropDatas[editIndex][refPropFields[i].id]) {
        const refPropDatas = this.refPropDatas[editIndex][refPropFields[i].id]
        if (!updateValues) updateValues = []
        updateValues.push({
          [refPropFields[i].id]: { defaultValue: refPropDatas },
        })
      }
    }
    if (updateValues) {
      await this.formUpdateValue(updateValues, editIndex)
    }
  }
  async updateFormNumber(editIndex = this.editIndex) {
    const formnumberIds = this.fields.filter(isFormNumberComponent)
    if (formnumberIds.length > 0) {
      for (let i = 0; i < formnumberIds.length; i += 1) {
        const formnumberId = formnumberIds[i].id
        let data = this.getEditRowData(editIndex)
        if (data.$$emptyId) data = this.newRowData
        const prefix = _get(data, `${formnumberId}.prefix`) || ''
        const result = await batchNumbyPrefix(this.props.appId, this.props.formsource.formId, formnumberId, prefix, 1)
        await this.formUpdateValue({ [formnumberId]: { serialnumber: result[0].serialNumber } }, editIndex)
      }
    }
  }
  async addQRValues(values) {
    if (values && values.length !== 0 && this.props.scanColumn) {
      this.isQRadd = true
      const originRowDataLength = this.rowDatas.length
      const originEditIndex = this.editIndex
      for (let i = 0; i < values.length; i += 1) {
        this.addRow(originRowDataLength+i)
        this.editIndex = originRowDataLength+i
        await this.startEdit(originRowDataLength+i)
        const updateFun = () => {
          this.handleSpecialComponents(this.rowDatas[originRowDataLength + i], this.fieldsRows[originRowDataLength + i], originRowDataLength  + i).then(()=>{
            if(i == values.length-1){
              console.log(i,'last    ');
              this.fns.commitFormByScan()
            }
          })
        }
        const data = {[this.props.scanColumn]:{ defaultValue: values[i]}}
        this.formUpdateValue(data, this.editIndex).then(updateFun)
      }
      this.editIndex = originEditIndex
    }
  }
  async addMutiRow(fieldId, newRowDatas, isQR = false) {
    await this.getInitData()
    const originRowDataLength = this.rowDatas.length
    const originEditIndex = this.editIndex
    const originEditFieldsRow = this.getEditFieldsRow()
    const { fieldIndex } = this.findFieldByFieldId(fieldId)
    for (let i = 0; i < newRowDatas.length; i += 1) {
      if (i >= originRowDataLength - originEditIndex || this.rowDatas[originEditIndex + i].$$emptyId) {
        this.rowDatas[originEditIndex + i] = this.newRowData
        this.fieldsRows[originEditIndex + i] = this.newRowFields
        if (!this.refPropDatas[originEditIndex + i]) {
          this.refPropDatas.push({})
        }
      }
      const editRowData = this.getEditRowData()
      editRowData.$$loaded = true
      const editFieldsRow = this.getEditFieldsRow()
      if (editFieldsRow[fieldIndex] && originEditFieldsRow[fieldIndex] && editFieldsRow[fieldIndex].filter !== originEditFieldsRow[fieldIndex].filter) {
        this.editIndex += 1
        continue
      }

      const datas = newRowDatas[i].refValue ? [{
        [fieldId]: {
          defaultValue: newRowDatas[i].defaultValue,
        },
      }, {
        [fieldId]: {
          refValue: newRowDatas[i].refValue,
        },
      }] : {
        [fieldId]: {
          defaultValue: newRowDatas[i].defaultValue,
        },
      }
      
      const updateFun = () => {
        this.handleSpecialComponents(this.rowDatas[originEditIndex + i], this.fieldsRows[originEditIndex + i], originEditIndex + i)
      }
      this.formUpdateValue(datas, this.editIndex).then(updateFun)
      this.editIndex += 1
    }
    this.editIndex = originEditIndex
    if (this.props.classifyProps) {
      // this.props.classifyProps.changeDistributeType('manual')
      this.props.classifyProps.listentRowDatas({ listenType: 'addMutiRow' })
      // this.editIndex = -1
    }
  }

  addMutiRowDatas = async (items, originEditIndex = this.editIndex) => {
    if (!items.length) return
    await this.getInitData()
    this.editIndex = originEditIndex
    const originRowDataLength = this.rowDatas.length
    const originEditFieldsRow = this.getEditFieldsRow()
    for (let i = 0; i < items.length; i += 1) {
      const { fieldId, datas } = items[i]
      const { fieldIndex } = this.findFieldByFieldId(fieldId)
      if (i >= originRowDataLength - originEditIndex || this.rowDatas[originEditIndex + i].$$emptyId) {
        this.rowDatas[originEditIndex + i] = this.newRowData
        this.fieldsRows[originEditIndex + i] = this.newRowFields
        if (!this.refPropDatas[originEditIndex + i]) {
          this.refPropDatas.push({})
        }
      }
      const editRowData = this.getEditRowData()
      editRowData.$$loaded = true
      const editFieldsRow = this.getEditFieldsRow()
      if (editFieldsRow[fieldIndex] && originEditFieldsRow[fieldIndex] && editFieldsRow[fieldIndex].filter !== originEditFieldsRow[fieldIndex].filter) {
        this.editIndex += 1
        continue
      }
      const updateFun = () => {
        this.handleSpecialComponents(this.rowDatas[originEditIndex + i], this.fieldsRows[originEditIndex + i], originEditIndex + i)
      }
      this.formUpdateValue(datas, this.editIndex).then(updateFun)
      this.editIndex += 1
    }
    this.editIndex = -1
  }

  removeRow(index) {
    if (!this.rowDatas[index]) return
    if (this.rowDatas[index].$$emptyId) {
      this.fieldsRows = this.fieldsRows.filter((v, i) => i !== index)
      this.rowDatas = this.rowDatas.filter((v, i) => i !== index)
      this.refPropDatas = this.refPropDatas.filter((v, i) => i !== index)
    } else {
      this.editIndex = -1
      const removedIndexId = this.rowDatas[index].id
      if (removedIndexId) {
        this.removedDatas.push({ id: removedIndexId, $$delete: true })
      }
      this.fieldsRows = [...this.fieldsRows.slice(0, index), ...this.fieldsRows.slice(index + 1)]
      this.rowDatas = [...this.rowDatas.slice(0, index), ...this.rowDatas.slice(index + 1)]
      this.refPropDatas = [...this.refPropDatas.slice(0, index), ...this.refPropDatas.slice(index + 1)]
    }
  }

  removeMutiRow(indexs, isDelete) {
    indexs.sort((a, b) => (b - a)).forEach((i) => {
      this.removeRow(i)
      if (isDelete) { this.removeRow(i) }
    })
  }

  checkIsNotBlankRow = (rowData) => {
    const { field } = this.findFieldByFieldId(this.props.formsource.blankColumn)
    if (!this.props.formsource.blankColumn || !field || rowData.$$emptyId) return true // 如果没有设置, 默认是非空行
    let value = rowData[this.props.formsource.blankColumn][field.setting.value]
    if (typeof value === 'undefined') {
      value = rowData[this.props.formsource.blankColumn].display
    }
    const notBlank = value === 0 || !!value
    if (rowData.id) {
      if (notBlank) {
        if (this.removedDatas.length > 0) {
          this.removedDatas = this.removedDatas.filter(({ id }) => id !== rowData.id)
        }
      } else {
        this.removedDatas.push({ id: rowData.id, $$delete: true })
      }
    }
    return notBlank
  }

  findFieldByFieldId = (fieldId, fields = this.fields) => {
    if (fields === this.fields && this._fieldsMap[fieldId]) {
      return this._fieldsMap[fieldId]
    }
    let fieldIndex = -1
    const field = fields.find((item, idx) => {
      if (item.id === fieldId) {
        fieldIndex = idx
        return true
      }
      return false
    })
    if (fields === this.fields) {
      this._fieldsMap[fieldId] = { fieldIndex, field }
    }
    return { fieldIndex, field }
  }
  valiedataRefValue = () => {
    const errors = []
    this.rowDatas.forEach((row, index) => {
      Object.keys(row).forEach((fid) => {
        const { fieldIndex, field } = this.findFieldByFieldId(fid)
        if (isRefComponent(field) && this.fieldsRows[index]) {
          const fieldProps = this.fieldsRows[index][fieldIndex]
          const data = row[fid]
          const { f1: value, ref } = data
          if (value && !isEmpty(ref)) {
            const refValue = fieldProps.reference.valueColumn ? ref[fieldProps.reference.valueColumn].value : ref.id.value
            if (value !== refValue) {
              errors.push({ fieldId: fid, data: cloneDeep(data), row: index })
            }
          }
        }
      })
    })
    return errors
  }
  isUniqueCheckInvalid = () => {
    const uniqueFields = this.props.formsource.uniqueFields
    if (!uniqueFields) return false
    const result = this.rowDatas
      .filter(this.checkIsNotBlankRow)
      .map((rowData) => {
        const uniqueFieldsDatas = uniqueFields.map((fieldId) => {
          const { field } = this.findFieldByFieldId(fieldId)
          if (!rowData[fieldId]) return undefined
          const valueData = rowData[fieldId][field.setting.value]
          const data = valueData || (typeof valueData === 'undefined' ? rowData[fieldId].value : null)
          return data
        })
        if (uniqueFieldsDatas.some(f => !f)) return undefined
        return uniqueFieldsDatas.join('|')
      })
      .filter(f => f)

    return result.length !== new Set(result).size
  }

  async formOperatorSubmit(fieldId, index) {
    this.setEditIndex(index)
    const rowData = this.getEditRowData()
    let data
    if (rowData.$$loaded) {
      data = {
        ...rowData,
        [fieldId]: {
          ...rowData[fieldId],
          value: true,
        },
      }
    } else {
      data = {
        [fieldId]: {
          ...rowData[fieldId],
          value: true,
        },
      }
    }
    const result = await put(`/apps/${this.props.appId}/forms/${this.props.formsource.formId}/datas/${rowData.id.value}`)({
      data,
    })
    if (result.errorCode === '0') {
      const {
        appId, formId: mainFormId, formsource: { formId: subFormId }, fieldId: selfFieldID, dataId, copyDataId,
      } = this.props
      const fnField = await this.getFk(appId, mainFormId, selfFieldID)
      if (!fnField) return
      const result1 = await get(`/apps/${appId}/forms/${subFormId}/datas?srcFormId=${mainFormId}&srcFormDataId=${dataId}&$operations=false&$title=false&$filter=${fnField} eq ${dataId || copyDataId} and id eq ${rowData.id.value}`)()
      if (result1.errorCode === '0' && result1.data && result1.data.items) {
        this.rowDatas[index] = result1.data.items[0]
        if (!this.refPropDatas[index]) {
          this.refPropDatas.push({})
        }
        this.fieldsRows[index] = []
        this.setEditIndex(-1)
      }
      this.fns.forceUpdate()
      Toast.success(result.errorMsg)
    } else {
      Toast.fail(result.errorMsg)
    }
  }
  getValueProp = (field, value) => has(value, field.setting.value) ? field.setting.value : 'value'
  getDefaultValueProp = (field => isExpression(field.data[field.setting.value].value)[1])

  updateValidation = (validations) => {
    const fieldId = Object.keys(validations)[0]
    const valueData = validations[fieldId]
    this.validations = {
      ...this.validations,
      [fieldId]: {
        ...this.validations[fieldId],
        ...valueData,
      },
    }
  }

  validate = () => {
    const validates = this.validations
    const editRowFields = this.getEditFieldsRow()
    const editRowData = this.getEditRowData()
    const fields = this.fields
    const verifyTips = {}
    let invalidFieldsId = []
    invalidFieldsId = invalidFieldsId.concat(Object.keys(validates).map((fieldId) => {
      const field = fields.find(({ id }) => id === fieldId)
      if (field && field.properties.required && validates[fieldId].required) {
        return { key: fieldId, status: 'required' }
      } else if (validates[fieldId].format) {
        return { key: fieldId, status: 'format' }
      }
      return null
    }).filter(item => item !== null))
    if (Object.keys(verifyTips).length > 0) {
      invalidFieldsId = invalidFieldsId.concat(Object.keys(verifyTips).map(i => ({ key: i, status: 'valiate' })))
    }
    const invalidFields = fields.filter(item => invalidFieldsId.find((i) => { if (i.key === item.id) { item.validateStatus = i.status; return true } }))
    return invalidFields
  }
  backup() {
    this.backupRowData = cloneDeep(this.getEditRowData())
    this.backupFieldRow = cloneDeep(this.getEditFieldsRow())
  }

  restore() {
    if (this.backupFlag === 'new') {
      this.rowDatas.pop()
      this.fieldsRows.pop()
    } else {
      this.setEditRowData(this.backupRowData)
      this.setEditFieldsRow(this.backupFieldRow)
    }
  }
  setUpdateEditModal = (fn) => {
    this.updateEditModal = fn
  }

  resetBackupFlag() {
    this.backupFlag = ''
  }
  loadRefDataBySearchValue = async (reffield, searchValue) => {
    const {
      searchcolumn, filter, orderby, reference,
    } = reffield.properties
    const orderbyFun = (o) => {
      const isExp = isExpression(o)
      const orders = isExp ? isExp[1] : o
      const datas = {}
      orders.split(',').forEach((data) => {
        const value = data.split(' ')
        if (value[1] === 'desc' || value[1] === 'asc') datas[value[0]] = value[1]
      })
      return datas
    }

    const orders = orderbyFun(orderby)
    const filterOrders = orders && !isEmpty(orders) ?
      Object.keys(orders).reduce((res, key) => `${res} ${key} ${orders[key]},`, '&$orderby=')
      : ''
    let q = ''
    searchcolumn.forEach((s, index) => {
      q += `${s} fulllike ${encodeURIComponent(searchValue)} ${index === searchcolumn.length - 1 ? '' : 'or '}`
    })
    let qf = isExpression(filter) ? '' : filter.replace(/ and $/, '').replace(/ or $/, '')
    if (qf) {
      qf = `$filter=(${qf}) and (${q})`
    } else {
      qf = `$filter=(${q})`
    }
    if (q) {
      const query = `${qf}${filterOrders}&srcFormId=${this.props.formId}`
      const result = await get(`/apps/${this.props.appId}/forms/${reference.formId}/datas?$operations=false&$count=false&$offset=0&$limit=2&${query}`)()
      if (result.errorCode === '0') {
        const { items } = result.data
        return items && items.length === 1 ? items[0] : undefined
      }
    }
  }
}
