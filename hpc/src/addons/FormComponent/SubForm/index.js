import PropTypes from 'prop-types'
import React from 'react'
import { View, Image, Dimensions, ActivityIndicator } from 'react-native'
import { Button, WhiteSpace } from 'nagz-mobile-lib'
import { get as _get, omit, isEqual, cloneDeep, debounce, difference, pick } from 'lodash-es'
import { Portal, Toast, Flex } from '@ant-design/react-native'
import ViewRow from './ViewRow'
import EditModal from './EditModal'
import QRModal from '../ScanInput/QRModal'
import Helper from './NewHelper'
import { GO_ROOTMODAL } from '../../../utils/jump'
import { screenWidth } from '../../../config/sysPara'
import SubFormTotal from './SubFormTotal'
import { UPDATE_FORMDATA } from '../../../containers/RunTime/Form/constants'
import PubSub from '../../../utils/pubsub'
import EventEmitter from '../../../utils/eventEmitter'
import { FormContext } from '../../../utils/reactContext'

import {
  thousandsFun,
  getDisplayValue,
} from '../../FormComponent/Number/displayValue'
import { isFormOperationComponent, isStatOperationComponent } from '../../constants'
import { TouchableOpacity } from 'react-native-gesture-handler'
import { isExpression } from '../../../utils/expression/transformExpression'

const { width } = Dimensions.get('window')
const fn = () => { }
// const random = () => Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, 9))

class SubForm extends React.Component {
  constructor(props) {
    super(props)
    this.updateValuesQueue = []

    this.fns = {
      forceUpdate: this.forceUpdate,
      startEdit: this.startEdit,
      isNotHideColumn: this.isNotHideColumn,
      commitFormByScan: this.commitFormByScan,

    }
  }
  static contextType = FormContext;

  state = {
    columns: [],
    fieldsRows: [],
    rowDatas: [],
    editIndex: -1,
    totalRow: null,
    isLoading: false,
    refPropDatas: [],
  }

  componentDidMount = async () => {
    if (this.props.isRunTime) {
      this.cloneRelations = cloneDeep(this.props.formsource.relations)
      this.setState({ isLoading: true })
      if (this.props.extraProps && this.props.extraProps.helper) {
        this.helper = this.props.extraProps.helper
        this.helper.fns = this.fns
        this.afterLoadFields()
      } else {
        this.helper = new Helper(this.props, this.context, this.fns)
        // await this.helper.loadFields()
        this.initListener = this.debounceListener()
        EventEmitter.on('referencePullData', async (fieldId, rowdatas) => {
          if (this.props.fieldId !== fieldId) {
            return
          }
          // 数据拉取是覆盖所有数据，所以需要先清空子表单数据
          const deleteRowIndex = this.helper.rowDatas.map((v, i) => i)
          this.removeMutiRow(deleteRowIndex, true)
          const displayColumns = this.props.mobileStyleFields
          const sequenceRowDatas = rowdatas.map(item => displayColumns.map(v => _get(item, `${v}.value`, '')))
          await this.helper.startEdit(0)
          this.helper.editIndex = 0
          this.helper.insertMatrix(
            sequenceRowDatas,
            displayColumns,
          )
        })
        PubSub.on(UPDATE_FORMDATA, this.initListener)
        this.initListener()
      }
    }
    // document.body.addEventListener('click', this.changeEditRow)
  }
  componentWillReceiveProps = ({ formsource: { relations }, enabled, expColumns }) => {
    if (!this.props.isRunTime) return
    if (relations !== this.cloneRelations) {
      for (let i = 0; i < relations.length; i += 1) {
        if (!isEqual(relations[i].value, this.cloneRelations[i].value)) {
          this.helper.relationChange(relations[i])
        }
      }
      this.cloneRelations = cloneDeep(this.props.formsource.relations)
      this.helper.props.formsource.relations = this.cloneRelations
    }
    if (enabled !== this.props.enabled) {
      this.helper.enabled = enabled
    }
    if (expColumns && expColumns !== this.props.expColumns && !isExpression(expColumns)) {
      const columns = this.getTableColumns(this.helper.originFields, expColumns)
      this.setState({ columns })
    }
  }

  shouldComponentUpdate = (nextProps, nextState) => {
    const shouldUpdate = Object.keys(nextProps)
      .some((key) => {
        if (key === 'defaultValue' || key === 'extraProps' || key === 'formsource') return false
        if (nextProps[key] !== this.props[key]) {
          return true
        }
        return false
      }) ||
      Object.keys(nextState)
        .some((key) => {
          if (key === 'totalRow') {
            if (nextState.totalRow === null || this.state.totalRow === null) return true
            return Object.keys(nextState.totalRow)
              .some((fieldId) => {
                if (fieldId === '$$id') {
                  return false
                }
                if (nextState.totalRow[fieldId] === null || this.state.totalRow[fieldId] === null) {
                  return true
                }
                return nextState.totalRow[fieldId].display !== this.state.totalRow[fieldId].display
              })
          }
          if (nextState[key] !== this.state[key]) {
            return true
          }
          return false
        })
    return shouldUpdate
  }
  componentWillUnmount = () => {
    EventEmitter.off('referencePullData')
  }
  getTableColumns = (originFields, expColumns = this.props.expColumns) => {
    let expCol = []
    if (expColumns && typeof expColumns === 'string' && !isExpression(expColumns)) {
      debugger
      expCol = expColumns.split(',')
    }
    const c = {}
    for (let i = 0; i < originFields.length; i += 1) {
      const field = originFields[i]
      if (this.isNotHideColumn(field)) {
        if (expCol && expCol.length !== 0) {
          if (expCol.indexOf(field.id) !== -1) {
            c[field.id] = i
          }
        } else {
          c[field.id] = i
        }
      }
    }
    return c
  }
  debounceListener = () => debounce(this.init, 300)

  init = async () => {
    PubSub.off(UPDATE_FORMDATA, this.initListener)
    this.props.updateExtraProps({
      fieldId: this.props.fieldId,
      props: {
        helper: this.helper,
      },
    })
    await this.helper.loadFields()
    this.afterLoadFields()
  }

  // eslint-disable-next-line react/sort-comp
  afterLoadFields = async () => {
    const columns = this.getTableColumns(this.helper.originFields)
    this.setState({
      isLoading: false,
      rowDatas: this.helper.rowDatas,
      fieldsRows: this.helper.fieldsRows,
      columns,
      editIndex: this.helper.editIndex,
      refPropDatas: this.helper.refPropDatas,
    })
    this.initDefaultValue(this.helper.rowDatas)
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
  }

  forceUpdate = () => {
    this.setState({
      rowDatas: this.helper.rowDatas,
      fieldsRows: this.helper.fieldsRows,
      columns: this.getTableColumns(this.helper.originFields),
      editIndex: this.helper.editIndex,
      refPropDatas: this.helper.refPropDatas,
    })
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
  }
  initDefaultValue = (rowDatas) => {
    if (this.props.dataId && rowDatas.length > 0) {
      this.updatePropsToMainForm()
      this.commitUpdateValue()
    }
  }
  isNotHideColumn = field => this.props.mobileStyleFields &&
    this.props.mobileStyleFields.indexOf(field.id) !== -1 &&
    !isFormOperationComponent(field) &&
    !isStatOperationComponent(field) &&
    field.properties.visible !== false

  startEdit = async (record, index) => {
    if (!this.props.enabled || this.helper.forceUpdateFlag) return
    this.helper.setEditIndex(index)
    if (record.$$emptyId) {
      await this.helper.startEdit()
      this.updatePropsToMainForm()
      this.genTotalRow()
      this.commitUpdateValue()
    } else if (!record.$$loaded) {
      await this.helper.loadData()
    }
    this.openModal()
    Portal.remove(this.tKey)
  }

  updateValue = async (value) => {
    await this.helper.formUpdateValue(value)
    this.updatePropsToMainForm()
    this.genTotalRow()
    this.commitUpdateValue()
  }

  updatePropsToMainForm = () => {
    const willStoredDatas = this.helper.rowDatas
      .filter(({ id, $$updated }) => id || $$updated) // 已有数据或已经更新
      .filter(this.helper.checkIsNotBlankRow)
      .map((rowData) => {
        // 没有改动的数据只传id
        if (rowData.id && !rowData.$$loaded) {
          return { id: rowData.id }
        }
        // 有改动或者新增的数据
        return omit(rowData, ['$$id', '$$updated', '$$loaded'])
      })
    this.pushUpdateValuesQueue({
      [this.props.fieldId]: {
        defaultValue: [...willStoredDatas, ...this.helper.removedDatas],
      },
    })
  }

  pushUpdateValuesQueue = (value) => {
    this.updateValuesQueue.push(value)
  }
  commitUpdateValue = () => {
    if (this.updateValuesQueue.length > 0) {
      this.props.changeValue(this.updateValuesQueue)
      this.updateValuesQueue = []
    }
  }
  commitFormByScan = () => {
    if (this.props.scanSubmitField) {
      setTimeout(() => {
        this.context.submit(this.props.scanSubmitField, this.props.operator)
      }, 1000)
    }
  }

  addRow = async (index) => {
    await this.helper.addRow(index)
    await this.startEdit(this.helper.rowDatas[index], index)
    // e.stopPropagation()
  }

  addMutiRow = async (fieldId, newRowDatas) => {
    await this.helper.addMutiRow(fieldId, newRowDatas)
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      rowDatas: this.helper.rowDatas,
      refPropDatas: this.helper.refPropDatas,
    })
    this.genTotalRow()
    this.props.updateEditedFlag(this.props.fieldId)
    this.updatePropsToMainForm()
    this.commitUpdateValue()
  }

  removeRow = (index) => {
    this.props.updateEditedFlag(this.props.fieldId)
    this.helper.removeRow(index)
    this.updatePropsToMainForm()
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      rowDatas: this.helper.rowDatas,
      refPropDatas: this.helper.refPropDatas,
      editIndex: -1,
    })
    this.genTotalRow()
    this.commitUpdateValue()
    // e.stopPropagation()
  }
  removeMutiRow = (indexs, isDelete) => {
    if (indexs.length <= 0) {
      return
    }
    this.props.updateEditedFlag(this.props.fieldId)
    this.helper.removeMutiRow(indexs, isDelete)
    this.updatePropsToMainForm()
    this.setState({
      fieldsRows: this.helper.fieldsRows,
      rowDatas: this.helper.rowDatas,
      refPropDatas: this.helper.refPropDatas,
      editIndex: -1,
    })
    this.genTotalRow()
    this.commitUpdateValue()
  }

  getNumberDisplay = (value, { displayMode, decimalLength, thousands, displayFormat }) => {
    if (displayMode === undefined || decimalLength === undefined || thousands === undefined) {
      return value
    }
    let display = getDisplayValue(value, displayMode, decimalLength)
    if (displayFormat === '1') {
      display = thousandsFun(value)
    } else if (displayFormat === '2') {
      display = `${Number(value) * 100}%`
    }
    return display
  }
  genTotalRow = () => {
    let {
      sum = [], avg = [], count = [], min = [], max = [],
    } = this.props.formsource.total
    sum = sum.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    avg = avg.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    max = max.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    min = min.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    count = count.filter(fieldId => this.helper.fields.find(({ id }) => id === fieldId))
    if (sum.length === 0 && avg.length === 0 && max.length === 0 && min.length === 0 && count.length === 0) return

    const colSum = fieldId => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : 0
      const n = Number(origin)
      return total + (isNaN(n) ? origin : n)
    }

    const colMin = (fieldId, type) => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : ''
      if (origin === '') return total
      const n = Number(origin)
      const value = isNaN(n) ? origin : n
      const maxValue = total[0] > total[1] ? total[0] : total[1]
      const minValue = total[0] > total[1] ? total[1] : total[0]
      if (type === 'max') {
        return [
          (total[0] === total[1] && total[0] === null) ? value : maxValue > value ? maxValue : value,
          minValue < value ? minValue : value,
        ]
      }
      return [
        maxValue > value ? maxValue : value,
        (total[0] === total[1] && total[0] === null) ? value : minValue < value ? minValue : value,
      ]
    }

    const colCount = fieldId => (total, datas) => {
      const fieldSetting = this.helper.fields.find(({ id }) => id === fieldId).setting
      const fieldData = datas[fieldId]
      const origin = fieldData ? fieldData[fieldSetting.value] || fieldData.value || fieldData.display : ''
      if (origin === '') return total
      return total + 1
    }

    const noBlankRows = this.helper.rowDatas.filter(({ $$emptyId }) => !$$emptyId).filter(this.helper.checkIsNotBlankRow)
    const totalSum = fieldId => noBlankRows.reduce(colSum(fieldId), 0)
    const totalMin = (fieldId, type) => noBlankRows.reduce(colMin(fieldId, type), [null, null])
    const totalCount = fieldId => noBlankRows.reduce(colCount(fieldId), 0)
    const resultSum = sum.reduce((result, fieldId) => ({ ...result, [fieldId]: totalSum(fieldId) }), {})
    const resultAvg = avg.reduce((result, fieldId) => ({ ...result, [fieldId]: totalSum(fieldId) / noBlankRows.length }), {})
    const resultMax = max.reduce((result, fieldId) => ({ ...result, [fieldId]: totalMin(fieldId, 'max')[0] }), {})
    const resultMin = min.reduce((result, fieldId) => ({ ...result, [fieldId]: totalMin(fieldId, 'min')[1] }), {})
    const resultCount = count.reduce((result, fieldId) => ({ ...result, [fieldId]: totalCount(fieldId) }), {})
    const total = type => (result, fieldId) => {
      const field = this.helper.fields.find(({ id }) => id === fieldId)
      return ({ ...result, [fieldId]: { display: this.getNumberDisplay(type[fieldId], field.properties) } })
    }
    const totalRow = {
      ...sum.reduce(total(resultSum), {}),
      ...avg.reduce(total(resultAvg), {}),
      ...max.reduce(total(resultMax), {}),
      ...min.reduce(total(resultMin), {}),
      ...count.reduce(total(resultCount), {}),
    }
    this.setState({
      totalRow: {
        $$id: 'TOTAL_ROW',
        ...totalRow,
      },
    })
    this.pushUpdateValuesQueue({
      [this.props.fieldId]: {
        formsource: {
          ...this.props.formsource,
          total: {
            ...this.props.formsource.total,
            value: {
              sum: resultSum,
              avg: resultAvg,
              max: resultMax,
              min: resultMin,
              count: resultCount,
            },
          },
        },
      },
    })
  }

  getRowKey = record => record.id ? record.id.value : record.$$id;

  isDisabledRemove=(record) => {
    const desableRemove = !record.$$emptyId
    && _get(record, 'id.value')
    && ((record.$$operationIds
    && record.$$operationIds.length !== 0
    && record.$$operationIds.indexOf('5') === -1
    ) || (!record.$$operationIds || record.$$operationIds.length === 0))
    // return true
    return desableRemove
  }
  openModal = () => {
    this.helper.backup()
    GO_ROOTMODAL({
      children: <EditModal
        {...this.props}
        helper={this.helper}
        updateValue={this.updateValue}
        columns={this.state.columns}
        close={this.forceUpdate}
        remove={() => { this.removeRow(this.helper.editIndex) }}
        hansRemove={!this.isDisabledRemove(this.helper.getEditRowData())}
        cb={() => { this.opening = false; this.helper.openEditModal = false }}
      />,
      isCustomGoBack: true,
    })
  }
  updateQRValue=(v) => {
    const originValue = this.getScanValue()
    const currValue = difference(v, originValue)
    this.helper.addQRValues(currValue)
  }
  showORBtn =() => this.props.scanColumn
  getScanValue = () => {
    const field = this.helper.fields.find(f => f.id === this.props.scanColumn)
    if (field) {
      const valuekey = _get(field, 'setting.value')
      return this.state.rowDatas
        .filter(row => _get(row, `${this.props.scanColumn}.${valuekey}`))
        .map(row => _get(row, `${this.props.scanColumn}.${valuekey}`))
    }
    return []
  }
  getQRPorps= () => pick(this.props, ['scanColumn', 'scanShowfield', 'scanSource', 'scanjoinformvaluefield'])
  openQRCamera =() => {
    const values = this.getScanValue()
    const scanProps = this.getQRPorps()
    const { scanLength, scanReg } = this.props
    GO_ROOTMODAL({
      children: <QRModal
        scanLength={scanLength}
        scanReg={scanReg}
        appId={this.props.appId}
        updateValue={this.updateQRValue}
        values={values}
        scanProps={scanProps}
      />,
    })
  }
  onPress = () => {
    if (this.opening) return
    this.tKey = Toast.loading('', 0)
    this.opening = true
    this.helper.openEditModal = true
    const { inputBlur } = this.props
    if (inputBlur) {
      inputBlur(() => { this.addRow(this.state.rowDatas.length) })
    } else {
      this.addRow(this.state.rowDatas.length)
    }
  }

  render() {
    const { label, required, mobileStyleFields } = this.props
    const columns = this.state.columns
    const colFields = this.props.mobileStyleFields.map((fieldId) => {
      const fIndex = columns[fieldId]
      if (fIndex || fIndex === 0) {
        return this.helper && this.helper.fields && this.helper && this.helper.fields[fIndex]
      }
    }).filter(f => f)
    return (
      <View>
        <WhiteSpace style={{ backgroundColor: '#F8F7F7' }} />

        <View style={{ backgroundColor: 'white', width: screenWidth }}>
          {this.helper &&
            <SubFormTotal
              fields={this.helper.fields}
              total={this.state.totalRow}
              totalRow={this.state.rowDatas.length}
              label={label}
              required={required}
            />
          }
        </View>
        <View
          style={{ backgroundColor: 'white' }}
        >
          {this.state.isLoading ? <ActivityIndicator style={{ height: 60 }} size="large" animating={this.state.isLoading} /> : null}
          {
            this.state.rowDatas
              // .filter(({ $$emptyId }) => !$$emptyId)
              .map((row, index) => (<ViewRow
                {...this.props}
                index={index}
                key={this.getRowKey(row)}
                colFields={colFields}
                row={row}
                rowRefPropData={this.state.refPropDatas[index]}
                onPress={() => this.startEdit(row, index)}
                helper={this.helper}
                close={this.forceUpdate}
                updateValue={this.updateValue}
                columns={this.state.columns}
              />))
          }
        </View>
        {this.props.enabled && (

        <View
          style={{
                height: 75,
                backgroundColor: 'white',
                paddingHorizontal: 20,
                alignItems: 'center',
              }}
        >
          <View
            style={{
                  width: width - 40,
                  height: 1,
                  backgroundColor: '#E5E5E5',
                  // marginBottom: 34,
                }}
          />
          <View style={{ width, display: 'flex', height: 70, alignItems: 'center', justifyContent: 'flex-end', flexDirection: 'row' }} >
            <TouchableOpacity
              onPress={this.onPress}
              disabled={this.state.isLoading}
              style={{ width: 36, height: 36, marginRight: this.showORBtn() ? 12 : 18 }}
            >
              <Image style={{ width: 36, height: 36 }} source={require('../../../images/add_subform_icon.png')} />
            </TouchableOpacity>
            {this.showORBtn() ?
              <TouchableOpacity
                onPress={this.openQRCamera}
                disabled={this.state.isLoading}
                style={{ width: 36, height: 36, marginRight: 18 }}
              >
                <Image style={{ width: 36, height: 36 }} source={require('../../../images/scan_lager_icon.png')} />
              </TouchableOpacity> : <View />
            }
          </View>
        </View>)
        }
        <WhiteSpace style={{ backgroundColor: '#F8F7F7' }} />
      </View>
    )
  }
}

SubForm.propTypes = {
  appId: PropTypes.string,
  scanColumn: PropTypes.string,
  dataId: PropTypes.string,
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  desc: PropTypes.string,
  height: PropTypes.number,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  hidelabel: PropTypes.bool,
  enabled: PropTypes.bool,
  updateEditedFlag: PropTypes.func,
  updateExtraProps: PropTypes.func,
  formsource: PropTypes.object,
  extraProps: PropTypes.object, // eslint-disable-line
  isView: PropTypes.bool,
  inputBlur: PropTypes.func,
  scanLength: PropTypes.number.isRequired,
  scanReg: PropTypes.string.isRequired,
}

export default SubForm
