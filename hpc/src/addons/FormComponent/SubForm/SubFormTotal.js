import React from 'react'
import { View, Text, Image, Dimensions } from 'react-native'
import { Accordion, WingBlank } from 'nagz-mobile-lib'
import { SimpleLine } from 'nagz-mobile-lib/lib/SimpleLine'

const { width } = Dimensions.get('window')

class SubFormTotal extends React.Component {
  state = {
    showLine: true,
  }
  onChange = (index) => {
    if (!index.length) {
      this.setState({
        showLine: true,
      })
    } else {
      this.setState({
        showLine: false,
      })
    }
  }
  render() {
    const {
      fields = [], total, totalRow, label, required,
    } = this.props
    return (
      <View>
        {totalRow === 0 || total === null ?
          <SimpleLine isRequire={required} label={label} editable={false} value={`共 ${totalRow} 条`} /> :
          <View
            style={{ alignItems: 'center' }}
          >
            <Accordion
              sections={{ label, value: `共 ${totalRow} 条` }}
              onChange={this.onChange}
              renderContent={() => (
                <WingBlank>
                  <View
                    style={{
                      backgroundColor: '#F8F7F7', paddingTop: 20, paddingLeft: 20, paddingBottom: 15,
                    }}
                  >
                    <View
                      style={{ width: 150 }}
                    >
                      {fields
                        .filter(field => total[field.id])
                        .map(field => (
                          <View
                            key={field.id}
                            style={{ flexDirection: 'row', marginBottom: 5, justifyContent: 'space-between' }}
                          >
                            <Text
                              style={{ fontSize: 14, color: '#FFC219' }}
                            >
                              {field.properties.label}:
                            </Text>
                            <Text
                              style={{ fontSize: 14, color: '#FFC219' }}
                            >
                              {total[field.id] && String(total[field.id].display)}
                            </Text>
                          </View>
                        ))
                      }
                    </View>
                    <View
                      style={{
                        paddingRight: 30, position: 'absolute', right: 0, bottom: 10,
                      }}
                    >
                      <Image source={require('../../../images/img-quote.png')} />
                    </View>
                  </View>
                </WingBlank>
              )}
            />
            {(this.state.showLine || total === null) && <View
              style={{
                width: width - 40,
                height: 1,
                backgroundColor: '#E5E5E5',
              }}
            />}
          </View>
        }
      </View>
    )
  }
}
export default SubFormTotal
