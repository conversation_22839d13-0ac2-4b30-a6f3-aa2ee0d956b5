import PropTypes from 'prop-types'
import React from 'react'
import { View } from 'react-native'
// import { DatePicker } from 'nagz-mobile-lib'
import { Toast } from '@ant-design/react-native'
import { DatePicker } from '../../../lib/DatePicker'
import moment from 'moment'
import { getUpdateValidationFn } from '../common'
import DateModal from './DateModal'
// import { GO_MODAL } from '../../../utils/jump'

const fn = () => { }

const replaceFormat = format => format.replace('yyyy', 'YYYY')
  .replace('dd', 'DD')
  .replace('hh', 'HH')
  .replace('EEEE', 'dddd')

class DateTime extends React.PureComponent {
  state = {
    value: this.props.defaultValue ? new Date(Number(this.props.defaultValue)) : null,
  }

  componentWillMount = () => {
    this.isDefaultEmpty = !!this.props.defaultValue
    this.updateValidation = getUpdateValidationFn(this.props.updateValidation, this.props.fieldId)(this.props.required, this.props.defaultValue)
  }
  componentDidMount() {
    // 因为高度为固定的，所以不通过onlayout计算高度
    this.cmpHeight = 50
  }
  componentWillReceiveProps = ({ defaultValue }) => {
    if (this.props.defaultValue !== defaultValue) {
      this.setState({ value: defaultValue ? new Date(Number(defaultValue)) : '' })
      if (this.updateValidation) {
        this.updateValidation = this.updateValidation(this.props.required, defaultValue)
      }
    }
    if (defaultValue && defaultValue !== '') {
      this.isDefaultEmpty = true
    }
  }

  onClick = () => {
    if (!this.props.defaultValue) { this.updateValue(new Date()) }
  }
  formatDate = (date) => {
    const { format } = this.props
    const feFormat = replaceFormat(format)
    return moment(date).format(feFormat)
  }
  updateValue = (value) => {
    const {
      changeValue,
      fieldId,
      isHalfhour,
    } = this.props
    if (changeValue) {
      // 当为半小时制的时候
      if (isHalfhour) {
        const dateValue = moment(value)
        dateValue.set('minute', dateValue.get('minute', 0) >= 30 ? 30 : 0)
        changeValue({ [fieldId]: { defaultValue: dateValue.valueOf() } })
        this.setState({ value: dateValue.toDate() })
      } else {
        changeValue({ [fieldId]: { defaultValue: value && `${value.getTime().toString()}` } })
        this.setState({ value })
      }
    }
  }
  updateModalDate = (timestamp) => {
    const {
      changeValue,
      fieldId,
      isHalfhour,
    } = this.props
    if (changeValue) {
      if (isHalfhour) {
        const dateValue = moment(timestamp)
        dateValue.set('minute', dateValue.get('minute', 0) >= 30 ? 30 : 0)
        changeValue({ [fieldId]: { defaultValue: dateValue.valueOf() } })
        this.setState({ value: dateValue.toDate() })
      } else {
        changeValue({ [fieldId]: { defaultValue: timestamp.toString() } })
        this.setState({ value: new Date(timestamp) })
      }
    }
  }
  contentOnClick = () => {
    // const { value } = this.state
    const { enabled } = this.props
    if (!enabled) {
      Toast.info('不可更改', 1)
      return
    }
    // GO_MODAL({
    //   opacity: 0.5, children: <DateModal format={replaceFormat(format)} value={value} updateValue={this.updateModalDate} />, animationIn: 'slideInUp', animationOut: 'slideOutDown',
    // })
    this.setState({ visible: true })
  }
  closeModal = () => { this.setState({ visible: false }) }
  render() {
    const {
      label,
      required,
      isRunTime,
      enabled,
      format,
      isView,
    } = this.props
    const disabled = isView ? true : (!isRunTime ? true : !enabled)
    const inputStyle = {
      backgroundColor: '#fff',
      width: '100%',
    }
    let mode = 'year'
    if (format === 'HH:mm' || format === 'HH:mm:ss') {
      mode = 'time'
    } else if (format === 'yyyy-MM-dd HH:mm:ss' || format === 'yyyy-MM-dd HH:mm' || format === 'yyyy-MM-dd HH') {
      mode = 'datetime'
    } else if (format === 'yyyy-MM-dd' || format === 'MM-dd' || format === 'yyyy-MM-dd EEEE' || format === 'EEEE') {
      mode = 'date'
    } else if (format === 'yyyy-MM') {
      mode = 'month'
    }
    return (
      <View>
        <DatePicker
          style={inputStyle}
          mode={mode}
          format={this.formatDate}
          onChange={this.updateValue}
          disabled={disabled}
          extra={format}
          value={this.state.value}
          minDate={new Date(1900, 1, 1, 0, 0, 0)}
          maxDate={new Date(2050, 1, 1, 23, 59, 59)}
          label={label}
          isRequire={required}
          contentOnClick={format === 'EEEE' ? null : this.contentOnClick}
        />
        {this.state.visible && <DateModal closeModal={this.closeModal} visible={this.state.visible} format={replaceFormat(format)} value={this.state.value} updateValue={this.updateModalDate} />}
      </View>
    )
  }
}

DateTime.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.any, // eslint-disable-line
  isView: PropTypes.bool,
  enabled: PropTypes.bool,
  desc: PropTypes.string,// eslint-disable-line
  required: PropTypes.bool,
  changeValue: PropTypes.func,
  isRunTime: PropTypes.bool,
  fieldId: PropTypes.string,
  format: PropTypes.string,
  validate: PropTypes.object,// eslint-disable-line
  updateValidation: PropTypes.func,
  isHalfhour: PropTypes.bool,
}

DateTime.defaultProps = {
  label: '',
  defaultValue: {},
  desc: '',
  required: false,
  changeValue: fn,
  isRunTime: false,
  fieldId: '',
  validate: {},
  enabled: true,
  format: 'YYYY-MM-DD HH:mm:ss',
  isHalfhour: false,
}

export default DateTime
