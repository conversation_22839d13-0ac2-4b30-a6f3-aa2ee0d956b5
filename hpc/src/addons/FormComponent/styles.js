import { StyleSheet } from 'react-native'
import { screenWidth } from '../../config/sysPara'

export default styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,

  },
  label: {
    width: screenWidth * 0.3,
    marginLeft: 10,
  },
  text: {
    flex: 1,
    borderBottomWidth: 0,
    justifyContent: 'flex-start',
    marginLeft: -45,
  },
  numText: {
    flex: 1,
    borderBottomWidth: 0,
    justifyContent: 'flex-start',
    marginLeft: -65,
  },
  subText: {
    marginLeft: -65,
    flex: 1,
    borderBottomWidth: 0,
    justifyContent: 'flex-start',
  },
  subLabel: {
    color: '#646464',
    fontSize: 17,
    width: 120,
  },
  mutilTextLabel: {
    color: '#646464',
    fontSize: 17,
    width: 150,
    marginTop: 5,
  },
  mainLabel: {
    fontSize: 17,
    width: 120,
    color: '#000',
  },
  rightEditRight: {
    height: 14,
    width: 33,
    backgroundColor: '#16a9ff',
    borderRadius: 7,
    marginTop: 3,
    marginLeft: 1,
  },
  rightEditIcon: {
    position: 'absolute',
    width: 22,
    height: 22,
    top: -1,
    left: -2,
  },
  rightEditIconRight: {
    position: 'absolute',
    width: 22,
    height: 22,
    right: -2,
    top: -1,
  },
  rightEdit: {
    height: 14,
    width: 33,
    backgroundColor: '#ffc219',
    borderRadius: 7,
    marginTop: 3,
    marginLeft: 1,
  },
  rightWarp: {
    position: 'relative',
    height: 22,
    width: 35,
    marginRight: 20,
    alignSelf: 'center',
  },
})
