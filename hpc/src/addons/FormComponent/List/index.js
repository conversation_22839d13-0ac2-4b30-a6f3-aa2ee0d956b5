import PropTypes from 'prop-types'
import React from 'react'
import { View, Text, TouchableOpacity, Image, ScrollView, Dimensions, TextInput, Keyboard, AsyncStorage } from 'react-native'
import { connect } from 'react-redux'
import { List } from 'nagz-mobile-lib'
import { Toast, Portal, Modal } from '@ant-design/react-native'
// import GestureSlide from '../../../components/GestureSlide'
import moment from 'moment'
import AGZList, { DataTablePage } from '../../../components/DataTable'
import { DO_OPERATION } from '../../../containers/RunTime/List/constants'
import { isExpression } from '../../../utils/expression/transformExpression'
import { GO_ROOTMODAL } from '../../../utils/jump'
import { 
  getDataCount,
  getDataList,
  getBtnsAndtitle,
  getFields,
  postReply,
  getReply,
  buttionIds 
} from './store'
// import Store, {
//   getDataCount,
//   getDataList,
//   getBtnsAndtitle,
//   getFields,
//   postReply,
//   getReply,
//   buttionIds
// } from './Store'

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { COMPONENT_FORM_ID, isInvocationServiceComponent, isSubformComponent, isProductSubformComponent, isFormOperationComponent, isFormAssistStatusComponent } from '../../constants'
const DimensionsWidth = Dimensions.get('window').width
class Lists extends React.PureComponent {
  state = {
    count: 0,
    Dateday: Date.parse(new Date()),
    inputModel: false,
    dataList: [],
    LoadStatus: '',
    users: {},
    todaTime: Date.parse(new Date()),
    fields:[]
  }

  componentWillMount = async () => {
    let userRes = null
    try {
      const resultA = await AsyncStorage.getItem('result')
      userRes = JSON.parse(resultA)
    } catch (e) {
      __DEV__ && console.log('获取本地用户数据失败', userRes, e)
    }
    // console.log('asdhajkdahdkjhakdja', userRes)
    this.setState({
      users: userRes
    })
    this.gitTime()
    const {
      immediatelyToList, filter, appId, source, isTab, ignoreEmpty,
    } = this.props
    if (immediatelyToList) { return this.goRootMoal() }
    if (isTab) return null
    const data = isExpression(filter) ? '' : filter.replace(/ and $/, '').replace(/ or $/, '')
    const count = await getDataCount(appId, source, data, ignoreEmpty)
    // console.log('asdjalkdjajdlajldjlasdlkajdl', this, count, Store, data)
    if (this.props.componentId === '161') {
      this.getAllfieldAndTitle(appId, this.props.source)
    }
    this.setState({ count })
  }

  componentWillReceiveProps = async ({ immediatelyToList, ...nextProps }) => {
    this.gitTime()
    if (this.props.immediatelyToList !== immediatelyToList && immediatelyToList > 0) {
      this.props = { ...nextProps }
      this.goRootMoal()
    }
    if (nextProps.filter !== this.props.filter) {
      const { filter, appId, source, ignoreEmpty } = nextProps
      const data = isExpression(filter) ? '' : filter.replace(/ and $/, '').replace(/ or $/, '')
      const count = await getDataCount(appId, source, data, ignoreEmpty)
      // console.log('asdjalkdjajdlajldjlasdlkajdl', this.props)
      if (this.props.componentId === '161') {
        this.getAllfieldAndTitle(this.props.appId, this.props.source)
      }
      this.setState({
        count,
      })
    }
  }

  onScroll = (event) => {
    this.setState({
      eventOffsetX: event.nativeEvent.contentOffset.x,
    })
    // console.log(event.nativeEvent.contentOffset.x);//水平滚动距离
    // this.props.onBroadcastScroll(event.nativeEvent.contentOffset.y)
  }

  onMomentumScrollEnd = (event) => {
    const { eventOffsetX } = this.state
    // console.log('asdnhalsdjdlaksjdasdj', event.nativeEvent.contentOffset.x, eventOffsetX)
    if (eventOffsetX < 50) {
      this.onBeforeDay()
    } else {
      // console.log('asdnhalsdjdlaksjdasdj11111')
    }
    if (eventOffsetX > 150) {
      this.onNextDay()
    } else {
      // console.log('asdnhalsdjdlaksjdasdj2222')
    }
    this.scrollView.scrollTo({ x: 100 }, 1)
    // this.props.onBroadcastScroll(event.nativeEvent.contentOffset.y)
  }

  onResponderRelease = (event) => {
    // this.scrollView.scrollTo({ x: 200 }, 1)
    // console.log('asdnhalsdjdlaksjdasdj', event)
  }

  getDataListFun = async (time) => {
    console.log('asdnhjkadjlkj', this.props)
    let modelFields = '$select='
    let replyComponent = ''
    let filterData1 = ''
    if (this.props.timefilter) {
      if (this.props.filter === "") {
        filterData1 += `${this.props.timefilter} ge ${time} and ${this.props.timefilter} le ${time + 86400000 - 1}`
      } else {
        filterData1 += `${this.props.filter.trim()} and ${this.props.timefilter} ge ${time} and ${this.props.timefilter} le ${time + 86400000 - 1}`
      }
    } else {
      filterData1 += `${this.props.filter}`
    }
    for (let i = 0; i < this.props.mobileshowfields.length; i++) {
      modelFields += `${this.props.mobileshowfields[i]},`

    }
    for (let i = 0; i < this.state.fields.length; i++) {
      if (this.state.fields[i].componentId === '62') {
        replyComponent = this.state.fields[i].id
      }
    }

    this.setState({
      replyComponentId: replyComponent,
    })
    // console.log('asdhkjadhaksjhk', modelFields)
    let key = Toast.loading('加载中')
    this.setState({
      LoadStatus: '加载中',
    })
    const result = await getDataList(this.props.appId, this.props.formId, time, modelFields, filterData1, this.props.source)
    if (result.errorCode === '0') {
      let a = []
      for (let i = 0; i < result.data.items.length; i++) {
        let tmp = []
        tmp = this._getDisplayCol(result.data.title, result.data.items[i])
        a.push(tmp)
      }
      this.setState({
        dataList: a,
        LoadStatus: '加载完成'
      })
      Portal.remove(key)
      // console.log('asdhlkajdlkajsdlk', result, a)
    } else {
      this.setState({
        dataList: [],
        LoadStatus: '加载失败'
      })
      Portal.remove(key)
    }
  }

  getAllfieldAndTitle = async (appId, formId) => {
    this.scrollView.scrollTo({ x: 100 }, 1)

    const { fieldId, fieldConfig = {} } = this.props
    const result = await getBtnsAndtitle(appId, formId) // 获取主表的title和button
    const loadfields = fieldConfig[fieldId] ? fieldConfig[fieldId].config.form.fields
      : await getFields(appId, formId) || [] // 获取主表所有字段
    // console.log('ashdkajshdkahdkhakjdhkajsdhkjashdkjashdkja', loadfields)

    this.formField = loadfields.find(field => field.id === COMPONENT_FORM_ID)
    const fields = loadfields.filter(field => field.id !== COMPONENT_FORM_ID && !isSubformComponent(field) && !isProductSubformComponent(field))
    this.subForm = loadfields.filter(field => isSubformComponent(field) || isProductSubformComponent(field)) // 过滤出子表单字段
    const formOperations = loadfields.filter(field => (isFormOperationComponent(field))) // 过滤出表单操作字段
    this.subFormFieldIds = this.subForm.map(({ id }) => id)
    this.subFormIDMap = {}
    const subFormData = this.subForm.map(item => ({
      subFormId: item.id,
      subFormlabel: item.properties.label,
      formId: item.properties.formsource.formId,
      subcols: item.properties.formsource.columns,
    }))
    let subfields = []
    let subTitle = []
    for (const index in subFormData) {
      const subformId = subFormData[index].formId
      const item = fieldConfig[fieldId] ? fieldConfig[fieldId][subFormData[index].subFormId].config.form.fields
        : await getFields(appId, subformId, formId) || []
      item.forEach((f) => {
        this.subFormIDMap = { ...this.subFormIDMap, [f.id]: subFormData[index].formId }
      })
      subfields = ([...subfields,
      ...(item
        .filter(field => field.id !== COMPONENT_FORM_ID
          && !isSubformComponent(field) && !isProductSubformComponent(field)
          && subFormData[index].subcols.indexOf(field.id) !== -1)
        .map(filterField => (
          {
            ...filterField,
            id: `${subFormData[index].subFormId}.${filterField.id}`,
            properties: {
              ...filterField.properties,
              label: `${subFormData[index].subFormlabel}-${filterField.properties.label}`,
            },
          }
        ))),
      ])
      subTitle = [
        ...subTitle,
        ...item
          .filter(f => subFormData[index].subcols.indexOf(f.id) !== -1)
          .reduce((res, f) => ([
            ...res,
            {
              [`${subFormData[index].subFormId}.${f.id}`]: `${subFormData[index].subFormlabel
                }-${f.properties.label}`,
            },
          ]), []),
      ]
    }
    this.titles = result.title && result.title.filter(tit => Object.keys(tit)[0] !== 'sn')
    const sourceForm = loadfields.find(i => i.type === 'Form')
    this.setState({
      allOperations: result.operations,
      fields: [...fields, ...subfields],
      titles: [...this.titles, ...subTitle],
      formOperations,
      sourceFormStateSetting: (sourceForm && sourceForm.properties.stateSetting) || [],
    }, () => {
      // console.log('asdkandmskjalkjdlakjdla', moment(moment(this.state.Dateday).format('YYYY-MM-DD')).unix() * 1000)
      this.getDataListFun(moment(moment(this.state.Dateday).format('YYYY-MM-DD')).unix() * 1000)
    })
  }

  _getDisplayCol = (title, item) => {
    // console.log('asdhkajdhkhaskdjasdhk', title, item)
    // 过滤掉id字段
    const displayTitle = title.filter(x => !x.hasOwnProperty('id')).filter(x => !x.form_status)
    // console.log('asdhkladjlkasjdljljljl', displayTitle)
    const rObj = {}
    const val = []
    let keyObject = {}
    for (let i = 0; i < displayTitle.length; i += 1) {
      // console.log('asdjakldjlasjdlkj', displayTitle)
      const key = Object.keys(displayTitle[i])[0]
      if (key.includes('.')) {
        const keys = key.split('.')
        keyObject = item
        for (let j = 0; j < keys.length; j += 1) {
          keyObject = keyObject[keys[j]]
        }
      } else {
        keyObject = (item instanceof Array) ? item[i][key] : item[key]
        if (keyObject) {
          if (keyObject.display instanceof Array) {
            keyObject.display.reverse()
          }
        }
        // console.log('asdjakldjlasjdlkj', key, keyObject)
      }

      val[i] = keyObject ? keyObject : ''
    }
    // console.log(';asdjdjljalkdjlaksdjlkajsd', val)
    let jsd = false
    for (let i = 0; i < val.length; i++) {
      if (val[i].componentId === '37') {
        jsd = true
      }
    }
    let asd = false
    for (let i = 0; i < val.length; i++) {
      if (val[i].componentId === '35') {
        asd = true
      }
    }
    if (asd && jsd) {
      let lja = null
      for (let i = 0; i < val.length; i++) {
        if (val[i].componentId === '35') {
          lja = val[i].display
        }
      }
      for (let i = 0; i < val.length; i++) {
        if (val[i].componentId === '37') {
          val[i].val = lja
        }
      }
    }
    // return val
    rObj.id = item.id.value
    rObj.value = val
    rObj.formStatus = item.form_status && item.form_status.value !== '已保存' ? item.form_status.value : null

    const { sourceFormStateSetting, fields } = this.state
    const assistStatus = fields.find(f => isFormAssistStatusComponent(f))
    let findItem = null
    if (assistStatus && assistStatus.id) {
      findItem = sourceFormStateSetting.find(sset =>
        sset.name === rObj.formStatus &&
        sset.assistStatus === item[assistStatus.id].value)
    } else {
      findItem = sourceFormStateSetting.find(sset => sset.name === rObj.formStatus)
    }
    if (findItem && findItem.statusColor) rObj.statusColor = findItem.statusColor

    return rObj
  }

  componentWillUnmount() {
    this.setState = () => { }
  }

  operations = {}
  orderbyFun = (orderby) => {
    const isExp = isExpression(orderby)
    const orders = isExp ? isExp[1] : orderby
    const datas = {}
    if (orderby) {
      orders.split(',').forEach((data) => {
        const value = data.split(' ')
        if (value[1] === 'desc' || value[1] === 'asc') datas[value[0]] = value[1]
      })
    }
    return datas
  }
  doOperate = (data, operationId, isView, query) => {
    this.props.doOperate(data, operationId, this.operations.reload, isView, query)
  }

  // 获取当前时间
  gitTime = () => {
    var myDate = new Date();
    var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
    var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
    var myToday = myDate.getDate(); //获取当前日(1-31)
    this.setState({
      myYear: myYear,
      myMonth: myMonth,
      myToday: myToday,
    })
  }

  // 前一天
  onBeforeDay = () => {
    let timeL = this.state.todaTime
    timeL -= 24 * 60 * 60 * 1000
    this.setState({
      todaTime: timeL,
    }, () => {
      this.getDataListFun(moment(moment(this.state.todaTime).format('YYYY-MM-DD')).unix() * 1000)
    })
  }
  // 后一天
  onNextDay = () => {
    // let timeStr = this.state.todaTime
    // console.log('asndajhdkahdkhaksdhkjashdkjhas', moment(this.state.todaTime).format('DD'), moment(this.state.Dateday).format('DD'))
    let timeL = this.state.todaTime
    timeL += 24 * 60 * 60 * 1000
    // if (moment(this.state.todaTime).format('DD') >= moment(this.state.Dateday).format('DD')) {
    //   Toast.fail("暂无更多数据")
    //   return
    // }
    this.setState({
      todaTime: timeL,
    }, () => {
      this.getDataListFun(moment(moment(this.state.todaTime).format('YYYY-MM-DD')).unix() * 1000)
    })
  }

  onReplyMessage = (item, item1, item2) => {
    const { users } = this.state
    // console.log('asdjklajdlkajdlkasdakjshdkjashdkjh', item, item1, item2)
    let replier = ''
    if (item2) {
      if (item1.componentId === '35') {
        replier = `回复：${item2.userName}`
      }
      if (item2.creator === users.userId) {
        return
      }
    } else {
      for (let i = 0; i < item1.length; i++) {
        if (item1.componentId === '35') {
          replier = `回复：${item1.display}`
        }
      }
    }

    this.setState({
      dataIdMesg: item.id,
      replierText: replier,
      inputModel: true,
    }, () => {
      this.inputRef.focus();
    })
  }

  onBlur = () => {
    this.setState({
      replierText: '',
      inputModel: false,
    })
  }

  onVirtualKeyboardConfirm = async () => {
    const { replierValue } = this.state
    this.setState({
      replierValue1: replierValue
    }, async () => {
      const a = await postReply(this.props.appId, this.props.source, this.state.replyComponentId, this.state.dataIdMesg, '0', this.state.replierValue1, 0)
      // console.log('asjhdkajs', a)
      if (a.errorCode === '0') {
        const c = await getReply(this.props.appId, a.data.id)
        const { dataList } = this.state
        for (let i = 0; i < this.state.dataList.length; i++) {
          if (this.state.dataIdMesg === this.state.dataList[i].id) {
            for (let f = 0; f < this.state.dataList[i].value.length; f++) {
              if (this.state.dataList[i].value[f].componentId === '62') {
                if (this.state.dataList[i].value[f].display === '') {
                  this.state.dataList[i].value[f].display = []
                }
                this.state.dataList[i].value[f].display.push(c.data)
              }
            }
          }
        }
        // console.log('asbhdkjashdklajdkjhaskjd', c)
      }
      this.setState({
        replierValue: ''
      })
    })
    // console.log('点击确认')
  }

  sizeChange = (width, height) => {
    if (height === 0) {
      return
    }
    this.scrollHeight = height
  }

  refScrollView = ref => this.scrollViewCmp = ref

  viewSizeChange = (e) => {
    if (e.nativeEvent.layout.height === 0) {
      return
    }
    this.fieldHeight = e.nativeEvent.layout.height
  }

  goRootMoal = () => {
    const { filter, isWorkFlowForm } = this.props
    // console.log('asdnakdnlkajdlkjasld', filter, isWorkFlowForm,
    //   GO_ROOTMODAL({
    //     children: this.goListPage(),
    //     animationIn: 'slideInRight',
    //     animationOut: 'slideOutRight',
    //     isCustomGoBack: true,
    //   }))
    if (isWorkFlowForm && this.state.count < 1) { return }
    if (isExpression(filter)) { return }
    GO_ROOTMODAL({
      children: this.goListPage(),
      animationIn: 'slideInRight',
      animationOut: 'slideOutRight',
      isCustomGoBack: true,
    })
  }

  checkPrivByOperId = (operId) => {
    let isAuth = false
    let isView = false
    let authArray = null
    if (this.props.authority) {
      authArray = this.props.authority
    } else if (this.state.allOperations) {
      if (this.props.operationarea) {
        authArray = this.state.allOperations.filter(oper =>
          this.props.operationarea.indexOf(oper.id) !== -1 ||
          oper.id === buttionIds.edit ||
          oper.id === buttionIds.delete ||
          oper.id === buttionIds.batch)
      } else {
        authArray = this.state.allOperations
      }
    }
    if (authArray !== null) {
      isAuth = authArray.findIndex(x => x.id === operId) !== -1
      if (!isAuth && operId === buttionIds.edit) {
        isView = this.state.allOperations.findIndex(x => x.id === buttionIds.view) !== -1
        isAuth = isView
      }
    }
    // console.log('asdhjkasdhkahdkjahdkhkjdhakd', operId, this.state.allOperations, this.state.allOperations, authArray, isAuth)

    return { isAuth, isView }
  }

  showButtom = () => {
    // const { showConfirmBut, showChecked } = this.state
    // const isDataTable = listType === 'dataTable'
    const showAddBut = this.checkPrivByOperId(buttionIds.add)
    if (showAddBut) {
      return (
        <TouchableOpacity onPress={() => {
          this.handleAdd()
          // this.rotateScreen()
        }} style={{ width: 65, paddingRight: 15 }}>
          <Image style={{ alignSelf: 'flex-end' }} source={require('../../../images/icon-arrow-plus-blue.png')} />
        </TouchableOpacity>
      )
    }
    return null
  }

  handleAdd = (InvoServiceId) => {
    const isView = this._operCheckPriv(buttionIds.add)
    if (isView !== undefined) {
      let query
      if (this.props.addDefaultValue
        && this.props.addDefaultValue !== null
        && this.props.addDefaultValue !== '') {
        query = { refcolumn: this.props.addDefaultValue, refdataId: this.props.dataId }
      }
      // console.log('asdadhkashdkahdhaksdhkad', buttionIds.add, isView, query, InvoServiceId)
      this.doOperate(null, buttionIds.add, isView, query, InvoServiceId)
    }
  }

  _operCheckPriv = (operId) => {
    const { isAuth, isView } = this.checkPrivByOperId(operId)
    // console.log('asdkjasdlkajdlkalasdnla', isAuth, isView, operId)
    if (isAuth === false) {
      Modal.alert('权限警告', '\n   对不起，您没有权限做此操作。', [
        { text: '确定', onPress: () => { }, style: { fontWeight: 'bold' } },
      ])
      return undefined
    }
    return isView
  }

  goListPage = () => {
    const {
      height, filter, isTab, ignoreEmpty, fieldConfig,
    } = this.props
    console.log(this.props)
    const order = this.orderbyFun(this.props.orderby)
    const data = isExpression(filter) ? '' : filter.replace(/ and $/, '').replace(/ or $/, '')
    const props = {
      appId: this.props.appId,
      formId: this.props.source,
      dataId: this.props.dataId,
      newFormId: this.props.formId,
      ownerFormId:this.props.formId,
      scroll: { x: true, y: (height * 80 - 180 < 0 ? 1 : height * 80 - 180) },
      colWidth: true,
      pagination: {},
      operations: this.operations,
      query: data,
      operationarea: this.props.operationarea,
      titlecolumn: this.props.titlecolumn,
      searchcolumn: this.props.searchcolumn,
      orders: order,
      showOperators: true,
      showAction: true,
      addDefaultValue: this.props.addDefaultValue,
      selectRow: this.doOperate,
      fieldId: this.props.fieldid || this.props.fieldId,
      viewvisiblecolumn: this.props.viewvisiblecolumn,
      statisticalColumn: this.props.statisticalColumn,
      changeESVS: this.props.changeESVS,
      enableScrollViewScroll: this.props.enableScrollViewScroll,
      listItemCount: this.props.listItemCount,
      showLable: !(this.props.hidelabel && this.props.isRunTime),
      listType: 'dataTable',
      tableSize:this.props.tableSize,
      mobileshowfields: this.props.mobileshowfields,
      ignoreEmpty,
      isTab,
      displaytype: this.props.displaytype,
      batcheditcolumn: this.props.batcheditcolumn,
      fieldConfig,
      mColWidth: this.props.mColWidth,
      treeField:this.props.treeField,
      matchTreefield:this.props.matchTreefield,
      treeFilter:this.props.treeFilter
    }
    if (isTab) { return (<DataTablePage {...props} />) }
    return (<AGZList {...props} />)
  }
  render = () => {
    const {
      height,
      isRunTime,
      label,
      changeValue,
      fieldId,
      hidelabel,
      immediatelyToList,
      inputBlur,
      isTab,
      filter,
    } = this.props

    if (isExpression(filter)) { return null }
    if (immediatelyToList) { return null }
    if (isTab) { return this.goListPage() }

    if (height < 2) {
      if (changeValue) changeValue({ [fieldId]: { height: 2 } })
    }
    console.log('ashdkasjdlajskldjaljdlasjlaj', this.state)
    return (
      <View>
        {
          this.props.componentId === '161' ?
            <ScrollView
              ref={scrollView => this.scrollView = scrollView}
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              style={{ width: DimensionsWidth + 200, height: '100%' }}
              onScroll={this.onScroll}
              // scrollEventThrottle={100}
              onMomentumScrollEnd={this.onMomentumScrollEnd}
            // onResponderRelease={this.onResponderRelease}
            >
              <View style={{ width: 100 }}></View>
              <KeyboardAwareScrollView
                style={{ height: '100%', width: DimensionsWidth + 200 }}
                onContentSizeChange={this.sizeChange}
                keyboardOpeningTime={0}
              >
                {
                  this.state.dataList.length > 0 ?
                    this.state.dataList.map((item, index) => (
                      <View key={index} style={{
                        width: DimensionsWidth,
                        backgroundColor: '#fff',
                        borderBottomWidth: 1,
                        padding: 15,
                        marginTop: index === 0 ? 45 : 0,
                        borderBottomColor: 'rgba(0, 0, 0, 0.12)'
                      }}>
                        {
                          item.value.map((item1, key) => (
                            <View key={key}>
                              {
                                item1.display === "" && item1.componentId !== '62' ?
                                  null
                                  :
                                  item1.componentId === '2' ?
                                    <View style={{ marginTop: 10 }} key={index}>
                                      <Text numberOfLines={100} style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300', lineHeight: 20 }}>{item1.display}</Text>
                                    </View>
                                    :
                                    item1.componentId === '1' ?
                                      <View style={{ marginTop: 10 }} key={index}>
                                        <Text numberOfLines={100} style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300' }}>{item1.display}</Text>
                                      </View>
                                      :
                                      item1.componentId === '37' ?
                                        <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                          <Text style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300' }}>{item1.display}</Text>
                                        </View>
                                        :
                                        item1.componentId === '38' ?
                                          <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                            <Text style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300' }}>{item1.display}</Text>
                                          </View>
                                          :
                                          item1.componentId === '3' ?
                                            <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                              <Text style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300' }}>{item1.display}</Text>
                                            </View>
                                            :
                                            item1.componentId === '15' ?
                                              <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                <Text style={{ color: 'rgba(0, 0, 0, 0.85)', fontSize: 14, fontWeight: key === 0 ? '600' : '300' }}>{item1.display}</Text>
                                              </View>
                                              :
                                              item1.componentId === '35' ?
                                                <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                  <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                </View>
                                                :
                                                item1.componentId === '36' ?
                                                  <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                    <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                  </View>
                                                  :
                                                  item1.componentId === '31' ?
                                                    <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                      <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                    </View>
                                                    :
                                                    item1.componentId === '13' ?
                                                      <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                        <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                      </View>
                                                      :
                                                      item1.componentId === '29' ?
                                                        <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                          <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                        </View>
                                                        :
                                                        item1.componentId === '16' ?
                                                          <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                            <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                          </View>
                                                          :
                                                          item1.componentId === '5' ?
                                                            <View style={{ flexDirection: 'row', alignItems: 'center', height: 30 }}>
                                                              <Text style={{ fontWeight: key === 0 ? '600' : '300', marginRight: 7, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>{item1.display}</Text>
                                                            </View>
                                                            :
                                                            item1.componentId === '62' ?
                                                              <View style={{ marginTop: 10, paddingBottom: item1.display ? 5 : 5 }}>
                                                                <View style={{ height: 34.5, borderBottomColor: 'rgba(0, 0, 0, 0.06)', borderBottomWidth: 1, flexDirection: 'row', alignItems: 'center', padding: 10, display: 'flex', paddingLeft: 0, paddingRight: 0 }}>
                                                                  <Text style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, flex: 1 }}>回复</Text>
                                                                  <TouchableOpacity style={{ width: 100, flexDirection: 'row', justifyContent: 'flex-end' }} onPress={this.onReplyMessage.bind(this, item, item1)}>
                                                                    <Image source={require('../../../images/icon-comment-default.png')} />
                                                                  </TouchableOpacity>
                                                                </View>
                                                                {
                                                                  item1.display !== '' ? item1.display.map((item2, lkey) => (
                                                                    <TouchableOpacity key={lkey} onPress={this.onReplyMessage.bind(this, item, item1, item2)} style={{ flexDirection: 'row', paddingLeft: 0, paddingRight: 0, paddingTop: 5 }}>
                                                                      <Text style={{ color: '#1890FF', fontSize: 14, fontWeight: '500', paddingTop: 1 }}>{item2.userName}：</Text>
                                                                      <Text numberOfLines={100} style={{ color: 'rgba(0, 0, 0, 0.75)', flex: 1, fontSize: 14, fontWeight: "normal", lineHeight: 20 }}>{item2.content}</Text>
                                                                    </TouchableOpacity>
                                                                  ))
                                                                    : null
                                                                }
                                                                {/* <TouchableOpacity onPress={this.onReplyMessage} style={{ flexDirection: 'row', paddingTop: 5 }}>
                                                <Text style={{ color: '#1890FF', fontSize: 14, fontWeight: '500', paddingTop: 1 }}>孔莎莎</Text>
                                                <Text style={{ color: 'rgba(0, 0, 0, 0.75)', fontSize: 14, fontWeight: "normal", paddingTop: 1 }}>回复</Text>
                                                <Text style={{ color: '#1890FF', fontSize: 14, fontWeight: '500', paddingTop: 1 }}>丁德平：</Text>
                                                <Text numberOfLines={100} style={{ color: 'rgba(0, 0, 0, 0.75)', flex: 1, fontSize: 14, fontWeight: "normal", lineHeight: 20 }}>下周会有实质性进展😁</Text>
                                              </TouchableOpacity>
                                              <TouchableOpacity onPress={this.onReplyMessage} style={{ flexDirection: 'row', paddingTop: 5 }}>
                                                <Text style={{ color: '#1890FF', fontSize: 14, fontWeight: '500', paddingTop: 1 }}>丁德平：</Text>
                                                <Text numberOfLines={100} style={{ color: 'rgba(0, 0, 0, 0.75)', flex: 1, fontSize: 14, fontWeight: "normal", lineHeight: 20 }}>看好你👍</Text>
                                              </TouchableOpacity> */}
                                                              </View>
                                                              : null
                              }
                            </View>
                          ))
                        }
                      </View>
                    ))
                    :
                    this.state.LoadStatus === '加载完成' ?
                      <View style={{ width: DimensionsWidth, alignItems: 'center', height: 400, paddingTop: 200 }}>
                        <Image source={require('../../../images/icon-word_report-empty.png')} />
                        <Text style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.45)' }}>{this.state.LoadStatus === '加载失败' ? '加载失败' : '没有内容'}</Text>
                      </View>
                      : null
                }
              </KeyboardAwareScrollView>
              <View style={{ width: 100 }}></View>
            </ScrollView>
            :
            <List.Item
              style={{ height: 70, borderBottomWidth: 0 }}
              arrow="horizontal"
              hideBottomLine
              onClick={() => {
                if (inputBlur) {
                  // console.log('asdhkjadhahdhakdhk', inputBlur)
                  inputBlur(this.goRootMoal)
                } else {
                  this.goRootMoal()
                }
              }}
            >
              {!(hidelabel && isRunTime) && <Text style={{ color: '#1B1B1B' }}>{label}</Text>}
              <List.Item.Brief style={{ fontSize: 14 }}>
                共{this.state.count}条
          </List.Item.Brief>
            </List.Item>
        }

        {
          this.props.componentId === '161' ?
            <View
              style={{
                height: 42,
                width: '100%',
                backgroundColor: '#E0F0FD',
                flexDirection: 'row',
                display: 'flex',
                alignItems: 'center',
                position: 'absolute',
                top: 0,
                left: 0
              }}
            >
              <View style={{ width: 85 }}></View>
              <View style={{ flex: 1, display: 'flex', flexDirection: 'row' }}>
                <TouchableOpacity style={{ width: 50 }} onPress={this.onBeforeDay}>
                  <Image source={require('../../../images/icon-arrow-left-blue.png')} />
                </TouchableOpacity>
                <TouchableOpacity style={{ flex: 1 }}>
                  <Text style={{ textAlign: 'center', color: '#1890FF', fontSize: 18 }}>{moment(this.state.todaTime).format('YYYY-MM-DD')}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={{ width: 50 }} onPress={this.onNextDay}>
                  <Image style={{ alignSelf: 'flex-end', paddingRight: 15 }} source={require('../../../images/icon-arrow-right-blue.png')} />
                </TouchableOpacity>
              </View>
              <View style={{ width: 20 }}></View>
              {
                this.showButtom() ? this.showButtom() : <View style={{ width: 65 }}></View>
              }

            </View>
            : null
        }
        {
          this.state.inputModel ?
            <View style={{
              backgroundColor: '#F8F8F8',
              height: 42,
              width: '100%',
              position: 'absolute',
              bottom: 0,
              left: 0
            }}>
              <TextInput
                value={this.state.replierValue}
                onSubmitEditing={this.onVirtualKeyboardConfirm}
                onChangeText={value => {
                  // console.log('asjdlkadasjkdljalsd', value)
                  this.setState({
                    replierValue: value,
                  });
                }}
                returnKeyType="send"
                clearTextOnFocus={true}
                onBlur={this.onBlur}
                ref={el => (this.inputRef = el)}
              />
            </View>
            : null
        }
      </View >
    )
  }
}

const mapDispatchToProps = (dispatch, ownProps) => ({
  doOperate: (selectedIds, operationId, callback, isView, query) =>
    dispatch({
      type: DO_OPERATION,
      payload: {
        operationId, appId: ownProps.appId, formId: ownProps.source, formName: ownProps.formName, selectedIds, callback, isView, query,
      },
    }),
})
Lists.propTypes = {
  source: PropTypes.string.isRequired,
  appId: PropTypes.string.isRequired,
  isRunTime: PropTypes.bool,
  height: PropTypes.number.isRequired,
  label: PropTypes.string,
  ignoreEmpty: PropTypes.bool,
  fieldid: PropTypes.string,
  changeValue: PropTypes.func,
  fieldId: PropTypes.string,
  filter: PropTypes.string,
  orderby: PropTypes.string,
  hidelabel: PropTypes.bool,
  operationarea: PropTypes.arrayOf(PropTypes.string),
  titlecolumn: PropTypes.string,
  searchcolumn: PropTypes.arrayOf(PropTypes.string),
  addDefaultValue: PropTypes.string,
  immediatelyToList: PropTypes.number,
  inputBlur: PropTypes.func,
  isTab: PropTypes.bool,
}
export default connect(null, mapDispatchToProps)(Lists)
