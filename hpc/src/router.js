import React from 'react'
import { PropTypes } from 'prop-types'
import {companyName} from '@project-config'
import { BottomTabBar, createStackNavigator, createSwitchNavigator, createAppContainer, createBottomTabNavigator, NavigationActions, StackActions } from 'react-navigation'
import SvgIcon from './components/SvgIcon'
import App from './containers/App'
import Login from './containers/Login'
import MessageBox from './components/Message'
import WorkflowBox from './components/Workflow'
import AppList from './containers/RunTime/List'
import RefList from './containers/RefList'
import AppForm from './containers/RunTime/Form'
// import ReportForm from './containers/RunTime/ReportForm'
import SignUp from './containers/Signup'
import UserForm from './components/UserSetting/UserForm'
import UserGeneralSettings from './components/UserSetting/UserGeneralSettings'
import AccessRights from './components/AccessRights'

import User from './containers/UserSetting'
import NetSolution from './components/Solution'
import OfficialWeb from './components/WebPage'
import CCWeb from './components/CCWeb'
import AppCreate from './containers/AppCreate'
import InviteUsers from './containers/RunTime/InviteUsers'
import UserOrGroup from './addons/FormComponent/UserOrGroup/Choice'
import FileManager from './containers/FileManager'
import FullEditorView from './addons/FormComponent/RichEditor/FullEditorView'
import ReportDetail from './components/ReportDetail'
import FullReportView from './addons/FormComponent/Report/FullReportView'
import ReportList from './addons/FormComponent/Report/ReportList'
// import SingleList from './components/singleList'
import AppListModal from './containers/appListModal'
import RuntimeNav from './components/Nav/RuntimeNav'
import RouterModal from './containers/RouterModal'
import FileDetail from './addons/FormComponent/Attachment/FileDetail'
import FileList from './addons/FormComponent/Attachment/ModalView'
import PictureList from './addons/FormComponent/Attachment/Picture/PictureList'
import AttendanceDetail from './addons/FormComponent/AttendanceList/detail'
import CountryCode from './components/CountryCode'
import VerifyCode from './components/Signup/VerifyCode'
import SignUpRegister from './components/Signup/SignUpRegister'
import AboutUs from './components/AboutUs'
import PwdSetting from './components/PwdSetting'
import RefPropertyModal from './addons/FormComponent/RefProperty/RefPropertyModal'
import AttendanceWifiList from './addons/FormComponent/CheckingAttendance/AttendanceWifiList'
import AttendanceRecordList from './addons/FormComponent/CheckingAttendance/CheckingRecordList'
import AttendanceConfig from './components/Attendance/AttendanceConfig'
import ScreenVideo from './components/ScreenVideo'
import CallCenterNum from './components/CallCenterNum'
import PrivacyPage from './components/PrivacyPage'
import ServicePage from './components/ServicePage'

import { GO_CREATE } from './utils/jump'

// 为了方便在props里注入navigate的带过去的参数
const mapNavigationStateParamsToProps = (SomeComponent) => class extends React.Component {
  static navigationOptions = SomeComponent.navigationOptions; // better use hoist-non-react-statics

  static propTypes = {
    navigation: PropTypes.object,
  }

  render() {
    const { navigation: { state: { params } }, screenProps } = this.props
    let newScreenProps = {}
    if (screenProps) {
      if (screenProps.currentNavIndex === 0) {
        newScreenProps = params
      } else {
        newScreenProps = {
          appId: screenProps.appId,
          appName: screenProps.appName,
          currentNavData: screenProps.currentNavData,
        }
      }
    }
    return <SomeComponent {...params} {...this.props} />
  }
}

const AppNavigationStack = createStackNavigator({
  home: {
    screen: mapNavigationStateParamsToProps(App),
    navigationOptions: {
      title: '首页',
      header: null,
      headerBackTitle: null,
    },
  },
  messageBox: {
    screen: mapNavigationStateParamsToProps(MessageBox),
    navigationOptions: {
      title: '消息',
      header: null,
      headerBackTitle: null,
    },
  },
  workflowBox: {
    screen: mapNavigationStateParamsToProps(WorkflowBox),
    navigationOptions: {
      title: '工作流',
      header: null,
      headerBackTitle: null,
    },
  },
  appList: {
    screen: mapNavigationStateParamsToProps(AppList),
    navigationOptions: {
      title: '应用列表页',
      header: null,
      headerBackTitle: null,
    },
  },
  accessRights: {
    screen: mapNavigationStateParamsToProps(AccessRights),
    navigationOptions: {
      title: '访问权限提示',
      header: null,
      headerBackTitle: null,
    },
  },
  userGenSettings: {
    screen: mapNavigationStateParamsToProps(UserGeneralSettings),
    navigationOptions: {
      title: '通用设置',
      headerBackTitle: null,
    },
  },
  userInfo: {
    screen: mapNavigationStateParamsToProps(UserForm),
    navigationOptions: {
      title: '账号管理',
      headerBackTitle: null,
    },
  },
  userSetting: {
    screen: mapNavigationStateParamsToProps(User),
    navigationOptions: {
      title: '个人资料',
      headerBackTitle: null,
    },
  },
  pwdSetting: {
    screen: mapNavigationStateParamsToProps(PwdSetting),
    navigationOptions: {
      title: '修改个人信息',
      headerBackTitle: null,
    },
  },
  aboutUs: {
    screen: mapNavigationStateParamsToProps(AboutUs),
    navigationOptions: {
      headerBackTitle: null,
      title: '关于我们',
    },
  },
  appForm: {
    screen: mapNavigationStateParamsToProps(AppForm),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '表单详情',
    },
  },
  rootRouterModal: {
    screen: mapNavigationStateParamsToProps(RouterModal),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '表单详情',
    },
  },
  attendanceDetail: {
    screen: mapNavigationStateParamsToProps(AttendanceDetail),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '表单详情',
    },
  },
  appReport: {
    screen: mapNavigationStateParamsToProps(AppForm),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '表单列表',
    },
  },
  appNav: {
    screen: mapNavigationStateParamsToProps(RuntimeNav),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '导航设置',
    },
  },
  userOrGroup: {
    screen: mapNavigationStateParamsToProps(UserOrGroup),
    navigationOptions: {
      title: '导航设置',
    },
  },
  refList: {
    screen: mapNavigationStateParamsToProps(RefList),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '引用表单列表',
    },
  },
  appCreate: {
    screen: mapNavigationStateParamsToProps(AppCreate),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '应用详情',
    },
  },

  inviteUsers: {
    screen: mapNavigationStateParamsToProps(InviteUsers),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '邀请用户',
    },
  },
  fileManager: {
    screen: mapNavigationStateParamsToProps(FileManager),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '文件列表',
    },
  },
  netSolution: {
    screen: mapNavigationStateParamsToProps(NetSolution),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '解决方案',
    },
  },

  fullEditorView: {
    screen: mapNavigationStateParamsToProps(FullEditorView),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '富文本',
    },
  },
  fullReportView: {
    screen: mapNavigationStateParamsToProps(FullReportView),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '报表',
    },
  },
  reportDetail: {
    screen: mapNavigationStateParamsToProps(ReportDetail),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '报表',
    },
  },
  reportList: {
    screen: mapNavigationStateParamsToProps(ReportList),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
      title: '报表',
    },
  },
  innerOfficialWeb: {
    screen: mapNavigationStateParamsToProps(OfficialWeb),
    navigationOptions: {
      headerBackTitle: null,
      title: companyName+'服务条款',
    },
  },
  callCenter: {
    screen: mapNavigationStateParamsToProps(CCWeb),
    navigationOptions: {
      title: '联系客服',
      headerBackTitle: null,

    },
  },
  callCenterNum: {
    screen: mapNavigationStateParamsToProps(CallCenterNum),
    navigationOptions: {
      title: '联系客服',
      headerBackTitle: null,

    },
  },
  fileDetail: {
    screen: mapNavigationStateParamsToProps(FileDetail),
    navigationOptions: {
      header: null,
    },
  },
  fileList: {
    screen: mapNavigationStateParamsToProps(FileList),
    navigationOptions: {
      header: null,
    },
  },
  pictureList: {
    screen: mapNavigationStateParamsToProps(PictureList),
    navigationOptions: {
      header: null,
    },
  },

  refPropertyModal: {
    screen: mapNavigationStateParamsToProps(RefPropertyModal),
    navigationOptions: {
      header: null,
    },
  },
  appListModal: {
    screen: mapNavigationStateParamsToProps(AppListModal),
    navigationOptions: {
      header: null,
    },
  },
  wifiList: {
    screen: mapNavigationStateParamsToProps(AttendanceWifiList),
    navigationOptions: {
      header: null,
    },
  },
  attendanceRecordList: {
    screen: mapNavigationStateParamsToProps(AttendanceRecordList),
    navigationOptions: {
      header: null,
    },
  },
  attendanceConfig: {
    screen: mapNavigationStateParamsToProps(AttendanceConfig),
    navigationOptions: {
      header: null,
    },
  },
}, {
  navigationOptions: {
    initialRouteName: 'home',
    headerStyle: {
      backgroundColor: '#fff',
    },
    gesturesEnabled: false,
  },
})

const LoginNavigationStack = createStackNavigator({
  auth: {
    screen: mapNavigationStateParamsToProps(Login),
    navigationOptions: {
      header: null,
      title: '登录',
      headerBackTitle: null,
    },
  },
  verifyCode: {
    screen: mapNavigationStateParamsToProps(VerifyCode),
    navigationOptions: {
      title: '忘记密码',
      headerBackTitle: null,
    },
  },
  sigupVerifyCode: {
    screen: mapNavigationStateParamsToProps(VerifyCode),
    navigationOptions: {
      title: '注册'+companyName,
      headerBackTitle: null,
    },
  },
  signUp: {
    screen: mapNavigationStateParamsToProps(SignUp),
    navigationOptions: {
      headerBackTitle: null,
      title: '找回密码',
    },
  },
  SignUpRegister: {
    screen: mapNavigationStateParamsToProps(SignUp),
    navigationOptions: {
      headerBackTitle: null,
      title: '注册'+companyName,
    },
  },
  countryCode: {
    screen: mapNavigationStateParamsToProps(CountryCode),
    navigationOptions: {
      title: '国家/地区',
      headerBackTitle: null,

    },
  },
  officialWeb: {
    screen: mapNavigationStateParamsToProps(OfficialWeb),
    navigationOptions: {
      title: companyName+'服务条款',
      headerBackTitle: null,
    },
  },
  privacyPage: {
    screen: mapNavigationStateParamsToProps(PrivacyPage),
    navigationOptions: {
      title: '隐私政策',
      headerBackTitle: null,

    },
  },
  servicePage: {
    screen: mapNavigationStateParamsToProps(ServicePage),
    navigationOptions: {
      title: '服务协议',
      headerBackTitle: null,

    },
  },
  hotFixModal: {
    screen: mapNavigationStateParamsToProps(RouterModal),
    navigationOptions: {
      header: null,
      headerBackTitle: null,
    },
  },
}, {
  initialRouteName: 'auth',
  navigationOptions: {
    headerStyle: {
      backgroundColor: '#fff',
    },
  },
})
const ScreenVideoStack = createStackNavigator({
  video: {
    screen: mapNavigationStateParamsToProps(ScreenVideo),
    navigationOptions: {
      header: null,
      title: '广告',
      headerBackTitle: null,
    },
  },
}, {
  initialRouteName: 'video',
  navigationOptions: {
    headerStyle: {
      backgroundColor: '#fff',
    },
  },
})

const TabBarComponent = (props) => (<BottomTabBar {...props} />)

// eslint-disable-next-line react/no-multi-comp
class CustomNavigator extends React.Component {
  static router = AppNavigationStack.router

  static navigationOptions = ({ navigation }) => {
    const currentNavData = navigation.getParam('currentNavData', { mobileIcon: '' })
    const [iconType, iconName] = currentNavData.mobileIcon.split('|')
    const hideTab = navigation.getParam('hideTab', false)
    const title = currentNavData.name || ''
    const newNav = {
      title,
      tabBarOnPress: ({ navigation: navigation1, defaultHandler }) => {
        const currentNavData = navigation1.getParam('currentNavData', {})
        const appId = navigation1.getParam('appId', '')
        const appName = navigation1.getParam('appName', '')
        // 当当前tab页不是指向默认主导航的时候，重置tab的route
        if (currentNavData.refId !== '1111') {
          const action = StackActions.reset({
            index: 0,
            actions: [NavigationActions.navigate({
              routeName: 'appForm',
              params: { appId, formId: currentNavData.refId, appName, isCustomMainNavHeader: true },
            })],
            key: navigation1.state.key,
          })
          navigation1.dispatch(action)
        } else {
          const action = StackActions.reset({
            index: 0,
            actions: [NavigationActions.navigate({
              routeName: 'home',
              params: { appId, appName, isCustomMainNavHeader: true },
            })],
            key: navigation1.state.key,
          })
          navigation1.dispatch(action)
        }
        defaultHandler()
      },
      tabBarOnLongPress: ({ navigation: navigation1, defaultHandler }) => {
        // const currentNavData = navigation1.getParam('currentNavData', {})
        // const appId = navigation1.getParam('appId', '')
        // const appName = navigation1.getParam('appName', '')
        // defaultHandler()
        // // 当当前tab页不是指向默认主导航的时候，重置tab的route
        // if (currentNavData.refId !== '1111') {
        //   const action = StackActions.reset({
        //     index: 0,
        //     actions: [NavigationActions.navigate({ routeName: 'appForm',
        //       params: { appId, formId: currentNavData.refId, appName, isCustomMainNavHeader: true },
        //     })],
        //   })
        //   navigation1.dispatch(action)
        // } else {
        //   const action = NavigationActions.setParams({
        //     params: { appId, appName, isCustomMainNavHeader: true },
        //   })
        //   navigation1.dispatch(action)
        // }
      },
      tabBarIcon: ({ focused, horizontal, tintColor }) => <SvgIcon type={iconType} name={iconName || 'action|dial_in'} size={20} bgColor={focused ? '#007aff' : '#8e8e93'} />,
      // 当不是第一级页面时隐藏tab
      tabBarVisible: hideTab ? false : navigation.state.index < 1,
    }
    return newNav
  }

  render() {
    const { navigation } = this.props
    const { state: { params, key, index } } = navigation
    return <AppNavigationStack navigation={navigation} />
  }
}

const TabNavigationStackChildOne = createBottomTabNavigator({
  tabChildOfOne1: {
    screen: CustomNavigator,
  },
})
const TabNavigationStackChildTwo = createBottomTabNavigator({
  tabChildOfTwo1: {
    screen: CustomNavigator,
  },
  tabChildOfTwo2: {
    screen: CustomNavigator,
  },
})
const TabNavigationStackChildThree = createBottomTabNavigator({
  tabChildOfThree1: CustomNavigator,
  tabChildOfThree2: CustomNavigator,
  tabChildOfThree3: CustomNavigator,
}, {
  tabBarOptions: {
    adaptive: true,
    style: {
      height: 54,
    },
    labelStyle: {
      paddingHorizontal: 5,
    },
  },
})
const TabNavigationStackChildFour = createBottomTabNavigator({
  tabChildOfFour1: {
    screen: CustomNavigator,
  },
  tabChildOfFour2: {
    screen: CustomNavigator,
  },
  tabChildOfFour3: {
    screen: CustomNavigator,
  },
  tabChildOfFour4: {
    screen: CustomNavigator,
  },
})
const TabNavigationStackChildFive = createBottomTabNavigator({
  tabChildOfFive1: {
    screen: CustomNavigator,
  },
  tabChildOfFive2: {
    screen: CustomNavigator,
  },
  tabChildOfFive3: {
    screen: CustomNavigator,
  },
  tabChildOfFive4: {
    screen: CustomNavigator,
  },
  tabChildOfFive5: {
    screen: CustomNavigator,
  },
})
// 根路由
const RootSwitchStack = createSwitchNavigator({
  bottomTabOne: TabNavigationStackChildOne,
  bottomTabTwo: TabNavigationStackChildTwo,
  bottomTabThree: TabNavigationStackChildThree,
  bottomTabFour: TabNavigationStackChildFour,
  bottomTabFive: TabNavigationStackChildFive,
  app: AppNavigationStack,
  indexVideo: ScreenVideoStack,
  login: LoginNavigationStack,
  // callCenter: CallCenterTabStack,
}, {
  initialRouteName: 'login',
  headerMode: 'none',
})
const RootContainer = createAppContainer(RootSwitchStack)
export default RootContainer
