import omit from 'lodash/omit'

import { envParm } from '../config/sysPara'
import { getToken } from '../utils/storage'
import { setCache, getCache } from './cache'
import NavigationService from '../NavigationService'

import {
  GET,
  POST,
  PUT,
  DELETE,
  APPLICATION_JSON,
  BUILD_OPTIONS_ERROR,
  REQUEST_ERROR_CODE,
  ERROR_MESSAGE,
  REQUEST_TIMEOUT_CODE,
  TIMEOUT_MESSAGE,
} from './constants'
import middlewares from './middlewares'
import { toString } from '../utils/querystring'
import {getUniqueId} from 'react-native-device-info'
import { NetworkInfo } from 'react-native-network-info'

const parse = async res => await res.json()
const REST_URL = envParm.URLS

const abortFetch = (fetchPromise, timeout = 60 * 1000) => {
  // 这是一个可以被reject的promise
  const abortPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve({
        errorCode: REQUEST_TIMEOUT_CODE,
        errorMsg: TIMEOUT_MESSAGE,
        data: {},
      })
    }, timeout)
  })
  // 这里使用Promise.race，以最快 resolve 或 reject 的结果来传入后续绑定的回调
  const abortablePromise = Promise.race([
    fetchPromise,
    abortPromise,
  ])

  return abortablePromise
}

export const buildOptions = async (url, options) => {
  const mergedOptions = {
    headers: {
      Accept: APPLICATION_JSON,
      'Content-Type': APPLICATION_JSON,
      'Accept-Language': 'zh-CN,zh;q=0.9',
      ...options.headers,
    },
    ...omit(options, 'headers'),
  }
  mergedOptions.headers.authToken = await getToken()
  let newUrl = url
  if (options.body && typeof options.body !== 'string') {
    throw new Error(`body的类型必须是json string, 现在是${typeof options.body}`)
  }
  if (options.data && typeof options.data !== 'object') {
    throw new Error(`data的类型必须是object, 现在是${typeof options.data}`)
  }
  // 如果是GET请求，把data的内容转换成query加到url后
  if (mergedOptions.method === GET) {
    const query = toString(options.data)
    if (newUrl.indexOf('?') > -1 && query) {
      newUrl += `&${query}`
    } else if (newUrl.indexOf('?') <= -1 && query) {
      newUrl += `?${query}`
    }
    delete mergedOptions.body
  } else if (mergedOptions.headers['Content-Type'] === false) {
    const formData = new FormData()
    Object.keys(options.data)
      .forEach((key) => {
        formData.append(key, options.data[key])
      })
    mergedOptions.body = formData
    delete mergedOptions.headers['Content-Type']
  } else {
    mergedOptions.body = options.body ? options.body : JSON.stringify(options.data)
  }
  delete mergedOptions.data
  return { newUrl, mergedOptions }
}

export const formatUrl = async (path) => {
  const bssid = await NetworkInfo.getBSSID()
  let urLparmas
  if (path.indexOf('?') > -1) {
    urLparmas = '&deviceId=' + getUniqueId() + '&networkId=' + bssid
  } else {
    urLparmas = '?deviceId=' + getUniqueId() + '&networkId=' + bssid
  }
  return REST_URL + path + urLparmas
}

/**
 * request
 * @param {String} path
 * @param {Object} options
 * path 必须以/开头
 * options一般可能填写method/body/data/headers参数，
 * 默认的headers是:
 * {
 *    Accept: 'application/json',
 *    'Content-Type': 'application/json',
 * }
 * path必须以/开头
 * body是fetch原始body的内容，类型为string
 * data是自定义的body内容，类型是object，会用JSON.stringify转换为string类型的body
 * ps: 同时使用body和data会使body覆盖data
 */
const request = path => async (options) => {
  // Toast.loading('努力加载中...', 0, ()=>{}, true)
  if (options.withCache) {
    const cache = await getCache(path, options)
    if (cache) {
      // Toast.hide()
      return cache
    }
  }
  const url = await formatUrl(path)
  let newUrl
  let mergedOptions
  try {
    const newOptions = await buildOptions(url, options)
    newUrl = newOptions.newUrl
    mergedOptions = newOptions.mergedOptions
  } catch (e) {
    // Toast.hide()
    return {
      errorCode: BUILD_OPTIONS_ERROR,
      errorMsg: e.message,
      data: {},
    }
  }
  try {
    const fetchRequest = fetch(newUrl, mergedOptions)
    let result
    // 当请求为超时的时候,会尝试3次重连
    for (let i = 0; i < 3; i++) {
      result = await abortFetch(fetchRequest)
      if (result.errorCode !== REQUEST_TIMEOUT_CODE) {
        break
      }
    }
    if (result.errorCode === REQUEST_TIMEOUT_CODE) {
      return {
        errorCode: REQUEST_TIMEOUT_CODE,
        errorMsg: TIMEOUT_MESSAGE,
        data: {},
      }
    }
    console.log('asdhklahdjlajsdlkjaslkdjlakdjla', result)
    // Toast.hide()
    if (result.ok) {
      if (options.isDownload) {
        if (result.headers.get('content-type') !== 'application/json') {
          const blob = await result.blob()
          const downloadURL = URL.createObjectURL(blob)
          let ele = document.createElement('a')
          ele.setAttribute('href', downloadURL)
          ele.download = getFilename(options.filename)
          ele.click()
          ele = null
          return {
            errorCode: '0',
          }
        }
      }
      const json = await parse(result)
      const hanldeByMiddlewares = await middlewares(json)
      if (hanldeByMiddlewares.errorCode == 'BGT-00009' || hanldeByMiddlewares.errorCode == 'BGT-00010' || hanldeByMiddlewares.errorCode == 'BGT-00011' || hanldeByMiddlewares.errorCode == 'BGT-00012' || hanldeByMiddlewares.errorCode == 'BGT-00013' || hanldeByMiddlewares.errorCode == 'BGT-00014' || hanldeByMiddlewares.errorCode == 'BGT-00015') {
        NavigationService.push('accessRights', { parmas: hanldeByMiddlewares.errorMsg, ishow: false })
      }
      if (hanldeByMiddlewares.data.leftTime && 0 <= Number(hanldeByMiddlewares.data.leftTime) && Number(hanldeByMiddlewares.data.leftTime) <= 5) {
        return NavigationService.push('accessRights', { parmas: '不在授权使用时段，请在授权时段内使用', ishow: true, leftTime: hanldeByMiddlewares.data.leftTime })
      }
      if (options.withCache) {
        setCache(path, options, hanldeByMiddlewares)
      }
      return hanldeByMiddlewares
    }
    return {
      errorCode: REQUEST_ERROR_CODE,
      errorMsg: ERROR_MESSAGE,
      data: {},
    }
  } catch (e) {
    // Toast.hide()
    return {
      errorCode: REQUEST_ERROR_CODE,
      errorMsg: ERROR_MESSAGE,
      trace: e.message,
      data: {},
    }
  }
}

export const post = path => options => request(path)({ ...options, method: POST })

export const get = path => options => request(path)({ ...options, method: GET })

export const getWithCache = path => options => request(path)({ ...options, method: GET, withCache: true })

export const put = path => options => request(path)({ ...options, method: PUT })

export const del = path => options => request(path)({ ...options, method: DELETE })

export const upload = path => options => request(path)({
  headers: { 'Content-Type': false },
  ...options,
  method: POST,
})

export default request
