import { NavigationActions, StackActions } from 'react-navigation'

let navigatorInstance

export function setTopLevelNavigator(navigatorRef) {
  navigatorInstance = navigatorRef
}

function filterParam(data = {}) {
  if (data.toString() !== '[object Object]') {
    return { data }
  }
  const proto = (data || {}).constructor.name
  // avoid passing React Native parameters
  if (!data || proto !== 'Object') {
    return {}
  }
  return data
}

// 跳转方法
function navigate(routeName, params = null, action = null, key = null) {
  // console.log(navigatorInstance, 'navigate')
  navigatorInstance.dispatch(NavigationActions.navigate({
    routeName,
    params,
    action,
    key,
  }))
  // console.log('navigate', navigatorInstance, routeName, params)
}
// 后退方法
function back(params) {
  navigatorInstance.dispatch(NavigationActions.back({ params }))
  // console.log('back', navigatorInstance)
}

function backToKey(key) {
  navigatorInstance.dispatch(NavigationActions.back({ key }))
  // console.log('backToKey', navigatorInstance)
}

function popAndRefresh(params) {
  const res = filterParam(params)
  if (res && res.timeout) {
    // console.log('popAndRefreshtime')
    setTimeout(() => popAndRefresh(params), res.timeout)
  } else {
    navigatorInstance.dispatch(NavigationActions.back())
    // console.log('popAndRefresh', navigatorInstance, params)
    if (res.refresh) {
      setTimeout(() => { refresh(res.refresh) }, 0)
    }
  }
  return true
}

function refresh(data) {
  const currentKey = getCurrentRoute(navigatorInstance.state.nav).key
  console.log(currentKey)
  const params = filterParam(data)
  navigatorInstance.dispatch(NavigationActions.setParams({ key: currentKey, params }))
  // console.log('refresh', navigatorInstance, currentKey, params)
}
// 推入新路由方法,与navigate的区别在于，如果有已经加载的页面，navigate方法将跳转到已经加载的页面，而不会重新创建一个新的页面。 push 总是会创建一个新的页面，所以一个页面可以被多次创建
function push(routeName, params, action) {
  // console.log(navigatorInstance, 'push')
  navigatorInstance.dispatch(StackActions.push({
    routeName, params, action,
  }))
  // console.log('push', navigatorInstance, routeName)
}
/**
 * Reset操作会擦除整个导航状态，并将其替换为多个操作的结果。
 * @param {*} index routes数组中state的活动路由的索引
 * @param {*} actions  将取代导航状态的导航行为数组，只能操作子路由
 * @param {*} key  如果设置，具有给定 key 的导航器将重置。 如果为null，则根导航器将重置
 */
function reset(index = 0, actions, key) {
  // console.log('reset', navigatorInstance)
  navigatorInstance.dispatch(StackActions.reset({
    index, actions, key,
  }))
  // console.log('reset', navigatorInstance)
}
// 用于完全重置整个路由,只能重置当前栈里的路由
function wholeReset(routeName, params = null, key) {
  navigatorInstance.dispatch(StackActions.reset({
    index: 0, actions: [NavigationActions.navigate({ routeName, params })], key,
  }))
  // console.log('wholeReset', navigatorInstance)
}

function init(params) {
  navigatorInstance.dispatch(NavigationActions.init())
  // console.log(navigatorInstance.state.nav)
}

function replace(key, newKey, routeName, params = {}, action = {}) {
  navigatorInstance.dispatch(StackActions.replace(key, newKey, routeName, params, action))
  // console.log(navigatorInstance)
}

export function getNavigatorInstance() {
  return navigatorInstance
}

function goBackToLogin() {
  navigatorInstance.dispatch(NavigationActions.navigate({
    routeName: 'login',
    params: null,
    action: StackActions.reset({
      index: 0,
      actions: [
        NavigationActions.navigate({ routeName: 'auth', params: { failFlag: true } }),
      ],
      key: 'login',
    }),
  }))
  // console.log(navigatorInstance)
}

function setParams(params, key) {
  const action = NavigationActions.setParams({
    params,
    key,
  })
  navigatorInstance.dispatch(action)
}

export function getCurrentRoute(nav, times = 10, intialTimes = 1) {
  const currentIndex = nav.index
  const currentRoute = nav.routes[currentIndex]
  if (currentRoute.routes && intialTimes < times) {
    return getCurrentRoute(currentRoute, times, intialTimes + 1)
  }
  return currentRoute
}

const checkIsHotFixWrapper = wrapper => (...params) => {
  // 当前路由为热更新界面时，阻拦后续跳转事件
  const currentRoute = getCurrentRoute(navigatorInstance.state.nav)
  if (navigatorInstance && currentRoute.routeName !== 'hotFixModal') {
    wrapper(...params)
  }
  //  console.log('当前界面为hotfix界面')
}

export default {
  navigate: checkIsHotFixWrapper(navigate),
  back,
  backToKey,
  push: checkIsHotFixWrapper(push),
  reset,
  popAndRefresh,
  wholeReset,
  refresh,
  replace,
  goBackToLogin,
  init,
  getNavigatorInstance,
  setTopLevelNavigator,
  setParams,
  getCurrentRoute,
}
