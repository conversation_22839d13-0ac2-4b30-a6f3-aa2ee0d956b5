import { deviceType } from './sysPara';

const iconName_ios = new Map([
  ['default','ios-cloud'],
  ['file-text','ios-document'],
  
  ['cloud','ios-cloud'],
  ['apple','logo-apple'],
  ['appstore','ios-apps'],
  ['global','ios-globe'],
  ['pie-chart','ios-pie'],
  ['team','ios-contacts'],
  ['add-circle-outline', 'ios-add-circle']
]);

const iconName_android = new Map([
  ['default','md-cloud'],
  ['file-text','md-document'],

  ['cloud','md-cloud'],
  ['apple','logo-apple'],
  ['appstore','md-apps'],
  ['global','md-globe'],
  ['pie-chart','md-pie'],
  ['team','md-contacts'],
  ['add-circle-outline', 'md-add-circle']
]);

function getIconName(name) {
    let platformIcon = (deviceType === 2) ? iconName_ios:iconName_android;
    let retName = platformIcon.get(name);

    if (!retName) {
      name = (deviceType === 2) ? 'ios-'+name : 'md-'+name;
    }

    return (retName ? retName : name);
};

export default getIconName;