/*
本文件存放全局使用的，属于配置性质的参数
*/
import { Platform, Dimensions, AsyncStorage } from 'react-native'

export const deviceType = (Platform.OS === 'ios') ? 2 : 1

export const isIos = Platform.OS === 'ios'

// 定义触摸事件的 点击和按压（长时间触摸）之间的时间长度，单位为ms
export const delayLongPress = 2000

const { height, width } = Dimensions.get('window')
export const screenHeight = height
export const screenWidth = width

// 百毒服务端api key
export const bmapServiceKey = 'xyREZbhpYa87oyAAtMGqpHL8'

const getEasterEgg = async () => {
  let easterEgg1 = null // 设置彩蛋
  try {
    const resultA = await AsyncStorage.getItem('easterEgg')
    easterEgg1 = JSON.parse(resultA)
  } catch (e) {
    __DEV__ && console.log('获取本地用户数据失败', easterEgg1, e)
  }
  return easterEgg1
}

const getEnvParm = () => {
  const API_VERSION = 'v1'
  let URLS = null
  let REPORTURLS = null
  let bucket = null
  const isPrivate = false
  const imgBuck = 'huapuc-pd'// 生产
  const imgName = deviceType === 2 ? 'bundle_ios.zip' : 'bundle.zip'
  // js更新版本号 需修改为整数
  // let version = '1.0.0';
  const version = '2021082701'
  const appStoreId = '1563323211'
  const environment = 'dev'
  // getEasterEgg().then((easterEgg) => {
  if (__DEV__) {
    bucket = 'huapuc-test'
    // URLS = `http://**************:52038/api/${API_VERSION}` // 生产环境android
    // REPORTURLS = 'http://**************:52038'// 生产环境android
    // URLS = `http://pro030.demo.com:52038/api/${API_VERSION}` // lb
    // URLS = `http://***********:52048/api/${API_VERSION}`
  
    URLS = `http://www.huapuc.com:52038/api/${API_VERSION}`
    // URLS = `http://*************:52048/api/${API_VERSION}`,
    // REPORTURLS = 'http://pro030.demo.com:52038'
    // URLS = `http://3579e1829f.qicp.vip/api/${API_VERSION}`
    // REPORTURLS = 'http://3579e1829f.qicp.vip'
    // URLS = `http://dev02.huapuc.com/api/${API_VERSION}`
    // REPORTURLS = 'http://dev11.demo.com:52038'

    REPORTURLS = 'http://www.huapuc.com:52038'
  } else {
    bucket = 'huapuc-pd'
    // URLS = `http://dev02.huapuc.com/api/${API_VERSION}` // dev
    // REPORTURLS = 'http://dev11.demo.com:52038'
    // URLS = `http://**************:52038/api/${API_VERSION}` // 生产环境android
    // REPORTURLS = 'http://**************:52038'// 生产环境android
    URLS = `http://www.huapuc.com:52038/api/${API_VERSION}`
    REPORTURLS = 'http://www.huapuc.com:52038'
    // URLS = `http://3579e1829f.qicp.vip/api/${API_VERSION}`
    // REPORTURLS = 'http://3579e1829f.qicp.vip'
  }
  return {
    URLS, REPORTURLS, bucket, imgBuck, imgName, version, appStoreId, API_VERSION, environment, isPrivate,
  }
  // })
}
// if (__DEV__) {
//   global.XMLHttpRequest = global.originalXMLHttpRequest
//     ? global.originalXMLHttpRequest
//     : global.XMLHttpRequest
//   global.FormData = global.originalFormData
//     ? global.originalFormData
//     : global.FormData
// }
export const envParm = getEnvParm()
