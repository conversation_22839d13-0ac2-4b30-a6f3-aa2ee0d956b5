import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { Dimensions, View } from 'react-native'
import { connect } from 'react-redux'
import Icon from 'react-native-vector-icons/FontAwesome5'
import ActionButton from './action-button'
import Draggable from './action-button/Draggable'
import { getMsgCounts } from './Store'
import { GO_MESSAGEBOX, GO_WORKFLOWBOX } from '../../utils/jump'
import { getUserSettings } from '../../utils/storage'


let timer=null
const { width, height } = Dimensions.get('window')
const AgzActionBtn = (props) => {
  const [countInfo, setCountInfo] = useState({ count: 0, msgCount: 0, todoTaskCount: 0 })
  const [isShow, setIsShow] = useState(false)
  const { isLogin, user, currentAppId, userSettings } = props

  useEffect(() => {
    const getMsgShow = async (isShow) => {
      const show = await getUserSettings()
      if (show?.msgShow &&show?.msgShow!== isShow) {
        setIsShow(show.msgShow)
      }
    }
    getMsgShow(isShow)
  },[])

  useEffect(() => {
    const getCount = async () => {
      const info = await getMsgCounts(0)
      if (info && (info.msgCount !== countInfo.msgCount || info.todoTaskCount !== countInfo.todoTaskCount)) {
        setCountInfo({ ...info })
      }
    }
    if(isLogin){
      getCount()
      timer = setInterval(getCount, 60 * 1000)
    }
   

    return () => clearInterval(timer)
  },[])

  const tShow = userSettings ? userSettings.msgShow : isShow

  if (isLogin === false || !tShow) {
    return (<View />)
  }

  const appId = user ? (user.defaultAppId.trim().length === 0 ? currentAppId : user.defaultAppId) : ''

  return (
    <Draggable
      debug
      x={width * 0.70}
      y={height - 240}
    >
      <ActionButton
        count={parseInt(countInfo.count, 10)}
        buttonColor="#15A5FF"
        renderIcon={(active) => active ? (<Icon name="bell" size={30} color="white" />) : (<Icon name="bell" size={30} color="white" />)}
      >
        <ActionButton.Item
          buttonColor="#3498db"
          title="消息"
          count={countInfo.msgCount}
          onPress={() => {
            GO_MESSAGEBOX(appId)
          }}
        >
          <Icon
            style={{
              width: 40,
              textAlign: 'center',
              justifyContent: 'center',
            }}
            name="bell"
            size={25}
            color="white"
          />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor="#1abc9c"
          title="任务"
          count={countInfo.todoTaskCount}
          onPress={() => {
            GO_WORKFLOWBOX(appId)
          }}
        >
          <Icon
            style={{
              width: 40,
              textAlign: 'center',
              justifyContent: 'center',
            }}
            name="tasks"
            size={25}
            color="white"
          />
        </ActionButton.Item>
      </ActionButton>
    </Draggable>
  )
}
ActionButton.propTypes = {
  user: PropTypes.object,
  isLogin: PropTypes.bool,
  currentAppId: PropTypes.string,
  userSettings: PropTypes.object,
}

const mapStateToProps = (state) => ({
  user: state.app.user,
  isLogin: state.app.logined,
  currentAppId: state.app.currentAppId,
  userSettings: state.app.userSettings,
})

const mapDispatchToProps = () => ({
})

export default connect(mapStateToProps, mapDispatchToProps)(AgzActionBtn)
