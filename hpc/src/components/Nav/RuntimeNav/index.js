import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { View, RefreshControl, ScrollView, Text, Image, Dimensions, StyleSheet, TouchableOpacity } from 'react-native'
import SortableGrid from 'react-native-sortable-grid'
import { Toast } from '@ant-design/react-native'
import _get from 'lodash/get'
import isObject from 'lodash/isObject'
import isArray from 'lodash/isArray'

import { GO_REPORT, GO_CREATE, GO_FULLREPORTVIEW, GO_EDIT, GO_ATTENDANCECONFIG } from '../../../utils/jump'
import { getIcon } from '../../../utils/ImageIcon'
import { getNav, getShortcutMenus, getAppSettingPermissions, saveShortcutMenus, getFilterDataId, getFormFirstDataId } from '../store'
import { ShortcutMenuStyle } from './styles'
import { FormHeader } from '../../../addons/FormComponent/ModalHeader'
import { getFormData } from '../../../containers/RunTime/Form/sagas'
import { COMPONENT_FORM_ID, COMPONENT_LIST_ID, COMPONENT_REPORT_ID } from '../../../addons/constants'
import expression from '../../../utils/expression'
import SideMenu from '../../../components/SideMenu'
import Lists from '../../../addons/FormComponent/List'
import NavigationService from '../../../NavigationService'
import SvgIcon from '../../SvgIcon'

const sMenuStyle = StyleSheet.create(ShortcutMenuStyle)
const { height, width } = Dimensions.get('window')

const addIcon = require('../../../images/icons-add-small-fill-default.png')
const decreaseIcon = require('../../../images/icons-decrease-small-fill-default.png')

class RuntimeNav extends React.Component {
  state = {
    navs: [],
    menus: [],
    editState: false,
    rightText: '编辑',
    mobileOnlyListField: false,
    formId: '',
    immediatelyToList: 0,
    reportData: null,
    refreshing: false,
    configPermission: [],
  }

  componentWillMount = async () => {
    const { appId } = this.props
    const navs = await getNav(appId) || []
    const menus = await getShortcutMenus(appId) || []
    const configPermission = await getAppSettingPermissions(appId)
    this.setState({ navs, menus: this.initMenus(navs, menus), configPermission })
  }
  componentWillReceiveProps = async ({ appId }) => {
    if (appId !== this.props.appId) {
      const navs = await getNav(appId) || []
      const menus = await getShortcutMenus(appId) || []
      const configPermission = await getAppSettingPermissions(appId)
      this.setState({ navs, menus: this.initMenus(navs, menus), configPermission })
    }
  }
  onPressButton = (nav) => {
    if (this.state.editState) {
      const index = this.state.menus.findIndex(m => m.id === nav.id)
      if (index === -1) {
        if (this.state.menus.length >= 12) {
          Toast.info('最多能设置12个常用菜单')
          return
        }
        this.setState({ menus: [...this.state.menus, { ...nav }] })
      } else {
        if (this.state.menus.length <= 1) {
          Toast.info('最少需要设置1个常用菜单')
          return
        }
        this.setState({
          menus: [...this.state.menus.slice(0, index), ...this.state.menus.slice(index + 1, this.state.menus.length)],
        })
      }
    } else {
      // console.log('asdhlkasdlkasdlkadkljaskldjkals', nav)
      this.setState({ disabledButton: true })
      setTimeout(() => { this.setState({ disabledButton: false }) }, 500)
      this.goPage(nav)
    }
  }
  onRefresh = async () => {
    this.setState({ refreshing: true })
    const { appId } = this.props
    const navs = await getNav(appId) || []
    const menus = await getShortcutMenus(appId) || []
    this.setState({ refreshing: false, navs, menus: this.initMenus(navs, menus) })
  }
  initField = async (field, keys = '') => {
    const properties = field.properties
    const property = {}
    const props = keys ? _get(properties, keys) : properties

    for (let i = 0; i < Object.keys(props).length; i += 1) {
      const propkeys = Object.keys(props)[i]
      const currkeys = keys ? `${keys}.${propkeys}` : propkeys
      const currProp = _get(properties, currkeys)
      if (isObject(currProp) && !isArray(currProp)) {
        this.initField({ properties }, currkeys)
      } else if (expression.isExpression(currProp)) {
        const data = await expression.exec(currProp, { [expression.EXPCONTEXT_NAMESPACE]: { ...properties } })
        property[currkeys] = data === undefined ? '' : data
      }
    }
    return {
      ...field,
      properties: {
        ...field.properties,
        ...property,
      },
    }
  }
  // 跳转到考勤点设置界面
  goToAttendanceConfig = () => {
    GO_ATTENDANCECONFIG(this.props.appId)
  }
  goPage = async (nav) => {
    const res = await getFormData(this.props.appId, nav.refId, '0')()
    if (res.errorCode !== '0') {
      Toast.fail(res.errorMsg)
    }
    const fields = res.data.config.form.fields

    const formField = fields.find(field => field.id === COMPONENT_FORM_ID)

    // 表单 移动端显示字段配置 属性只有列表字段
    const mobileFieldsConfig = formField && formField.properties ? formField.properties.mobileFieldsConfig : []
    const mobileOnlyListField = mobileFieldsConfig.length === 1 ? fields.find(field => field.id === mobileFieldsConfig[0]) : false
    // console.log('asdkadjalkdjaajlkdjasldjlksajdlkjkj', res, fields, formField, mobileOnlyListField)

    if (mobileOnlyListField && mobileOnlyListField.componentId === COMPONENT_LIST_ID) {
      // 处理字段中的表达式
      const field = await this.initField(mobileOnlyListField, '')
      this.setState({
        mobileOnlyListField: field,
        formId: nav.refId,
        immediatelyToList: this.state.immediatelyToList + 1,
      })
    } else if (mobileOnlyListField && mobileOnlyListField.componentId === COMPONENT_REPORT_ID) {
      this.setState({
        reportData: mobileOnlyListField,
      })
      GO_FULLREPORTVIEW({
        appId: this.props.appId, label: this.state.reportData.properties.label, selectReport: this.state.reportData.properties.selectReport, reportParameter: this.state.reportData.properties.reportParameter,
      })
    } else if (nav.filter) {
      // console.log('第一关')
      const filter = await expression.exec(nav.filter, { [expression.EXPCONTEXT_NAMESPACE]: {} })
      const dataId = await getFilterDataId(this.props.appId, nav.refId, { filter })
      if (dataId) GO_EDIT(null, this.props.appId, nav.refId, 'formName', dataId)
    } else if (nav.formType === 1 || nav.formType === '1') {
      // console.log('第二关')
      GO_REPORT(null, this.props.appId, nav.refId, nav.formType, nav.name)
    } else if (nav.formType === 2 || nav.formType === '2') {
      // console.log('第三关')
      const dataId = await getFormFirstDataId(this.props.appId, nav.refId)
      if (dataId) GO_EDIT(null, this.props.appId, nav.refId, 'formName', dataId)
    } else {
      // console.log('第四关')
      GO_CREATE(null, this.props.appId, nav.refId, 0, nav.name)
    }
  }

  initMenus = (navs, menuIds) => {
    const forms = []
    navs.forEach(nav => nav.children.forEach((form) => { forms.push(form) }))
    const menus = menuIds.map(id => forms.find(form => form.id === id)).filter(m => m && m.id && m.refId)
    return menus
  }

  save = async () => {
    if (this.state.editState) {
      let navs = this.state.menus.map(menu => menu.id)
      if (this.itemOrder) {
        navs = this.itemOrder.sort((a, b) => a.order - b.order).map(n => n.key) // id
        if (this.itemOrder.length !== this.state.menus.length) {
          navs = [...navs,
          ...this.state.menus.slice(this.itemOrder.length, this.state.menus.length).map(menu => menu.id)]
        }
        navs = navs.map((id) => {
          const nav = this.state.menus.find(m => m.id === id || m.refId === id)
          return nav ? nav.id : undefined
        })
      }
      const res = await saveShortcutMenus(this.props.appId, navs)
      if (res) {
        Toast.success('保存成功！')
        const newMenus = navs.map(id => this.state.menus.find(m => m.id === id || m.refId === id))
        this.setState({ mobileOnlyListField: false, editState: false, rightText: '编辑', menus: newMenus })
      }
    } else {
      this.setState({ editState: true, rightText: '保存' })
    }
  }
  crateArray = (length) => {
    const res = []
    for (let i = 0; i < length; i += 1) {
      res.push(i)
    }
    return res
  }
  renderIcon = (node) => {
    const Icon = getIcon(node.mobileIcon)
    const code = node.mobileIcon && node.mobileIcon.split('|')
    if (code.length === 2) {
      return <SvgIcon name={code[1]} type={code[0]} />
    }
    return <Image source={Icon} style={sMenuStyle.sMenuStyle} alt="" />
  }
  renderQmenu = (menus, titleText, key) => {
    // console.log('asdkjalsdjlkjlkajslkdjlasdjljdl', menus, titleText, key)
    return (
      <View style={sMenuStyle.container} key={key}>
        <View style={sMenuStyle.qtitle} >
          <View style={sMenuStyle.tag} />
          <Text numberOfLines={2} style={sMenuStyle.qtitleText}>{titleText}</Text>
        </View>
        <View style={sMenuStyle.qMenuContainer}>
          {menus && menus.filter(m => m && m.id).map(m => (
            <TouchableOpacity disabled={this.state.disabledButton} key={m.id} onPress={() => this.onPressButton(m)}>
              <View style={sMenuStyle.qmItem}>
                {this.renderIcon(m)}
                <Text numberOfLines={2} style={sMenuStyle.qItemtext}>{m.name}</Text>
              </View>
            </TouchableOpacity>
          ))
          }
        </View>
      </View>
    )
  }
  renderAppConfigMenu = () => {
    const { configPermission } = this.state
    return (
      <View style={sMenuStyle.container} >
        <View style={sMenuStyle.qtitle} >
          <View style={sMenuStyle.tag} />
          <Text numberOfLines={2} style={sMenuStyle.qtitleText}>应用设置</Text>
        </View>

        <View style={sMenuStyle.qMenuContainer}>
          {configPermission.find(i => i.id === '14') ?
            <TouchableOpacity onPress={this.goToAttendanceConfig}>
              <View style={sMenuStyle.qmItem}>
                <SvgIcon name="ui--settings" type="agzcustomer" />
                <Text numberOfLines={2} style={sMenuStyle.qItemtext}>考勤点设置</Text>
              </View>
            </TouchableOpacity> : null}
        </View>
      </View>
    )
  }
  renderQmenuEdited = (menus, titleText, key) => {
    return (
      <View style={sMenuStyle.container} key={key} >
        <View style={sMenuStyle.qtitleEdit} >
          <View style={sMenuStyle.tag} />
          <Text style={sMenuStyle.qtitleText}>{titleText}</Text>
        </View>
        <View style={sMenuStyle.qMenuContainerEidt}>
          {menus && menus.filter(m => m && m.id).map(m => (
            <TouchableOpacity key={m.id} onPress={() => this.onPressButton(m)} >
              <View style={sMenuStyle.qmItemEdit}>
                <View style={sMenuStyle.qmItemWarp}>
                  {/* <Image source={getIcon(m.mobileIcon)} style={sMenuStyle.sMenuStyle} /> */}
                  {this.renderIcon(m)}
                  <Text numberOfLines={2} style={sMenuStyle.qItemtext}>{m.name}</Text>
                </View>
                <View style={sMenuStyle.brdgeImage}>
                  <Image source={this.state.menus.find(me => me.id === m.id) ? decreaseIcon : addIcon} />
                </View>
                {/*  */}
              </View>
            </TouchableOpacity>
          ))
          }
        </View>
      </View>
    )
  }
  renderSortabMenu = (menus, titleText) => (
    <View
      style={[sMenuStyle.container, {
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(201, 202, 202, 0.2)',
      }]}
    >
      <View style={sMenuStyle.qtitleEdit} >
        <View style={sMenuStyle.tag} />
        <Text style={sMenuStyle.qtitleText}>{titleText}</Text>
        <Text style={sMenuStyle.qtitleTipText}>(按住拖动调整顺序)</Text>
      </View>
      {menus && menus.length !== 0 &&
        <View style={[sMenuStyle.qMenuContainerSortable, { height: (((width - 15) / 4) + 5) * Math.ceil(menus.filter(m => m && m.id).length / 4) }]}>
          <SortableGrid
            itemsPerRow={4}
            onDragRelease={({ itemOrder }) => {
              this.itemOrder = itemOrder
            }}
          >
            {menus.filter(m => m && m.id).map(m => (
              <View key={m.id} style={sMenuStyle.qmItemEdit} onTap={() => { this.onPressButton(m) }}>
                <View style={sMenuStyle.qmItemWarp}>
                  {/* <Image source={getIcon(m.mobileIcon)} style={sMenuStyle.sMenuStyle} /> */}
                  {this.renderIcon(m)}
                  <Text numberOfLines={2} style={sMenuStyle.qItemtext}>{m.name}</Text>
                </View>
                <View style={sMenuStyle.brdgeImage}>
                  <Image source={this.state.menus.find(me => me.id === m.id) ? decreaseIcon : addIcon} />
                </View>
              </View>
            ))}
          </SortableGrid>
        </View >
      }
    </View>
  )

  render = () => {
    if (this.state.editState) {
      return (
        <View style={{ height, flex: 1, backgroundColor: '#F8F7F7' }} >
          <FormHeader
            centerText={this.props.appName}
            right={(<Text style={{ color: '#41454B', fontSize: 18 }}>{this.state.rightText}</Text>)}
            onPressLeft={() => NavigationService.popAndRefresh({ refresh: { refreshNav: new Date().getTime() } })}
            onPressRight={this.save}
            isShowBack={false}
            toggleMenu={this.props.toggleMenu}
          />
          {
            this.renderSortabMenu(this.state.menus, '常用表单')
          }
          <ScrollView style={{ flex: 1 }}>
            {
              this.state.navs && this.state.navs.map((dir, index) => this.state.editState ? this.renderQmenuEdited(dir.children, dir.name, index) : this.renderQmenu(dir.children, dir.name, index))
            }
          </ScrollView>
        </View>
      )
    }
    // console.log('askdjlkajdlkajdjalskdjljdllkl', this.state, this.props)
    return (
      <View style={{ height, flex: 1, backgroundColor: '#F8F7F7' }}>
        <FormHeader
          centerText={this.props.appName}
          right={(<Text style={{ color: '#41454B', fontSize: 18 }}>{this.state.rightText}</Text>)}
          onPressLeft={() => NavigationService.popAndRefresh({ refresh: { refreshNav: new Date().getTime() } })}
          onPressRight={this.save}
          isShowBack={false}
          toggleMenu={this.props.toggleMenu}
        />
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.onRefresh}
            />
          }
          style={{ borderTopWidth: 1, borderTopColor: '#f6f6f6', backgroundColor: '#F8F7F7' }}
        >
          {
            this.renderQmenu(this.state.menus, '常用表单')
          }
          {
            this.state.navs && this.state.navs.map(dir => this.state.editState ? this.renderQmenuEdited(dir.children, dir.name) : this.renderQmenu(dir.children, dir.name))
          }
          {this.renderAppConfigMenu()}
        </ScrollView>
        {
          this.state.mobileOnlyListField &&
          <Lists
            {...this.state.mobileOnlyListField.properties}
            appId={this.props.appId}
            formId={this.state.formId}
            isRunTime
            changeESVS={(isTrue) => { this.setState({ enableScrollViewScrollList: isTrue }) }}
            enableScrollViewScroll={this.state.enableScrollViewScrollList}
            immediatelyToList={this.state.immediatelyToList}
          />
        }
      </View>
    )
  }
}
const mapStateToProps = (data, { appId, appName }) => ({ appId, appName })
RuntimeNav.defaultProps = {
  appName: '',
}
RuntimeNav.propTypes = {
  appId: PropTypes.string.isRequired,
  toggleMenu: PropTypes.func.isRequired,
  appName: PropTypes.string,
}

export default connect(mapStateToProps)(SideMenu(RuntimeNav))
