import { Toast } from '@ant-design/react-native'
import { get, post } from '../../request'

export const getNav = async (appId) => {
  const res = await get(`/apps/${appId}/navs?isMobile=true`)()
  if (res.errorCode === '0') {
    return res.data.navigations
  }
  Toast.fail(res.errorMsg)
  return []
}

export const getShortcutMenus = async (appId) => {
  const res = await get(`/apps/${appId}/navs/favorite`)()
  if (res.errorCode === '0') {
    return res.data.navigations
  }
  Toast.fail(res.errorMsg)
  return []
}

export const saveShortcutMenus = async (appId, menus) => {
  const res = await post(`/apps/${appId}/navs/favorite`)({ data: menus })
  if (res.errorCode === '0') {
    return res.data
  }
  Toast.fail(res.errorMsg)
  return undefined
}

export const getFilterDataId = async (appId, formId, data) => {
  const res = await get(`/apps/${appId}/forms/${formId}/datas/firstId`)({ data })
  if (res.errorCode !== '0') {
    Toast.fail(res.errorMsg)
    return undefined
  }
  return res.data.id
}

export const getFormFirstDataId = async (appId, formId) => {
  const res = await get(`/apps/${appId}/forms/${formId}/datas/findFormFirstDataId`)()
  if (res.errorCode !== '0') {
    Toast.fail(res.errorMsg)
    return undefined
  }
  return res.data.dataId
}

export const getAppSettingPermissions = async (appId) => {
  const res = await get(`/apps/${appId}/forms/${60005}/permissions/operations`)()
  if (res.errorCode !== '0') {
    Toast.fail(res.errorMsg)
    return []
  }
  return res.data.operations
}
