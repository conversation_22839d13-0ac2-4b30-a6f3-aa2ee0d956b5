import React from 'react'
import { View, Text, TouchableHighlight, StyleSheet, Dimensions, Image, TouchableOpacity, Keyboard, StatusBar, Platform } from 'react-native'
import PropTypes from 'prop-types'
import { createForm } from 'rc-form'
import { InputItem, <PERSON><PERSON>, Modal } from '@ant-design/react-native'
import DropdownAlert from 'react-native-dropdownalert'
import InputItemStyle from '@ant-design/react-native/lib/input-item/style/index'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import {
  RULE_NULL_MOBILE,
  RULE_INVALID_MOBILE,
  RULE_NULL_CAPTCHA,
  RULE_INVALID_CAPTCHA,
  RULE_NULL_PASSWORD,
  RULE_INVALID_PASSWORD,
} from '../../utils/validation/rules'
import { getCaptchaImage, signUp, changePassword } from './store'
import { GO_AIGONGZUOWEB, GO_COUNTRYCODE } from '../../utils/jump'
import NavigationService from '../../NavigationService'

// 获取短信验证

const bgColor = ['#DFEBE8', '#DAD9D0', '#FEFFF5', '#EFF9F9', '#EFF9F9', '#D7E9F4', '#EDCFC4', '#D3EFEC', '#534E74', '#F7F0C5', '#FCF0E0', '#B5B3D1']
const bgIamge = [
  require('../../images/Login/bg/app-login-background-01.png'),
  require('../../images/Login/bg/app-login-background-02.png'),
  require('../../images/Login/bg/app-login-background-03.png'),
  require('../../images/Login/bg/app-login-background-04.png'),
  require('../../images/Login/bg/app-login-background-05.png'),
  require('../../images/Login/bg/app-login-background-06.png'),
  require('../../images/Login/bg/app-login-background-07.png'),
  require('../../images/Login/bg/app-login-background-08.png'),
  require('../../images/Login/bg/app-login-background-09.png'),
  require('../../images/Login/bg/app-login-background-10.png'),
  require('../../images/Login/bg/app-login-background-11.png'),
  require('../../images/Login/bg/app-login-background-12.png')
]

const DEFAULT_COUNT = 60
const { height, width } = Dimensions.get('window')
const statusBarHeight = StatusBar.currentHeight; // 安卓获取状态栏高度
// const { StatusBarManager } = NativeModules; // ios获取状态栏高度

/**
 * 判断IphoneX， 用于 UI适配
 * 直接一次生成结果，后续不再可执行
 */
const isIphoneX = (function () {
  // iPhoneX SIZE
  const X_WIDTH = 375
  const X_HEIGHT = 812

  // DEVICE SIZE
  const { width, height } = Dimensions.get('window')
  const sizes = [X_WIDTH, X_HEIGHT]

  return Platform.OS === 'ios'
    ? sizes.includes(width) && sizes.includes(height)
    : false

})()

class Signup extends React.Component {
  state = {
    count: DEFAULT_COUNT,
    captcha: '',
    randomId: '',
    countryCode: '86',
    countryName: '中国',
    countDownReg: 0,
  }

  componentDidMount = () => {
    console.log('asdasdasdasdadasda', this.props)
    NavigationService.refresh({ title: this.props.isFindPassword ? '忘记密码' : ' ' })
    // navigationOptions.refresh({ title: this.props.isFindPassword ? '忘记密码' : '立即注册' })
    // NavigationService.refresh({ title: this.props.isFindPassword ? '忘记密码' : '立即注册' })
  }

  componentWillMount = () => {
    NavigationService.refresh({ title: this.props.isFindPassword ? '忘记密码' : '立即注册' })

    this.changeCaptchaImage()
  }

  // 样式数组处理方法
  convertArray = (item) => {
    return item instanceof Array ? item : [item]
  }

  // 样式合并
  styleMerge = (src, ...styles) => {
    src = this.convertArray(src)
    return src.concat(...styles)
  }

  // 登录页的背景样式
  backgroundIamge = () => {
    let nowTime = new Date()
    const month = nowTime.getMonth() + 1
    return month - 1
  }

  // 获取状态栏高度
  getCurrentHeight() {
    return StatusBar.currentHeight || (isIphoneX ? 44 : 20)
  }

  refreshCode = (code, name) => {
    this.setState({
      countryName: name,
      countryCode: code,
    })
  }

  changeCaptchaImage = async () => {
    const randomId = Math.random().toString().slice(2)
    const captcha = await getCaptchaImage(randomId)
    if (captcha && randomId) {
      this.setState({ captcha: `data:image/png;base64,${captcha}`, randomId })
    }
  }

  // handleSubmit = () => {
  //   Keyboard.dismiss()
  //   const { form: { validateFields }, register } = this.props
  //   validateFields((err, value) => {
  //     if (!err) {
  //       // register(value)
  //       // Actions.pop()
  //     } else {
  //       let message = ''
  //       if (err.mobile && err.mobile.errors.length > 0) {
  //         message += err.mobile.errors[0].message
  //         message += '\n'
  //       }
  //       if (err.password && err.password.errors.length > 0) {
  //         message += err.password.errors[0].message
  //         message += '\n'
  //       }
  //       if (err.checkCode && err.checkCode.errors.length > 0) {
  //         message += err.checkCode.errors[0].message
  //       }
  //       // this.dropdown.alertWithType('error', 'Error', message)
  //       Modal.alert('', message, [{ text: '确定' }])
  //     }
  //   })
  // }

  // 短信验证码倒计时
  captchaOpenTimeFun = () => {
    const { countDownReg } = this.state
    if (countDownReg <= 0) {
      return
    }
    this.captchaOpenTime = setTimeout(() => {
      var a = countDownReg - 1
      this.setState({
        countDownReg: a
      }, () => {
        this.captchaOpenTimeFun()
        // clearTimeout(this.captchaOpenTime)
      })
    }, 1000)
  }

  // 点击注册验证成功跳转并获取验证码
  handleGetCaptcha = (shouldStartCountting) => {
    const randomId = this.state.randomId
    const { form: { validateFields }, getCaptcha, register } = this.props
    validateFields(['mobile', 'captcha'], (err, value) => {
      if (!err) {
        // 开始倒计时
        // shouldStartCountting(true)
        this.setState({
          countDownReg: 60,
        }, () => {
          this.captchaOpenTimeFun()
        })
        getCaptcha(value.mobile, this.props.isFindPassword, randomId, value.captcha, this.state.countryCode)
      } else {
        let message = ''
        if (err.mobile && err.mobile.errors.length > 0) {
          message += err.mobile.errors[0].message
          message += '\n'
        }
        if (err.captcha && err.captcha.errors.length > 0) {
          message += err.captcha.errors[0].message
        }
        Modal.alert('', message, [{ text: '确定' }])
      }
    })
  }


  // 提交数据注册或者修改密码
  handleSubmit = () => {
    Keyboard.dismiss()
    const { form: { validateFields }, register } = this.props

    validateFields(async (err, value) => {
      console.log('asdasdasdad', value, err, this.props.isFindPassword)
      if (!err) {
        // register(value)
        // console.log(value)
        if (value.password === value.password1) {
          if (this.props.isFindPassword) {
            await this.changePwd(value)
          } else {
            await this.registerT(value)
          }
        } else {
          Modal.alert('错误警告', '两次密码输入不一致', [{ text: '确定' }])
        }
      } else {
        let message = ''
        if (err.password && err.password.errors.length > 0) {
          message += err.password.errors[0].message
          message += '\n'
        }
        if (err.checkCode && err.checkCode.errors.length > 0) {
          message += err.checkCode.errors[0].message
          message += '\n'
        }
        if (err.captcha && err.captcha.errors.length > 0) {
          message += err.captcha.errors[0].message
          message += '\n'
        }
        if (err.mobile && err.mobile.errors.length > 0) {
          message += err.mobile.errors[0].message
        }

        Modal.alert('', message, [{ text: '确定' }])
      }
    })
  }

  changePwd = async (value) => {
    const res = await changePassword(value.mobile, value.password, value.checkCode, value.countryCode, this.state.randomId)
    if (res === '密码重置成功') {
      Modal.alert('', '密码重置成功', [{ text: '去登录', onPress: NavigationService.goBackToLogin }])
    } else {
      Modal.alert('', res.errorMsg, [{ text: '确定' }])
    }
  }

  registerT = async (value) => {
    console.log('asdasdasdadsasdasda', value, res)
    const res = await signUp(value.mobile, value.password, value.checkCode, this.props.countryCode)
    if (res == '注册成功') {
      Modal.alert('', '注册成功', [{ text: '去登录', onPress: NavigationService.goBackToLogin }])
    } else {
      Modal.alert('', res.errorMsg, [{ text: '确定' }])
    } 4275
  }

  chooseCountryCode = () => {
    GO_COUNTRYCODE({ chooseCode: this.refreshCode })
  }

  toNextEditing = () => {
    this.pwdInput.focus()
  }

  render() {
    const { form: { getFieldDecorator } } = this.props
    return (
      <KeyboardAwareScrollView
        style={{ flex: 1, backgroundColor: '#ffffff', backgroundColor: bgColor[this.backgroundIamge()] }}
      >
        <View style={{ height: height - this.getCurrentHeight() - 56 }}>
          <Image style={{ position: 'absolute', top: height - 260, left: 0, width: '100%' }} source={bgIamge[this.backgroundIamge()]} />
          {
            this.props.isFindPassword ?
              null
              :
              <View style={{ flexDirection: 'row', marginLeft: 20, marginTop: 21 }}>
                <Text style={{ fontSize: 12, fontWeight: '400', color: "rgb(168, 169, 173)" }}>点击注册代表您阅读并同意</Text>
                <TouchableOpacity onPress={() => {
                  NavigationService.navigate('officialWeb')
                }} >
                  <Text style={{
                    fontSize: 12, fontWeight: '400', color: "rgb(0, 156, 216)"
                  }} >
                    《服务协议》
              </Text>
                </TouchableOpacity>
              </View>
          }

          <View style={{ paddingLeft: 0, paddingRight: 0, marginTop: 30 }}>
            {getFieldDecorator('mobile', {
              rules: [RULE_NULL_MOBILE, RULE_INVALID_MOBILE],
              getValueFromEvent: e => e.replace(/\s+/g, ''),
            })(
              <InputItem
                type="phone"
                autoComplete="off"
                placeholder="请输入手机号"
                style={styles.textInputStyle}
                styles={{ container: { marginRight: 20, marginLeft: 20, borderBottomColor: '#fff', paddingRight: 0 } }}
                returnKeyType="next"
                labelNumber={2}
                onSubmitEditing={this.toNextEditing}
              >
                <TouchableOpacity style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 12 }} onPress={this.chooseCountryCode}>
                  <Text style={{ fontSize: 14, color: '#000' }}>{`+${this.state.countryCode}`}</Text>
                </TouchableOpacity>
              </InputItem>,
            )}
          </View>
          <View style={{ marginLeft: 5, marginRight: 5, marginTop: 28 }}>
            {getFieldDecorator('password', {
              rules: [RULE_NULL_PASSWORD, RULE_INVALID_PASSWORD],
            })(
              <InputItem
                type="password"
                autoComplete="off"
                style={styles.textInputStyle}
                placeholder={this.props.isFindPassword ? "请输入新密码" : "请输入密码"}
                labelNumber={2}
              >
                <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 8 }}>
                  <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-password.png')} />
                </View>
              </InputItem>,
            )}
          </View>
          <View style={{ marginLeft: 5, marginRight: 5, marginTop: 28 }}>
            {getFieldDecorator('password1', {
              rules: [RULE_NULL_PASSWORD, RULE_INVALID_PASSWORD],
            })(
              <InputItem
                type="password"
                autoComplete="off"
                style={styles.textInputStyle}
                placeholder={this.props.isFindPassword ? "请再次输入新密码" : "请再次输入密码"}
                labelNumber={2}
              >
                <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 8 }}>
                  <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-password.png')} />
                </View>
              </InputItem>,
            )}
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', height: 58, marginTop: 28, marginLeft: 6, marginRight: 6 }}>
            <View style={{ flexDirection: 'column', width: width - 88 - 27 }}>
              {getFieldDecorator('captcha', {
                rules: [RULE_INVALID_CAPTCHA, RULE_NULL_CAPTCHA],
              })(
                <InputItem
                  ref={input => this.pwdInput = input}
                  style={styles.textInputStyle}
                  placeholder="请输入验证码"
                  styles={{ container: { borderBottomWidth: 0, borderBottomColor: '#fff', paddingRight: 0, backgroundColor: '#fff' } }}
                  autoComplete="off"
                  labelNumber={2}
                  underlineColorAndroid={'transparent'}

                >
                  <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 10 }}>
                    <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-turing_captcha.png')} />
                  </View>
                </InputItem>,
              )}
            </View>
            <TouchableHighlight style={{ backgroundColor: '#fff', height: 44, marginRight: 15 }} underlayColor="transparent" onPress={this.changeCaptchaImage}>
              <Image
                style={{ width: 88, height: 44, backgroundColor: 'white', borderRadius: 5 }}
                source={this.state.captcha ? { uri: this.state.captcha } : require('../../images/verification-code-invalidation-default.png')}
                resizeMode="contain"
              />
            </TouchableHighlight>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', height: 58, marginTop: 10, marginLeft: 6, marginRight: 6 }}>
            <View style={{ flexDirection: 'column', width: width - 27 - 88 }}>
              {getFieldDecorator('checkCode', {
                rules: [RULE_NULL_CAPTCHA],
              })(
                <InputItem
                  ref={input => this.pwdInput = input}
                  style={styles.textInputStyle}
                  placeholder="请输入短信验证码"
                  styles={{ container: { borderBottomWidth: 0, borderBottomColor: '#fff', paddingRight: 0, backgroundColor: '#fff' } }}
                  autoComplete="off"
                  labelNumber={2}
                  underlineColorAndroid={'transparent'}
                >
                  <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 10 }}>
                    <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-sms_captcha.png')} />
                  </View>
                </InputItem>,
              )}
            </View>
            {
              this.state.countDownReg > 0 ?
                <View style={{ backgroundColor: '#fff', height: 44, marginRight: 15, width: 88, alignItems: 'center', justifyContent: 'center' }} underlayColor="transparent" >
                  <Text style={{ color: 'rgb(0, 156, 216)', fontSize: 14, marginLeft: 20 }}>{this.state.countDownReg}秒</Text>
                </View>
                :
                <TouchableHighlight style={{ backgroundColor: '#fff', height: 44, marginRight: 15, width: 88, alignItems: 'center', justifyContent: 'center' }} underlayColor="transparent" onPress={this.handleGetCaptcha}>
                  <Text style={{ color: 'rgb(0, 156, 216)', fontSize: 14, marginLeft: 20 }}>发送</Text>
                </TouchableHighlight>
            }
          </View>
          {/* <View style={{ marginLeft: 5, marginRight: 5, marginTop: 28 }}>
            {getFieldDecorator('checkCode', {
              rules: [RULE_NULL_CAPTCHA],
            })(
              <InputItem
                type="number"
                autoComplete="off"
                placeholder="请输入短信验证码"
                style={styles.textInputStyle}
                labelNumber={2}
              />,
            )}
          </View> */}
          <Button
            style={{ marginTop: 20, marginLeft: 20, marginRight: 20, height: 44, backgroundColor: '#4CBDFF', borderWidth: 0 }}
            onPress={this.handleSubmit}
            type="primary"
          >
            {this.props.isFindPassword ? '确认' : '注册'}
          </Button>
          <View style={{ marginTop: 40, flexDirection: 'row', height: 30, alignItems: 'center', alignSelf: 'center', opacity: this.props.isFindPassword ? 0 : 1 }}>
            {/**
            <Text style={{ fontSize: 12, color: '#9B9B9B' }}>点击注册表示您已阅读并同意</Text><Text style={{ fontSize: 12, color: '#4CBDFF' }} onPress={() => GO_AIGONGZUOWEB()}>《华普云服务条款》</Text>
          */}
          </View>
        </View>
        {/* <DropdownAlert closeInterval={4000} ref={ref => this.dropdown = ref} /> */}
      </KeyboardAwareScrollView >
    )
  }
}

Signup.propTypes = {
  form: PropTypes.shape({
    getFieldDecorator: PropTypes.func.isRequired,
    validateFields: PropTypes.func.isRequired,
  }).isRequired,
  register: PropTypes.func.isRequired,
  getCaptcha: PropTypes.func.isRequired,
}

const styles = StyleSheet.create({
  captchaInputStyle: {
    width: width * 0.6,
  },
  textInputStyle: {
    flex: 1,
    width: '100%',
    color: '#9B9B9B',
    height: 44,
    fontSize: 14,
    backgroundColor: 'white',
    borderBottomWidth: 0,
    borderBottomColor: '#E5E5E5',
    paddingHorizontal: 20,
  },
})

export default createForm()(Signup)
