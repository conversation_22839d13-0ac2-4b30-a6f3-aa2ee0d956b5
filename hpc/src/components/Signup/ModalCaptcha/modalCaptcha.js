import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  Modal,
} from 'react-native'
import { Button } from '@ant-design/react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { screenWidth, screenHeight, deviceType } from '../../../config/sysPara'
import { getCaptchaImage, getModalSms } from '../store'

class ModalCaptcha extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      visible: this.props.visible,
      mobile: this.props.mobile,
      captcha: '',
      codeStr: '',
      errOpactiy: 0,
      randomId: this.props.randomId,
      shouldStartCountting: this.props.shouldStartCountting,
    }
  }

  getMessage = async () => {
    const randomId = this.state.randomId
    const sms = await getModalSms(this.props.mobile, this.props.isFindPassword, randomId, this.state.codeStr, this.props.notionCode)
    if (sms === '短信发送成功') {
      // 倒计时
      this.state.shouldStartCountting(true)
      this.close()
    } else {
      // modal 红字提示
      this.setState({ errOpactiy: 1 })
    }
  }

  changeCaptchaImage = async () => {
    const randomId = Math.random().toString().slice(2)
    const captcha = await getCaptchaImage(randomId)
    if (captcha && randomId) {
      this.setState({ captcha: `data:image/png;base64,${captcha}`, randomId })
    }
  }

  close = () => {
    this.setState({
      visible: false,
    })
  }

  handleSubmit = () => {
    // 提交验证码获取短信
    if (this.state.codeStr.length !== 4) {
      this.setState({ errOpactiy: 1 })
    } else {
      this.setState({ errOpactiy: 0 })
      this.getMessage()
    }
  }

  render() {
    const { onAnimationEnd, captchaImg } = this.props
    return (
      <KeyboardAwareScrollView>
        <Modal
          isVisible={this.state.visible}
          backdropColor="black"
          backdropOpacity={0.5}
          onBackButtonPress={() => {}}
          onModalHide={onAnimationEnd}
          style={{ justifyContent: 'center', alignItems: 'center' }}
        >
          <View style={{ backgroundColor: 'white', height: screenHeight * (deviceType === 2 ? 0.28 : 0.3), width: screenWidth - 80, borderRadius: 3 }}>
            <Text style={{ marginTop: screenHeight * 0.04, marginLeft: 15, fontSize: 14, color: '#41454B' }}>{'需要验证'}</Text>
            <View style={{ flexDirection: 'row', justifyContent: 'center', borderWidth: 1, borderColor: '#D7D7D7', borderRadius: 3, marginTop: screenHeight * 0.02, marginLeft: 15, width: screenWidth * 0.7 }}>
              <TextInput
                style={{ width: screenWidth * 0.46, height: 0.06 * screenHeight, marginLeft: 10 }}
                placeholder={'请输入'}
                underlineColorAndroid="transparent"
                onChangeText={codeStr => this.setState({ codeStr })}
                value={this.state.codeStr}
              />
              <TouchableOpacity
                onPress={this.changeCaptchaImage}
                style={{ width: 80, height: 0.06 * screenHeight, justifyContent: 'center', alignItems: 'center' }}
              >
                <Image
                  source={{ uri: this.state.captcha ? this.state.captcha : captchaImg }}
                  style={{ width: deviceType === 2 ? 75 : 70, height: deviceType === 2 ? 38 : 32 }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <Text style={{ fontSize: 12, color: '#FF6F74', marginLeft: 15, marginTop: screenHeight * 0.02, opacity: this.state.errOpactiy }}>{'错误信息'}</Text>
            <View style={{ flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', marginTop: screenHeight * 0.03, height: screenHeight * (deviceType == 2 ? 0.07 : 0.07), borderRadius: 3, backgroundColor: '#F8F7F7' }}>
              <Button
                onPress={() => { this.close() }}
                activeStyle={{ backgroundColor: 'transparent' }}
                style={{ borderWidth: 0, backgroundColor: 'transparent', width: (screenWidth - 80) / 2 }}
              ><Text style={{ fontSize: 12, fontWeight: 'bold', color: '#888888', textAlign: 'center' }}>取消</Text></Button>
              <Button
                onPress={this.handleSubmit}
                activeStyle={{ backgroundColor: 'transparent' }}
                style={{ borderWidth: 0, backgroundColor: 'transparent', width: (screenWidth - 80) / 2 }}
              >
                <Text style={{ fontSize: 12, fontWeight: 'bold', color: '#17A9FF', textAlign: 'center' }}>确定</Text>
              </Button>
            </View>
          </View>
        </Modal>
      </KeyboardAwareScrollView>)
  }
}

export default ModalCaptcha

ModalCaptcha.propTypes = {
  visible: PropTypes.bool,
  isFindPassword: PropTypes.bool,
  mobile: PropTypes.string,
  captchaImg: PropTypes.string,
  randomId: PropTypes.string,
  onAnimationEnd: PropTypes.func,
  shouldStartCountting: PropTypes.func,
}
