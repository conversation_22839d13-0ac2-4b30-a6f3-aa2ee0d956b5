import PropTypes from 'prop-types'
import React from 'react'
import { View } from 'react-native'
import { connect } from 'react-redux'
import DropdownAlert from 'react-native-dropdownalert'
import Fields from '../../components/FormDesign/Fields/Fields'
import * as formConstants from './constants'
import { CLEAR } from '../FormDesign/constants'
import { GO_FORM_DESIGN } from '../../utils/jump'
import dismissKeyboard from 'dismissKeyboard'
import NavigationService from '../../NavigationService'

/*
和 RUNTIME/Form 完全一样，是为了解决从列表组件页进入详情页而导致的数据错乱而增加
该问题尚不清楚是react-native-router-flux导致还是react-native导致，主要怀疑前者。
*/
class ReportForm extends React.Component {
  handlerBack = () => {
    this.props.doClear()
    this.props.doDesignClear()
    formForceUpd = !formForceUpd
    NavigationService.back()
  }

  androidBack = (() => {
    this.handlerBack()
    return true
  }) // don't forget bind this, you will remember anyway.

  componentWillMount() {
    // console.log('ReportForm',this.props.appId,this.props.formId, this.props.dataId, this.props.operator);
    this.props.initFormData(this.props.appId, this.props.formId, this.props.dataId, this.props.operator)
    if ((this.props.formType === 1 || this.props.formType === '1') || this.props.isView) { // 报表类型
      NavigationService.refresh({ title: this.props.formName, backTitle: '返回', onBack: () => { this.handlerBack() }, renderRightButton: () => {} })
    } else {
      NavigationService.refresh({ title: this.props.formName,
        onRight: () => { dismissKeyboard(); this.props.submit(-1) },
        onBack: () => { this.handlerBack() },
      })
    }
  }

  componentWillReceiveProps = ({ appId, dataId, formId, operator, fields }) => {
    if (this.props.appId !== appId || this.props.dataId !== dataId || this.props.formId !== formId ||
      this.props.operator !== operator) {
      this.props.initFormData(appId, formId, dataId, operator)
    }
  }

  render() {
    // console.log('ReportForm',this.props.fields);
    return (
      <View style={{ flex: 1 }}>
        <Fields
          items={this.props.fields}
          isRunTime
          updateValue={this.props.updateValue}
          updateEveryValue={this.props.updateEveryValue}
          formValidate={this.props.formValidate}
          useDragHandle
          formId={this.props.formId}
          dataId={this.props.operator === '9' ? undefined : this.props.dataId}
          appId={this.props.appId}
          formDataInited={this.props.submittable}
          updateExtraProps={this.props.updateExtraProps}
          updateValidation={this.props.updateValidation}
          updateEditedFlag={this.props.updateEditedFlag}
          currEditFieldId={this.props.currEditFieldId}
          viewData={this.props.viewData}
          formStatus={this.props.formStatus}
          edited={this.props.edited}
          isView={this.props.isView}
        />
        <DropdownAlert closeInterval={1100} ref={ref => this.dropdown = ref} />
      </View>
    )
  }
}

const mapStateToProps = ({
  reportFormDataState: { fields, data, validates, submittable, edited, currEditFieldId, queryCondtAndData },
}, ownProps) => ({
  fields,
  appId: ownProps.appId,
  formId: ownProps.formId,
  dataId: ownProps.dataId,
  operator: ownProps.operator,
  formValidate: validates,
  submittable,
  edited,
  currEditFieldId,
  queryCondtAndData,
  formStatus: data.form_status,
  data,
})

const mapDispatchToProps = (dispatch, ownProps) => ({
  initFormData: (appId, formId, dataId, operator) => {
    dispatch({
      type: formConstants.REPORT_CLEAR,
    })
    dispatch({
      type: formConstants.REPORT_FORM_INIT,
      payload: { ...ownProps, appId, formId, dataId, operator },
    })
  },
  updateValue: (value, index) => dispatch({
    type: formConstants.REPORT_FORM_UPDATE_VALUE,
    payload: { value, index },
  }),
  updateEveryValue: (value, index) => dispatch({
    type: formConstants.REPORT_FORM_UPDATE_EVERY_VALUE,
    payload: { value, index },
  }),
  updateExtraProps: ({ fieldId, props }) => dispatch({
    type: formConstants.REPORT_UPDATE_EXTRA_PROPS,
    payload: { fieldId, props },
  }),
  submit: fieldId => dispatch({
    type: formConstants.REPORT_FORM_SUBMIT,
    payload: {
      appId: ownProps.appId,
      formId: ownProps.formId,
      dataId: ownProps.dataId,
      callback: ownProps.callback,
      fieldId,
      editPage: typeof (ownProps.dataId) !== 'undefined',
    },
  }),
  goDesign: () => dispatch(GO_FORM_DESIGN(ownProps.appId, ownProps.formId, ownProps.formName)),
  goEidt: () => dispatch(GO_EDIT(params.appId, params.formId, params.dataId)),
  updateValidation: validation => dispatch({ type: formConstants.REPORT_UPDATE_VALIDATION, payload: validation }),
  updateEditedFlag: fieldId => dispatch({ type: formConstants.REPORT_FORM_UPDATE_FLAG, payload: fieldId }),
  doClear: () => dispatch({ type: formConstants.REPORT_CLEAR }),
  doDesignClear: () => dispatch({ type: CLEAR }),
})

ReportForm.propTypes = {
  fields: PropTypes.arrayOf(PropTypes.object),
  initFormData: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired,
  updateValue: PropTypes.func.isRequired,
  formValidate: PropTypes.object.isRequired,// eslint-disable-line
  formId: PropTypes.string.isRequired,
  appId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  updateValidation: PropTypes.func,
  submittable: PropTypes.bool,
}

export default connect(mapStateToProps, mapDispatchToProps)(ReportForm)
