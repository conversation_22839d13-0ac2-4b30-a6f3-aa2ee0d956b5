import React from 'react'
import {
  View,
  Text,
  TouchableHighlight,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ListView,
} from 'react-native'
import { SearchBar } from '@ant-design/react-native'
import codeGroup from './CodeGroup'
import { deviceType } from '../../config/sysPara.js'
import NavigationService from '../../NavigationService'

let { height, width } = Dimensions.get('window')
let letters = []
let totalHeight = []// 每个字母对应的城市
const SectionHeight = 20
const rowHeight = 60

class CountryCode extends React.PureComponent {
  constructor(props) {
    super(props)

    const ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
    const getSectionData = (dataBlob, sectionID) => dataBlob[sectionID]

    const getRowData = (dataBlob, sectionID, rowID) => dataBlob[`${sectionID}:${rowID}`]

    this.state = {
      name: '',
      code: '',
      filter: '',
      oldDataSource: [], // 存储原有列表数据用于搜索后还原列表
      defaultValue: [], // 被选中的数据
      dataSource: new ListView.DataSource({
        getSectionData, // 获取组中数据
        getRowData, // 获取行中的数据
        rowHasChanged: (r1, r2) => r1 !== r2,
        sectionHeaderHasChanged: (s1, s2) => s1 !== s2,
      }),
    }
  }

  componentWillMount() {
    letters = []
    totalHeight = []
    this.formatAreaCode(codeGroup)
  }

  formatAreaCode(codeGroup) {
    let dataBlob = {},
      sectionIDs = [],
      rowIDs = []
    contact = []
    for (let i = 0; i < codeGroup.length; i++) {
            // 组号放入
      sectionIDs.push(i)
            // 组头
      dataBlob[i] = codeGroup[i].secId
            // 组中所有元素
      contact = codeGroup[i].secData
      rowIDs[i] = []
            // 所有字母集合
      letters.push(codeGroup[i].secId)
            // 遍历联系人数组
      for (let j = 0; j < contact.length; j++) {
              // 行号放入rowIds
        rowIDs[i].push(j)
              //
        dataBlob[`${i}:${j}`] = contact[j]
      }
            // 计算每个字母和下面section的总高度
      const eachHeight = SectionHeight + rowHeight * codeGroup[i].secData.length
      totalHeight.push(eachHeight)
    }

    this.setState({
      dataSource: this.state.dataSource.cloneWithRowsAndSections(dataBlob, sectionIDs, rowIDs),
      oldDataSource: this.state.dataSource.cloneWithRowsAndSections(dataBlob, sectionIDs, rowIDs),
    })
  }

  header = {
    leftView: <Text onPress={this.props.closeModal} style={{ fontSize: 18, color: '#41454b' }}>取消</Text>,
    centerText: '国家/地区',
  }

  render() {
    if (this.state.filter === '') {
      return (
        <View style={{ flex: 1, backgroundColor: '#fff' }}>
          {/**
        <ModalHeader  header={this.header}  style={{ backgroundColor: '#F8F7F7', marginBottom: 1 }} />
        */}
          <SearchBar
            placeholder="搜索国家/地区"
            onChange={this.onSearch}
            onSubmit={this.onChange}
            onClear={this.clear}
            onCancel={this.clear}
          />
          <ListView
            style={{ backgroundColor: '#fff' }}
            removeClippedSubviews={false}
            automaticallyAdjustContentInsets={false}
            enableEmptySections
            dataSource={this.state.dataSource}
            renderRow={this.renderRow}
            renderSectionHeader={this.renderSectionHeader}
            contentContainerStyle={styles.listViewStyle}
            ref={listView => this._listView = listView}
          />
          <View style={styles.letters}>
            {letters.map((letter, index) => this.renderLetters(letter, index))}
          </View>

        </View>)
    } else {
      return (
        <View style={{ flex: 1, backgroundColor: '#fff' }}>
          <SearchBar
            placeholder="搜索国家/地区"
            onChange={this.onSearch}
            onClear={this.clear}
            onSubmit={this.onChange}
            onCancel={this.clear}
          />
          <ListView
            renderSeparator={this.renderSeparator}
            removeClippedSubviews={false}
            enableEmptySections
            dataSource={this.state.dataSource}
            renderRow={this.renderRow}
            contentContainerStyle={styles.listViewStyle}
          />
        </View>
      )
    }
  }

  // 搜索
  onSearch = (value) => {
    if (value.length > 0) {
      const searchArray = []
      this.state.filter = value
      for (let i = 0; i < codeGroup.length; i++) {
        let shortArr = new Array()
        shortArr = codeGroup[i].secData
        if (shortArr.length > 1) {
          shortArr.map((vvCon) => {
            const str = vvCon.countryName
            if (str.indexOf(this.state.filter) != -1) {
              // 包含
              searchArray.push(vvCon)
            }
          })
        } else {
          const str = shortArr[0].countryName
          if (str.indexOf(this.state.filter) != -1) {
            searchArray.push(shortArr[0])
          }
        }
      }
      var ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
      this.setState({
        dataSource: ds.cloneWithRows(searchArray),
      })
    } else {
      var ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2, sectionHeaderHasChanged: (s1, s2) => s1 !== s2 })
      this.setState({
        dataSource: this.state.oldDataSource,
      })
    }
  };

  // 清除事件
  clear = (value) => {
    this.state.filter = ''
    value = ''
    const ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 })
    this.setState({
      dataSource: this.state.oldDataSource,
    })
  };

  // 每行数据
  renderRow=(rowData, rowID, sectionID) => (

    <TouchableHighlight
      key={rowID} underlayColor={'#ddd'} onPress={() => {
          // this.changedata(rowData)
          // this.props.refresh = {(rowData.code,rowData.name)=>{ this.setState({name:rowData.name,code:rowData.code})}}
        this.props.chooseCode(rowData.countryCode, rowData.countryName)
          // this.props.closeModal()
        NavigationService.back()
      }}
    >
      <View style={styles.cellViewStyle}>
        {/**
      <Image style={styles.ImageStyle} source={{uri:'https://pic3.zhimg.com/ff35a298a36376b0026b932e16d37b82_im.png'}}/>
      */}
        <View style={styles.rightTextStyle}>
          <Text style={{ fontSize: 14, color: '#1B1B1B' }}>{rowData.countryName}</Text>
          <Text style={{ fontSize: 14, color: '#A0A2A5', marginTop: 4 }}>{rowData.countryCode}</Text>
        </View>
      </View>
    </TouchableHighlight>
    )

  // 每组数据
  renderSectionHeader(sectionData, sectionID) {
    return (
      <View style={styles.headerView}>
        <Text style={styles.headerText}>{sectionData}</Text>
      </View>)
  }

  // render right index letters
  renderLetters(letter, index) {
    return (
      <TouchableOpacity key={index} activeOpacity={0.6} onPress={() => { this.scrollTo(index) }}>
        <View style={styles.letter}>
          <Text style={styles.letterText}>{letter}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  renderSeparator=(sectionID, rowID, adjacentRowHighlighted) => {
    <View
      key={`${sectionID}-${rowID}`}
      style={{
        marginLeft: '20',
        width,
        marginRight: 20,
        borderBottomWidth: deviceType === 2 ? 0.5 : 0.6,
        borderBottomColor: '#ddd',
      }}
    />
  }
  changedata=(contactName) => {
    // this.props.r
  }

  // touch right indexletters,
  scrollTo=(index) => {
    let position = 0
    for (let i = 0; i < index; i++) {
      position += totalHeight[i]
    }
    this._listView && this._listView.scrollTo({
      y: position, animated: false,
    })
  }

}

export default CountryCode

const styles = StyleSheet.create({
  cellViewStyle: {
    height: 60,
    flexDirection: 'row',
    paddingLeft: 20,
    borderBottomWidth: 0.5,
    backgroundColor: '#fff',
    borderBottomColor: '#ddd',
    alignItems: 'center',
    paddingRight: 20,
  },
  ImageStyle: {
    margin: 5,
    height: 44,
    width: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightTextStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
    marginLeft: 10,
    height: 44,
    margin: 5,
  },
  rowTxtStyle: {
    height: 25,
    margin: 10,
  },
  listViewStyle: {
    marginTop: 0,
  },
  headerView: {
    height: 20,
    borderBottomColor: '#ddd',
    borderBottomWidth: 0.5,
    backgroundColor: '#f5f5f9',
  },
  headerText: {
    marginLeft: 20,
    fontWeight: '600',
  },
  bottomView: {
    height: 50,
    backgroundColor: '#fff',
    // borderTopWidth:0.5,
    borderTopColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomButton: {
    width: 90,
    marginRight: 15,
    height: 32,

  },
  bottomText: {
    width: 100,
    marginLeft: 15,
    fontSize: 15,
    color: '#2192ee',
  },
  letterText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#17A9FF',
  },
  letters: {
    position: 'absolute',
    height: deviceType === 2 ? height - 50 - 64 - 44 : height - 50 - 44 - 54,
    top: 44,
    bottom: 0,
    right: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  letter: {
    height: 20,
    width: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

})
