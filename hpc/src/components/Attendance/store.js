import { Toast } from '@ant-design/react-native'

import { get, post } from '../../request'

const serviceKey = 'xyREZbhpYa87oyAAtMGqpHL8'

export const getAttendancePoint = async (appId) => {
  const res = await get(`/apps/${appId}/attendances/points`)()
  if (res.errorCode === '0') {
    return res.data.points
  }
  return []
}

export const addAttendancePoint = async (appId, data) => {
  const res = await post(`/apps/${appId}/attendances/points/mobile/add`)({ data })
  if (res.errorCode !== '0') {
    Toast.fail('新增失败')
    return false
  }
  return true
}

export const postAtdVirtualDevice = async (appId, devId, data) =>
  post(`/apps/${appId}/attendances/${devId}/virtual`)({ data })
  // const res = await post(`/apps/${appId}/attendances/${devId}/virtual`)({ data })
  // if (res.errorCode !== '0') {
  //   Toast.fail('保存失败')
  //   return false
  // }
  // return true

export const geocoderLocationService = async (lat, lng, range = 500) => {
  const url = `https://api.map.baidu.com/geocoder/v2/?location=${lat},${lng}&radius=${range}&output=json&pois=1&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}

export const searchPositionService = async (keyword, region = null) => {
  const url = `https://api.map.baidu.com/place/v2/search?query=${keyword}&region=${region}&scope=2&page_size=20&output=json&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}

export const searchLocationService = async (keyword, location, radius) => {
  const url = `https://api.map.baidu.com/place/v2/search?query=${keyword}&location=${location}&radius=${radius}&scope=2&page_size=20&output=json&ak=${serviceKey}`
  const data = await fetch(url).then(response => response.json()).then(result => result).catch(err => err)
  return data
}
