import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import { Text, View, Dimensions, TextInput, ScrollView, Image, TouchableOpacity, Platform, StyleSheet } from 'react-native'
import { Checkbox } from '@ant-design/react-native'
import {
  MapView,
  Geolocation,
  Overlay,
} from 'react-native-baidu-map'
import { initializeRegistryWithDefinitions } from 'react-native-animatable'
import NavigationService from '../../NavigationService'
import ModalHeader from '../../addons/FormComponent/ModalHeader'
import { geocoderLocationService, searchPositionService, searchLocationService } from './store'
import { bd09togcj02, gcj02towgs84, wgs84togcj02, gcj02tobd09 } from '../../utils/coordtransform'
import ResItem from './ResItem'

const isIos = Platform.OS === 'ios'
const { height: screenHeight, width: screenWidth } = Dimensions.get('window')
const { Marker } = Overlay
// 定义searchbar的动画
initializeRegistryWithDefinitions({
  searchBarIn: {
    0: {
      left: 0,
      height: isIos ? 64 : 44,
    },
    0.5: {
      left: -screenWidth,
      height: isIos ? 64 : 44,
    },
    1: {
      left: -screenWidth,
      height: isIos ? 74 : 54,
    },
  },
  searchBarOut: {
    0: {
      left: -screenWidth,
      height: isIos ? 74 : 54,

    },
    0.5: {
      left: -screenWidth,
      height: isIos ? 64 : 44,
    },
    1: {
      left: 0,
      height: isIos ? 64 : 44,
    },
  },
})

// 匹配是否为直辖市
function matchMunicipality(province) {
  let flag = false
  const MunicipalityCityArray = ['北京市', '上海市', '天津市', '重庆市', '台湾省', '香港特别行政区', '澳门特别行政区']
  MunicipalityCityArray.map((i) => { if (province === i) { flag = true } })
  return flag
}

class LocationModal extends PureComponent {
  constructor(props) {
    super(props)
    this.searchInput = React.createRef()
    this.state = {
      searchWord: this.props.searchWord,
      searchResults: [],
      center: { latitude: 39.928216, longitude: 116.402544 },
      marker: { latitude: 39.928216, longitude: 116.402544, title: '' },
      address: {
        place: '',
        lng: '',
        lat: '',
      },
      range: this.props.range,
      unlimitedscope: !!this.props.unlimitedscope,
    }
  }

  componentDidMount() {
    this.createMap()
    // if (Platform.OS === 'android') {
    //   BackHandler.addEventListener('hardwareBackPress', this.goBack)
    // }
  }
  componentWillUnmount() {
    // if (Platform.OS === 'android') {
    //   BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    // }
  }

  goBack = () => {
    NavigationService.back()
    return true
  }
  createMap = () => {
    const {
      address, searchWord, isAllowModify, allowDistance,
    } = this.props
    const mapComponent = this
    // 因百度地图搜索区域始终要有范围限制，所以当不允许修改的时候，搜索范围限制为50
    let allowRange = 50
    mapComponent.setState({
      searchWord,
    })
    if (isAllowModify) {
      allowRange = +allowDistance
    }
    if (address.lng && address.lat && address.lng !== '' && address.lat !== '') {
      // 只要有经纬度,按照经纬度地图周边查询
      // string转换为number
      const lng = +address.lng
      const lat = +address.lat
      const gjc02Position = wgs84togcj02(lng, lat)
      const bd09Position = gcj02tobd09(gjc02Position[0], gjc02Position[1])
      // params: lat ,lng,range
      geocoderLocationService(bd09Position[1], bd09Position[0], allowRange)
        .then((results) => {
          if (results.status === 0) {
            const poiData = results.result.pois
            mapComponent.setState({
              //  isOnlyShowSearchResults: false,
              searchResults: poiData,
              location: { lat: bd09Position[1], lng: bd09Position[0] },
              center: { latitude: bd09Position[1], longitude: bd09Position[0] },
              marker: { latitude: bd09Position[1], longitude: bd09Position[0], title: address.place || '' },
            })
          }
        })
        .catch((e) => {
          console.warn(e, 'error')
        })
    } else if (isAllowModify && allowDistance === 'null') {
      // 当默认有搜索字段切搜索范围无限制的时候
      this.geoSearch(searchWord)
    } else { // 当无经纬度，也无查询字段，获取当前定位
      this.getCurrentLocationHandle(searchWord, allowRange)
    }
  }
  // 获取当前定位后的处理
  getCurrentLocationHandle = async (searchWord, allowRange) => {
    const mapComponent = this
    const geoResults = await Geolocation.getCurrentPosition()
    if (searchWord !== null && searchWord !== '') {
      const location = `${geoResults.latitude},${geoResults.longitude}`
      const data = await searchLocationService(searchWord, location, allowRange)
      if (data.status === 0 && data.results.length) {
        mapComponent.setState({
          searchResults: Object.keys(data.results[0]).length > 2 ? data.results : [],
          center: { latitude: geoResults.latitude, longitude: geoResults.longitude },
          marker: { latitude: geoResults.latitude, longitude: geoResults.longitude, title: geoResults.address || '' },
        })
      } else {
        mapComponent.setState({
          searchResults: [],
          center: { latitude: geoResults.latitude, longitude: geoResults.longitude },
          marker: { latitude: geoResults.latitude, longitude: geoResults.longitude, title: geoResults.address || '' },
        })
      }
    } else {
      const data = await geocoderLocationService(geoResults.latitude, geoResults.longitude, allowRange)

      if (data.status === 0) {
        const { location } = data.result
        const poiData = data.result.pois
        mapComponent.setState({
          // isOnlyShowSearchResults: false,
          searchResults: poiData,
          center: { latitude: location.lat, longitude: location.lng },
          marker: { latitude: location.lat, longitude: location.lng, title: data.sematic_description || '' },
        })
      } else {
        mapComponent.setState({
          // isOnlyShowSearchResults: true,
          searchResults: [],
        })
      }
    }
  }
  // 搜索方法
  geoSearch = async (searchWord) => {
    const {
      address, isAllowModify, allowDistance,
    } = this.props
    const { province, city } = address
    const mapComponent = this
    // 当搜索距离为无限制的时候，按照区域搜索
    if (isAllowModify && allowDistance === 'null') {
      let region
      // 当选择了省市区的时候，搜索范围变为在省市区内查找
      if (province === '') {
        region = null
      } else if (matchMunicipality(province)) {
        region = province
      } else {
        region = city
      }
      searchPositionService(searchWord, region)
        .then((results) => {
          if (results.status === 0) {
            if (results.results.length) {
              mapComponent.setState({
                // Object.keys(results.results[0]).length > 2 =>因为百度searchapi 某些搜索结果只包含市的name和num，无法做到详细定位，所以排除这些搜索结果
                searchResults: Object.keys(results.results[0]).length > 2 ? results.results : [],
              })
            } else {
              Promise.reject('搜索结果为空')
            }
          } else {
            Promise.reject(results.status)
          }
        })
        .catch((e) => {
          console.warn(e)
          mapComponent.setState({
            searchResults: [],
          })
        })
    } else {
      // 当搜索距离有限制的时候，按照当前定位或内部经纬度范围内搜索
      const { center } = this.state
      let allowRange = 50
      if (isAllowModify) {
        allowRange = +allowDistance
      }
      const lat = +center.latitude
      const lng = +center.longitude
      // const gjc02Position = wgs84togcj02(lng, lat)
      // const bd09Position = gcj02tobd09(gjc02Position[0], gjc02Position[1])
      const location = `${lat},${lng}`
      const data = await searchLocationService(searchWord, location, allowRange)
      if (data.status === 0 && data.results.length) {
        mapComponent.setState({
          searchResults: Object.keys(data.results[0]).length > 2 ? data.results : [],
        })
      } else {
        mapComponent.setState({
          searchResults: [],
        })
      }
    }
  }
  closeModal = () => {
    this.props.closeModal()
  }
  savePositionInfo = async (item) => {
    let formatLocation
    let formatAddress
    // 因为百度不同api返回的poi结果参数名不同，所以需要判断情况转换参数
    if (item.point) {
      formatLocation = {
        lat: item.point.y,
        lng: item.point.x,
      }
      formatAddress = item.addr
    } else {
      formatLocation = {
        lat: item.location.lat,
        lng: item.location.lng,
      }
      formatAddress = item.address
    }
    const gcj02Position = bd09togcj02(formatLocation.lng, formatLocation.lat)
    const wgs84Position = gcj02towgs84(gcj02Position[0], gcj02Position[1])
    this.setState({
      searchWord: formatAddress,
      address: {
        place: formatAddress,
        lng: `${wgs84Position[0]}`,
        lat: `${wgs84Position[1]}`,
      },
      center: { latitude: formatLocation.lat, longitude: formatLocation.lng },
      marker: { latitude: formatLocation.lat, longitude: formatLocation.lng, title: formatAddress || '' },
    })
  }
  saveAddressConfig = () => {
    const { address, range, unlimitedscope } = this.state
    this.props.saveAddressConfig({
      address: address.place, range, latitude: address.lat, longitude: address.lng, altitude: 0, unlimitedscope: unlimitedscope ? 1 : 0,
    })
  }
  changeSearchWord = (e) => {
    this.setState({
      searchWord: e.nativeEvent.text,
    })
  }
  changeRangeValue = (v) => {
    this.setState({
      range: v,
    })
  }
  submitEditingHandle = (e) => {
    this.geoSearch(e.nativeEvent.text)
  }

  searchHeader = () => {
    const { searchWord } = this.state

    return (
      <View transition="left" style={{ backgroundColor: '#fff', borderWidth: StyleSheet.hairlineWidth, width: screenWidth, flexDirection: 'row', paddingHorizontal: 20, height: 54, alignItems: 'center' }}>
        <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'center', alignItems: 'center', height: 40, marginRight: 13 }}>
          <Image source={require('../../images/icons-magnifier-search-default.png')} />
          <TextInput ref={this.searchInput} style={{ paddingLeft: 6, flex: 1, fontSize: 16, height: 40 }} blurOnSubmit placeholder="搜索" defaultValue={searchWord} selectTextOnFocus returnKeyType="search" onSubmitEditing={this.submitEditingHandle} />
        </View>
      </View>)
  }
  render = () => {
    const {
      center, marker, searchResults, range, unlimitedscope,
    } = this.state
    const headerProps = {
      leftView: <Text onPress={this.closeModal} style={{ fontSize: 18, color: '#41454b' }}>关闭</Text>,
      centerText: '位置',
      rightView: <TouchableOpacity onPress={this.saveAddressConfig}><Text style={{ fontSize: 18, color: '#41454b' }}>保存</Text></TouchableOpacity>,
    }

    return (
      <View transition="height" style={{ flex: 1, height: screenHeight }}>
        <ModalHeader style={{ width: screenWidth }} header={headerProps} />
        <View style={{ marginVertical: 10, marginHorizontal: 16 }}><Text>范围</Text></View>
        <View style={{ marginHorizontal: 16 }}><TextInput keyboardType="numeric" value={range} style={{ borderWidth: 1, borderColor: '#eee', padding: 5 }} onChangeText={this.changeRangeValue} /></View>
        <View style={{ marginVertical: 10, marginHorizontal: 16 }}>
          <Checkbox checked={unlimitedscope} onChange={e => this.setState({ unlimitedscope: e.target.checked })}>不限范围</Checkbox>
        </View>
        <View style={{ marginVertical: 10, marginHorizontal: 16 }}><Text>选择地点</Text></View>
        {this.searchHeader()}
        <View style={{ height: 400 }}>
          <MapView
            style={{ height: 400, marginHorizontal: 16 }}
            center={center}
            zoom={20}
            marker={marker}
          >
            <Marker location={marker} title={marker.title} />
          </MapView>
        </View>
        <View style={{ borderTopWidth: 1, borderTopColor: '#e5e5e5', backgroundColor: '#fff', flex: 1, position: 'absolute', top: isIos ? 20 + 44 + 400 : 44 + 400, left: 0, width: screenWidth, height: screenHeight - 400 - 44 }}>
          <ScrollView style={{ backgroundColor: '#fff', flex: 1 }} keyboardDismissMode="on-drag">
            {searchResults.length ? <View style={{ paddingBottom: 20 }}>{searchResults.map((item, index) => <ResItem item={item} key={index} index={index} savePositionInfo={this.savePositionInfo} />)}</View> : <Text style={{ fontSize: 16, textAlign: 'center', lineHeight: 24 }}>暂无数据</Text>}
          </ScrollView>
        </View>
      </View>
    )
  }
}

LocationModal.propTypes = {
  address: PropTypes.shape({
    province: PropTypes.string,
    city: PropTypes.string,
    area: PropTypes.string,
    room: PropTypes.string,
    place: PropTypes.string,
    lng: PropTypes.string,
    lat: PropTypes.string,
  }),
  fieldId: PropTypes.string,
  searchWord: PropTypes.string,
  isAllowModify: PropTypes.bool,
  allowDistance: PropTypes.string,
  closeModal: PropTypes.func,
  changeValue: PropTypes.func.isRequired,
  currentHeight: PropTypes.number,
}
LocationModal.defaultProps = {
  address: {
    province: '',
    city: '',
    area: '',
    room: '',
    place: '',
    lng: '',
    lat: '',
  },
  fieldId: '',
  searchWord: '',
  isAllowModify: true,
  allowDistance: '500',
}
export default LocationModal
