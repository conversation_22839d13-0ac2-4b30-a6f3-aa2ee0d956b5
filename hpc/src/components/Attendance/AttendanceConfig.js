import React, { Component } from 'react'
import { Text, View, ScrollView, TouchableOpacity, StyleSheet, Button, TextInput, Modal as RnModal, Dimensions } from 'react-native'
import PropTypes from 'prop-types'
import { Icon, Picker, List, Modal, Toast } from '@ant-design/react-native'
import set from 'lodash/set'
import get from 'lodash/get'
import pullAt from 'lodash/pullAt'
import assignWith from 'lodash/assignWith'
import isUndefined from 'lodash/isUndefined'
import { getAttendancePoint, postAtdVirtualDevice, addAttendancePoint } from './store'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import NavigationService from '../../NavigationService'
import WifiModal from './WifiModal'
import LocationModal from './LocationModal'

const { height: screenHeight, width: screenWidth } = Dimensions.get('window')

const fullIntialDetailData = {
  frequency: '10',
  interval: '10',
  datas: [
    { type: 'wifi', data: [], name: 'Wi-Fi' },
    { type: 'gps', data: [], name: 'GPS' },
  ],
}

const frequencyOptions = [
  { label: '10秒', value: '10' },
  { label: '30秒', value: '30' },
  { label: '一分钟', value: '60' },
]
const checkFrequencyOptions = [
  { label: '一分钟', value: '60' },
  { label: '十分钟', value: '600' },
  { label: '半小时', value: '1800' },
  { label: '一小时', value: '3600' },
]
const WifiItem = ({ data, deleteItem, editItem }) => (
  <View style={{ padding: 10, borderWidth: StyleSheet.hairlineWidth, borderColor: '#dedede', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
    <View>
      <View style={{ marginBottom: 10 }}><Text>WI-FI名称(ssid):{data.name}</Text></View>
      <View><Text>MAC地址(bssid):{data.mac}</Text></View>
    </View>
    <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
      <TouchableOpacity onPress={editItem} style={{ padding: 5 }}><Icon name="edit" color="#1890ff" /></TouchableOpacity>
      <TouchableOpacity onPress={deleteItem} style={{ padding: 5 }}><Icon name="delete" color="red" /></TouchableOpacity>
    </View>
  </View>
)

const GpsItem = ({ data, deleteItem, editItem }) => (
  <View style={{ padding: 10, borderWidth: StyleSheet.hairlineWidth, borderColor: '#dedede', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
    <View style={{ flex: 1 }}>
      <View style={{ marginBottom: 10 }}><Text style={{ lineHeight: 16 }}>地点:{data.address}</Text></View>
      <View style={{ marginBottom: 10 }}>{data.unlimitedscope ? <Text>不限范围</Text> : <Text>距离范围:{data.range}</Text>}</View>
      <View><Text>高度:{data.altitude}</Text></View>
    </View>
    <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
      <TouchableOpacity onPress={editItem} style={{ padding: 5 }}><Icon name="edit" color="#1890ff" /></TouchableOpacity>
      <TouchableOpacity onPress={deleteItem} style={{ padding: 5 }}><Icon name="delete" color="red" /></TouchableOpacity>
    </View>
  </View>
)

export class AttendanceConfig extends Component {
  constructor(props) {
    super(props)
    this.addPointInputRef = React.createRef()
    this.state = {
      attendanceData: [],
      // 记录数据发生变化的考勤点
      pointHadChangedIndex: new Map(),
      pointEditModalVisible: false,
      wifiEditModalVisible: false,
      gpsEditModalVisible: false,
      currentEditPointIndex: null,
      currentEditDetailIndex: null,
      currentEditItemIndex: null,

    }
  }

  componentWillMount = async () => {
    this.loadAttendanceData()
  }
  loadAttendanceData = async () => {
    const { appId } = this.props
    const res = await getAttendancePoint(appId)
    const newRes = res.map(i => assignWith(i, fullIntialDetailData, (objValue, srcValue, key, object, source) => {
      if (!isUndefined(objValue)) {
        if (key === 'datas') {
          return srcValue.map((item, index) => ({ ...item, ...objValue[index] }))
        }
        return objValue
      }
      return srcValue
    }))
    this.setState({
      attendanceData: newRes,
    })
  }
  goBack = () => {
    const { navigation } = this.props
    NavigationService.back()
  }
  // 更改频率
  changeFrequency = (value, type, pointIndex) => {
    const { attendanceData } = this.state
    const newAttendanceData = Array.from(attendanceData)
    set(newAttendanceData[pointIndex], type, value[0])
    this.updateState(newAttendanceData, pointIndex)
  }

  // 上移配置项
  shiftUpItem = (pointIndex, detailIndex) => {
    if (detailIndex === 0) {
      return
    }
    const { attendanceData } = this.state
    const newAttendanceData = Array.from(attendanceData)
    const newDetailData = newAttendanceData[pointIndex].datas
    const [removed] = newDetailData.splice(detailIndex, 1)
    newDetailData.splice(detailIndex - 1, 0, removed)
    newAttendanceData[pointIndex].datas = newDetailData
    this.updateState(newAttendanceData, pointIndex)
  }

  addConfig = (data) => {
    const { attendanceData, currentEditPointIndex, currentEditDetailIndex, currentEditItemIndex } = this.state
    const newAttendance = Array.from(attendanceData)

    // 当currentEditDetailIndex存在值时，为编辑操作

    const detailData = get(newAttendance, [currentEditPointIndex, 'datas', currentEditDetailIndex, 'data'])
    if (Array.isArray(detailData)) {
      if (currentEditItemIndex !== null) {
        const itemData = detailData[currentEditItemIndex]
        detailData.splice(currentEditItemIndex, 1, { ...itemData, ...data })
      } else {
        detailData.push(data)
      }
      this.updateState(newAttendance, currentEditPointIndex)
    }
    this.hideModal()
  }

  addItem = () => {

  }

  addPoint = async () => {
    const value = this.addPointInputRef.current._lastNativeText
    const { appId } = this.props
    const res = await addAttendancePoint(appId, { name: value })
    if (res) {
      this.loadAttendanceData()
    }
  }
  deleteItem = (pointIndex, detailIndex, itemIndex) => {
    const { attendanceData } = this.state
    const newAttendanceData = Array.from(attendanceData)
    const newDetailData = newAttendanceData[pointIndex].datas
    pullAt(newDetailData[detailIndex].data, itemIndex)
    newAttendanceData[pointIndex].datas = newDetailData
    this.updateState(newAttendanceData, pointIndex)
    this.hideModal()
  }
  showAddPointModal = () => {
    this.setState({
      pointEditModalVisible: true,
    })
  }

  showWifiModal = (pointIndex, detailIndex, itemIndex) => {
    this.setState({
      wifiEditModalVisible: true,
      currentEditPointIndex: pointIndex,
      currentEditDetailIndex: detailIndex,
      currentEditItemIndex: itemIndex,
    })
  }
  showGpsModal = (pointIndex, detailIndex, itemIndex) => {
    this.setState({
      gpsEditModalVisible: true,
      currentEditPointIndex: pointIndex,
      currentEditDetailIndex: detailIndex,
      currentEditItemIndex: itemIndex,
    })
  }
  hideModal = () => {
    this.setState({
      wifiEditModalVisible: false,
      gpsEditModalVisible: false,
      pointEditModalVisible: false,
      currentEditPointIndex: null,
      currentEditDetailIndex: null,
      currentEditItemIndex: null,
    })
  }
  hideWifiModal = () => {
    this.setState({
      wifiEditModalVisible: false,
    })
  }
  hideGpsModal = () => {
    this.setState({
      gpsEditModalVisible: false,
    })
  }
  updateState = (newAttendanceData, index) => {
    const { pointHadChangedIndex } = this.state
    const newPointHadChangedIndex = new Map(pointHadChangedIndex)
    newPointHadChangedIndex.set(index, true)
    this.setState({
      attendanceData: newAttendanceData,
      pointHadChangedIndex: newPointHadChangedIndex,
    })
  }
  saveConfig = () => {
    const { appId } = this.props
    const { pointHadChangedIndex, attendanceData } = this.state
    // postAtdVirtualDevice
    const promiseArray = []
    const keyArray = []
    pointHadChangedIndex.forEach((v, k) => {
      if (v) {
        const currentData = attendanceData[k]
        const data = {
          datas: currentData.datas,
          frequency: currentData.frequency,
          interval: currentData.interval,
        }
        const devId = currentData.deviceId
        promiseArray.push(postAtdVirtualDevice(appId, devId, data))
        keyArray.push(k)
      }
      return true
    })
    if (!promiseArray.length) {
      Toast.fail('无数据改变')
      return
    }
    Promise.all(promiseArray).then((values) => {
      const hasNotSuccessFlag = values.map((item, index) => {
        if (item.errorCode === '0') {
          pointHadChangedIndex.set(keyArray[index], false)
          return true
        }
        return false
      }).find(i => !i)
      this.setState({
        pointHadChangedIndex,
      })
      if (!hasNotSuccessFlag) {
        Toast.success('保存成功')
      } else {
        Toast.fail('保存失败')
      }
    })
  }
  renderAttendanceDetail = (detail, pointIndex, detailIndex) => {
    if (detail.type === 'wifi') {
      return (
        <View style={{ padding: 10 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: '#1890ff', fontSize: 14, padding: 10, borderWidth: StyleSheet.hairlineWidth, borderColor: '#dedede', borderBottomWidth: 0 }}>
            <Text style={{ color: '#fff' }}>WiFi</Text>
            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity onPress={() => this.shiftUpItem(pointIndex, detailIndex)} style={{ padding: 5, marginRight: 10 }}><Icon name="caret-up" color="#fff" /></TouchableOpacity>
              <TouchableOpacity onPress={() => this.showWifiModal(pointIndex, detailIndex, null)} style={{ padding: 5 }}><Icon name="plus" color="#fff" /></TouchableOpacity>
            </View>
          </View>
          {detail.data.length ? detail.data.map((item, itemIndex) => <WifiItem data={item} deleteItem={() => this.deleteItem(pointIndex, detailIndex, itemIndex)} editItem={() => this.showWifiModal(pointIndex, detailIndex, itemIndex)} />) : <View style={{ paddingVertical: 10 }}><Text>暂无配置，请新增配置</Text></View>}
        </View>
      )
    } else if (detail.type === 'gps') {
      return (
        <View style={{ padding: 10 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: '#1890ff', fontSize: 14, padding: 10, borderWidth: StyleSheet.hairlineWidth, borderColor: '#dedede', borderBottomWidth: 0 }}>
            <Text style={{ color: '#fff' }}>GPS</Text>
            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity onPress={() => this.shiftUpItem(pointIndex, detailIndex)} style={{ padding: 5, marginRight: 10 }}><Icon name="caret-up" color="#fff" /></TouchableOpacity>
              <TouchableOpacity onPress={() => this.showGpsModal(pointIndex, detailIndex, null)} style={{ padding: 5 }}><Icon name="plus" color="#fff" /></TouchableOpacity>
            </View>
          </View>
          {detail.data.length ? detail.data.map((item, itemIndex) => <GpsItem data={item} deleteItem={() => this.deleteItem(pointIndex, detailIndex, itemIndex)} editItem={() => this.showGpsModal(pointIndex, detailIndex, itemIndex)} />) : <View style={{ paddingVertical: 10 }}><Text>暂无配置，请新增配置</Text></View>}
        </View>
      )
    }
    return null
  }
  render() {
    const { attendanceData, pointHadChangedIndex, pointEditModalVisible, wifiEditModalVisible, gpsEditModalVisible, currentEditPointIndex, currentEditDetailIndex, currentEditItemIndex } = this.state
    const detailData = get(attendanceData, [currentEditPointIndex, 'datas', currentEditDetailIndex, 'data'], [])
    const itemData = get(attendanceData, [currentEditPointIndex, 'datas', currentEditDetailIndex, 'data', currentEditItemIndex], {})
    let isAdd = false
    if (currentEditItemIndex) {
      isAdd = false
    }
    console.log(detailData, itemData)
    const rightView = (
      <Text style={{ fontSize: 18, color: '#41454b' }} onPress={this.saveConfig}>保存</Text>
    )
    return (
      <View style={{ flex: 1, backgroundColor: '#fff' }}>
        <FormHeader centerText="考勤点设置" onPressLeft={this.goBack} rightView={rightView} />
        <Button style={{ width: 100 }} title="新增考勤点" onPress={this.showAddPointModal} />
        <ScrollView style={{ flex: 1, backgroundColor: '#fff' }}>
          {
            attendanceData.map((item, index) =>
              (
                <View style={{ marginHorizontal: 5, marginVertical: 5, paddingVertical: 10, borderWidth: StyleSheet.hairlineWidth, borderColor: '#dedede' }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', margin: 10 }}>{item.name}</Text>
                  {item.datas.map((detailItem, detailIndex) => this.renderAttendanceDetail(detailItem, index, detailIndex))}
                  <Picker
                    data={frequencyOptions}
                    cols={1}
                    value={[attendanceData[index].frequency]}
                    onChange={value => this.changeFrequency(value, 'frequency', index)}
                  >
                    <List.Item arrow="horizontal" onPress={this.onPress}>更新频率</List.Item>
                  </Picker>
                  <Picker
                    data={checkFrequencyOptions}
                    cols={1}
                    value={[attendanceData[index].interval]}
                    onChange={value => this.changeFrequency(value, 'interval', index)}
                  >
                    <List.Item arrow="horizontal" onPress={this.onPress}>打卡间隔</List.Item>
                  </Picker>
                </View>
              ))
          }
        </ScrollView>
        <Modal
          transparent
          onClose={this.hideModal}
          maskClosable
          popup
          visible={pointEditModalVisible}
          footer={[
            { text: '取消', onPress: this.hideModal },
            { text: '确定', onPress: this.addPoint },
          ]}
        >
          <View><TextInput style={{ borderWidth: 1, borderColor: '#eee', padding: 5 }} ref={this.addPointInputRef} /></View>
        </Modal>
        <Modal
          transparent
          onClose={this.hideModal}
          maskClosable
          popup
          visible={wifiEditModalVisible}
        >
          <WifiModal addConfig={this.addConfig} hideModal={this.hideModal} itemData={isAdd ? { name: '', mac: '' } : itemData} />
        </Modal>
        <Modal
          style={{ width: screenWidth, height: screenHeight }}
          bodyStyle={{ width: screenWidth, height: screenHeight }}
          transparent={false}
          onClose={this.hideModal}
          maskClosable
          animationType="slide"
          visible={gpsEditModalVisible}
        >
          <LocationModal closeModal={this.hideModal} saveAddressConfig={this.addConfig} address={isAdd ? null : { lat: itemData.latitude, lng: itemData.longitude, place: itemData.address }} searchWord={itemData.address} range={itemData.range ? `${itemData.range}` : '50'} unlimitedscope={itemData.unlimitedscope} />
        </Modal>
      </View>
    )
  }
}

export default AttendanceConfig

AttendanceConfig.propTypes = {
  appId: PropTypes.string,
}
AttendanceConfig.defaultProps = {
  appId: '',
}
