import React, { Component } from 'react'
import { View, Text, TextInput, Platform, ScrollView, ActivityIndicator, TouchableOpacity, Dimensions } from 'react-native'
import { Button } from '@ant-design/react-native'
import wifi from 'react-native-android-wifi'
import PropTypes from 'prop-types'

const maxHeight = Dimensions.get('window').height * 0.7

class WifiModal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      name: props.itemData.name,
      mac: props.itemData.mac,
      wifiList: [],
      isLoadingWifiList: true,
    }
  }
  componentDidMount() {
    this.getWifiInfo()
  }
  getWifiInfo = async () => {
    wifi.reScanAndLoadWifiList((wifiStringList) => {
      const wifiArray = JSON.parse(wifiStringList)
      this.setState({
        wifiList: wifiArray,
        isLoadingWifiList: false,
      })
    }, (error) => {
      this.setState({
        wifiList: [],
        isLoadingWifiList: false,

      })
    })
  }
  changeName = (text) => {
    this.setState({
      name: text,
    })
  }
  changeMac = (text) => {
    this.setState({
      mac: text,
    })
  }
  selectWifi = (item) => {
    const data = {
      name: item.SSID,
      mac: item.BSSID,
    }
    this.props.addConfig(data)
  }

  saveWifi = () => {
    const { name, mac } = this.state
    const data = {
      name,
      mac,
    }
    this.props.addConfig(data)
  }

  renderInputView = () => {
    const { name, mac } = this.state
    return (
      <View>
        <Text style={{ marginVertical: 10 }}>WI-FI名称(ssid)</Text>
        <TextInput style={{ borderWidth: 1, borderColor: '#eee', padding: 5 }} value={name} onChangeText={this.changeName} />
        <Text style={{ marginVertical: 10 }}>MAC地址(bssid)</Text>
        <TextInput style={{ borderWidth: 1, borderColor: '#eee', padding: 5 }} value={mac} onChangeText={this.changeMac} />
        <View style={{ marginVertical: 10, flexDirection: 'row' }}>
          <Button style={{ flex: 1, margin: 10 }} onPress={this.props.hideModal}>取消</Button>
          <Button type="primary" style={{ flex: 1, margin: 10 }} onPress={this.saveWifi}>确定</Button>
        </View>
      </View>
    )
  }

  renderSelectView = () => {
    const { wifiList, name, mac, isLoadingWifiList } = this.state
    return (
      <View style={{ maxHeight }}>

        <ScrollView>
          {isLoadingWifiList ? <ActivityIndicator /> : wifiList.map(item => (
            <TouchableOpacity onPress={() => this.selectWifi(item)}>
              <View style={{ paddingVertical: 10 }}>
                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>{item.SSID}</Text>
                <Text>{item.BSSID}</Text>
              </View>
            </TouchableOpacity>
          ))
          }
        </ScrollView>
      </View>

    )
  }
  render() {
    if (Platform.OS === 'android') {
      return this.renderSelectView()
    }
    return this.renderInputView()
  }
}

export default WifiModal

WifiModal.defaultProps = {
  hideModal: PropTypes.func.isRequired,
}
