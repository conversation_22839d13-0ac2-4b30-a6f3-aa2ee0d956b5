import PropTypes from 'prop-types'
import React from 'react'
import { Text, TouchableOpacity } from 'react-native'
import { Toast } from '@ant-design/react-native'
import dismissKeyboard from 'dismissKeyboard'
import { ActionSheet } from 'nagz-mobile-lib'
import { GO_COPY, GO_MODAL } from '../../utils/jump'
import { getBackHisNodes } from '../Workflow/Store'
import WfappointBack from '../Workflow/WfappointBack'
import WfrefUser from '../Workflow/WfrefUser'

class FormButtons extends React.PureComponent {
  state = {
    fieldId: '',
  }

  componentWillUnmount() {
    if (this.actionSheet) {
      this.actionSheet.close()
    }
  }

  doOperation = async (datas) => {
    let data = { wfComment: datas.comments }
    let recall = ''
    if (this.innerOprId === 'appointBack') {
      data = {
        ...data,
        backActId: datas.backnode.nodeId || '',
        backActName: datas.backnode.name || '',
        backIsRepeat: datas.isRepeat ? 'yes' : 'no',
      }
    }
    if (this.innerOprId === 'recall') {
      data = {
        ...data,
        taskId: datas.doneTask.taskId || '',
      }
      recall = datas.doneTask.innerOprId !== this.innerOprId ? datas.doneTask.innerOprId : ''
    }
    if (this.innerOprId === 'delegate' || this.innerOprId === 'turn') {
      data = {
        ...data,
        delegateUserId: datas.delegateUserId,
      }
    }
    this.props.submit(this.state.fieldId, this.props.operator, { $$wf: { ...data }, recall })
  }

  openModal = (datas) => {
    if (this.innerOprId === 'appointBack') {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfappointBack
          {...datas}
          doOperation={this.doOperation}
        />,
        opacity: 0.5,
      })
    } else if (this.innerOprId === 'delegate' || this.innerOprId === 'turn') {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfrefUser
          {...datas}
          doOperation={this.doOperation}
        />,
        opacity: 0.5,
      })
    } else {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfappointBack
          {...datas}
          wfComment
          doOperation={this.doOperation}
          appId={this.props.appId}
          formId={this.props.formId}
          dataId={this.props.dataId}
        />,
        opacity: 0.5,
      })
    }
  }

  // 选择
  showActionSheet = () => {
    const { submit, buttons, operator, appId, formId, dataId, isWorkFlowForm } = this.props
    const items = buttons.filter(({ properties }) => properties.visible !== false)
    const BUTTONS = []
    const IDSETS = []

    items.map(({ id, properties }) => {
      if (properties.enabled) {
        BUTTONS.push(properties.label)
        IDSETS.push(id)
      }
    })
    BUTTONS.push('取消')
    this.actionSheet = ActionSheet
    this.actionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        // destructiveButtonIndex: BUTTONS.length - 2,
        // title: '标题',
        // message: '请选择您的附件',
        'data-seed': 'logId',
      },

      async (buttonIndex) => {
        this.isclick = false
        if (buttonIndex < 0) return
        if (buttonIndex !== BUTTONS.length - 1) {
          const field = items.find(item => item.id === IDSETS[buttonIndex])
          const { id, properties: { label, wfCommentRequired, innerOprId, isWfComment } } = field
          this.innerOprId = innerOprId
          dismissKeyboard()
          if (!isWorkFlowForm || !isWfComment) {
            if (field.type === 'CopyFormData') {
              GO_COPY(() => { }, appId, formId, dataId, IDSETS[buttonIndex])
            } else {
              submit(IDSETS[buttonIndex], operator)
            }
          } else {
            let backHisNodes = []
            if (innerOprId === 'appointBack') {
              backHisNodes = await getBackHisNodes(appId, formId, dataId)
              if (!backHisNodes.length) {
                Toast.info(`当前没有可${label}的节点`)
                return
              }
            }
            this.setState({ fieldId: id })
            const datas = {
              item: { appId, nagz_commentsRequired: wfCommentRequired },
              innerOpr: { defaultValue: innerOprId, name: label },
              backHisNodes,
            }
            this.openModal(datas)
          }
        }
      },
    )
  }

  render() {
    const { inputBlur } = this.props
    return (
      <TouchableOpacity
        onPress={() => {
          if (!this.isclick) {
            this.isclick = true
            setTimeout(() => {
              if (inputBlur) {
                inputBlur(() => {
                  this.showActionSheet()
                })
              } else {
                this.showActionSheet()
              }
            }, 300)
          }
        }}
      >
        <Text style={{ color: '#41454B', fontSize: 18 }}>操作</Text>
      </TouchableOpacity>
    )
  }
}

FormButtons.propTypes = {
  submit: PropTypes.func.isRequired,
  appId: PropTypes.string,
  formId: PropTypes.string,
  dataId: PropTypes.string,
  buttons: PropTypes.arrayOf(PropTypes.object),
  inputBlur: PropTypes.func,
  operator: PropTypes.string,
  isWorkFlowForm: PropTypes.bool,
}

export default FormButtons
