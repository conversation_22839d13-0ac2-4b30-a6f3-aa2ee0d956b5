import React, { Component, createRef } from 'react'
import { View, StyleSheet} from 'react-native'
import { WebView } from "react-native-webview";

import htmlContent from './h5/html'
import injectedSignaturePad from './h5/js/signature_pad'
import injectedApplication from './h5/js/app'

const styles = StyleSheet.create({
  signature: {
    width: 200,
    height: 110,
    borderWidth: 2,
    borderColor: 'grey',
  },
  signaturBg: {
    alignItems: 'center',
    marginTop: 20,
  },
  webView: {},
  webBg: {
    width: '100%',
    backgroundColor: '#FFF',
    flex: 1,
  },
})

class SignatureView extends Component {
  static defaultProps = {
    webStyle: '',
    onOK: () => {},
    onEmpty: () => {},
    descriptionText: 'Sign above',
    clearText: 'Clear',
    confirmText: 'Confirm',
  };

  constructor(props) {
    super(props)
    const { descriptionText, clearText, confirmText, emptyText, webStyle } = props
    this.state = {
      base64DataUrl: props.dataURL || null,
    }

    const injectedJavaScript = injectedSignaturePad + injectedApplication
    let html = htmlContent(injectedJavaScript)
    html = html.replace('<%style%>', webStyle)
    html = html.replace('<%description%>', descriptionText)
    html = html.replace('<%confirm%>', confirmText)
    html = html.replace('<%clear%>', clearText)

    this.source = { html }
    this.webViewRef = createRef()
  }

  getSignature = (e) => {
    const { onOK, onEmpty } = this.props
    if (e.nativeEvent.data === 'EMPTY') {
      onEmpty()
    } else {
      onOK(e.nativeEvent.data)
    }
  };
  readSignature=() => {
    if (this.webViewRef.current) {
      this.webViewRef.current.injectJavaScript('readSignature();true;')
    }
  }
  clearSignature=() => {
    if (this.webViewRef.current) {
      this.webViewRef.current.injectJavaScript('clearSignature();true;')
    }
  }
  fromDataURL=(dataUrl) => {
    if (this.webViewRef.current) {
      this.webViewRef.current.injectJavaScript(`fromDataURL('${dataUrl}');true;`)
    }
  }
  _renderError = (args) => {
    console.log('error', args)
  };

  render() {
    return (
      <View style={styles.webBg}>
        <WebView
          ref={this.webViewRef}
          useWebKit
          style={styles.webView}
          source={this.source}
          onMessage={this.getSignature}
          javaScriptEnabled
          onError={this._renderError}
        />
      </View>
    )
  }
}

export default SignatureView
