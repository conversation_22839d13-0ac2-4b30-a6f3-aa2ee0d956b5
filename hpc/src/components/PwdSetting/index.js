/**
 * 关于我们
 */

import React from 'react'
import { View, StyleSheet, StatusBar, Text, Dimensions, Platform, TouchableOpacity, Image, TouchableHighlight } from 'react-native'
import { List, InputItem, Button, Toast, Modal } from '@ant-design/react-native'
import { createForm } from 'rc-form'
import NavigationService from '../../NavigationService'
import {
	RULE_NULL_MOBILE,
	RULE_INVALID_MOBILE,
	RULE_NULL_PASSWORD,
	RULE_INVALID_PASSWORD,
	RULE_NULL_CAPTCHA,
	RULE_INVALID_CAPTCHA,
} from '../../utils/validation/rules'
import { putUsetSett, putPwdSett } from './store'
import { getCaptchaImage, signUp, changePassword } from '../Signup/store'

const { height, width } = Dimensions.get('window')
const statusBarHeight = StatusBar.currentHeight; // 安卓获取状态栏高度
// const { StatusBarManager } = NativeModules; // ios获取状态栏高度

/**
 * 判断IphoneX， 用于 UI适配
 * 直接一次生成结果，后续不再可执行
 */

const DEFAULT_COUNT = 60
const isIphoneX = (function () {
	// iPhoneX SIZE
	const X_WIDTH = 375
	const X_HEIGHT = 812

	// DEVICE SIZE
	const { width, height } = Dimensions.get('window')
	const sizes = [X_WIDTH, X_HEIGHT]

	return Platform.OS === 'ios'
		? sizes.includes(width) && sizes.includes(height)
		: false

})()

class PwdSetting extends React.PureComponent {

	state = {
		count: DEFAULT_COUNT,
		captcha: '',
		randomId: '',
		countryCode: '86',
		countryName: '中国',
		countDownReg: 0,
	}

	componentWillMount = () => {
		this.changeCaptchaImage()
	}

	handleSubmit = () => {
		const { getFieldDecorator, validateFields } = this.props.form
		const { params } = this.props.navigation.state
		validateFields((err, values) => {
			console.log('asdhajdlkajsldjlaksdjlk', err, values)
			if (!err) {
				if (params.type === 'pwd') {
					if (values.password === values.password1) {
						putPwdSett(params.userId, values, this.state.randomId).then((res) => {
							if (res.errorCode === '0') {
								this.props.logout()
							}
						})
					} else {
						Modal.alert('错误警告', '两次密码输入不一致', [{ text: '确定' }])
					}
				}
				if (params.type === 'userName' || params.type === 'nickname') {
					putUsetSett(params.userId, values).then((res) => {
						if (res.errorCode === '0') {
							this.props.updateUser(params.type, params.type === 'userName' ? values.userName : values.nickname)
							this.props.navigation.goBack()
						}
					})
				}
			} else {
				let message = ''
				if (err.mobile && err.mobile.errors.length > 0) {
					message += err.mobile.errors[0].message
					message += '\n'
				}
				if (err.oldPassword && err.oldPassword.errors.length > 0) {
					message += err.oldPassword.errors[0].message
					message += '\n'
				}
				if (err.password && err.password.errors.length > 0) {
					message += err.password.errors[0].message
					message += '\n'
				}
				if (err.captcha && err.captcha.errors.length > 0) {
					message += err.captcha.errors[0].message
				}
				Toast.fail(message, 2)
			}
		})
	}

	changeCaptchaImage = async () => {
		const randomId = Math.random().toString().slice(2)
		const captcha = await getCaptchaImage(randomId)
		if (captcha && randomId) {
			this.setState({ captcha: `data:image/png;base64,${captcha}`, randomId })
		}
	}

	render() {
		const { form, navigation } = this.props
		const { getFieldDecorator, validateFields } = form
		const { params } = navigation.state
		// console.log('asdhkjadhlkajdlajdl', this.props.params.dispatch)
		return (
			<View >
				<View style={{ paddingLeft: 5, paddingRight: 5, marginTop: 20 }}>
					{
						params.type === 'userName' || params.type === 'nickname' ?
							getFieldDecorator(params.type, {
								// rules: [RULE_INVALID_MOBILE, RULE_NULL_MOBILE],
								initialValue: params.type === 'userName' ? params.userName : params.nickname,
								// getValueFromEvent: e => e.replace(/\s+/g, ''),
							})(<InputItem
								style={styles.textInputStyle}
								styles={{ container: { marginRight: 15, paddingRight: 0 } }}
								placeholder="请输入手机号或者邮箱"
								type="text"
								autoComplete="off"
								returnKeyType="next"
								labelNumber={2}
								onSubmitEditing={this.toNextEditing}
								caretHidden={false}// 光标
								underlineColorAndroid="transparent"
							>
							</InputItem>)
							: null
					}
					{/* {
						getFieldDecorator('mobile', {
							// rules: [RULE_INVALID_MOBILE, RULE_NULL_MOBILE],
							// initialValue: mobile,
							// getValueFromEvent: e => e.replace(/\s+/g, ''),
						})(<InputItem
							style={styles.textInputStyle}
							styles={{ container: { marginRight: 15, paddingRight: 0 } }}
							placeholder="请输入手机号或者邮箱"
							type="text"
							autoComplete="off"
							returnKeyType="next"
							labelNumber={2}
							onSubmitEditing={this.toNextEditing}
							caretHidden={false}// 光标
							underlineColorAndroid="transparent"
						>
						</InputItem>)
					} */}
					{/* <View style={{ height: 28 }} /> */}
					{
						params.type === 'pwd' ?
							<View>
								<View style={{ width: '100%', }}>
									{getFieldDecorator('oldPassword', {
										rules: [RULE_INVALID_PASSWORD, RULE_NULL_PASSWORD],
									})(<InputItem
										ref={input => this.pwdInput = input}
										style={styles.textInputStyle}
										styles={{ container: { marginRight: 15, paddingRight: 0 } }}
										placeholder="请输入密码"
										type="password"
										secureTextEntry
										autoComplete="off"
										editable
										labelNumber={2}
										disabled={false}
										underlineColorAndroid="transparent"
									>
									</InputItem>)}
								</View>
								<View style={{ height: 28 }} />
								<View style={{ width: '100%', }}>
									{getFieldDecorator('password', {
										rules: [RULE_INVALID_PASSWORD, RULE_NULL_PASSWORD],
									})(<InputItem
										ref={input => this.pwdInput = input}
										style={styles.textInputStyle}
										styles={{ container: { marginRight: 15, paddingRight: 0 } }}
										placeholder="请输入新密码"
										type="password"
										secureTextEntry
										autoComplete="off"
										editable
										labelNumber={2}
										disabled={false}
										underlineColorAndroid="transparent"
									>
									</InputItem>)}
								</View>
								<View style={{ height: 28 }} />
								<View style={{ width: '100%', }}>
									{getFieldDecorator('password1', {
										rules: [RULE_INVALID_PASSWORD, RULE_NULL_PASSWORD],
									})(<InputItem
										ref={input => this.pwdInput = input}
										style={styles.textInputStyle}
										styles={{ container: { marginRight: 15, paddingRight: 0 } }}
										placeholder="请再次输入新密码"
										type="password"
										secureTextEntry
										autoComplete="off"
										editable
										labelNumber={2}
										disabled={false}
										underlineColorAndroid="transparent"
									>
									</InputItem>)}
								</View>
								<View style={{ flexDirection: 'row', justifyContent: 'space-between', height: 58, marginTop: 20, }}>
									<View style={{ flexDirection: 'column', width: width - 88 - 27 }}>
										{getFieldDecorator('checkCode', {
											rules: [RULE_INVALID_CAPTCHA, RULE_NULL_CAPTCHA],
										})(
											<InputItem
												ref={input => this.pwdInput = input}
												style={styles.textInputStyle}
												placeholder="请输入验证码"
												styles={{ container: { marginRight: 15, paddingRight: 0 } }}
												autoComplete="off"
												labelNumber={2}
												underlineColorAndroid={'transparent'}

											>
											</InputItem>,
										)}
									</View>
									<TouchableHighlight style={{ backgroundColor: '#fff', height: 44, marginRight: 15 }} underlayColor="transparent" onPress={this.changeCaptchaImage}>
										<Image
											style={{ width: 88, height: 44, backgroundColor: 'white', borderRadius: 5 }}
											source={this.state.captcha ? { uri: this.state.captcha } : require('../../images/verification-code-invalidation-default.png')}
											resizeMode="contain"
										/>
									</TouchableHighlight>
								</View>
							</View>
							: null
					}

				</View>
				<TouchableOpacity type="primary" style={styles.loginBtnStyle} onPress={this.handleSubmit} >
					<Text style={styles.loginBtnStyleText}>
						确定
					</Text>
				</TouchableOpacity>
			</View>
		)
	}

}

export default createForm()(PwdSetting)

const styles = StyleSheet.create({
	container: {
		flex: 1,
		// justifyContent: 'center',
		// alignItems: 'center',
		backgroundColor: 'white',
	},
	pic: {
		width: 65,
		height: 65,
		marginTop: 129,
		marginLeft: (width - 80) / 2,
		marginBottom: 22.5,
	},
	textInputStyle: {
		flex: 1,
		width: '100%',
		color: '#9B9B9B',
		height: 44,
		fontSize: 14,
		backgroundColor: 'white',
		paddingLeft: 10,
		// marginLeft: 20,
		// marginRight: 20,
		borderRadius: 2,
	},
	captchaInputStyle: {
		width: width * 52 / 100,
		height: 40,
		backgroundColor: 'white',
		marginLeft: 20,
	},
	loginBtnStyle: {
		height: 44,
		marginLeft: 20,
		marginRight: 20,
		marginTop: 30,
		backgroundColor: '#4CBDFF',
		borderWidth: 0,
		borderRadius: 2,
		fontSize: 16,
		flexDirection: 'row',
		justifyContent: 'center',
		paddingTop: 12,
	},
	loginBtnStyleText: {
		fontSize: 18,
		color: '#fff',
		fontWeight: '500',
	},
	settingStyle: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		width: width - 40,
	},
})
