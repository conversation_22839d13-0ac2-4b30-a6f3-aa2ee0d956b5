/**
 * Created by tdzl2003 on 4/4/16.
 */

import { NativeAppEventEmitter } from 'react-native'

import { get } from '../../request'
import { getOSSToken } from '../../addons/FormComponent/Attachment/uploadFiles'
import { isIos } from '../../config/sysPara'

const HotUpdate = require('react-native').NativeModules.HotUpdate

export const downloadRootDir = HotUpdate.downloadRootDir
export const packageVersion = HotUpdate.packageVersion
export const currentVersion = HotUpdate.currentVersion
export const isFirstTime = HotUpdate.isFirstTime
export const isRolledBack = HotUpdate.isRolledBack

export async function checkUpdate() {
  const resp = await get('/apps/version')()
  console.log('asdjnkasmdlamdl;amsd;lmanslkdalkdjlkadl;', resp)
  if (resp.errorCode !== '0') {
    throw new Error('无法获取版本号')
  }
  const data = isIos ? resp.data.data.find(d => d.type === 1) : resp.data.data.find(d => d.type === 0)
  const hash = data.version.split('.')
  return { ...data, hashName: hash[hash.length - 1] }
}

export async function downloadUpdate(options) {
  const info = await getOSSToken()
  options = { ...options, ...info }
  if (options.diffUrl) {
    await HotUpdate.downloadPatchFromPpk({
      updateUrl: options.diffUrl,
      hashName: options.hash,
      originHashName: currentVersion,
    })
  } else if (options.pdiffUrl) {
    await HotUpdate.downloadPatchFromPackage({
      updateUrl: options.pdiffUrl,
      hashName: options.hash,
    })
  } else {
    await HotUpdate.downloadUpdate(options)
  }
  const hash = options.version.split('.')
  return hash[hash.length - 1]

  // return options.version;
}

export async function switchVersion(hash) {
  HotUpdate.reloadUpdate({ hashName: hash })
}

export async function switchVersionLater(hash) {
  HotUpdate.setNeedUpdate({ hashName: hash })
}

export function markSuccess() {
  HotUpdate.markSuccess()
}

NativeAppEventEmitter.addListener('RCTHotUpdateDownloadProgress', (params) => {

})

NativeAppEventEmitter.addListener('RCTHotUpdateUnzipProgress', (params) => {

})
