import React from 'react'
import PropTypes from 'prop-types'
import {

  ActivityIndicator,
  View,
  Image,
  TouchableOpacity,
} from 'react-native'
import { WebView } from "react-native-webview";

import Orientation from 'react-native-orientation'
import { connect } from 'react-redux'
import NavigationService from '../../NavigationService'
import PubSub from '../../utils/pubsub'
import { screenHeight, screenWidth, deviceType, envParm, API_VERSION } from '../../config/sysPara'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import { get } from '../../request'
import { getToken } from '../../utils/storage/index'
import * as formConstants from '../../containers/RunTime/Form/constants'
// import * as reportformConstants from '../../containers/RunTime/ReportForm/constants'
import { GO_ROOTMODAL, GO_FORM_DESIGN, GO_EDIT, GO_COPY } from '../../utils/jump'
import ReportModal from './ReportModal'

const URLS = envParm.REPORTURLS

class ReportDetail extends React.PureComponent {
  state = {
    url: '',
    loading: true,
    screenDirection: 'PORTRAIT',
    fields: this.props.fields,
    data: this.props.data,
    filterFields: this.props.filterFields,
    reportParameter: this.props.reportParameter,
  }

  componentDidMount= async () => {
    Orientation.lockToPortrait()
    const initial = await Orientation.getInitialOrientation()
    this.setState({
      screenDirection: initial,
      fields: this.props.fields,
      data: this.props.data,
      filterFields: this.props.filterFields,
      reportParameter: this.props.reportParameter,
    })
    Orientation.addOrientationListener(this._orientationDidChange)
  }

  componentWillReceiveProps = async ({ fields, data }) => {
    if (this.props.fields !== fields || this.props.data !== data) {
      const reportfield = fields.find(f => f.id === this.props.fieldid)
      const filterFields = fields.filter(f => this.props.filterFields.map(ff => ff.id).indexOf(f.id) !== -1)
      this.setState({
        fields, data, filterFields, reportParameter: reportfield.properties.reportParameter,
      })
      await this.getUrl()
    }
  }

  componentWillUnmount() {
    Orientation.getOrientation((err, orientation) => {
      // console.log(`Current Device Orientation: ${orientation}`)
    })

    // Remember to remove listener
    Orientation.lockToPortrait()
    Orientation.removeOrientationListener(this._orientationDidChange)
  }

  setSrc = async () => {
    let src = ''
    this.state.reportParameter.forEach((par) => {
      if (par.parameter) {
        const value = this.expressFun(par.value) ? this.getValue(par.value) : par.value
        const parameter = this.expressFun(par.parameter) ? this.getValue(par.parameter) : par.parameter
        src = `${src}&${parameter}=${value}`
      }
    })
    if (this.src === src && src !== '') {
      return this.url
    }
    this.src = src
    const res = await get(this.reportAddress + src)()
    this.url = res.data.url
    if (API_VERSION) {
      this.url += (`&version=${API_VERSION}`)
    }
    return this.url
  }

  setDatas=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    const field = this.state.fields.find(k => k.id === fieldId)
    const dataKey = field ? field.setting.value : ''
    this.datas[fieldId] = {
      dataKey,
      value: dataKey ? this.state.data && this.state.data[fieldId][dataKey] : '',
    }
  }

  getValue=(express) => {
    const fieldId = express.replace(/[${}]/g, '')
    return this.datas[fieldId].value
  }

  init=() => {
    this.state.reportParameter.forEach((par) => {
      if (par.parameter) {
        if (this.expressFun(par.value)) { this.setDatas(par.value) }
        if (this.expressFun(par.parameter)) { this.setDatas(par.parameter) }
      }
    })
  }

  getUrl = async () => {
    this.setState({ loading: true })
    this.datas = {}
    PubSub.on(formConstants.UPDATE_FORMDATA, ({ fieldId, dataKey, value }) => {
      if (this.datas[fieldId] && this.datas[fieldId].dataKey === dataKey) {
        this.datas[fieldId].value = value
      }
    })
    this.init()
    const token = await getToken()
    this.reportAddress = `/apps/${this.props.appId}/reports/${this.props.selectReport}/preview?authToken=${token}`
    if (this.props.selectReport) {
      const src = await this.setSrc()
      if (!this.state.url || this.state.url === URLS + src) this.webviewLoaded()
      this.setState({ url: URLS + src })
    }
  }

  expressFun = value => (/^\$\{(.+)\}$/).test(value)

  rotateScreen = () => {
    if (this.state.screenDirection === 'LANDSCAPE') {
      Orientation.lockToPortrait()
      this.setState({
        screenDirection: 'PORTRAIT',
      })
    } else {
      if (deviceType === 2) {
        Orientation.lockToLandscapeRight()
      } else {
        Orientation.lockToLandscapeLeft()
      }

      this.setState({
        screenDirection: 'LANDSCAPE',
      })
    }
  }

  _orientationDidChange = (orientation) => {
    if (orientation === 'LANDSCAPE') {
      // Orientation.lockToLandscapeRight()
      // this.setState({
      //   screenDirection: orientation,
      // })
    } else {
      // Orientation.lockToPortrait()
      // this.setState({
      //   screenDirection: orientation,
      // })
    }
  }

  openModal = () => {
    if (this.open) return
    this.open = true
    if (this.state.screenDirection !== 'PORTRAIT') {
      this.rotate = true
      this.rotateScreen()
    }
    GO_ROOTMODAL({
      isCustomGoBack: true,
      animationIn: 'slideInRight',
      animationOut: 'slideOutRight',
      children: <ReportModal
        {...this.props}
        screenDirection={this.state.screenDirection}
        filterFields={this.state.filterFields}
        goBack={this.goBack}
      />,
      opacity: 0.5,
    })
  }

  webviewLoaded=() => {
    this.setState({
      loading: false,
    })
  }

  goBack= () => {
    this.open = false
    if (this.rotate) {
      this.rotate = false
      this.rotateScreen()
    }
    NavigationService.back()
    return true
  }

  renderImage = () => (<View style={{
 flex: 1, justifyContent: 'flex-end', flexDirection: 'row', alignItems: 'center',
}}>
    {this.props.filterFields && this.props.filterFields.length > 0 && <TouchableOpacity
      onPress={this.openModal}
      style={{
 width: 40, height: 40, justifyContent: 'center', alignItems: 'center',
}}
    ><Image style={{ width: 19, height: 19, resizeMode: 'contain' }} source={require('../../images/icons-filter-press-default.png')} />
                                                                      </TouchableOpacity>}
    <TouchableOpacity
      onPress={this.rotateScreen}
      style={{
 width: 40, height: 40, justifyContent: 'center', alignItems: 'center',
}}
    ><Image style={{ width: 19, height: 19, resizeMode: 'contain' }} source={this.state.screenDirection === 'PORTRAIT' ? require('../../images/icons-rotate-landscape-default.png') : require('../../images/icons-rotate-longitudinal-default.png')} />
    </TouchableOpacity>
  </View>)

  render() {
    const { label, url } = this.props
    return (
      <View style={{ flex: 1, backgroundColor: 'white' }}>
        <FormHeader
          style={{ width: this.state.screenDirection === 'PORTRAIT' ? screenWidth : screenHeight }}
          centerText={label}
          onPressLeft={NavigationService.back}
          right={this.renderImage()}
        />
        <WebView startLoadingState onLoadEnd={this.webviewLoaded} bounces={false} style={{ marginHorizontal: 15, flex: 1, backgroundColor: 'transparent' }} source={{ uri: this.state.url ? this.state.url : url }} />
        <View style={{
 position: 'absolute', top: deviceType === 2 ? 84 : 50, left: 0, right: 0, justifyContent: 'center',
}}
        >
          <ActivityIndicator
            animating={this.state.loading}
            color="#A0A2A5"
            size="small"
          />
        </View>
      </View>)
  }
}
const mapStateToProps = (state, ownProps) => {
  const {
    formDataState: {
      fields, data, validates, submittable, edited, currEditFieldId, queryCondtAndData, submitInfoBox, refPropDatas, refDisplay,
    },
    navState: { formTitle },
  } = state
  return ({
    fields,
    appId: ownProps.appId,
    formId: ownProps.formId,
    fieldId: ownProps.fieldId,
    dataId: ownProps.dataId,
    operator: ownProps.operator,
    fromFormId: ownProps.fromFormId,
    srcDataId: ownProps.srcDataId,
    formValidate: validates,
    submittable,
    edited,
    currEditFieldId,
    queryCondtAndData,
    formStatus: data.form_status,
    data,
    formTitle,
    InvoServiceId: ownProps.InvoServiceId,
    submitInfoBox,
    refPropDatas,
    formDataInited: submittable,
    filterFields: ownProps.filterFields,
    refDisplay,
  })
}

const mapDispatchToProps = (dispatch, ownProps) => ({
  initFormData: (appId, formId, dataId, operator, query, fieldId, fromFormId, srcDataId) => {
    dispatch({
      type: formConstants.CLEAR,
    })
    dispatch({
      type: formConstants.FORM_INIT,
      payload: {
        ...ownProps, appId, formId, dataId, operator, query, fieldId, fromFormId, srcDataId,
      },
    })
  },
  updateValue: (value, index) => dispatch({
    type: formConstants.FORM_UPDATE_VALUE,
    payload: { value, index, extraParam: { ...ownProps } },
  }),
  updateEveryValue: (value, index) => dispatch({
    type: formConstants.FORM_UPDATE_EVERY_VALUE,
    payload: { value, index, extraParam: { ...ownProps } },
  }),
  updateExtraProps: ({ fieldId, props }) => dispatch({
    type: formConstants.UPDATE_EXTRA_PROPS,
    payload: { fieldId, props, extraParam: { ...ownProps } },
  }),
  submit: (fieldId, operator) => {
    dispatch({
      type: formConstants.FORM_SUBMIT,
      payload: {
        ...ownProps,
        callback: ownProps.callback,
        fieldId,
        operator,
        editPage: typeof (ownProps.dataId) !== 'undefined',
        srcFieldId: ownProps.fieldId,
      },
    })
  },
})

ReportDetail.propTypes = {
  fields: PropTypes.arrayOf(PropTypes.object),
  initFormData: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired,
  updateValue: PropTypes.func.isRequired,
  formValidate: PropTypes.object.isRequired,// eslint-disable-line
  formId: PropTypes.string.isRequired,
  appId: PropTypes.string.isRequired,
  dataId: PropTypes.string,
  fieldId: PropTypes.string,
  updateValidation: PropTypes.func,
  submittable: PropTypes.bool,
  InvoServiceId: PropTypes.string,
  data: PropTypes.object,// eslint-disable-line
  operator: PropTypes.string,
  getNavTitle: PropTypes.func,
  formTitle: PropTypes.string,
  query: PropTypes.string,
  title: PropTypes.string,// eslint-disable-line
  formUpdate: PropTypes.any,// eslint-disable-line
  isView: PropTypes.bool,
  doClear: PropTypes.func,
  updateEveryValue: PropTypes.func,
  updateExtraProps: PropTypes.func,
  updateEditedFlag: PropTypes.func,
  currEditFieldId: PropTypes.string,
  viewData: PropTypes.arrayOf(PropTypes.object),
  formStatus: PropTypes.object,// eslint-disable-line
  edited: PropTypes.bool,
  submitInfoBox: PropTypes.shape({
    isShow: PropTypes.bool,
    isLoading: PropTypes.bool,
    fields: PropTypes.array,
  }),
  hideSubmitBox: PropTypes.func,
  refPropDatas: PropTypes.object,// eslint-disable-line
  filterField: PropTypes.array,// eslint-disable-line
  filterFields: PropTypes.array,// eslint-disable-line
}

export default connect(mapStateToProps, mapDispatchToProps)(ReportDetail)
