import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { View, BackHandler, Text, TextInput, Keyboard, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native'
import { connect } from 'react-redux'
import { Button } from 'nagz-mobile-lib'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { screenHeight, screenWidth, deviceType } from '../../config/sysPara'
import NavigationService from '../../NavigationService'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import Fields from '../FormDesign/Fields/Fields'

const isIos = Platform.OS === 'ios'

class ReportModal extends Component {
  static propTypes = {
    screenDirection: PropTypes.string,
    filterFields: PropTypes.array,
  }
  state={
    keyboardHeight: 0,
    filterFields: [],
  }
  componentDidMount() {
    this.setState({ filterFields: this.props.filterFields })
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this.keyboardDidShow)
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
  }
  componentWillReceiveProps = ({ filterFields }) => {
    if (this.props.filterFields !== filterFields) {
      this.setState({ filterFields })
    }
  }
  componentWillUnmount() {
    if (this.keyboardDidShowListener) this.keyboardDidShowListener.remove()
    if (this.keyboardDidHideListener) this.keyboardDidHideListener.remove()
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    }
  }

  keyboardDidShow=(e) => {
    this.keyboardShow = true
    this.setState({
      keyboardHeight: e.endCoordinates.height,
    })
  }

  keyboardDidHide=() => {
    this.keyboardShow = false
    this.setState({
      keyboardHeight: 0,
    })
  }
  goBack= () => {
    this.props.goBack()
  }
  render() {
    const { keyboardHeight, filterFields } = this.state
    const {
      screenDirection, InvoServiceId, updateEveryValue, updateValue, updateValidation,
      formValidate, formId, operator, dataId, appId, submittable, updateExtraProps, updateEditedFlag,
      currEditFieldId, viewData, formStatus, edited, isView, formType, submitInfoBox, hideSubmitBox, refPropDatas, showCameraFun, inputBlur, copyDataId, refDisplay,
    } = this.props
    const params = {
      ref: ref => this.fieldsCmp = ref,
      items: filterFields,
      isRunTime: true,
      updateValue,
      updateEveryValue,
      formValidate,
      useDragHandle: true,
      formId,
      dataId,
      appId,
      formDataInited: true,
      updateExtraProps,
      updateValidation,
      updateEditedFlag,
      currEditFieldId,
      viewData,
      formStatus,
      edited,
      isView,
      showCameraFun,
      InvoServiceId,
      inputBlur,
      copyDataId,
      refPropDatas,
      operator,
      refDisplay,
    }
    return (<View style={{ height: screenHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
      <TextInput ref={r => this.searchInput = r} style={{ display: 'none' }} />
      <FormHeader
        style={{ width: screenWidth }}
        centerText="请选择筛选条件"
        leftView={<TouchableOpacity onPress={this.goBack} >
          <Text style={{ color: '#41454b', fontSize: 18 }}>关闭</Text>
        </TouchableOpacity>}
      />
      <View style={{ height: screenHeight - 74 - keyboardHeight }}>
        <KeyboardAvoidingView
          keyboardVerticalOffset={50}
          behavior={isIos ? 'padding' : null}
          style={{ flex: 1 }}
        >
          <Fields
            {...params}
          />
        </KeyboardAvoidingView>
        <View style={{ margin: 20 }}>
          <Button
            style={{ container: { height: 40, backgroundColor: '#17A9FF', borderRadius: 2 }, textContent: { fontSize: 14, color: '#fff' } }}
            onPress={this.goBack}
          >确定
          </Button>
        </View>
      </View>
    </View>)
  }
}

const mapStateToProps = (state) => {
  const {
    formDataState: {
      refDisplay,
    },
  } = state
  return ({
    refDisplay,
  })
}
export default connect(mapStateToProps)(ReportModal)

