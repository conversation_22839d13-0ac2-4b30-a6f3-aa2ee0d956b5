/**
 * 关于我们
 */

import React from 'react'
import { View, ScrollView, Image, Text, Dimensions, TouchableOpacity, Platform, Clipboard } from 'react-native'
import NavigationService from '../../NavigationService'
import {getUniqueId} from 'react-native-device-info'
import { NetworkInfo } from 'react-native-network-info'
import PropTypes from 'prop-types'
import {companyName,companyFullName} from '@project-config'


const { width, height } = Dimensions.get('window')

class AboutUs extends React.PureComponent {
  toOfficialWeb = () => {
    NavigationService.navigate('innerOfficialWeb')
  }

  onCope = async () => {
    const { navigation, userName, nickname, mobile } = this.props
    const { params, } = navigation.state
    const bssid = await NetworkInfo.getBSSID()
    Clipboard.setString(`应用名称：${companyName}，
    平台账号：${userName}，
    用户名：${nickname}，
    手机号：${mobile}，
    系统信息：${Platform.OS}${Platform.Version},
    设备ID：${getUniqueId()}，
    网关MAC：${bssid}，
    软件版本：${params.versionC}/${params.versionT}
    `);
    Toast.success('复制成功')
    let str = await Clipboard.getString();
    console.log('复制的内容', str)
  }

  render() {
    const { navigation } = this.props
    const { params } = navigation.state
    return (
      <View style={{ flex: 1, alignItems: 'center', backgroundColor: '#FAFAFA' }}>
        <Image source={require('../../images/logo-huapuc-icon.png')} />
        <Text style={{ fontSize: 20, color: '#000', marginTop: -60, fontWeight: '500' }}>{companyName}</Text>
        <View style={{ width, padding: 18, marginTop: 80 }}>
          <View style={{ paddingLeft: 16, paddingRight: 16, backgroundColor: '#fff' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0.5, borderBottomColor: '#cacacb', display: 'flex', height: 40 }}>
              <Text style={{ flex: 0, color: 'rgba(0,0,0,0.75)', fontWeight: '500', fontSize: 14, marginLeft: 12 }}>软件版本</Text>
              <Text style={{ flex: 1, color: 'rgba(0,0,0,0.65)', fontWeight: '400', fontSize: 14, textAlign: 'right' }}>{params.versionC}/{params.versionT}</Text>
            </View>
          </View>
          <View style={{ paddingLeft: 16, paddingRight: 16, backgroundColor: '#fff' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0.5, borderBottomColor: '#cacacb', display: 'flex', height: 40 }}>
              <Text style={{ flex: 0, color: 'rgba(0,0,0,0.75)', fontWeight: '500', fontSize: 14, marginLeft: 12 }}>系统信息</Text>
              <Text style={{ flex: 1, color: 'rgba(0,0,0,0.65)', fontWeight: '400', fontSize: 14, textAlign: 'right' }}>{Platform.OS}{Platform.Version}</Text>
            </View>
          </View>
          <View style={{ paddingLeft: 16, paddingRight: 16, backgroundColor: '#fff', height: 40, justifyContent: 'center', flexDirection: 'row' }}>
            <Text onPress={this.onCope} style={{ color: '#2D9CDB', fontSize: 14, fontWeight: '500', textAlign: 'center', paddingTop: 12 }}>复制</Text>
          </View>
        </View>
        <View style={{ width, position: 'absolute', bottom: 35 }}>
          <Text onPress={this.toOfficialWeb} style={{ color: '#17A9FF', fontSize: 12, textAlign: 'center' }}>《用户协议》</Text>
          <Text style={{ fontSize: 10, color: '#BCBCBB', marginTop: 10, textAlign: 'center' }}>{companyFullName} 版权所有</Text>
          <Text style={{ fontSize: 10, color: '#BCBCBB', marginTop: 10, textAlign: 'center' }}>Copyright © 2013-2021 All Rights Reserved</Text>
        </View>
      </View>
    )
  }

}

export default (AboutUs)
