import React from 'react';
import { View, Text, TouchableHighlight } from 'react-native';
import { delayLongPress } from '../../config/sysPara';

class FloatButton extends React.Component {
    render() {
        return(
            <View style={{position: 'absolute', flex: 1,right: 10, bottom: 50, alignSelf: 'flex-end', justifyContent: 'center', alignItems: 'center'}}>
            <TouchableHighlight delayLongPress={Number(delayLongPress)} 
                style={{ 
                backgroundColor: 'red', 
                width: 50, 
                height: 50, 
                borderRadius: 25, 
                justifyContent: 'center', 
                alignItems: 'center', 
                margin: 20
                }} onPress={()=>{this.props.onClick();}}  >
                <Text>
                +
                </Text>
            </TouchableHighlight>
            </View>
        );
    }
}

export default FloatButton