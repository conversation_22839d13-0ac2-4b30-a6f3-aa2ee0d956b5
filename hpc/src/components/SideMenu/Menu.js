import React from 'react'
import { connect } from 'react-redux'
import PropTypes from 'prop-types'
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  NativeModules,
  Platform,
  ScrollView,
  Keyboard,
  SafeAreaView,
} from 'react-native'
import { Button, Modal, Icon } from '@ant-design/react-native'

import * as Animatable from 'react-native-animatable'
import { envParm } from '../../config/sysPara'
import { GO_USERSETTING, GO_APPS_HOME, GO_APPS_BOTTOM_TAB } from '../../utils/jump'
import { getItem } from '../../utils/storage/asyncStore'
import { menuStyle } from './style'
import NavigationService from '../../NavigationService'
import { GET_APPS } from '../../containers/Home/constants'
import { SET_CURRENTAPPID } from '../../containers/App/constants'
import { getBottomTabNav, getTimes } from './store'
import { setCurrentAppId, setCurrentAppName } from '../../utils/storage'

const style = StyleSheet.create(menuStyle)
const closeIcon = require('../../images/icon-close-outline-default.png')
const infoicon = require('../../images/info.png')
const myAppIcon = require('../../utils/ImageIcon/images/Icons-Object-002.png')
const kefuIcon = require('../../utils/ImageIcon/images/Icons-Office-011.png')
const msgIcon = require('../../utils/ImageIcon/images/Icons-Office-010.png')
const wfIcon = require('../../utils/ImageIcon/images/Icons-Office-008.png')

const settingsIcon = require('../../images/icon-settings-default.png')
const arrowdIcon = require('../../images/icons-arrow-down-small-fill-default.png')

class Menu extends React.PureComponent {
  state = {
    height: 0,
    rotate: '0deg',
    image: null,
    showSetting: true
  }

  componentWillMount = async () => {
    this.updateMenuPhoto()
    this.keyboardWillHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this.keyboardDidShow)
  }
  componentDidMount = () => {
    this.props.getApps()
  }
  componentWillReceiveProps(nextprops) {
    this.updateMenuPhoto()
  }

  componentWillUnmount() {
    this.keyboardWillHideListener.remove()
    this.keyboardDidShowListener.remove()
  }

  getAppsHeight = () => {
    const { apps } = this.props
    const maxshowapp = 4
    return apps.length < maxshowapp ? apps.length * 50 : maxshowapp * 50
  }

  updateMenuPhoto = async () => {
    // const img = await getItem('userPhotos', 666)
    this.setState({
      image: this.props.user ? this.props.user.headImage : '',
    })
  }

  keyboardDidHide = () => {
    setTimeout(() => {
      this.setState({ showSetting: true })
    }, 100)
  }

  keyboardDidShow = () => {
    this.setState({ showSetting: false })
  }

  handleMyapp = () => {
    this.setState({
      height: this.state.height === 0 ? this.getAppsHeight() : 0,
      rotate: this.state.rotate === '0deg' ? '180deg' : '0deg',
    })
  }

  goUserSetting = () => {
    this.props.closeMenu()
    GO_USERSETTING({ updateMenuPhoto: this.updateMenuPhoto })
  }

  goMessageBox = () => {
    this.props.closeMenu()
    const { appId } = this.props
    // 跳转到消息界面
    NavigationService.navigate('messageBox', { currentAppId: appId })
  }

  goWorkflowBox = () => {
    this.props.closeMenu()
    const { appId } = this.props
    // 跳转到工作流界面
    NavigationService.navigate('workflowBox', { currentAppId: appId })
  }

  handleApp = async (appId, appName) => {
    this.props.closeMenu()
    const date = {
      isDuringDate: function (beginDateStr, endDateStr) {
        let curDate = new Date(),
          beginDate = new Date(beginDateStr),
          endDate = new Date(endDateStr);
        console.log(curDate)
        if (curDate >= beginDate && curDate <= endDate) {
          return true;
        }
        return false;
      }
    }
    const navRes = await getBottomTabNav(appId)
    // const timeRes = await getTimes(appId)
    // console.log(navRes,'***********')
    // for (const iterator of timeRes.data.timePeriod) {
    //   if(timeRes.data.timePeriod.length==0){
    //       return
    //   }
    //  let time = new Date()
    //  let fullYear = time.getFullYear()
    //  let month = time.getMonth() + 1
    //  let day  = time.getDate()
    //  if(!date.isDuringDate(fullYear+'/'+month+'/'+ day +' ' + iterator.split('-')[0], fullYear+'/'+month+'/'+ day +' '+ iterator.split('-')[1])){
    //   return NavigationService.push('accessRights')
    //   }
    // }
    // 切换应用时，需要变更当前应用id

    await setCurrentAppId(appId)
    await setCurrentAppName(appName)
    this.props.setCurrentAppId(appId)
    if (navRes.errorCode !== '0' || !navRes.data.navGroups || !navRes.data.navGroups.length) {
      GO_APPS_HOME(appId, appName)
    } else {
      // 因为路由的定义必须为静态，所以根据自定义tab标签页的tab个数，决定跳转到对应的tabstack
      const navs = navRes.data.navGroups
      GO_APPS_BOTTOM_TAB(appId, appName, navs, true)
    }
  }

  kefu = () => {
    this.props.closeMenu()
    NavigationService.navigate('callCenterNum')
  }

  renderApps = () => {
    const { appId, apps } = this.props
    const currentApp = apps.find(app => app.id === appId) || {}
    if (appId && apps && apps.length !== 0) {
      return (

        <Animatable.View transition="height" style={[style.apps, { height: this.state.height }]}>

          <ScrollView>
            <TouchableOpacity onPress={() => { this.handleApp(currentApp.id, currentApp.name) }}>
              <View style={style.appItem}>
                <View style={style.appCurrentdot} />
                <Text numberOfLines={1} style={style.appCruuentName}>{currentApp.name}</Text>
              </View>
            </TouchableOpacity>
            {apps.filter(app => app.id !== appId).map(app => (
              <TouchableOpacity key={app.id} onPress={() => { this.handleApp(app.id, app.name) }}>
                <View style={[style.appItem, { paddingLeft: 36 }]}>
                  <Text numberOfLines={1} style={[style.appCruuentName, { color: '#41454B' }]}>{app.name}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Animatable.View>
      )
    }
  }

  renderItem = (onPress, content, icon = myAppIcon, rightNode) => (
    <TouchableOpacity activeOpacity={1} onPress={onPress}>
      <View style={style.item}>
        <View style={style.itemIcon}>
          <Image source={icon} />
        </View>
        <View style={style.itemContent}>
          <Text style={style.itemContentText}>{content}</Text>
        </View>
        <View style={style.itemOpContent}>
          {rightNode}
        </View>
      </View>
    </TouchableOpacity>
  )
  renderMessageItem = (onPress, content, icon = myAppIcon, rightNode) => (
    <TouchableOpacity activeOpacity={1} onPress={onPress}>
      <View style={style.item}>
        <View style={style.itemIcon}>
          <Image source={icon} />
        </View>
        <View style={[style.itemContent, { flexDirection: 'row' }]}>
          <Text style={style.itemContentText}>{content}</Text>
          {!!this.props.unReadCount && <View style={{ width: 16, height: 16, borderRadius: 8, backgroundColor: '#FF3B30', justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>{this.props.unReadCount > 9 ? '9+' : this.props.unReadCount}</Text></View>}
        </View>
        <View style={style.itemOpContent}>
          {rightNode}
        </View>
      </View>
    </TouchableOpacity>
  )
  render = () => (
    <SafeAreaView style={style.menu}>
      <View style={style.header}>
        <TouchableOpacity style={style.closeImage} onPress={this.props.closeMenu}>
          <Image source={closeIcon} />
        </TouchableOpacity>
      </View>
      {this.renderItem(
        this.handleMyapp, '应用', myAppIcon,
        (<Animatable.Image
          transition="rotate"
          source={arrowdIcon}
          style={{ width: 10, height: 10, transform: [{ rotate: this.state.rotate }] }}
        />),
      )}
      {this.renderApps()}
      {this.renderMessageItem(this.goMessageBox, '消息', msgIcon)}
      {this.renderMessageItem(this.goWorkflowBox, '流程', wfIcon)}
      {this.renderItem(this.kefu, '客服', kefuIcon)}
      {this.state.showSetting
        && (
          <TouchableOpacity style={style.footer} activeOpacity={1} onPress={this.goUserSetting}>
            <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
              <View style={style.footerPic}>
                <Image
                  source={this.state.image ? { uri: this.state.image } : (this.props.user && this.props.user.sex === 0 ? require('../UserSetting/UserForm/defaultAvatar_boy.png') : require('../UserSetting/UserForm/defaultAvatar_girl.png'))}
                  style={{ width: 40, height: 40, borderRadius: 20 }}
                />
              </View>
              <View style={style.footerContent}>
                <Text style={{ fontSize: 12, color: '#41454B' }}>{this.props.user && this.props.user.nickname}</Text>
                <Text style={{ fontSize: 12, color: '#A0A2A5' }}>{this.props.user && this.props.user.mobile.substr(0, 3) + "****" + this.props.user.mobile.substr(7)}</Text>
              </View>
              <View>
                <Image source={settingsIcon} />
              </View>
            </View>
          </TouchableOpacity>
        )
      }
    </SafeAreaView>
  )
}

const mapStateToProps = ({ app: { user, currentAppId }, homeState }) => ({
  apps: [...homeState.owned, ...homeState.joined],
  user,
  appId: currentAppId,
})
const mapDispatchToProps = dispatch => ({
  getApps: () => dispatch({ type: GET_APPS }),
  setCurrentAppId: (appId) => { dispatch({ type: SET_CURRENTAPPID, payload: { appId } }) },
})

Menu.propTypes = {
  closeMenu: PropTypes.func,
  user: PropTypes.shape({
    nickname: PropTypes.string,
    mobile: PropTypes.string,
  }),
  apps: PropTypes.array,
  appId: PropTypes.string,
}
export default connect(mapStateToProps, mapDispatchToProps)(Menu)
