import React from 'react'
import PropTypes from 'prop-types'
import { View, Dimensions } from 'react-native'
import SideMenu from 'react-native-side-menu'
import { getMessageUnreadCount } from '../Message/Store'
import { Button } from 'nagz-mobile-lib'
import NavigationService, { getNavigatorInstance, getCurrentRoute } from '../../NavigationService'

import Menu from './Menu'

const { width, height } = Dimensions.get('window')
export default function (WrapComponent) {
  return class extends React.PureComponent {
    state = {
      isOpen: false,
      show: false,
      unReadCount: 0,
      appId: this.props.appId,
    }
   
    setOpenState = async (isOpen) => {
      if (isOpen !== this.state.isOpen) {
        this.changeSideMenuState(!isOpen)
      }
      if (isOpen) {
        // const count = await getMessageUnreadCount(this.state.appId)
        // console.log(count, this.state.appId)
        // this.setState({
        //   unReadCount: count,
        // })
      }
    }

    isHideBottomTab = (isOpen) => {
      const nav = getNavigatorInstance()
      const currentSwitchRouteKey = getCurrentRoute(nav.state.nav, 2).key
      if (!currentSwitchRouteKey) {
        return
      }
      if (nav) {
        if (!isOpen) {
          NavigationService.setParams({
            hideTab: true,
          }, currentSwitchRouteKey)
        } else {
          NavigationService.setParams({
            hideTab: false,
          }, currentSwitchRouteKey)
        }
      }
    }

    closeMenu = () => {
      this.changeSideMenuState(true)
    }

    openMenu = () => {
      this.changeSideMenuState(false)
      // getMessageUnreadCount()
    }

    toggleMenu = () => {
      this.changeSideMenuState(this.state.isOpen)
    }

    changeSideMenuState = (isOpen) => {
      this.isHideBottomTab(isOpen)
      if (isOpen) {
        this.setState({ show: false, isOpen: false })
      } else {
        this.setState({ show: true, isOpen: true })
      }
    }

    changeOpen = (isOpen) => {
      if (isOpen) {
        setTimeout(() => {
          this.setState({ show: false })
        }, 300)
      } else {
        this.setState({ show: true })
      }
    }

    render = () => (
      <SideMenu
        openMenuOffset={width * 0.72}
        isOpen={this.state.isOpen}
        disableGestures
        onChange={this.setOpenState}
        menu={this.state.show && (<Menu appId={this.props.appId} isOpen={this.state.show} unReadCount={this.state.unReadCount} closeMenu={this.closeMenu} />)}
      >
        <WrapComponent
          {...this.props}
          closeMenu={this.closeMenu}
          openMenu={this.openMenu}
          toggleMenu={this.toggleMenu}
        />
        {this.state.isOpen
          && (
            <View
              style={{
                flex: 1,
                width,
                height,
                position: 'absolute',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
              }}
            />
          )}
      </SideMenu>
    )
  }
}

