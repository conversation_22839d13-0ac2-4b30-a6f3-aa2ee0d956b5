import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import { View, TouchableOpacity, Image, Text, ScrollView } from 'react-native'
import { Icon, Modal, Toast, Portal } from '@ant-design/react-native'
import { ActionSheet } from 'nagz-mobile-lib'
import { GO_EDIT, GO_MODAL } from '../../utils/jump'
import { getWorkflowDiagram, getBackHisNodes, doWorkflowOper, delWorkflowData } from './Store'
import WfappointBack from './WfappointBack'
import WfrefUser from './WfrefUser'

class ListItem extends PureComponent {
  static propTypes = {
    refreshWfDatas: PropTypes.func,
    item: PropTypes.object,
    showTask: PropTypes.bool,
    canDelete: PropTypes.bool,
    status: PropTypes.string,
  }

  componentWillUnmount() {
    if (this.actionSheet) {
      this.actionSheet.close()
    }
  }

  goAppForm = () => {
    const { item } = this.props
    GO_EDIT(null, item.appId, item.formId, null, item.dataId, false)
  }

  showWfDiagram = async () => {
    const { item } = this.props
    if (this.open === item.instId) return
    const tKey = Toast.loading(null, 0)
    this.open = item.instId
    const src = await getWorkflowDiagram(item.appId, item.formId, item.dataId, item.instId)
    Portal.remove(tKey)
    if (src) {
      Modal.alert(
        '流程图',
        <ScrollView style={{ flex: 1, borderColor: '#e5e5e5', borderTopWidth: 1 }} horizontal>
          <Image style={{ height: 264, width: 1000 }} resizeMode="contain" source={{ uri: `data:image/png;base64,${src}` }} />
        </ScrollView>,
        [
          { text: '关闭', onPress: () => { this.open = false } },
        ],
      )
    } else {
      this.open = false
    }
  }

  doOperation = async (datas) => {
    const { item, refreshWfDatas } = this.props
    const tKey = Toast.loading(null, 0)
    let data = {
      wfComment: datas.comments,
    }
    const formDataIds = [{
      formId: item.formId,
      id: item.dataId,
    }]
    if (this.innerOprId === 'appointBack') {
      data = {
        ...data,
        backActId: datas.backnode.nodeId || '',
        backActName: datas.backnode.name || '',
        backIsRepeat: datas.isRepeat ? 'yes' : 'no',
      }
    }
    if (this.innerOprId === 'delegate' || this.innerOprId === 'turn') {
      data = {
        ...data,
        delegateUserId: datas.delegateUserId,
      }
    }
    const wfDatas = {
      $$wf: data,
      [this.oprId]: { $$click: true, value: true },
    }
    const res = await doWorkflowOper(item.appId, formDataIds, wfDatas, this.innerOprName)
    Portal.remove(tKey)
    if (res) {
      refreshWfDatas()
    }
  }

  delWorkflow = async (data) => {
    const tKey = Toast.loading(null, 0)
    const res = await delWorkflowData(data.appId, data.instId)
    Portal.remove(tKey)
    if (res) {
      this.props.refreshWfDatas()
    }
  }

  openModal = (datas) => {
    if (this.innerOprId === 'appointBack') {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfappointBack
          {...datas}
          doOperation={this.doOperation}
        />,
        opacity: 0.5,
      })
    } else if (this.innerOprId === 'delegate' || this.innerOprId === 'turn') {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfrefUser
          {...datas}
          doOperation={this.doOperation}
        />,
        opacity: 0.5,
      })
    } else {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfappointBack
          {...datas}
          wfComment
          formActionSheet
          taskId={datas.item.taskId}
          doOperation={this.doOperation}
        />,
        opacity: 0.5,
      })
    }
  }

  // 选择
  showActionSheet = () => {
    const { item, canDelete } = this.props
    const BUTTONS = []
    const IDSETS = []
    if (!this.isclick) {
      this.isclick = true
      if (canDelete) {
        const btns = ['删除', '取消']
        this.actionSheet = ActionSheet
        this.actionSheet.showActionSheetWithOptions(
          {
            options: btns,
            cancelButtonIndex: btns.length - 1,
            destructiveButtonIndex: btns.length - 2,
            wrapProps: {
              onTouchStart: (e) => e.preventDefault(),
            },
          },

          async (buttonIndex) => {
            this.isclick = false
            if (buttonIndex < 0) return
            if (buttonIndex !== btns.length - 1) {
              Modal.alert('确认删除？', '', [
                { text: '取消', onPress: () => console.log('cancel') },
                { text: '确定', onPress: () => this.delWorkflow(item), style: { fontWeight: 'bold' } },
              ])
            }
          },
        )
      } else {
        (item.fields || []).map(({ id, innerOprId, name }) => {
          BUTTONS.push(name)
          IDSETS.push({ id, innerOprId })
        })
        BUTTONS.push('取消')
        this.actionSheet = ActionSheet
        this.actionSheet.showActionSheetWithOptions(
          {
            options: BUTTONS,
            cancelButtonIndex: BUTTONS.length - 1,
            wrapProps: {
              onTouchStart: (e) => e.preventDefault(),
            },
          },

          async (buttonIndex) => {
            this.isclick = false
            if (buttonIndex < 0) return
            if (buttonIndex !== BUTTONS.length - 1) {
              this.oprId = IDSETS[buttonIndex].id
              this.innerOprId = IDSETS[buttonIndex].innerOprId
              this.innerOprName = BUTTONS[buttonIndex]
              let backHisNodes = []
              if (IDSETS[buttonIndex].innerOprId === 'appointBack') {
                backHisNodes = await getBackHisNodes(item.appId, item.formId, item.dataId)
                if (!backHisNodes.length) {
                  Toast.info(`当前没有可${BUTTONS[buttonIndex]}的节点`)
                  return
                }
              }
              const datas = {
                item,
                innerOpr: { defaultValue: IDSETS[buttonIndex].innerOprId, name: BUTTONS[buttonIndex] },
                backHisNodes,
              }
              this.openModal(datas)
            }
          },
        )
      }
    }
  }

  headImage = ({ src }) => {
    if (src) {
      if (src.indexOf('undefined') > -1) return <Icon name="star" color="#fff" style={{ height: 16, width: 16, backgroundColor: '#a6dbff', fontSize: 16, borderRadius: 8 }} />
      return <Image style={{ height: 16, width: 16, borderRadius: 8, backgroundColor: '#a6dbff' }} source={{ uri: src }} />
    }
    return <Icon name="fire" color="#fff" style={{ height: 16, width: 16, backgroundColor: '#a6dbff', fontSize: 16, borderRadius: 8 }} />
  }

  render() {
    const { item, showTask, status, canDelete } = this.props
    const showAction = item.fields && item.fields.length !== 0
    return (
      <View style={{ margin: 10, backgroundColor: '#fff' }}>
        <TouchableOpacity style={{ alignItems: 'center', marginHorizontal: 18, flexDirection: 'row', justifyContent: 'flex-start', height: 45 }} onPress={this.goAppForm}>
          <Text style={{ fontSize: 16, color: '#1b1b1b', flex: 1, fontWeight: '600' }} numberOfLines={1}>{item.title}</Text>
          {(showTask && showAction) || canDelete
            ? (
              <TouchableOpacity style={{ height: 45, paddingLeft: 10, justifyContent: 'center', opacity: 0.6 }} onPress={this.showActionSheet}>
                <Image source={require('../../images/icons-more-dot-default.png')} />
              </TouchableOpacity>
            )
            : null}
        </TouchableOpacity>
        <View style={{ padding: 18, borderColor: '#f2f2f2', borderTopWidth: 1, borderBottomWidth: 1 }}>
          <View style={{ height: 36, flexDirection: 'row', justifyContent: 'flex-start' }}>
            <Text style={{ fontSize: 12, lineHeight: 16, color: '#1b1b1b', opacity: 0.45, width: 50 }}>参与人</Text>
            <ScrollView style={{ flex: 1, height: 16 }} horizontal>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
                {this.headImage({ src: item.instStartUserHeadImg })}
                <Text style={{ marginLeft: 5, fontSize: 12, color: '#000', opacity: 0.85 }}>{item.instStartUserName}</Text>
              </View>
              {(item.doneUsers || []).map((users, i) => (
                <View style={{ marginLeft: 16, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }} key={i}>
                  {users.map((user, j) => (
                    <View style={{ marginLeft: -4 }} key={j}>{this.headImage({ src: user.headImage })}</View>
                  ))}
                  <Text style={{ marginLeft: 5, fontSize: 12, color: '#000', opacity: 0.85 }}>{users.length === 1 ? users[0].userName : '多人'}</Text>
                </View>
              ))}
            </ScrollView>
          </View>
          <View style={{ height: 16, flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
            <Text style={{ fontSize: 12, color: '#1b1b1b', opacity: 0.45, width: 50 }}>时间</Text>
            {showTask
              ? (
                <Text style={{ fontSize: 12, color: '#000', opacity: 0.85 }} numberOfLines={1}>
                  {item.taskStartTime}
                  {item.taskEndTime ? ` ~ ${item.taskEndTime}` : ''}
                </Text>
              )
              : (
                <Text style={{ fontSize: 12, color: '#000', opacity: 0.85 }} numberOfLines={1}>
                  {item.instStartDate}
                  {item.instEndDate ? ` ~ ${item.instEndDate}` : ''}
                </Text>
              )}
          </View>
        </View>
        <TouchableOpacity onPress={this.showWfDiagram} style={{ alignItems: 'center', height: 40, paddingHorizontal: 18, flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 16, color: '#262626', opacity: 0.65, fontWeight: '600' }}>查看流程图</Text>
          <Image source={require('../../images/icon-arrow-right-small-press.png')} />
        </TouchableOpacity>
      </View>
    )
  }
}

export default ListItem
