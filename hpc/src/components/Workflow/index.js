import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { DeviceEventEmitter, View, TouchableOpacity, ActivityIndicator, Text, FlatList, Image, Dimensions, Platform } from 'react-native'
import { connect } from 'react-redux'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import { GO_MODAL } from '../../utils/jump'
import { refreshCurrDatas } from '../../utils/event/DeviceEventEmitter'
import SideMenu from '../SideMenu'
import { getWorkflowDonelist, getWorkflowTodolist, getWorkflowStatusList } from './Store'
import styles from './styles'
import ListItem from './ListItem'
import WfFilterPage from './WfFilterPage'

const isTrueApp = appId => !!(appId && appId !== '0' && appId !== 0)
const deviceHeight = Dimensions.get('window').height
const platformFixHeight = Platform.OS === 'android' ? 24 : 0
const filterImg = require('../../images/icons-filter-press-default.png')

class Workflow extends Component {
  constructor(props) {
    super(props)
    this.wfdatas = {
      page: 0,
      pageSize: 10,
      category: '全部',
      status: 'UNFINISHED',
    }
    this.state = {
      showTask: true,
      workflowDatas: [],
      totalCount: 0,
      status: 'todo',
      currentAppId: props.currentAppId,
      hasMore: true,
      isRefreshing: false,
      canDelete: false,
    }
  }

  componentDidMount = async () => {
    const { currentAppId } = this.state
    if (isTrueApp(currentAppId)) {
      this.refreshWfDatas()
    }
    this.subscription = DeviceEventEmitter.addListener(refreshCurrDatas, (flag) => {
      if (flag && this.state.showTask && this.state.status === 'todo') {
        this.refreshWfDatas()
      }
    })
  }

  componentWillUnmount() {
    this.subscription.remove()
  }

  // 刷新数据
  refreshWfDatas = async () => {
    const { currentAppId, status, showTask } = this.state
    this.wfdatas.page = 0
    this.setState({
      isRefreshing: true,
    })
    let result = {}
    let data = []
    if (showTask) {
      result = status === 'todo' ? await getWorkflowTodolist(currentAppId, this.wfdatas) : await getWorkflowDonelist(currentAppId, this.wfdatas)
      data = (status === 'todo' ? result.todoTaskList : result.doneTaskList) || []
    } else {
      result = await getWorkflowStatusList(currentAppId, this.wfdatas)
      data = result.startInstList || []
    }
    this.setState({
      workflowDatas: data,
      totalCount: result.listCount || 0,
      isRefreshing: false,
      canDelete: result.canDelete,
    })
  }
  loadMoreWfDatas = async () => {
    const { workflowDatas, currentAppId, status, showTask, totalCount } = this.state
    this.setState({
      isRefreshing: true,
    })
    let result = {}
    let data = []
    if (showTask) {
      result = status === 'todo' ? await getWorkflowTodolist(currentAppId, this.wfdatas) : await getWorkflowDonelist(currentAppId, this.wfdatas)
      data = (status === 'todo' ? result.todoTaskList : result.doneTaskList) || []
    } else {
      result = await getWorkflowStatusList(currentAppId, this.wfdatas)
      data = result.startInstList || []
    }
    this.setState({
      workflowDatas: workflowDatas.concat(data),
      totalCount: result.listCount || totalCount,
      isRefreshing: false,
    })
    this.listloadmoreLock = false
  }
  loadmoreList = () => {
    const { workflowDatas, totalCount } = this.state
    this.setState({ hasMore: true })
    if (workflowDatas.length < totalCount) {
      if (this.listloadmoreLock) {
        return
      }
      this.listloadmoreLock = true
      this.loadMoreWfDatas()
    } else {
      this.setState({ hasMore: false })
    }
  }
  selectTab = (flag) => {
    if (flag !== this.state.showTask) {
      this.wfdatas.status = 'UNFINISHED'
      this.setState({ status: 'todo' })
    }
    this.setState({
      showTask: flag,
    }, () => {
      this.refreshWfDatas()
    })
  }
  changeStatus = (newstatus) => {
    if (newstatus === this.state.status) {
      return
    }
    this.wfdatas.status = newstatus === 'todo' ? 'UNFINISHED' : 'FINISHED'
    this.setState({
      status: newstatus,
    }, () => {
      this.refreshWfDatas()
    })
  }

  gowfFilter = () => {
    if (!this.isclick) {
      this.isclick = true
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfFilterPage
          wfStatus={this.wfdatas.status}
          currentAppId={this.props.currentAppId}
          canDelete={this.state.canDelete}
          status={this.state.status}
          showTask={this.state.showTask}
          changeClick={() => { this.isclick = false }}
        />,
        opacity: 0.5,
      })
    }
  }

  renderItem = ({ item, index }) => (
    <ListItem item={item} key={index} canDelete={this.state.canDelete} refreshWfDatas={this.refreshWfDatas} status={this.state.status} showTask={this.state.showTask} />
  )

  ListFooterComponent = () => {
    if (this.state.totalCount < 1) {
      return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>暂无数据</Text></View>
    } else if (!this.state.hasMore) {
      return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>没有更多了</Text></View>
    } else if (this.listloadmoreLock) {
      return <View style={{ paddingVertical: 20 }}><ActivityIndicator size="large" /></View>
    }
    return null
  }

  centerText = () => (
    <View style={styles.centerText}>
      <TouchableOpacity
        style={[styles.touchBut, styles.touchLeftBut, { backgroundColor: this.state.showTask ? '#F8F7F7' : '#fff' }]}
        onPress={() => this.selectTab(true)}
        activeOpacity={0.8}
      >
        <Text style={{ fontSize: 12, color: '#41454B' }} numberOfLines={1}>任务</Text>
      </TouchableOpacity>
      <View style={{ backgroundColor: '#E5E5E5', width: 1 }} />
      <TouchableOpacity
        style={[styles.touchBut, styles.touchRightBut, { backgroundColor: this.state.showTask ? '#fff' : '#F8F7F7' }]}
        onPress={() => this.selectTab(false)}
        activeOpacity={0.8}
      >
        <Text style={{ fontSize: 12, color: '#41454B' }} numberOfLines={1}>流程</Text>
      </TouchableOpacity>
    </View>
  )

  render() {
    const { workflowDatas, status } = this.state
    return (
      <View style={{ height: deviceHeight - platformFixHeight }}>
        <FormHeader
          isShowBack={false}
          centerText={this.centerText()}
          rightView={<View />}
          toggleMenu={this.props.toggleMenu}
        />
        <View style={styles.statusView}>
          <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity onPress={() => this.changeStatus('todo')} style={{ justifyContent: 'center', paddingHorizontal: 10, marginRight: 10, borderBottomWidth: 2, borderBottomColor: status === 'todo' ? '#17A9FF' : '#fff' }}>
              <Text>{this.state.showTask ? '待办' : '进行中'}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.changeStatus('done')} style={{ justifyContent: 'center', paddingHorizontal: 10, borderBottomWidth: 2, borderBottomColor: status === 'done' ? '#17A9FF' : '#fff' }}>
              <Text>{this.state.showTask ? '已办' : '已完成'}</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={this.gowfFilter} style={{ justifyContent: 'center', paddingHorizontal: 10 }}>
            <Image source={filterImg} />
          </TouchableOpacity>
        </View>
        <View style={{ flex: 1, backgroundColor: '#F8F7F7' }}>
          <FlatList
            data={workflowDatas}
            renderItem={this.renderItem}
            extraData={this.state}
            onRefresh={this.refreshWfDatas}
            refreshing={this.state.isRefreshing}
            onEndReached={this.loadmoreList}
            onEndReachedThreshold={0.1}
            ListFooterComponent={this.ListFooterComponent}
          />
        </View>
      </View>
    )
  }
}

const mapStateToProps = () => ({

})
const mapDispatchToProps = () => ({

})

Workflow.propTypes = {
  toggleMenu: PropTypes.func,
  currentAppId: PropTypes.string,
}

export default connect(mapStateToProps, mapDispatchToProps)(SideMenu(Workflow))
