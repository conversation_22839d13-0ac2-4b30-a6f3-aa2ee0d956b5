import PropTypes from 'prop-types'
import React from 'react'
import { View, BackHandler, Text, TextInput, FlatList, Image, TouchableOpacity, ScrollView } from 'react-native'
import { Toast } from '@ant-design/react-native'
import { Switch as AntdSwitch } from 'nagz-mobile-lib'
import { screenHeight, screenWidth, deviceType } from '../../config/sysPara'
import NavigationService from '../../NavigationService'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import SideMenu from '../SideMenu'
import { getDoneTasks } from './Store'
import { Picker } from '../../lib/Picker'

const prevIcon = require('../../images/icon-arrow-previous-default.png')
const menuIcon = require('../../images/icons-burger-outline-line-default.png')
const checkIcon = require('../../images/icon-radio-checked-default.png')

const wrapstyle = {
  backgroundColor: '#fff',
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
}

class WfappointBack extends React.PureComponent {
  state = {
    backnode: {},
    isConfim: '',
    isRepeat: false,
    wfComment: false,
    comments: '',
    doneTasks: [],
    selectnode: '',
  }

  componentDidMount() {
    this.setState({ wfComment: this.props.wfComment, isConfim: this.props.wfComment })
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
    this.loadDonTask()
  }
  loadDonTask= async () => {
    const { appId, formId, dataId, innerOpr, formActionSheet } = this.props
    if (innerOpr.defaultValue === 'recall' && !formActionSheet) {
      const doneTasks = await getDoneTasks(appId, formId, dataId)
      if (!doneTasks.length) {
        return Toast.fail(`当前没有可${innerOpr.name}的节点`)
      }
      this.setState({ doneTasks, selectnode: doneTasks[0].nodeId })
    }
  }
  componentWillUnmount() {
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    }
  }

  goBack = () => {
    if (this.state.isConfim === 'wfComment') {
      this.setState({ wfComment: false, isConfim: 'wfBack' })
      return true
    }
    NavigationService.back()
    return true
  }

  changeComment = (e) => {
    this.setState({ comments: e.nativeEvent.text })
  }
  confimChange = () => {
    const { isConfim, comments, backnode, isRepeat, doneTasks, selectnode } = this.state
    if (isConfim === 'wfBack') {
      this.setState({ wfComment: true, isConfim: 'wfComment' }, () => {
        setTimeout(() => {
          this.modalInput.focus()
          this.scrollRef.scrollToEnd()
        }, 300)
      })
    } else {
      if (this.props.item.nagz_commentsRequired && !comments) return Toast.info('审批意见不能为空')
      let doneTask = {}
      if (this.props.innerOpr.defaultValue === 'recall') {
        const selectTask = doneTasks.find(d => d.nodeId === selectnode)
        if (!this.props.formActionSheet) {
          doneTask = { taskId: selectTask.taskId }
        } else {
          doneTask = selectTask
        }
      }
      this.props.doOperation({ comments, backnode, isRepeat, doneTask })
      this.props.closeModal()
    }
  }

  leftView = () => this.state.wfComment ?
    (
      <TouchableOpacity onPress={this.goBack}>
        <Text style={{ fontSize: 18, color: '#41454B' }}>返回</Text>
      </TouchableOpacity>
    ) : (
      <View style={{ height: '100%', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
        <TouchableOpacity onPress={this.goBack}>
          <Image source={prevIcon} />
        </TouchableOpacity>
        <TouchableOpacity style={{ marginLeft: 5 }} onPress={this.props.openMenu}>
          <Image source={menuIcon} />
        </TouchableOpacity>
      </View>
    )
  centerText = () => {
    if (this.state.wfComment) {
      return '审批意见'
    }
    return '指定要驳回的节点'
  }
  rightView = () => {
    if (this.state.isConfim) {
      return (
        <TouchableOpacity onPress={this.confimChange} >
          <Text style={{ fontSize: 18, color: '#41454B' }}>确定</Text>
        </TouchableOpacity>
      )
    }
    return undefined
  }

  changeSize = () => {
    if (this.state.wfComment) {
      this.scrollRef.scrollToEnd()
    }
  }
  scrollRefHandle = ref => this.scrollRef = ref
  onSelectClick=() => {}
  renderItem = ({ item, index }) => (
    <TouchableOpacity style={{ backgroundColor: '#fff', paddingHorizontal: 18 }} activeOpacity={0.8} key={item.nodeId} onPress={() => this.setState({ backnode: item, isConfim: 'wfBack' })}>
      <View style={{ ...wrapstyle, borderBottomWidth: index === this.props.backHisNodes.length - 1 ? 0 : 1, borderBottomColor: '#e5e5e5' }}>
        <Text style={{ lineHeight: 50, fontWeight: 'bold', color: this.state.backnode.nodeId === item.nodeId ? '#0091FF' : '#1B1B1B' }}>{item.name}</Text>
        {this.state.backnode.nodeId === item.nodeId && <Image source={checkIcon} />}
      </View>
    </TouchableOpacity>
  )
  render() {
    const { wfComment, doneTasks } = this.state
    const { backHisNodes, currentHeight, innerOpr, formActionSheet } = this.props
    let opt = []
    let selectn = {}
    if (innerOpr.defaultValue === 'recall' && !formActionSheet) {
      opt = doneTasks.map(dt => ({ label: dt.name, value: dt.nodeId }))
      selectn = opt.find(o => o.value === this.state.selectnode) || {}
    }
    return (
      <View style={{ flex: 1, height: screenHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
        <FormHeader
          centerText={this.centerText()}
          leftView={this.leftView()}
          rightView={this.rightView()}
        />
        {innerOpr.defaultValue === 'recall' && !formActionSheet &&
        <View style={{ marginTop: 5, marginBottom: 5 }}>
          <Picker
            data={opt}
            cols={1}
            label="指定节点"
            isRequire
            onOk={(v) => { this.setState({ selectnode: v[0] }) }}
            value={[this.state.selectnode]}
          >
            <Text
              numberOfLines={1}
              style={{ width: '100%', fontSize: 14, color: selectn.label ? '#1B1B1B' : '#BCBCBB', textAlign: 'right' }}
            >{selectn.label || '请选择'}
            </Text>
          </Picker>
        </View>
        }
        {!wfComment &&
          <View style={{ height: currentHeight - 74 }}>
            <View style={{ flex: 1 }}>
              <FlatList
                data={backHisNodes}
                keyExtractor={i => i.nodeId}
                ListFooterComponent={null}
                removeClippedSubviews={false}
                renderItem={this.renderItem}
                style={{ borderTopWidth: 1, borderTopColor: '#e5e5e5', marginVertical: 10 }}
              />
            </View>
            <View style={{ ...wrapstyle, paddingHorizontal: 18 }}>
              <Text numberOfLines={1} style={{ lineHeight: 50, color: '#1b1b1b', fontWeight: 'bold' }}>重走流程</Text>
              <AntdSwitch
                checked={this.state.isRepeat}
                onChange={v => this.setState({ isRepeat: v })}
              />
            </View>
          </View>
        }

        {wfComment &&
          <ScrollView ref={this.scrollRefHandle} onLayout={this.changeSize} style={{ height: currentHeight - 74, backgroundColor: '#fff', paddingVertical: 10 }}>
            <TextInput
              autoFocus
              ref={input => this.modalInput = input}
              autoCorrect={false}
              style={{ flex: 1, marginHorizontal: 20, textAlignVertical: 'top', color: '#1b1b1b', fontSize: 14, lineHeight: 21 }}
              value={this.state.comments}
              onChange={this.changeComment}
              placeholder="请输入"
              underlineColorAndroid="transparent"
              placeholderTextColor="#bbb"
              multiline
            />
          </ScrollView>}
      </View>
    )
  }
}

WfappointBack.propTypes = {
  doOperation: PropTypes.func,
  item: PropTypes.object,
  innerOpr: PropTypes.object,
}

export default SideMenu(WfappointBack)

