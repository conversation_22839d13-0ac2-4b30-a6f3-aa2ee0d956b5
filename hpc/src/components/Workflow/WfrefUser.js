import PropTypes from 'prop-types'
import React from 'react'
import { View, BackHandler, Text, TextInput, FlatList, Image, TouchableOpacity, Animated, Keyboard, ScrollView } from 'react-native'
import { Toast } from '@ant-design/react-native'
import { Button } from 'nagz-mobile-lib'
import { screenHeight, screenWidth, deviceType } from '../../config/sysPara'
import NavigationService from '../../NavigationService'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import SideMenu from '../SideMenu'
import DataTable from '../DataTable'
import { getTreeData } from './Store'

const prevIcon = require('../../images/icon-arrow-previous-default.png')
const menuIcon = require('../../images/icons-burger-outline-line-default.png')
const searchButImg = require('../../images/icons-magnifier-search-gray.png')

const wrapstyle = {
  backgroundColor: '#fff',
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
}
const selectUserStyles = {
  searchBut: {
    height: 32,
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#F2F2F2',
  },
  searchAnimated: {
    height: 32,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
}

class WfrefUser extends React.PureComponent {
  constructor(props) {
    super(props)
    this.hisTreeIndex = -1
    this.hisTreeNode = []
    this.state = {
      isSearch: false,
      searchImgLeft: new Animated.Value((screenWidth / 2) - 90),
      treeData: [],
      filterData: '',
      isConfim: 'depart',
      wfComment: false,
      comments: '',
      delegateUser: [],
    }
  }

  componentWillMount = async () => {
    const treeData = await getTreeData(this.props.item.appId)
    this.setState({ treeData })
    this.treeFieldProps = {
      valueColumn: true,
      outputValue: '1',
      selectMode: '0',
    }
  }

  componentDidMount() {
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide)
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
  }

  componentWillUnmount() {
    this.keyboardDidHideListener.remove()
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', () => true)
    }
  }

  onClickDepart = (treeNode) => {
    const { id, value, display, children } = treeNode
    this.depart = display
    const vals = []
    let isConfim = ''
    if (children) {
      this.hisTreeNode.push(this.state.treeData)
      if (this.treeFieldProps.outputValue === '1') {
        const getValue = (data) => {
          data.forEach((i) => {
            vals.push(this.treeFieldProps.valueColumn ? i.value : i.id)
            if (i.children) {
              getValue(i.children)
            }
          })
        }
        getValue([treeNode])
      }
      isConfim = 'depart'
      this.setState({ treeData: children })
    } else {
      const val = this.treeFieldProps.valueColumn ? value : id
      vals.push(val)
      isConfim = 'refUser'
    }
    this.changeFilter(vals)
    this.setState({ isConfim, treekey: '' })
  }

  changeFilter = (v) => {
    const matchTreefield = 'depart'
    const opt = v.length < 2 ? 'eq' : 'in'
    const value = v.join(',')
    const filterData = value ? `${matchTreefield} ${opt} ${value}` : ''
    this.setState({ filterData })
  }

  keyboardDidHide = () => {
    if (!this.searchValue) {
      this.setState({
        isSearch: false,
      }, () => {
        Animated.timing(this.state.searchImgLeft, {
          toValue: (screenWidth / 2) - 90,
          duration: 100,
        }).start()
      })
    }
  }

  goBack = () => {
    if (this.state.wfComment) {
      this.setState({ wfComment: false, isConfim: 'refUser' })
      return true
    } else if (this.state.isConfim === 'refUser') {
      this.searchValue = ''
      this.depart = ''
      this.keyboardDidHide()
      this.setState({ isConfim: 'depart', filterData: '' })
      return true
    } else if (this.hisTreeNode.length && this.state.isConfim === 'depart') {
      this.hisTreeIndex = this.hisTreeNode.length - 1
      const treeData = this.hisTreeNode[this.hisTreeIndex]
      this.hisTreeNode = this.hisTreeNode.slice(0, this.hisTreeIndex)
      this.setState({ treeData })
      return true
    }
    NavigationService.back()
    return true
  }

  changeComment = (e) => {
    this.setState({ comments: e.nativeEvent.text })
  }
  confimChange = () => {
    const { isConfim, comments, delegateUser = [] } = this.state
    if (isConfim === 'depart') {
      this.setState({ isConfim: 'refUser', treekey: '' })
    } else if (isConfim === 'refUser') {
      this.setState({ wfComment: true, isConfim: 'wfComment' }, () => {
        setTimeout(() => {
          this.modalInput.focus()
          this.scrollRef.scrollToEnd()
        }, 300)
      })
    } else {
      const delegateUserId = delegateUser.map(d => d.value).join(',')
      if (this.props.item.nagz_commentsRequired && !comments) return Toast.info('审批意见不能为空', 1)
      this.props.doOperation({ comments, delegateUserId })
      this.props.closeModal()
    }
  }

  searchBut = () => {
    this.setState({
      isSearch: true,
    }, () => {
      Animated.timing(this.state.searchImgLeft, {
        toValue: 10,
        duration: 100,
      }).start()
      this.searchInput.focus()
    })
  }
  searchFun = (e) => {
    const value = e.nativeEvent.text
    this.searchValue = value
    const filterData = value ? `name like ${value} or depart.display like ${value}` : ''
    this.setState({ filterData, isConfim: 'refUser', treekey: '' })
  }

  selectRow = (rowData) => {
    if (this.props.filterPage) {
      this.props.filterUser({
        label: rowData.name.display,
        value: rowData.id.value,
      })
      return this.props.closeModal()
    }
    this.setState({
      delegateUser: [{
        label: rowData.name.display,
        value: rowData.id.value,
      }],
      isConfim: 'refUser',
      treekey: rowData.id.value,
    })
  }
  multiSelectFn=(selectValue) => {
    this.setState({
      delegateUser: selectValue.map(rowData => ({ label: rowData.name.display, value: rowData.id.value })),
      isConfim: 'refUser',
    }, () => { this.confimChange() })
  }
  leftView = () => this.state.wfComment ?
    (
      <TouchableOpacity onPress={this.goBack}>
        <Text style={{ fontSize: 18, color: '#41454B' }}>返回</Text>
      </TouchableOpacity>
    ) : (
      <View style={{ height: '100%', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
        <TouchableOpacity style={{}} onPress={this.goBack}>
          <Image source={prevIcon} />
        </TouchableOpacity>
        <TouchableOpacity style={{ marginLeft: 5 }} onPress={this.props.openMenu}>
          <Image source={menuIcon} />
        </TouchableOpacity>
      </View>
    )
  centerText = () => {
    const { innerOpr, filterPage } = this.props
    if (filterPage) return '选择发起人'
    if (this.state.wfComment) {
      return '审批意见'
    }
    return innerOpr.defaultValue === 'delegate' ? '指定传阅人' : '指定交派人'
  }
  rightView = () => {
    if (this.state.isConfim && this.state.isConfim !== 'depart') {
      if (this.state.isConfim === 'refUser' && !this.state.treekey) return undefined
      return (
        <TouchableOpacity onPress={this.confimChange} >
          <Text style={{ fontSize: 18, color: '#41454B' }}>确定</Text>
        </TouchableOpacity>
      )
    }
    return undefined
  }

  changeSize = () => {
    if (this.state.wfComment) {
      this.scrollRef.scrollToEnd()
    }
  }
  scrollRefHandle = ref => this.scrollRef = ref

  renderItem = ({ item, index }) => (
    <TouchableOpacity style={{ backgroundColor: '#fff', paddingHorizontal: 18 }} activeOpacity={0.8} key={item.id} onPress={() => this.onClickDepart(item, index)}>
      <View style={{ ...wrapstyle, borderBottomWidth: index === this.state.treeData.length - 1 ? 0 : 1, borderColor: '#e5e5e5' }}>
        <Text style={{ lineHeight: 50, fontWeight: 'bold', color: '#1B1B1B' }}>{item.display}</Text>
        <Image style={{ transform: [{ rotate: '180deg' }] }} source={prevIcon} />
      </View>
    </TouchableOpacity>
  )
  render() {
    const { wfComment } = this.state
    const { item, currentHeight, innerOpr } = this.props
    return (
      <View style={{ flex: 1, height: screenHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
        <FormHeader
          centerText={this.centerText()}
          leftView={this.leftView()}
          rightView={this.rightView()}
        />
        {!wfComment &&
          <View style={{ height: currentHeight - 74 }}>
            <View style={{ height: 52, padding: 10, width: '100%' }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.searchBut}
              >
                <View style={selectUserStyles.searchBut}>
                  <Animated.View style={[selectUserStyles.searchAnimated, { left: this.state.searchImgLeft }]} >
                    <Image source={searchButImg} />
                    {this.state.isSearch ?
                      <TextInput
                        ref={(r) => { this.searchInput = r }}
                        style={{ width: '90%', marginLeft: 10, height: 20, padding: 0, borderColor: '#fff' }}
                        value={this.searchValue}
                        onChange={this.searchFun}
                        underlineColorAndroid="transparent"
                        onSubmitEditing={this.searchFun}
                      />
                      :
                      <Text style={{ fontSize: 16, color: '#A0A2A5', marginLeft: 4 }}>请输入员工或部门</Text>
                    }
                  </Animated.View>
                </View>
              </TouchableOpacity>
            </View>
            <View style={{ flex: 1 }}>
              {this.state.isConfim === 'depart' && <FlatList
                data={this.state.treeData}
                keyExtractor={i => i.id}
                ListFooterComponent={null}
                removeClippedSubviews={false}
                renderItem={this.renderItem}
                style={{ borderTopWidth: 1, borderTopColor: '#e5e5e5' }}
              />}
              {this.state.isConfim === 'refUser' && <DataTable
                appId={item.appId}
                formId="60102"
                dataId=""
                newFormId="LIST"
                colWidth
                valueData={[]}
                pagination={{}}
                query={this.state.filterData}
                operationarea={[]}
                titlecolumn=""
                searchcolumn={[]}
                displayColumns={['name', 'category', 'depart']}
                orders={{}}
                customerBack
                addDefaultValue=""
                isRefMulti={innerOpr.defaultValue === 'delegate'}
                showAction={innerOpr.defaultValue === 'delegate'}
                showOperators={innerOpr.defaultValue === 'delegate'}
                selectRow={innerOpr.defaultValue === 'delegate' ? undefined : this.selectRow}
                multiSelectFn={innerOpr.defaultValue === 'delegate' ? this.multiSelectFn : undefined}
                fieldId=""
                viewvisiblecolumn={['name', 'category', 'depart']}
                listType={innerOpr.defaultValue === 'delegate' ? 'refMulti' : 'reference'}
                isSubForm={false}
                extraParams=""
                tree
                treekey={this.state.treekey}
                isTab
              />}
            </View>
            {this.state.isConfim === 'depart' &&
              <View style={{ margin: 20, marginBottom: 10 }}>
                <Button
                  style={{ container: { height: 40, backgroundColor: '#17A9FF', borderRadius: 2 }, textContent: { fontSize: 14, color: '#fff' } }}
                  onPress={this.confimChange}
                >{this.depart ? `选择${this.depart}` : '选择所有部门'}
                </Button>
              </View>}
          </View>
        }

        {wfComment &&
          <ScrollView ref={this.scrollRefHandle} onLayout={this.changeSize} style={{ height: currentHeight - 74, backgroundColor: '#fff', paddingVertical: 10 }} keyboardShouldPersistTaps="always">
            <TextInput
              autoFocus
              ref={input => this.modalInput = input}
              autoCorrect={false}
              style={{ flex: 1, marginHorizontal: 20, textAlignVertical: 'top', color: '#1b1b1b', fontSize: 14, lineHeight: 21 }}
              value={this.state.comments}
              onChange={this.changeComment}
              placeholder="请输入"
              underlineColorAndroid="transparent"
              placeholderTextColor="#bbb"
              multiline
            />
          </ScrollView>}
      </View>
    )
  }
}

WfrefUser.propTypes = {
  doOperation: PropTypes.func,
  item: PropTypes.object,
  innerOpr: PropTypes.object,
}

export default SideMenu(WfrefUser)

