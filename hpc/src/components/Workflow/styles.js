import { StyleSheet } from 'react-native'
import { screenWidth } from '../../config/sysPara'

const styles = StyleSheet.create({
  centerText: {
    width: 160,
    height: 30,
    borderColor: '#E5E5E5',
    borderWidth: 1,
    borderRadius: 6,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  touchBut: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchLeftBut: {
    width: 78,
    borderBottomLeftRadius: 6,
    borderTopLeftRadius: 6,
  },
  touchRightBut: {
    width: 79,
    borderBottomRightRadius: 6,
    borderTopRightRadius: 6,
  },
  statusView: {
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 50,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#e5e5e5',
  },
  headerRight: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  headerSearchBut: {
    margin: 0,
    width: screenWidth - 32,
    height: 30,
    position: 'absolute',
    backgroundColor: '#fff',
    borderColor: '#E5E5E5',
    zIndex: 30,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
  },
  searchInput: {
    flex: 1,
    padding: 0,
    paddingLeft: 10,
    paddingRight: 10,
    fontSize: 16,
  },
})

export default styles
