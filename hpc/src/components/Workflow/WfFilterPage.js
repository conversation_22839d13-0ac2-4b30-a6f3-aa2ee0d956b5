import PropTypes from 'prop-types'
import React from 'react'
import { BackHandler, View, TouchableOpacity, ActivityIndicator, Text, FlatList, Image, TextInput, ScrollView } from 'react-native'
import moment from 'moment'
import { Button } from 'nagz-mobile-lib'
import { DatePicker } from '../../lib/DatePicker'
import { FormHeader } from '../../addons/FormComponent/ModalHeader'
import DateModal from '../../addons/FormComponent/DateTime/DateModal'
import { screenHeight, screenWidth, deviceType } from '../../config/sysPara'
import { GO_MODAL } from '../../utils/jump'
import { getWorkflowDonelist, getWorkflowTodolist, getWorkflowStatusList, getWorkflowCategory } from './Store'
import ListItem from './ListItem'
import styles from './styles'
import WfrefUser from './WfrefUser'

const platformFixHeight = deviceType === 1 ? 24 : 0
const searchButImg = require('../../images/icons-magnifier-search-default.png')

const contents = [
  // {
  //   key: 'initiator',
  //   label: '发起人',
  // },
  {
    key: 'time',
    label: '时间',
  },
  {
    key: 'category',
    label: '类型',
  },
]

class WfFilterPage extends React.PureComponent {
  constructor(props) {
    super(props)
    this.wfdatas = {
      page: 0,
      pageSize: 10,
      category: '全部',
      status: 'UNFINISHED',
    }
    this.state = {
      workflowDatas: [],
      categorys: [],
      totalCount: 0,
      hasMore: true,
      isRefreshing: false,
      canDelete: false,
      showTask: true,
      showList: false,
      showFilter: false,
    }
  }

  componentDidMount = async () => {
    const categorys = await getWorkflowCategory(this.props.currentAppId)
    this.wfdatas = {
      ...this.wfdatas,
      status: this.props.wfStatus,
    }
    this.setState({
      currentAppId: this.props.currentAppId,
      showTask: this.props.showTask,
      status: this.props.status,
      canDelete: this.props.canDelete,
      categorys: ['全部', ...categorys],
    })
    if (deviceType === 1) {
      BackHandler.addEventListener('hardwareBackPress', this.goBack)
    }
  }

  componentWillUnmount() {
    if (deviceType === 1) {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack)
    }
  }

  goBack = () => {
    this.onSearch(false)
    return true
  }

  // 刷新数据
  refreshWfDatas = async () => {
    const { currentAppId, status, showTask } = this.state
    this.wfdatas.page = 0
    this.setState({
      isRefreshing: true,
    })
    let result = {}
    let data = []
    if (showTask) {
      result = status === 'todo' ? await getWorkflowTodolist(currentAppId, this.wfdatas) : await getWorkflowDonelist(currentAppId, this.wfdatas)
      data = (status === 'todo' ? result.todoTaskList : result.doneTaskList) || []
    } else {
      result = await getWorkflowStatusList(currentAppId, this.wfdatas)
      data = result.startInstList || []
    }
    this.setState({
      workflowDatas: data,
      totalCount: result.listCount || 0,
      isRefreshing: false,
    })
  }
  loadMoreWfDatas = async () => {
    const { workflowDatas, currentAppId, status, showTask, totalCount } = this.state
    this.setState({
      isRefreshing: true,
    })
    let result = {}
    let data = []
    if (showTask) {
      result = status === 'todo' ? await getWorkflowTodolist(currentAppId, this.wfdatas) : await getWorkflowDonelist(currentAppId, this.wfdatas)
      data = (status === 'todo' ? result.todoTaskList : result.doneTaskList) || []
    } else {
      result = await getWorkflowStatusList(currentAppId, this.wfdatas)
      data = result.startInstList || []
    }
    this.setState({
      workflowDatas: workflowDatas.concat(data),
      totalCount: result.listCount || totalCount,
      isRefreshing: false,
    })
    this.listloadmoreLock = false
  }
  loadmoreList = () => {
    const { workflowDatas, totalCount } = this.state
    this.setState({ hasMore: true })
    if (workflowDatas.length < totalCount) {
      if (this.listloadmoreLock) {
        return
      }
      this.listloadmoreLock = true
      this.loadMoreWfDatas()
    } else {
      this.setState({ hasMore: false })
    }
  }

  filterFunc = (data, key) => {
    this.wfdatas = {
      ...this.wfdatas,
      ...data,
    }
    this.setState({ showList: true, showFilter: false, searchValue: data[key] || this.state.searchValue })
    this.refreshWfDatas()
  }

  onchange = (text) => {
    this.setState({ searchValue: text }, () => {
      this.onSearch(true)
    })
  }
  onSearch = (flag) => {
    const { searchValue, showList, showFilter } = this.state
    this.wfdatas = {
      page: 0,
      pageSize: 10,
      category: '全部',
      status: this.props.wfStatus,
    }
    if (flag) {
      this.wfdatas.qcond = searchValue
      if (!searchValue) {
        this.setState({ showList: false, showFilter: false })
      } else {
        this.setState({ showList: true, showFilter: false })
        this.refreshWfDatas()
      }
    } else if ((searchValue && showList) || showFilter) {
      this.setState({ showList: false, searchValue: '', showFilter: false })
    } else {
      this.props.closeModal()
    }
    this.props.changeClick()
  }

  filterUser = (data) => {
    this.wfdatas = {
      ...this.wfdatas,
      filter: 'FQR',
      actUserId: data.value,
    }
    this.setState({ showList: true, searchValue: data.label, startTime: null, endTime: null })
    this.refreshWfDatas()
  }
  quickFilter = (data) => {
    if (data.key === 'initiator') {
      GO_MODAL({
        isCustomGoBack: true,
        animationIn: 'slideInRight',
        animationOut: 'slideOutRight',
        children: <WfrefUser
          filterPage
          filterUser={this.filterUser}
          item={{ appId: this.state.currentAppId }}
        />,
        opacity: 0.5,
      })
    } else {
      this.setState({ searchValue: data.key === 'time' ? this.timeTit() : data.label, showFilter: data, startTime: null, endTime: null })
    }
  }

  formatDate = date => moment(date).format('YYYY-MM-DD')
  updateValue = (value, key) => {
    this.setState({ [key]: value })
  }
  updateModalDate = (timestamp) => {
    this.setState({ [this.state.visible]: new Date(timestamp) })
  }
  setTime = (date) => {
    const value = moment(date).format('YYYY-MM-DD')
    return value.indexOf('Invalid') > -1 ? '' : value
  }
  timeTit = () => {
    const { status, showTask } = this.state
    const value = showTask ? (status === 'todo' ? '任务开始时间' : '任务结束时间') :
      (this.wfdatas.status === 'UNFINISHED' ? '流程开始时间' : '流程结束时间')
    return value
  }
  filterContent = () => {
    const { showFilter, searchValue, categorys, startTime, endTime } = this.state
    if (showFilter.key === 'category') {
      return (
        <FlatList
          data={categorys}
          renderItem={({ item, index }) => (
            <TouchableOpacity style={{ backgroundColor: '#fff', paddingHorizontal: 18 }} key={index} onPress={() => this.filterFunc({ category: item }, 'category')}>
              <Text style={{ lineHeight: 50, fontWeight: 'bold', color: searchValue === item ? '#0091FF' : '#1B1B1B', borderBottomWidth: index === categorys.length - 1 ? 0 : 1, borderBottomColor: '#e5e5e5' }}>{item}</Text>
            </TouchableOpacity>
          )}
          ListFooterComponent={null}
          removeClippedSubviews={false}
          style={{ marginVertical: 10 }}
        />
      )
    }
    if (showFilter.key === 'time') {
      return (
        <View style={{ marginVertical: 10 }}>
          <DatePicker
            mode="date"
            format={this.formatDate}
            onChange={value => this.updateValue(value, 'startTime')}
            extra="YYYY-MM-DD"
            value={startTime}
            minDate={new Date(1900, 1, 1, 0, 0, 0)}
            maxDate={new Date(2050, 1, 1, 23, 59, 59)}
            label="开始"
            contentOnClick={() => this.setState({ visible: 'startTime', modalTime: startTime })}
          />
          <DatePicker
            mode="date"
            format={this.formatDate}
            onChange={value => this.updateValue(value, 'endTime')}
            extra="YYYY-MM-DD"
            value={endTime}
            minDate={new Date(1900, 1, 1, 0, 0, 0)}
            maxDate={new Date(2050, 1, 1, 23, 59, 59)}
            label="结束"
            contentOnClick={() => this.setState({ visible: 'endTime', modalTime: endTime })}
          />
          {this.state.visible && <DateModal closeModal={() => this.setState({ visible: false })} visible={!!this.state.visible} format="YYYY-MM-DD" value={this.state.modalTime} updateValue={this.updateModalDate} />}
          <View style={{ margin: 20, marginBottom: 10 }}>
            <Button
              style={{ container: { height: 40, backgroundColor: '#17A9FF', borderRadius: 2 }, textContent: { fontSize: 14, color: '#fff' } }}
              onPress={() => this.filterFunc({ startTime: this.setTime(startTime), endTime: this.setTime(endTime) }, 'time')}
            >确定
            </Button>
          </View>
        </View>
      )
    }
  }

  rightView = () => (
    <View style={styles.headerRight}>
      <View style={styles.headerSearchBut}>
        <TouchableOpacity
          onPress={() => this.onSearch(true)}
        >
          <Image source={searchButImg} />
        </TouchableOpacity>
        <TextInput
          value={this.state.searchValue}
          style={styles.searchInput}
          placeholder="搜索"
          onChangeText={this.onchange}
          // onBlur={() => this.onSearch(true)}
          underlineColorAndroid="transparent"
        />
        <TouchableOpacity
          onPress={() => this.onSearch(false)}
        >
          <Text style={{ fontSize: 18 }}>取消</Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  renderItem = ({ item, index }) => (
    <ListItem item={item} key={index} canDelete={this.state.canDelete} refreshWfDatas={this.refreshWfDatas} status={this.state.status} showTask={this.state.showTask} />
  )

  ListFooterComponent = () => {
    if (this.state.totalCount < 1) {
      return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>暂无数据</Text></View>
    } else if (!this.state.hasMore) {
      return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>没有更多了</Text></View>
    } else if (this.listloadmoreLock) {
      return <View style={{ paddingVertical: 20 }}><ActivityIndicator size="large" /></View>
    }
    return null
  }

  render() {
    const { workflowDatas, showList, showFilter } = this.state
    return (
      <View style={{ height: screenHeight - platformFixHeight, width: screenWidth, backgroundColor: '#f8f7f7' }}>
        <FormHeader
          isShowBack={false}
          leftView={<View />}
          centerText=""
          rightView={this.rightView()}
        />
        <View style={{ flex: 1, backgroundColor: '#F8F7F7' }}>
          {!showList && !showFilter ?
            <ScrollView style={{ paddingHorizontal: 16 }} keyboardShouldPersistTaps="always">
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingTop: 30 }}>
                <Text style={{ height: 1, flex: 1, backgroundColor: '#ccc' }} />
                <Text style={{ lineHeight: 32, marginHorizontal: 10 }}>搜索指定内容</Text>
                <Text style={{ height: 1, flex: 1, backgroundColor: '#ccc' }} />
              </View>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'flex-start' }}>
                {contents.map(data => (
                  <TouchableOpacity key={data.key} style={{ alignItems: 'center', width: (screenWidth - 80) / 3, borderColor: '#17A9FF', borderWidth: 1, borderRadius: 4, margin: 10 }} onPress={() => this.quickFilter(data)}>
                    <Text style={{ lineHeight: 32 }}>{data.key === 'time' ? this.timeTit(data) : data.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView> : showFilter ?
              this.filterContent() :
              <FlatList
                data={workflowDatas}
                renderItem={this.renderItem}
                extraData={this.state}
                onRefresh={this.refreshWfDatas}
                refreshing={this.state.isRefreshing}
                onEndReached={this.loadmoreList}
                onEndReachedThreshold={0.1}
                ListFooterComponent={this.ListFooterComponent}
                style={{ flex: 1 }}
              />}
        </View>
      </View>
    )
  }
}

WfFilterPage.propTypes = {
  closeModal: PropTypes.func,
  changeClick: PropTypes.func,
  item: PropTypes.object,
}

export default WfFilterPage

