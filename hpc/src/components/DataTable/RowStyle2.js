import React from "react";
import PropTypes from "prop-types";
import {
  Text,
  View,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  TouchableOpacity,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
} from "react-native";
import { Modal, SegmentedControl } from "@ant-design/react-native";
import get from "lodash/get";
import {
  isPictureComponent,
  isSinglePictureComponent,
  isNumberComponent,
  isFormStatusComponent,
} from "../../addons/constants";
import { rowStyle, tableListto as style, tagColor } from "./style";
import filterImg from "../../images/icon-grid-filter.png";
import sortImg from "../../images/icon-grid-sort.png";
import arrowRightPress from "../../images/icon-arrow-next.png";
import arrowSmallPress from "../../images/icon-arrow-right-small-press.png";
import checkboxUnDefault from "../../images/icon-checkbox-un_checked-default.png";
import checkboxDefault from "../../images/icon-checkbox-checked-default.png";
import arrowBack from "../../images/icon-arrow-previous-default.png";
const { width } = Dimensions.get("window");

const segemntedArray = { 升序: "asc", 降序: "desc" };

class RowStyle2 extends React.PureComponent {
  static propTypes = {
    extraData: PropTypes.shape({ showChecked: PropTypes.bool }),
    fields: PropTypes.arrayOf(PropTypes.object),
    datas: PropTypes.arrayOf(PropTypes.object),
    columns: PropTypes.arrayOf(PropTypes.object),
    onEndReached: PropTypes.func,
    showChecked: PropTypes.bool,
    ListFooterComponent: PropTypes.func,
    mColWidth: PropTypes.object,
    onScrollBeginDrag: PropTypes.func,
    selectedRows: PropTypes.arrayOf(PropTypes.object),
    isReference: PropTypes.bool,
    onClickRow: PropTypes.func,
    getDisplayCol: PropTypes.func,
  };
  state = {
    showColumns: [],
    tableWidth: width,
    offsetX: 0,
    selectedRows: this.props.selectedRows,
    showChecked: this.props.showChecked,
    filterCheck: "",
    sortCheck: "",
    filterConfig: new Map(),
    sortConfig: new Map(),
    modalMode: "",
    modalInputValue: "",
    modalSegementValue: "",
    pageIndex: 0,
  };
  componentWillMount = () => {
    if (this.props.fields.length !== 0 && this.props.columns.length !== 0) {
      this.changeTableWidth(this.props.fields, this.props.columns);
    }
  };
  componentWillReceiveProps = ({
    fields,
    columns,
    showChecked,
    selectedRows,
  }) => {
    if (
      fields.length !== 0 &&
      columns.length !== 0 &&
      (fields.length !== this.props.fields.length ||
        columns !== this.props.columns)
    ) {
      this.changeTableWidth(fields, columns);
    }
    if (showChecked !== this.props.showChecked) {
      this.setState({
        showChecked,
        tableWidth: this.state.tableWidth + (showChecked ? +50 : -50),
      });
    }
    if (selectedRows !== this.props.selectedRows) {
      this.setState({ selectedRows });
    }
  };

  changeTableWidth = (fields, columns) => {
    let showColumns = columns.map((fid) => {
      const columnID = Object.keys(fid)[0];
      const field = fields.find((f) => f.id === columnID);
      if (field) {
        if (this.props.mColWidth && this.props.mColWidth[field.id]) {
          return {
            fid: field.id,
            width: this.props.mColWidth[field.id],
            title: fid[columnID],
          };
        }
        if (
          isPictureComponent(field) ||
          isSinglePictureComponent(field) ||
          field.displaytype === "SinglePicture" ||
          field.displaytype === "Picture"
        ) {
          return { fid: field.id, width: 60, title: fid[columnID] };
        } else if (isNumberComponent(field)) {
          return { fid: field.id, width: 70, title: fid[columnID] };
        }
        return { fid: field.id, width: 140, title: fid[columnID] };
      }
      return { fid: columnID, width: 0, title: fid[columnID] };
    });
    let tableWidth = showColumns.reduce((res, c) => c.width + res, 0);
    if (tableWidth < width && showColumns.length !== 0) {
      showColumns = showColumns.map((c) => ({
        ...c,
        width: c.width + (width - tableWidth) / showColumns.length,
      }));
      tableWidth = width;
    }
    this.setState({ showColumns, tableWidth });
  };
  selectOperColHandle = (type, field, value) => {
    this.setState({
      [type]: field,
      pageIndex: 1,
      modalInputValue: value || "",
      modalSegementValue: value || "",
    });
  };
  addFilterOrSort = () => {
    const {
      modalInputValue,
      modalSegementValue,
      filterCheck,
      sortCheck,
      filterConfig,
      sortConfig,
      modalMode,
    } = this.state;
    if (modalMode === "filter") {
      filterConfig.set(filterCheck, modalInputValue);
      this.setState({ filterConfig: new Map(filterConfig) });
      this.props.changeFilterData(filterConfig);
    } else if (modalMode === "sort") {
      if (modalSegementValue === "") {
        return;
      }
      sortConfig.set(sortCheck, modalSegementValue);
      this.setState({ sortConfig: new Map(sortConfig) });
      this.props.changeOrdersData(sortConfig);
    }
    this.setState({
      modalMode: null,
      pageIndex: 0,
      modalInputValue: "",
      modalSegementValue: "",
    });
  };
  resetFilterOrSort = () => {
    const {
      sortConfig,
      filterCheck,
      modalSegementValue,
      sortCheck,
      filterConfig,
      modalMode,
    } = this.state;
    if (modalMode === "filter") {
      filterConfig.set(filterCheck, "");
      this.setState({ filterConfig: new Map(filterConfig) });
      this.props.changeFilterData(filterConfig);
    } else if (modalMode === "sort") {
      if (modalSegementValue === "") {
        return;
      }
      sortConfig.set(sortCheck, "");
      this.setState({ sortConfig: new Map(sortConfig) });
      this.props.changeOrdersData(sortConfig);
    }
    this.setState({
      modalMode: null,
      pageIndex: 0,
      modalInputValue: "",
      modalSegementValue: "",
    });
  };
  pageBackHandle = () => {
    this.setState({
      pageIndex: 0,
    });
  };
  changeModalInputValue = (v) => {
    this.setState({
      modalInputValue: v,
    });
  };
  changeModalSegementValue = (v) => {
    this.setState({
      modalSegementValue: v,
    });
  };
  showSort = () => {
    this.setState({
      modalMode: "sort",
    });
  };
  showFilter = () => {
    this.setState({
      modalMode: "filter",
    });
  };
  hideModal = () => {
    this.setState({
      modalMode: null,
      pageIndex: 0,
      modalInputValue: "",
      modalSegementValue: "",
    });
  };
  confirmOper = () => {
    const {
      modalMode,
      modalInputValue,
      modalSegementValue,
      sortCheck,
      filterCheck,
    } = this.state;
    console.log(
      modalMode,
      modalInputValue,
      modalSegementValue,
      sortCheck,
      filterCheck
    );
    if (modalMode === "sort") {
      if (!sortCheck) {
        return;
      }
      this.props.changeOrdersData(
        sortCheck,
        segemntedArray[modalSegementValue]
      );
    } else if (modalMode === "filter") {
      if (!filterCheck) {
        return;
      }
      this.props.changeFilterData(modalInputValue, filterCheck);
    }
    this.setState({
      modalMode: "",
    });
  };
  clearOper = () => {
    const {
      modalMode,
      modalInputValue,
      modalSegementValue,
      sortCheck,
      filterCheck,
    } = this.state;
    if (modalMode === "sort") {
      if (!sortCheck) {
        return;
      }
      this.props.changeOrdersData(sortCheck);
      this.setState({
        modalMode: "",
        modalSegementValue: "升序",
        sortCheck: "",
      });
    } else if (modalMode === "filter") {
      if (!filterCheck) {
        return;
      }
      this.props.changeFilterData("", filterCheck);
      this.setState({
        modalMode: "",
        modalInputValue: "",
        filterCheck: "",
      });
    }
  };
  ItemSeparatorComponent = (sectionID, rowID) => (
    <View
      key={`${sectionID}-${rowID}`}
      style={{
        marginLeft: 10,
        width: this.state.tableWidth - 10,
        borderBottomWidth: Platform.OS !== "ios" ? 0.5 : 0.6,
        borderBottomColor: "#ddd",
      }}
    />
  );
  renderTableHeader = () => (
    <View style={style.tableHeaderContainer}>
      <View
        style={[
          style.tableHeaderTitleContainer,
          { paddingLeft: this.state.showChecked ? 40 : 0 },
        ]}
      >
        {this.state.showColumns.map((col) => {
          return (
            <View
              key={col.fid}
              style={[{ width: col.width }, style.tableHeader]}
            >
              <Text style={[style.rowItemtitleText, { fontSize: 10 }]}>
                {col.title}
              </Text>
            </View>
          );
        })}
      </View>
      {Platform.OS !== "ios" && (
        <Image
          style={{
            position: "absolute",
            top: 24,
            width: this.state.tableWidth,
          }}
          resizeMode="stretch"
          source={require("../../images/data-table-head-shadow.png")}
        />
      )}
      <View style={[style.tableHeaderLine, { width: this.state.tableWidth }]} />
    </View>
  );
  renderItem = ({ item }) => {
    const {
      isReference,
      onClickRow,
      getDisplayCol,
      columns,
      isRefMulti,
      screenDirection,
      rotateScreen,
    } = this.props;
    const { showChecked, selectedRows } = this.state;
    const { tableSize } = this.props
    const checked = !!selectedRows.find((d) => d.id.value === item.id.value);
    const { id, statusColor } = getDisplayCol(columns, item);
    if(tableSize==='normal'){
    return  <TouchableOpacity
      onPress={() => {
        onClickRow({
          checked,
          dataItem: item,
          id,
          isReference,
          isRefMulti,
          screenDirection,
          rotateScreen,
        });
      }}
      activeOpacity={1}
    >
      <View style={[{ width: this.state.tableWidth },style.rowContainer]}>
        {showChecked && (
          <Image
            style={{  marginLeft: 20 }}
            source={checked ? checkboxDefault : checkboxUnDefault}
          />
        )}
        {this.state.showColumns.map((column) => {
          const displayValue = get(item, column.fid, {}).display || "";
          const field = this.props.fields.find((f) => f.id === column.fid);
          if (
            field &&
            (isPictureComponent(field) ||
              isSinglePictureComponent(field) ||
              field.displaytype === "SinglePicture" ||
              field.displaytype === "Picture")
          ) {
            return (
              <View
                key={column.fid}
                style={[style.rowItem, { width: column.width }]}
              >
                <Image
                  style={{ width: 30, height: 30 }}
                  source={{
                    uri: `${displayValue}?x-oss-process=image/resize,h_30`,
                  }}
                />
              </View>
            );
          }
          if (field && isFormStatusComponent(field)) {
            return (
              <View
                key={column.fid}
                style={[style.rowItem, { width: column.width }]}
              >
                <View
                  style={[
                    rowStyle.formStatus,
                    { maxWidth: 12 * displayValue.length + 12 },
                    { ...(statusColor && tagColor[statusColor]) },
                  ]}
                >
                  <Text
                    numberOfLines={1}
                    style={[
                      rowStyle.formStatusFun,
                      {
                        color: statusColor
                          ? tagColor[statusColor].color
                          : "rgba(0,0,0,.65)",
                      },
                    ]}
                  >
                    {displayValue}
                  </Text>
                </View>
              </View>
            );
          }
          return (
            <View
              key={column.fid}
              style={[style.rowItem, { width: column.width }]}
            >
              <Text numberOfLines={1} style={style.rowItemText}>
                {displayValue}
              </Text>
            </View>
          );
        })}
        {!showChecked && !isReference && (
          <Image source={arrowSmallPress} style={{ marginLeft: 31 }} />
        )}
      </View>
    </TouchableOpacity>
    }
    return (
      <TouchableOpacity
        onPress={() => {
          onClickRow({
            checked,
            dataItem: item,
            id,
            isReference,
            isRefMulti,
            screenDirection,
            rotateScreen,
          });
        }}
        activeOpacity={1}
      >
        <View style={[{ width: this.state.tableWidth },style.rowSmallContainer]}>
          {showChecked && (
            <Image
              style={{ marginLeft: 20 }}
              source={checked ? checkboxDefault : checkboxUnDefault}
            />
          )}
          {this.state.showColumns.map((column) => {
            const displayValue = get(item, column.fid, {}).display || "";
            const field = this.props.fields.find((f) => f.id === column.fid);
            if (
              field &&
              (isPictureComponent(field) ||
                isSinglePictureComponent(field) ||
                field.displaytype === "SinglePicture" ||
                field.displaytype === "Picture")
            ) {
              return (
                <View
                  key={column.fid}
                  style={[style.rowItem, { width: column.width }]}
                >
                  <Image
                    style={{ width: 30, height: 30 }}
                    source={{
                      uri: `${displayValue}?x-oss-process=image/resize,h_30`,
                    }}
                  />
                </View>
              );
            }
            if (field && isFormStatusComponent(field)) {
              return (
                <View
                  key={column.fid}
                  style={[style.rowItem, { width: column.width }]}
                >
                  <View
                    style={[
                      rowStyle.formStatus,
                      { maxWidth: 12 * displayValue.length + 12 },
                      { ...(statusColor && tagColor[statusColor]) },
                    ]}
                  >
                    <Text
                      numberOfLines={1}
                      style={[
                        rowStyle.formStatusFun,
                        {
                          color: statusColor
                            ? tagColor[statusColor].color
                            : "rgba(0,0,0,.65)",
                        },
                      ]}
                    >
                      {displayValue}
                    </Text>
                  </View>
                </View>
              );
            }
            return (
              <View
                key={column.fid}
                style={[style.rowItem, { width: column.width }]}
              >
                <Text numberOfLines={1} style={style.rowItemText}>
                  {displayValue}
                </Text>
              </View>
            );
          })}
          {!showChecked && !isReference && (
            <Image source={arrowSmallPress} style={{ marginLeft: 31 }} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  render = () => {
    const { filterConfig, sortConfig, pageIndex, filterCheck, sortCheck } =
      this.state;
    return (
      <View>
        <View style={style.operRow}>
          <TouchableOpacity style={style.operItem} onPress={this.showFilter}>
            <Image source={sortImg}></Image>
            <Text style={{ color: "#4CA8F8", fontSize: 14 }}>过滤</Text>
          </TouchableOpacity>
          <TouchableOpacity style={style.operItem} onPress={this.showSort}>
            <Image source={filterImg}></Image>
            <Text style={{ color: "#4CA8F8", fontSize: 14 }}>排序</Text>
          </TouchableOpacity>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={[style.tableContainer]}
          refreshing={false}
          onMomentumScrollEnd={() => {
            this.scrollEnd = true;
            this.setState({ offsetX: this.offsetX });
          }}
        >
          {this.renderTableHeader()}
          <FlatList
            style={[
              style.tableList,
              { width: this.state.tableWidth, height: this.props.height - 75 },
            ]}
            data={this.props.datas}
            getItemLayout={(data, index) => ({
              length: 70,
              offset: 70 * index,
              index,
            })}
            renderItem={this.renderItem}
            extraData={this.props.extraData}
            onEndReached={this.props.onEndReached}
            onEndReachedThreshold={0.01}
            keyExtractor={(item) => item.id.value}
            removeClippedSubviews={false}
            onScrollBeginDrag={this.props.onScrollBeginDrag}
            ListFooterComponent={this.props.ListFooterComponent}
          />
        </ScrollView>
        <Modal
          visible={!!this.state.modalMode}
          maskClosable
          onClose={this.hideModal}
          animationType="slide-up"
          popup
        >
          {pageIndex === 0 && (
            <KeyboardAvoidingView>
              <View style={style.modalHeader}>
                <View></View>
                {this.state.modalMode === "sort" && (
                  <Text style={{ fontSize: 18 }}>排序</Text>
                )}
                {this.state.modalMode === "filter" && (
                  <Text style={{ fontSize: 18 }}>筛选</Text>
                )}
                <View></View>
              </View>
              <FlatList
                style={{ height: 420, backgroundColor: "#f7f7f7" }}
                keyExtractor={(item) => item.fid}
                renderItem={({ item, index }) => (
                  <ColumnItem
                    selectOperCol={this.selectOperColHandle}
                    modalMode={this.state.modalMode}
                    data={item}
                    value={
                      this.state.modalMode === "filter"
                        ? filterConfig.get(item.fid)
                        : sortConfig.get(item.fid)
                    }
                    sortCheck={this.state.sortCheck}
                    filterCheck={this.state.filterCheck}
                  ></ColumnItem>
                )}
                extraData={[
                  this.state.modalMode,
                  this.state.sortCheck,
                  this.state.filterCheck,
                ]}
                data={this.state.showColumns}
              ></FlatList>
            </KeyboardAvoidingView>
          )}
          {pageIndex === 1 && (
            <KeyboardAvoidingView>
              <View style={style.modalHeader}>
                <TouchableOpacity onPress={this.pageBackHandle}>
                  <Image source={arrowBack} style={{ width: 12 }}></Image>
                </TouchableOpacity>
                {this.state.modalMode === "sort" && (
                  <Text style={{ fontSize: 18 }}>排序</Text>
                )}
                {this.state.modalMode === "filter" && (
                  <Text style={{ fontSize: 18 }}>筛选</Text>
                )}
                <View style={{ width: 12 }}></View>
              </View>
              <View
                style={{
                  height: 420,
                  backgroundColor: "#F7F7F7",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                }}
              >
                {this.state.modalMode === "sort" && (
                  <SegmentedControl
                    selectedIndex={["升序", "降序"].indexOf(
                      this.state.modalSegementValue
                    )}
                    onValueChange={this.changeModalSegementValue}
                    values={["升序", "降序"]}
                    tintColor={"#1890FF"}
                    style={{ height: 32, marginHorizontal: 25, marginTop: 9 }}
                  />
                )}
                {this.state.modalMode === "filter" && (
                  <TextInput
                    value={this.state.modalInputValue}
                    style={style.modalInput}
                    onChangeText={this.changeModalInputValue}
                  />
                )}
                <View>
                  <TouchableOpacity
                    style={style.modalBtn}
                    onPress={this.addFilterOrSort}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        lineHeight: 32,
                        color: "#fff",
                      }}
                    >
                      {(
                        this.state.modalMode === "filter"
                          ? filterConfig.get(filterCheck)
                          : sortConfig.get(sortCheck)
                      )
                        ? "修改"
                        : "添加"}
                    </Text>
                  </TouchableOpacity>
                  {(this.state.modalMode === "filter"
                    ? filterConfig.get(filterCheck)
                    : sortConfig.get(sortCheck)) && (
                    <TouchableOpacity
                      style={style.modalResetBtn}
                      onPress={this.resetFilterOrSort}
                    >
                      <Text
                        style={{
                          textAlign: "center",
                          lineHeight: 32,
                          color: "#fff",
                        }}
                      >
                        清除
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </KeyboardAvoidingView>
          )}
        </Modal>
      </View>
    );
  };
}
export default RowStyle2;

const ColumnItem = ({
  data,
  sortCheck,
  filterCheck,
  modalMode,
  selectOperCol,
  value,
}) => {
  let isCheck = false;
  if (modalMode && modalMode === "sort" && sortCheck === data.fid) {
    isCheck = true;
  } else if (modalMode && modalMode === "filter" && filterCheck === data.fid) {
    isCheck = true;
  }
  function selectCol() {
    if (modalMode === "sort") {
      selectOperCol("sortCheck", data.fid, value);
    } else if (modalMode === "filter") {
      selectOperCol("filterCheck", data.fid, value);
    }
  }
  return (
    <TouchableOpacity style={style.columnItem} onPress={selectCol}>
      <Text style={{ lineHeight: 45 }}>{data.title}</Text>
      {value ? (
        <Text style={{ marginRight: 16, color: "#1890FF" }}>修改</Text>
      ) : (
        <Image
          style={{ width: 12, height: 12, marginRight: 16 }}
          source={arrowRightPress}
        />
      )}
    </TouchableOpacity>
  );
};
