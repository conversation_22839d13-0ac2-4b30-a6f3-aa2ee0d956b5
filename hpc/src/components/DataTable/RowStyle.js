import React from 'react'
import PropTypes from 'prop-types'
import { Text, View, TouchableOpacity, Image } from 'react-native'
import { SwipeAction, Modal } from '@ant-design/react-native'

import { buttionIds } from './Store'
import { rowStyle, width, tagColor } from './style'

const checkboxUnDefault = require('../../images/icon-checkbox-un_checked-default.png')
const checkboxDefault = require('../../images/icon-checkbox-checked-default.png')
const arrowSmallPress = require('../../images/icon-arrow-right-small-press.png')
const checkIcon = require('../../images/icon-radio-checked-default.png')

class RowStyle extends React.Component {
  state = {
    selectedRows: this.props.selectedRows,
    showChecked: this.props.showChecked,
    treekey: this.props.treekey,
  }

  componentWillReceiveProps = ({ selectedRows, showChecked, treekey }) => {
    if (selectedRows !== this.props.selectedRows) { this.setState({ selectedRows }) }
    if (showChecked !== this.props.showChecked) { this.setState({ showChecked }) }
    if (treekey !== this.props.treekey) { this.setState({ treekey }) }
  }

  displayRow = () => {
    const { dataItem, isReference, isRefMulti, tmp, onClickRow, valueColumn, tree, width: roteWidth, screenDirection, rotateScreen } = this.props
    const valId = (isRefMulti && valueColumn) || 'id'
    const checked = !!this.state.selectedRows.find(d => d[valId].value === dataItem[valId].value)
    const viewWidth = (roteWidth - (18 * (this.state.showChecked ? 4 : 2)) - (this.state.showChecked ? 0 : 38)) / 2
    const { viewLeft, viewRight } = this.rowType(tmp, viewWidth)
    if (tree) return this.renderTreedata(tmp, checked)
    return (
      <TouchableOpacity
        onPress={() => { onClickRow({ checked, dataItem, id: tmp.id, isReference, isRefMulti, screenDirection, rotateScreen }) }}
        style={[rowStyle.displayRow, { width: roteWidth }]}
        activeOpacity={1}
      >
        {(this.state.showChecked || isRefMulti) && <Image style={{ marginRight: 18 }} source={checked ? checkboxDefault : checkboxUnDefault} />}
        <View style={{ width: viewWidth, height: 42, overflow: 'hidden' }} >{viewLeft}</View>
        <View style={{ width: viewWidth, height: 42, overflow: 'hidden' }} >{viewRight}</View>
        {!this.state.showChecked && !isReference && !isRefMulti && <Image source={arrowSmallPress} style={{ marginLeft: 31 }} />}
      </TouchableOpacity>
    )
  }

  rowType = ({ value, formStatus }, viewWidth) => {
    if (value.length === 1) { return { viewLeft: this.viewLeftFun(value[0], formStatus), viewRight: null } }
    if (value.length === 2) {
      return {
        viewLeft: this.viewLeftFun(value[0], formStatus),
        viewRight: this.viewRight(value[1]),
      }
    }
    if (value.length >= 3) {
      const widths = (((value[1] ? value[1].length : 0) * 14) + ((formStatus ? formStatus.length : 0) * 12)) + 20
      if ((value[1] ? widths : 0) > viewWidth) {
        return {
          viewLeft: this.viewLeftFun(value[0], null, value[1]),
          viewRight: this.viewRight(value[2], formStatus),
        }
      }
      return {
        viewLeft: this.viewLeftFun(value[0], formStatus, value[1]),
        viewRight: this.viewRight(value[2]),
      }
    }
    return { viewLeft: null, viewRight: null }
  }

  viewLeftFun = (value0, formStatus, value1) => {
    if (formStatus || value1) {
      return (
        <View>
          <Text style={rowStyle.viewLeftValue0}>{value0}</Text>
          <View style={rowStyle.viewLeftFun}>
            {!!value1 && <Text numberOfLines={3} style={rowStyle.viewLeftValue1}>{value1}</Text>}
            {!!formStatus && this.formStatusFun(formStatus, 0)}
          </View>
        </View>
      )
    }
    return <View style={{flex:1}}><Text style={rowStyle.viewLeftText}>{value0}</Text></View>
  }

  viewRight = (value, formStatus) => (
    <View style={rowStyle.viewRightView}>
      <Text numberOfLines={1} style={{ fontSize: formStatus ? 14 : 16, color: '#A0A2A5' }} >{value}</Text>
      {!!formStatus && this.formStatusFun(formStatus, 0)}
    </View>
  )

  formStatusFun = (formStatus, marginTop) => (
    <View style={[rowStyle.formStatus, { marginTop }, { ...this.props.tmp.statusColor && tagColor[this.props.tmp.statusColor] }]} >
      <Text numberOfLines={1} style={[rowStyle.formStatusFun, { color: this.props.tmp.statusColor ? tagColor[this.props.tmp.statusColor].color : 'rgba(0,0,0,.65)' }]}>{formStatus}</Text>
    </View >
  )

  renderTreedata = ({ value, id }, checked) => {
    const { dataItem, isReference, isRefMulti, onClickRow, screenDirection, rotateScreen } = this.props
    const viewWidth = (width - 18 * 2 - 38) / 2
    return (
      <TouchableOpacity
        onPress={() => { onClickRow({ dataItem, id, isReference, checked, isRefMulti, screenDirection, rotateScreen }) }}
        style={rowStyle.displayRow}
        activeOpacity={1}
      >
        <View style={{ width: viewWidth, height: 42, overflow: 'hidden' }} >
          <Text numberOfLines={1} style={{ ...rowStyle.viewLeftValue0, fontWeight: '600', color: this.state.treekey === id || checked ? '#0091FF' : '#1b1b1b' }}>{value[0]}</Text>
          <View style={rowStyle.viewLeftFun}>
            <Text numberOfLines={1} style={{ ...rowStyle.viewLeftValue1, color: this.state.treekey === id || checked ? '#0091FF' : '#A0A2A5' }}>{value[1]}</Text>
          </View>
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center', width: viewWidth, height: 42, overflow: 'hidden' }} >
          <Text numberOfLines={1} style={{ fontSize: 16, color: this.state.treekey === id || checked ? '#0091FF' : '#A0A2A5' }} > {value[2]} </Text>
          {this.state.treekey === id ? <Image style={{ marginLeft: 30 }} source={checkIcon} /> : <View style={{ width: 48 }} />}
        </View>
      </TouchableOpacity>
    )
  }

  render = () => {
    const { checkPrivByOperId, handleDel, tmp } = this.props
    // console.log('ashdjlakjdlkajdljasldjlkasjdljalsdjlasjd', this.props)
    return (
      <View>
        <SwipeAction
          style={{ backgroundColor: 'gray' }}
          autoClose
          disabled={!checkPrivByOperId(buttionIds.delete).isAuth}
          right={[
            {
              text: '取消',
              onPress: () => console.log('cancel'),
              style: { backgroundColor: '#ddd', color: 'white' },
            },
            {
              text: '删除',
              onPress: () => Modal.alert('删除', '确定删除?', [
                { text: '取消', onPress: () => console.log('cancel') },
                { text: '确定', onPress: () => handleDel(tmp.id), style: { fontWeight: 'bold' } },
              ]),
              style: { backgroundColor: '#F4333C', color: 'white' },
            },
          ]}
        >
          <View style={{ backgroundColor: 'white' }} key={tmp.id}>
            {this.displayRow()}
          </View>
        </SwipeAction>
      </View>
    )
  }
}

RowStyle.propTypes = {
  tmp: PropTypes.object, // eslint-disable-line
  dataItem: PropTypes.object, // eslint-disable-line
  isReference: PropTypes.bool,
  isRefMulti: PropTypes.bool,
  onClickRow: PropTypes.func,
  selectedRows: PropTypes.array, // eslint-disable-line
  checkPrivByOperId: PropTypes.func,
  handleDel: PropTypes.func,
  showChecked: PropTypes.bool,
  valueColumn: PropTypes.string,
}

export default RowStyle
