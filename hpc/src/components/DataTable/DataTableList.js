import React from 'react'
import PropTypes from 'prop-types'
import { Text, View, FlatList, Image, ActivityIndicator, SafeAreaView } from 'react-native'
import { Button, WhiteSpace, ActionSheet } from 'nagz-mobile-lib'
import { Modal } from '@ant-design/react-native'
import { remove } from 'lodash'

import { dataTableListStyles as styles, isIos } from './style'
import { deviceType } from '../../config/sysPara'
import { buttionIds } from './Store'
import { isCopyFormDataComponent } from '../../addons/constants'
import RowStyle2 from './RowStyle2'
import NavigationService from '../../NavigationService'

export default class DataTableList extends React.Component {
  constructor(props) {
    super(props)
    this.onEndReachedCalledDuringMomentum = false
    this._data = this.props.datas
  }

  state = {
    showConfirmBut: this.props.showConfirmBut,
    refreshing: false,
    isLoading: false,
    showChecked: this.props.showChecked,
    data: this.props.datas,
    current: this.props.pagination.current,
    hasMore: true,
    isLoadingMore: false,
    treekey: this.props.treekey,
  }

  componentWillReceiveProps({ datas, pagination, showConfirmBut, showChecked, treekey }) {
    // console.log('asdjlkasdjlkajsdlkjaskldjlkadlkajdl', this.props.datas)
    if (datas[0] !== this.props.datas[0] || datas.length !== this.props.datas.length) {
      this.setState({
        data: datas,
      })
      this._data = datas
    }

    if (pagination && this.state.refreshing && pagination.current !== this.props.pagination.current) {
      this.setState({
        pagination: {
          ...this.state.pagination,
          current: pagination.current,
        },
        hasMore: true,
        refreshing: false,
      })
    }
    if (showConfirmBut !== this.props.showConfirmBut) {
      this.setState({ showConfirmBut })
    }
    if (showChecked !== this.props.showChecked) {
      this.setState({ showChecked })
    }
    if (treekey !== this.props.treekey) {
      this.setState({ treekey })
    }
  }

  componentWillUnmount() {
    if (this.actionSheet) {
      this.actionSheet.close()
    }
  }

  onEndReached = async () => {
    if (!this.onEndReachedCalledDuringMomentum) {
      if (!this.props.onLoading) {
        return
      }
      if (this.state.isLoading || !this.state.hasMore) {
        return
      }
      const current = this.state.current + 1
      this.setState({
        current,
        isLoadingMore: true,
      })
      const datas = await this.props.onLoading(current)
      if (datas) {
        this._data = this._data.concat(datas)
        this.setState({
          data: this._data,
          isLoading: false,
          hasMore: true,
        })
      } else {
        this._data = this._data.concat([])
        this.setState({
          data: this._data,
          isLoading: false,
          hasMore: false,
        }, () => {
          // console.log('asdhlkajdlkajldks2222j', this.state.data)
        })
      }
      this.onEndReachedCalledDuringMomentum = false
    }
  }

  onRefresh = async () => {
    if (this.props.onLoading) {
      this.onEndReachedCalledDuringMomentum = false
      this.setState({ refreshing: true })
      const datas = await this.props.onLoading(1)
      if (datas) {
        this.setState({
          data: datas,
          current: 1,
          hasMore: true,
        })
        this._data = datas
      }
      this.setState({ refreshing: false })
    }
  };

  ListFooterComponent = () => {
    let loadingview = null
    if (!this.state.hasMore) {
      loadingview = <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>没有更多了</Text></View>
      return loadingview
    } else if (this.state.isLoadingMore) {
      loadingview = <View style={{ paddingVertical: 20 }}><ActivityIndicator size="large" /></View>
      return loadingview
    }
    return null
  }
  renderEmptyComponent=()=>{
   return <View style={{ paddingVertical: 20 }}><Text style={{ textAlign: 'center', color: '#bcbcbb', fontSize: 12 }}>暂无数据</Text></View>
  }
  ItemSeparatorComponent = (sectionID, rowID) => (
    <View
      key={`${sectionID}-${rowID}`}
      style={{
        marginLeft: '5%',
        width: '90%',
        borderBottomWidth: deviceType === 2 ? 0.5 : 0.6,
        borderBottomColor: '#ddd',
      }}
    />
  )

  showFormOperation = () => {
    let buttons = this.props.formOperations.filter(field => !field.properties.addForm)
    if (this.state.showConfirmBut === 1 && this.showAddBut) {
      buttons = buttons.concat(this.props.fields.filter(field => isCopyFormDataComponent(field)))
    }
    buttons = buttons.filter(b => this.props.formOperatorStaus.hide.indexOf(b.id) === -1 &&
      this.props.formOperatorStaus.locked.indexOf(b.id) === -1)

    const BUTTONS = buttons.map(field => field.properties.label)
    BUTTONS.push('删除')
    BUTTONS.push('取消')
    this.actionSheet = ActionSheet
    this.actionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        destructiveButtonIndex: BUTTONS.length - 2,
      },
      (buttonIndex) => {
        if (buttons.length === buttonIndex) {
          Modal.alert('删除', '确定删除?', [
            { text: '取消', onPress: () => console.log('cancel') },
            { text: '确定', onPress: () => { this.props.operationFunc(buttionIds.batchDelete) } },
          ])
        } else if (buttons.length > buttonIndex) {
          const field = buttons[buttonIndex]
          if (isCopyFormDataComponent(field)) {
            this.props.goCopy(field.id)
          } else {
            const datas = {}
            datas[field.id] = {
              value: true,
            }
            this.props.batchEdit(datas)
          }
        }
      },
    )
  }

  multiSelectOpt = () => {
    const valueData = this.props.valueData
    const valId = this.props.valueColumn || 'id'
    const hasValueSelect = valueData.filter(item => this.props.selectedRowKeys.find(i => i === item.key))
    remove(this.props.selectedRowKeys, item => hasValueSelect.find(i => i.key === item))
    const value = this.props.selectedRowKeys.map((item) => {
      const findItem = this._data.find(i => i[valId].value === item)
      return findItem
    }).filter(s => s)
    this.props.multiSelectFn(value, hasValueSelect, this._data)
    if (!this.props.customerBack) {
      NavigationService.back()
    }
  }
  rotateScreen = () => {
    const { screenDirection, rotateScreen } = this.props
    screenDirection !== 'PORTRAIT' && rotateScreen && rotateScreen()
  }
  showButtom = () => {
    const { showConfirmBut, showChecked } = this.state
    const {
      listType, checkPrivByOperId, handleAdd, isRefMulti, screenDirection, rotateScreen, height,
    } = this.props
    const isDataTable = listType === 'dataTable'
    const showAddBut = isDataTable && checkPrivByOperId(buttionIds.add)
    // console.log('asdhakjdhashdkjas,mzxcn,mzxncslkadjalsd', buttionIds.add, checkPrivByOperId, checkPrivByOperId(buttionIds.add))
    this.showAddBut = showAddBut
    let confirmBut = {
      position: 'absolute',
      right: 0,
      bottom: isIos && screenDirection === 'PORTRAIT' ? (height === 812 ? 34 : 0) : 0,
      width: '100%',
    }
    if (isIos && screenDirection !== 'PORTRAIT') {
      confirmBut = { ...confirmBut, bottom: -15 }
    }
    if (!!showConfirmBut && (showChecked || isRefMulti)) {
      return (
        <Button
          style={{ container: confirmBut }}
          onPress={() => {
            if (isDataTable) {
              this.showFormOperation()
              this.rotateScreen()
            } else if (isRefMulti) {
              this.multiSelectOpt()
              this.rotateScreen()
            }
          }}
        >
          {isDataTable && <Image source={require('../../images/icons-list-fill-small-default.png')} />}
          <Text style={{ marginLeft: 5, color: '#17A9FF' }}>{showAddBut ? '操作' : '确定'}({showConfirmBut})</Text>
        </Button>
      )
    } else if (showAddBut) {
      return (
        <Button
          style={{ container: confirmBut }}
          onPress={() => {
            handleAdd()
            this.rotateScreen()
          }}
        >
          <Image source={require('../../images/icon-add-outline-default.png')} />
          <Text style={{ marginLeft: 5, color: '#17A9FF' }}>新增</Text>
        </Button>
      )
    }
    return null
  }
  onMomentumScrollBegin = () => {
    if (this.onEndReachedCalledDuringMomentum) {
      this.onEndReachedCalledDuringMomentum = false
    }
  }
  renderItem = ({ item, index }) => {
    const { width, height, screenDirection, rotateScreen } = this.props
    if (index === 0) {
      return [<WhiteSpace key={index} style={{ borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }} />, this.props.rowRender(item, width, height, screenDirection, rotateScreen)]
    }
    return this.props.rowRender(item, width, height, screenDirection, rotateScreen)
  }
  render() {
    const { showConfirmBut } = this.state
    const { style, isTab, displaytype, screenDirection, rotateScreen, width, height } = this.props
    const showAddBut = this.props.listType === 'dataTable' && this.props.checkPrivByOperId(buttionIds.add)
    const listHeight = isIos && height === 812 ? height - 88 + 34 : height - 64
    const calHeight = listHeight - ((!showConfirmBut && !showAddBut) ? 0 : 70) - (isTab ? 44 : 0)
    // console.log('asdhklajdljasdlkjaslkdllkzxnclklklkasjdajdslkjl', this.state.data, displaytype)

    return (
      <SafeAreaView style={[styles.DataTableList, style]}>
        <View style={{ height: calHeight, position: 'relative' }}>
          {
            displaytype === '2' ?
              <RowStyle2
                {...this.props}
                extraData={this.state}
                onEndReached={this.onEndReached}
                onEndReachedThreshold={0.1}
                keyExtractor={(item, index) => index}
                ListFooterComponent={this.ListFooterComponent}
                removeClippedSubviews={false}
                onScrollBeginDrag={this.onMomentumScrollBegin}
                ItemSeparatorComponent={this.ItemSeparatorComponent}
                renderItem={this.renderItem}
                style={styles.dataList}
                datas={this.state.data}
                showChecked={this.state.showChecked}
                screenDirection={screenDirection}
                rotateScreen={rotateScreen}
              /> :
              <FlatList
                extraData={this.state}
                data={this.state.data}
                onEndReached={this.onEndReached}
                onEndReachedThreshold={0.1}
                refreshing={this.state.refreshing}
                onRefresh={this.onRefresh}
                keyExtractor={(item, index) => index}
                ListFooterComponent={this.ListFooterComponent}
                ListEmptyComponent={this.renderEmptyComponent}

                removeClippedSubviews={false}
                // onScrollBeginDrag={this.onMomentumScrollBegin}
                ItemSeparatorComponent={this.ItemSeparatorComponent}
                renderItem={this.renderItem}
                style={[styles.dataList, { width }]}
              />
          }
          {!isIos && <Image
            style={{ position: 'absolute', bottom: 0, width: '100%' }}
            resizeMode="stretch"
            source={require('../../images/img-shadow-footer.png')}
          />}
        </View>
        {this.showButtom()}
      </SafeAreaView>
    )
  }
}

DataTableList.propTypes = {
  showConfirmBut: PropTypes.number,
  datas: PropTypes.array,
  pagination: PropTypes.object,
  onLoading: PropTypes.func,
  style: PropTypes.object,
  rowRender: PropTypes.func,
  multiSelectFn: PropTypes.func,
  displayTitle: PropTypes.array,
  listType: PropTypes.string,
  displaytype: PropTypes.string,
  showChecked: PropTypes.bool,
  formOperations: PropTypes.array,
  checkPrivByOperId: PropTypes.func,
  handleAdd: PropTypes.func,
  operationFunc: PropTypes.func,
  batchEdit: PropTypes.func,
  isTab: PropTypes.bool,
  fields: PropTypes.array,
  goCopy: PropTypes.func,
  isRefMulti: PropTypes.bool,
  selectedRowKeys: PropTypes.array,
  valueData: PropTypes.array,
  valueColumn: PropTypes.string,
}

