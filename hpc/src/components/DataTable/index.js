import PropTypes from "prop-types";
import React from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import union from "lodash/union";
import _get from "lodash/get";
import lodash from "lodash";
import { Modal, Toast, Portal } from "@ant-design/react-native";
import { ActionSheet } from "nagz-mobile-lib";
import NavigationService from "../../NavigationService";
import TreeSelect from "./TreeSelect";
import {
  COMPONENT_FORM_ID,
  isInvocationServiceComponent,
  isSubformComponent,
  isProductSubformComponent,
  isFormOperationComponent,
  isFormAssistStatusComponent,
} from "../../addons/constants";
import Store, {
  buttionIds,
  deleteData,
  getBtnsAndtitle,
  exportList,
  getViews,
  getView,
  saveScheme,
  getFields,
  batchEditDatas,
} from "./Store";
import {
  setView,
  getSelectView,
  setCurrPage,
  getCurrPage,
  setPageSize,
  getPageSize,
} from "../../utils/storage";
import QueryBuilds, { QueryBuild } from "./QueryBuild";
import DataTableStyle from "./DataTableStyle";
import { styles, width, height } from "./style";
import SideMenu from "../SideMenu";
import RowStyle from "./RowStyle";
import { GO_COPY, GO_FULLREPORTVIEW } from "../../utils/jump";
import { getTreeData } from "@request/api";

// 判断fieldType是否属于字段值的display和value含义不同的特殊类型
const fieldTypeSift = (fieldType) =>
  fieldType === "AppUser" ||
  fieldType === "Reference" ||
  fieldType === "RefProperty" ||
  fieldType === "RefMultiSelect";
const segemntedArray = { 升序: "asc", 降序: "desc" };
class DataTable extends React.Component {
  constructor(props) {
    super(props);
    this.conditions = {
      querys: "",
      orders: "",
    };
    this.clicked = false;
    this.querys = {};
    this.filterData = {};

    this.tableRef = React.createRef();
  }

  state = {
    columns: [],
    titles: [],
    show: false,
    datas: [],
    selectedRowKeys: [],
    pagination: undefined,
    allOperations: [], // 记录用户针对该表单所具有的权限
    searchPlaceholder: "请输入",
    selectedRows: [],
    count: 0,
    dataLoaded: false,
    showImportBox: false,
    views: [],
    orders: {},
    ordersDatas: new Map(),
    fields: [],
    showColumnConfig: false,
    view: {
      id: "",
      filter: {},
      columns: null,
      orders: [],
      columnWidths: {},
      name: "",
    },
    openQuerys: false,
    editView: {},
    enableScrollViewScroll: true,
    emptyData: null,
    searchdDatas: [],
    showConfirmBut: 0,
    showChecked: false,
    formOperations: [],
    valueData: [],
    sourceFormStateSetting: [],
    treeIsOpen: this.props.treeFilter || false,
    treeDatas: [],
    checkOrderField: "",
    checkFilterFiled: "",
  };

  componentWillMount = async () => {
    const tKey = Toast.loading("加载中...", 0);
    const {
      appId,
      formId,
      query,
      fieldId,
      newFormId,
      statisticalColumn,
      isSubForm,
      listType,
      value = [],
      valueData,
      valueColumn,
      treekey,
    } = this.props;

    if (appId && formId) {
      await this.init(appId, formId);
      this.conditions.orders = this.ordersFun(this.state.ordersDatas);
      this.conditions.querys = query;
      const views =
        newFormId === "LIST"
          ? []
          : (await getViews(appId, newFormId, fieldId)) || [];
      const { viewId, currPage, pageSize } = this.getStorage();
      const stat = this.statFun(statisticalColumn || []);
      // console.log('asdhjkadlkasdlkjkldjlkajdlkajdklajsdlk', stat)
      if (views && views.length !== 0) {
        const view = views.find(({ id }) => id === viewId);
        let vid = null;
        let defaultValue = false;
        if (view) {
          this.store.setViewId(viewId);
          this.store.setPage(currPage);
          this.store.setPageSize(pageSize);
          this.store.setStat(stat);
          this.initPagination(currPage, pageSize);
          vid = view.id;
          defaultValue = true;
        } else {
          vid = views[0].id;
        }
        await this.selectView(vid, defaultValue);
      } else {
        this.reload();
      }
      if (listType === "refMulti") {
        const valId = valueColumn || "id";
        this.setState({
          selectedRowKeys: value,
          selectedRows: value.map((v) => ({ [valId]: { value: v } })),
          showConfirmBut: value.length,
          valueData,
        });
      }
      if (this.props.treeField && listType === "dataTable") {
        this.loadTreeData();
      }
      this.setState({
        views,
        orders: this.ordersDatas,
        showChecked: isSubForm || false,
        treekey,
      });
    }
    this.changeOnRight();
    Portal.remove(tKey);
    this.setState({ dataLoaded: true });
  };

  componentWillReceiveProps = async ({
    formId,
    appId,
    query,
    enableScrollViewScroll,
    value,
    valueData,
    listType,
    valueColumn,
    treekey,
  }) => {
    if (this.props.formId !== formId || this.props.appId !== appId) {
      await this.init(appId, formId);
      this.reload();
    }
    if (this.props.query !== query) {
      this.defaultQuery = query;
      this.reload();
    }
    if (this.props.enableScrollViewScroll !== enableScrollViewScroll) {
      this.setState({ enableScrollViewScroll });
    }
    if (listType === "refMulti" && this.props.value !== value) {
      const valId = valueColumn || "id";
      this.setState({
        selectedRowKeys: value,
        selectedRows: value.map((v) => ({ [valId]: { value: v } })),
        showConfirmBut: value.length,
        valueData,
      });
    }
    if (treekey !== this.props.treekey) {
      this.setState({ treekey });
    }
  };

  componentWillUnmount() {
    this.setState = () => {};
  }

  changeOnRight = () => {
    if (this.checkPrivByOperId(buttionIds.add).isAuth) {
      NavigationService.refresh({
        rightTitle: "新增",
        onRight: () => {
          this._handleAdd();
        },
      });
    }
  };

  statFun = (statisticalColumn) => {
    const datas = {};
    statisticalColumn.forEach((val) => {
      datas[val.fieldId] = val.type;
    });
    return encodeURI(JSON.stringify(datas));
  };

  initAll = () => {
    this.conditions = {
      querys: "",
      orders: "",
    };
    this.querys = {};
    this.filterData = {};
    this.queryCondtAndData = {
      dataIds: [],
      urlQuery: "",
      count: 0,
    };
    this.setState({
      stat: {},
      columns: [],
      titles: [],
      datas: [],
      pagination: undefined,
      allOperations: [],
      searchPlaceholder: "",
      selectedRows: [],
      count: 0,
      showImportBox: false,
      views: [],
      orders: {},
      ordersDatas: {},
      showColumnConfig: false,
      view: {
        id: "",
        filter: {},
        columns: [],
        orders: [],
        columnWidths: {},
        name: "",
      },
      openQuerys: false,
      editView: {},
      emptyData: null,
      searchdDatas: [],
      showConfirmBut: 0,
    });
  };
  init = async (appId, formId) => {
    this.initAll();
    this.initOrder();
    this.initStore();
    this.initQuery();
    this.store.setSource(`/apps/${appId}/forms/${formId}/datas`);
    this.initPagination();
    if (this.props.showRowSelection || this.props.addMutiRow) {
      this.initRowSelection();
    }
    this.initOperations();
    await this.getAllfieldAndTitle(appId, formId);
  };

  initOrder = (orders) => {
    const ordersDatas = new Map(Object.entries(this.props.orders));
    if (orders) {
      orders.forEach((data) => {
        ordersDatas.set(data.column, data.order);
      });
    }
    this.setState({ ordersDatas });
  };

  initQuery = () => {
    this.queryBuilds = new QueryBuilds();
    if (this.props.query) {
      this.defaultQuery = this.props.query;
    }
  };

  initRowSelection = () => {
    this.rowSelection = {
      type: this.props.selectionType,
      onChange: this.selectRow,
    };
  };

  initOperations = () => {
    if (this.props.operations) {
      this.props.operations.reload = this.reload.bind(this);
      this.props.operations.getCondition = this.getCondition.bind(this);
    }
  };

  getCondition = () => this.conditions;
  initStore = () => {
    const {
      appId,
      formId,
      displayColumns,
      pagination,
      query,
      extraParams,
      ignoreEmpty,
    } = this.props;
    this.store = new Store({
      source: `/apps/${appId}/forms/${formId}/datas`,
      displayColumns,
      pagination,
      query,
      orders: this.ordersFun(this.state.ordersDatas),
      extraParams,
      ignoreEmpty,
    });
  };

  initPagination = (currPage, pageSize) => {
    const pagination = this.props.pagination;
    if (pagination) {
      this.setState({
        pagination: {
          current: pagination.page || currPage || 1,
          pageSize: pagination.pageSize || pageSize || 10,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          onChange: this.changePage,
          onShowSizeChange: this.changePageSize,
        },
      });
    }
  };

  getAllfieldAndTitle = async (appId, formId) => {
    const { fieldId, fieldConfig = {} } = this.props;
    const result = await getBtnsAndtitle(appId, formId); // 获取主表的title和button
    const loadfields = fieldConfig[fieldId]
      ? fieldConfig[fieldId].config.form.fields
      : (await getFields(appId, formId)) || []; // 获取主表所有字段
    this.formField = loadfields.find((field) => field.id === COMPONENT_FORM_ID);
    const fields = loadfields.filter(
      (field) =>
        field.id !== COMPONENT_FORM_ID &&
        !isSubformComponent(field) &&
        !isProductSubformComponent(field)
    );
    this.subForm = loadfields.filter(
      (field) => isSubformComponent(field) || isProductSubformComponent(field)
    ); // 过滤出子表单字段
    const formOperations = loadfields.filter((field) =>
      isFormOperationComponent(field)
    ); // 过滤出表单操作字段
    this.subFormFieldIds = this.subForm.map(({ id }) => id);
    this.subFormIDMap = {};
    const subFormData = this.subForm.map((item) => ({
      subFormId: item.id,
      subFormlabel: item.properties.label,
      formId: item.properties.formsource.formId,
      subcols: item.properties.formsource.columns,
    }));
    let subfields = [];
    let subTitle = [];
    for (const index in subFormData) {
      const subformId = subFormData[index].formId;
      const item = fieldConfig[fieldId]
        ? fieldConfig[fieldId][subFormData[index].subFormId].config.form.fields
        : (await getFields(appId, subformId, formId)) || [];
      item.forEach((f) => {
        this.subFormIDMap = {
          ...this.subFormIDMap,
          [f.id]: subFormData[index].formId,
        };
      });
      subfields = [
        ...subfields,
        ...item
          .filter(
            (field) =>
              field.id !== COMPONENT_FORM_ID &&
              !isSubformComponent(field) &&
              !isProductSubformComponent(field) &&
              subFormData[index].subcols.indexOf(field.id) !== -1
          )
          .map((filterField) => ({
            ...filterField,
            id: `${subFormData[index].subFormId}.${filterField.id}`,
            properties: {
              ...filterField.properties,
              label: `${subFormData[index].subFormlabel}-${filterField.properties.label}`,
            },
          })),
      ];
      subTitle = [
        ...subTitle,
        ...item
          .filter((f) => subFormData[index].subcols.indexOf(f.id) !== -1)
          .reduce(
            (res, f) => [
              ...res,
              {
                [`${subFormData[index].subFormId}.${f.id}`]: `${subFormData[index].subFormlabel}-${f.properties.label}`,
              },
            ],
            []
          ),
      ];
    }
    this.titles =
      result.title &&
      result.title.filter((tit) => Object.keys(tit)[0] !== "sn");
    const sourceForm = loadfields.find((i) => i.type === "Form");
    this.setState({
      allOperations: result.operations,
      fields: [...fields, ...subfields],
      titles: [...this.titles, ...subTitle],
      formOperations,
      sourceFormStateSetting:
        (sourceForm && sourceForm.properties.stateSetting) || [],
    });
  };

  _getDisplayCol = (title, item) => {
    // console.log('asdhkajdhkhaskdjasdhk', title, item)
    // 过滤掉id字段
    const displayTitle = title
      .filter((x) => !x.hasOwnProperty("id"))
      .filter((x) => !x.form_status);
    const rObj = {};
    const val = [];
    let keyObject = {};
    for (let i = 0; i < displayTitle.length; i += 1) {
      const key = Object.keys(displayTitle[i])[0];
      if (key.includes(".")) {
        const keys = key.split(".");
        keyObject = item;
        for (let j = 0; j < keys.length; j += 1) {
          keyObject = keyObject[keys[j]];
        }
      } else {
        keyObject = item instanceof Array ? item[i][key] : item[key];
      }
      val[i] = keyObject ? keyObject.display : "";
    }
    rObj.id = item.id.value;
    rObj.value = val;
    rObj.formStatus =
      item.form_status && item.form_status.value !== "已保存"
        ? item.form_status.value
        : null;
    const { sourceFormStateSetting, fields } = this.state;
    // console.log("ajsdklandkladlkanldkhlkajdlkajdlajsdlkj", sourceFormStateSetting, fields)
    const assistStatus = fields.find((f) => isFormAssistStatusComponent(f));
    let findItem = null;
    if (assistStatus && assistStatus.id) {
      findItem = sourceFormStateSetting.find(
        (sset) =>
          sset.name === rObj.formStatus &&
          sset.assistStatus === item[assistStatus.id].value
      );
    } else {
      findItem = sourceFormStateSetting.find(
        (sset) => sset.name === rObj.formStatus
      );
    }
    if (findItem && findItem.statusColor)
      rObj.statusColor = findItem.statusColor;
    // console.log(';asdjdjljalkdjlaksdjlkajsd', rObj, displayTitle)

    return rObj;
  };

  getNextPage = async () => {
    const result = await this.store.load();
    let tmp = null;
    if (result.errorCode === "0") {
      const { items, title, count } = result.data;
      if (items && items.length > 0) {
        tmp = items;
      }
    }

    return tmp;
  };

  changePage = (page) => {
    this.store.setPage(page);
    this.setState({
      pagination: {
        ...this.state.pagination,
        current: page,
      },
    });
    this.store.setOrders(this.ordersFun(this.state.ordersDatas));
    return this.getNextPage();
  };

  changePageSize = (current, size) => {
    this.store.setPageSize(size);
    this.store.setPage(1);
    this.setState({
      pagination: {
        ...this.state.pagination,
        pageSize: size,
        current: 1,
      },
    });
    this.store.setOrders(this.ordersFun(this.state.ordersDatas));
    this.getNextPage();
  };
  changeFilterData = (filters) => {
    // this.filterData = { ...this.filterData, [fieldId]: data };
    if (!lodash.isMap(filters)) {
      return;
    }
    filters.forEach((value, fieldId) => {
      const isString = lodash.isString(value);
      if (!isString) {
        value = "";
      }
      const fieldType = this.state.fields.find((f) => f.id === fieldId).type;
      const key = fieldTypeSift(fieldType) ? `${fieldId}.display` : fieldId;
      const formatValue = value.trim().replace(/(\u002f)/g, ",");
      const qb = new QueryBuild({
        key,
        operator:
          formatValue.indexOf(",") >= 0
            ? QueryBuild.OPERATOR_IN
            : QueryBuild.OPERATOR_LINK,
        value: formatValue,
      });
      const findItem = this.queryBuilds.queryTexts.find((i) => i.id === key);
      if (findItem) {
        if (formatValue !== "") {
          this.queryBuilds.update(key, qb);
        } else {
          this.queryBuilds.remove(key);
        }
      } else {
        this.queryBuilds.add(qb);
      }
    });
    console.log(this.queryBuilds);
    this.reload();
  };
  oldchangeFilterData = (data, fieldId) => {
    const fieldType = this.state.fields.find((f) => f.id === fieldId).type;
    const key = fieldTypeSift(fieldType) ? `${fieldId}.display` : fieldId;
    this.filterData = { ...this.filterData, [fieldId]: data };
    const query = data.replace(/(\s*$)/g, "");
    const { checkFilterFiled } = this.state;
    if (checkFilterFiled) {
      this.queryBuilds.remove(this.querys[checkFilterFiled].id);
      delete this.querys[checkFilterFiled];
    }
    if (query) {
      const qb = new QueryBuild({
        key,
        operator: QueryBuild.OPERATOR_LINK,
        value: query,
      });
      if (this.querys[fieldId]) {
        this.queryBuilds.update(this.querys[fieldId].id, qb);
      } else {
        this.queryBuilds.add(qb);
      }
      this.querys[fieldId] = qb;
    } else if (this.querys?.[fieldId] && this.querys?.[fieldId]?.id) {
      this.queryBuilds.remove(this.querys[fieldId].id);
      delete this.querys[fieldId];
    }
    this.setState({
      checkFilterFiled: data ? fieldId : "",
    });
    this.reload();
  };
  ordersFun = (orders) => {
    let strArray = [];
    orders.forEach((value, key) => {
      strArray.push(`${key} ${value}`);
    });
    return strArray.join(",");
  };
  changeOrdersData = (orders) => {
    const { checkOrderField, ordersDatas } = this.state;
    orders.forEach((value, key) => {
      if(value){
        ordersDatas.set(key, segemntedArray[value])
      }else{
        ordersDatas.delete(key)
      }
    });
    const orderStr = this.ordersFun(ordersDatas);
    this.conditions.orders = orderStr;
    this.store.setOrders(orderStr);
    this.reload();
  };
  oldchangeOrdersData = (field, order) => {
    const orders = { ...this.state.orders };
    const { checkOrderField } = this.state;
    const { newFormId, appId, fieldId } = this.props;
    if (checkOrderField) {
      delete orders[checkOrderField];
    }
    if (order) {
      orders[field] = order;
    } else {
      delete orders[field];
    }

    const datas = Object.keys(orders).map((column) => ({
      column,
      order: orders[column],
    }));
    const sortDatas = this.ordersFun(orders);
    this.conditions.orders = sortDatas;
    this.store.setOrders(sortDatas);
    this.reload();
    this.setState({
      orders,
      checkOrderField: field,
    });
  };
  setStorage = () => {
    const { appId, newFormId, fieldId } = this.props;
    if (fieldId) {
      setView(appId, newFormId, fieldId, this.state.view.id);
      setCurrPage(
        appId,
        newFormId,
        fieldId,
        this.store.page + 1,
        this.urlOperator
      );
      setPageSize(
        appId,
        newFormId,
        fieldId,
        this.store.pageSize || 10,
        this.urlOperator
      );
    }
  };
  getStorage = () => {
    const { appId, newFormId, fieldId } = this.props;
    if (fieldId) {
      return {
        viewId: getSelectView(appId, newFormId, fieldId),
        currPage: getCurrPage(appId, newFormId, fieldId, this.urlOperator),
        pageSize: getPageSize(appId, newFormId, fieldId, this.urlOperator),
      };
    }
    return {};
  };
  getIntDisplay = (item, keys) => {
    let subItem;
    for (let j = 0; j < keys.length; j++) {
      subItem = item[keys[j]];
    }
  };
  reload = async (type) => {
    // 可能因为上滑的原因，page不指向第一页，目前reload时，都从第一页开始加载数据
    this.store.setPage(1);

    if (type === "refresh") {
      this.store.setQuerys("").addQuerys(this.defaultQuery);
    } else {
      this.store
        .setQuerys("")
        .addQuerys(`${this.queryBuilds.sqlText}`) // 表头
        .addQuerys(this.searchQuery) // 搜索框的
        .addQuerys(this.defaultQuery); // 设计时设定的默认搜索条件
    }

    const result = await this.store.load();
    if (result.errorCode === "0") {
      this.setStorage();
      const { items, title, count } = result.data;
      if (this.props.searchcolumn) {
        const searchPlaceholder = title
          .filter(
            (t) => this.props.searchcolumn.indexOf(Object.keys(t)[0]) !== -1
          )
          .map((t) => `“${t[Object.keys(t)[0]]}”`)
          .join("、");
        this.setState({ searchPlaceholder: `可通过${searchPlaceholder}搜索` });
      }
      if (!items || items.length === 0) {
        this.setState({
          columns: title || [],
          datas: [],
          pagination: {
            ...this.state.pagination,
            current: 1,
            total: Number(count),
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          },
          emptyData: true,
        });
      } else {
        // 当为引用多选的时候，列表进来的值为字符串，引用多选的值字段时数值的时候，将数值转为字符串
        if (this.props.listType === "refMulti") {
          const valueField = this.state.fields.find(
            (i) => i.id === this.props.valueColumn
          );
          if (valueField && valueField.type === "Number") {
            items.map(
              (item) =>
                (item[this.props.valueColumn].value = `${
                  item[this.props.valueColumn].value
                }`)
            );
          }
          // console.log('asdnakjdlkadlajdkljalkdjklkajdlkjadlkjalkd', valueField, this.state.fields)
        }
        // console.log('asdnakjdlkadlajdkljalkdjklkajdlkjadlkjalkd', items, result)
        this.setState({
          columns: title || [],
          datas: items,
          pagination: {
            ...this.state.pagination,
            current: 1,
            total: Number(count),
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          },
          emptyData: null,
        });
      }
    } else {
      Toast.fail(result.errorMsg);
    }
  };

  batchDeleteFun = async () => {
    if (this.state.selectedRowKeys && this.state.selectedRowKeys.length !== 0) {
      const { appId, formId } = this.props;
      const result = await deleteData(
        appId,
        formId,
        this.state.selectedRowKeys
      );
      if (result) {
        this.setState({
          selectedRowKeys: [],
          selectedRows: [],
          showConfirmBut: 0,
        });
        this.changePageSize(
          this.state.pagination.current,
          this.state.pagination.pageSize
        );
        this.reload();
      }
    }
  };

  batchEdit = async (data, ids = this.state.selectedRowKeys) => {
    const datas = {
      ids,
      data,
    };
    const result = await batchEditDatas(
      this.props.appId,
      this.props.formId,
      datas
    );
    if (result) {
      this.setState({
        selectedRowKeys: [],
        selectedRows: [],
        showConfirmBut: 0,
      });
      this.reload();
    }
  };

  copyFormDatabtn = (fieldId, callback) => {
    const { appId, formId } = this.props;
    GO_COPY(callback, appId, formId, this.state.selectedRowKeys[0], fieldId);
    this.setState({ selectedRowKeys: [], selectedRows: [], showConfirmBut: 0 });
  };

  operationFunc = (bId, value) => {
    if (bId === buttionIds.textSearch) {
      this.textSearchFun(bId, value);
    } else if (bId === buttionIds.batchDelete) {
      this.batchDeleteFun();
    } else if (bId === buttionIds.import) {
      this.importList(true);
    } else if (bId === buttionIds.export) {
      const query = Object.keys(this.filterData)
        .map((key) => `${key} like ${this.filterData[key]}`)
        .join(" and ");
      const allQuerys = [];
      if (this.props.query) allQuerys.push(`(${this.props.query})`);
      if (this.searchQuery) allQuerys.push(`(${this.searchQuery})`);
      if (query) allQuerys.push(`(${query})`);
      const querys = allQuerys.join(" and ");
      this.conditions.querys = querys;
      this.conditionData(this.conditions);
    } else if (bId === buttionIds.viewQuery) {
      if (this.state.editView.id) {
        this.setState({ editView: {} });
      }
      this.setState({ openQuerys: true });
    }
  };
  conditionData = async (conditions) => {
    const querys = conditions.querys ? `&$filter=${conditions.querys}` : "";
    const orders = conditions.orders ? `&$orderby=${conditions.orders}` : "";
    const filter =
      !this.state.view.id && conditions.filter
        ? `&$querys=${conditions.filter}`
        : "";
    const select =
      !this.state.view.id && conditions.select
        ? `&$select=${conditions.select}`
        : "";
    const viewId =
      this.state.view.id && conditions.viewId
        ? `&viewId=${conditions.viewId}`
        : "";
    const cond = (querys + orders + filter + select + viewId).replace(/^&/, "");
    const condition = cond ? `${cond}` : "";
    const exportResult = await exportList(
      this.props.appId,
      this.props.formId,
      condition
    );
    if (exportResult.errorCode !== "0") Toast.fail(exportResult.errorMsg);
  };
  textSearchFun = (bId, value) => {
    const qs = new QueryBuilds();
    if (value) {
      this.searchText = value;
      this.props.searchcolumn.forEach((fieldId, index) => {
        const fieldType = this.state.fields.find((f) => f.id === fieldId).type;
        const keyId = fieldTypeSift(fieldType) ? `${fieldId}.display` : fieldId;
        const qd = new QueryBuild({});
        if (index !== this.props.searchcolumn.length - 1) {
          qd.key(keyId)
            .operator(QueryBuild.OPERATOR_LINK)
            .value(this.searchText)
            .or();
        }
        if (index === this.props.searchcolumn.length - 1) {
          qd.key(keyId)
            .operator(QueryBuild.OPERATOR_LINK)
            .value(this.searchText);
        }
        qs.add(qd);
      });
      this.searchQuery = qs.sqlText;
    } else {
      this.searchQuery = "";
    }
    this.reload();
  };

  changeSearchValue = (value) => {
    this.searchText = value;
  };
  _operCheckPriv = (operId) => {
    const { isAuth, isView } = this.checkPrivByOperId(operId);
    if (isAuth === false) {
      Modal.alert("权限警告", "\n   对不起，您没有权限做此操作。", [
        { text: "确定", onPress: () => {}, style: { fontWeight: "bold" } },
      ]);
      return undefined;
    }
    return isView;
  };

  _handleEdit = (id) => {
    // TODO：需确定权限控制如何进行
    const isView = this._operCheckPriv(buttionIds.edit);
    if (isView !== undefined) {
      // 遍历组件判断是有且只显示一个报表组件
      //
      // const reportListArr = this.state.fields.map((fieldEle) => { if (fieldEle.properties.visible && fieldEle.type === 'Report') {} })
      const reportListArr = [];
      const otherCom = [];
      for (let i = 0; i < this.state.fields.length; i++) {
        const fieldEle = this.state.fields[i];
        if (fieldEle.properties.visible && fieldEle.type === "Report") {
          reportListArr.push(fieldEle);
        } else if (fieldEle.properties.visible) {
          otherCom.push(fieldEle);
        }
      }

      if (otherCom.length === 0 && reportListArr.length === 1) {
        GO_FULLREPORTVIEW({
          label: reportListArr[0].properties.label,
          url: "",
          appId: this.props.appId,
          item: reportListArr,
        });
      } else {
        this.props.selectRow(id, buttionIds.edit, isView);
      }
      this.tableRef.current.reloadList();
    }
  };

  _handleAdd = (InvoServiceId) => {
    const isView = this._operCheckPriv(buttionIds.add);
    if (isView !== undefined) {
      let query;
      if (
        this.props.addDefaultValue &&
        this.props.addDefaultValue !== null &&
        this.props.addDefaultValue !== ""
      ) {
        query = {
          refcolumn: this.props.addDefaultValue,
          refdataId: this.props.dataId,
        };
      }
      // console.log('asdadhkashdkahdhaksdhkad', buttionIds.add, isView, query, InvoServiceId)
      this.props.selectRow(null, buttionIds.add, isView, query, InvoServiceId);
    }
  };

  _handleDel = async (id) => {
    this._operCheckPriv(buttionIds.delete);
    await deleteData(this.props.appId, this.props.formId, [id]);
    this.checkedFunc(true, { id: { value: id } });
    this.reload();
  };

  selectView = async (viewId, defaultView) => {
    if (this.state.view.id !== viewId) {
      const { appId, newFormId, fieldId } = this.props;
      // console.log('asjdlkadlkdla,mnxzc,mn,maslkdlkasjlkadjlasjdl', this.props.mobileshowfields)
      if (!this.props.displayColumns)
        this.store.setDisplayColumns(this.props.mobileshowfields || "");
      await getView(appId, newFormId, viewId, fieldId).then(async (views) => {
        const view = this.initViews(views);
        this.initOrder(view.orders);
        this.store.setViewId(viewId);
        this.store.setViewQuery("");
        this.store.setOrders(this.ordersFun(this.state.ordersDatas));
        this.setState({
          view,
          selectedRowKeys: [],
          selectedRows: [],
          showConfirmBut: 0,
          pagination: defaultView
            ? {
                ...this.state.pagination,
              }
            : {
                ...this.state.pagination,
                current: 1,
              },
        });
        if (!defaultView) this.store.setPage(1);
        await this.reload();
      });
    }
  };
  initViews = (views) => {
    const titles = [...this.state.titles];
    const view = { ...views };
    const columns =
      this.props.tableType === "reference"
        ? this.props.viewvisiblecolumn
        : view.columns;
    view.columns = columns.filter((fieldId) =>
      titles.find((value) => fieldId === Object.keys(value)[0])
    );
    return view;
  };

  getViews = () => {
    const { appId, newFormId, fieldId } = this.props;
    getViews(appId, newFormId, fieldId).then((views) => {
      this.setState({ views });
    });
  };
  validateViews = (viewfilter) => {
    let validate = true;
    const find = (group) => {
      if (group.groups.length === 0 && group.querys.length === 0) {
        validate = false;
      }
      group.querys.forEach((q) => {
        if (!q.field || !q.operator) {
          validate = false;
        }
      });
      group.groups.forEach((g) => find(g));
    };
    if (
      !viewfilter ||
      (viewfilter.groups.length === 0 && viewfilter.querys.length === 0)
    ) {
      return true;
    }
    viewfilter && find(viewfilter);
    return validate;
  };
  searchByView = () => {
    this.initOrder();
    this.store.setOrders(this.ordersFun(this.state.ordersDatas));
    if (!this.validateViews(this.state.editView.filter)) {
      Toast.info("查询条件不正确!!!");
      return;
    }
    this.setState({
      view: {
        ...this.state.editView,
        columns: this.state.view.columns,
        columnWidths: {},
        orders: [],
      },
      openQuerys: false,
      selectedRowKeys: [],
    });

    const { appId, newFormId, fieldId } = this.props;
    setSelectIds(appId, newFormId, fieldId, [], this.urlOperator);
    // 刷新列表
    this.store.setViewId("");
    this.store.setViewQuery(this.state.editView.filter);
    this.store.setPage(1);
    this.store.setDisplayColumns(
      this.props.mobileshowfields ||
        this.props.displayColumns ||
        this.state.view.columns
    );
    this.reload();
  };

  getByteLen = (val) => {
    val = val || "";
    let len = 0;
    for (let i = 0; i < val.length; i++) {
      const a = val.charAt(i);
      if (a.match(/[^\x00-\xff]/gi) != null) {
        len += 2;
      } else {
        len += 1;
      }
    }
    return len;
  };
  /*
    Operator ID (string):
      1: 查看；
      2：新增；
      3：编辑；
      4：删除；
      5：导入；
      6：导出；
      7：批量；
      10：查询；
      11：文本检索；
      15：视图方案;
  */
  checkPrivByOperId = (operId) => {
    let isAuth = false;
    let isView = false;
    let authArray = null;
    if (this.props.authority) {
      authArray = this.props.authority;
    } else if (this.state.allOperations) {
      if (this.props.operationarea) {
        authArray = this.state.allOperations.filter(
          (oper) =>
            this.props.operationarea.indexOf(oper.id) !== -1 ||
            oper.id === buttionIds.edit ||
            oper.id === buttionIds.delete ||
            oper.id === buttionIds.batch
        );
      } else {
        authArray = this.state.allOperations;
      }
    }
    if (authArray !== null) {
      isAuth = authArray.findIndex((x) => x.id === operId) !== -1;
      if (!isAuth && operId === buttionIds.edit) {
        isView =
          this.state.allOperations.findIndex(
            (x) => x.id === buttionIds.view
          ) !== -1;
        isAuth = isView;
      }
    }
    // console.log('asdhjkasdhkahdkjahdkhkjdhakd', operId, this.state.allOperations, this.state.allOperations, authArray, isAuth)

    return { isAuth, isView };
  };
  rowRender =
    (listType) => (dataItem, width, height, screenDirection, rotateScreen) => {
      const tmp = this._getDisplayCol(this.state.columns, dataItem);
      // console.log('asdjlkajsdlkajsdlkjalsdjlajdlajldalsdjljdlkd', tmp)
      return (
        <RowStyle
          key={dataItem.id}
          tmp={tmp}
          dataItem={dataItem}
          isReference={listType === "reference"}
          isRefMulti={listType === "refMulti"}
          onClickRow={this.onClickRow}
          valueColumn={this.props.valueColumn}
          selectedRows={this.state.selectedRows}
          checkPrivByOperId={this.checkPrivByOperId}
          handleDel={this._handleDel}
          showChecked={this.state.showChecked}
          tree={this.props.tree}
          treekey={this.state.treekey}
          width={width}
          height={height}
          screenDirection={screenDirection}
          rotateScreen={rotateScreen}
        />
      );
    };

  onClickRow = async ({
    checked,
    dataItem,
    id,
    isReference,
    isRefMulti,
    screenDirection,
    rotateScreen,
  }) => {
    if (this.state.showChecked) {
      this.checkedFunc(checked, dataItem);
    } else {
      if (isReference) {
        if (!this.clicked) {
          this.clicked = true;
          if (this.props.tree) {
            this.setState({ treekey: id });
          } else {
            NavigationService.back();
          }
          await this.props.selectRow(dataItem);
          this.clicked = false;
        }
      } else if (isRefMulti) {
        this.multiSelect(checked, dataItem);
      } else {
        this._handleEdit(id);
      }
      if (screenDirection !== "PORTRAIT" && rotateScreen) {
        rotateScreen();
      }
    }
  };

  showNagzActionSheet = (invocationServiceFields) => {
    const BUTTONS = invocationServiceFields.map(
      (field) => field.properties.label
    );
    BUTTONS.push("取消");
    ActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: BUTTONS.length - 1,
        message: "选择调用服务",
      },
      (buttonIndex) => {
        if (invocationServiceFields[buttonIndex]) {
          if (invocationServiceFields[buttonIndex].properties.enabled) {
            this._handleAdd(invocationServiceFields[buttonIndex].id);
          } else {
            Toast.info("不可更改", 1);
          }
        }
      }
    );
  };

  emptyData = () => {
    const datas = [];
    const addDatas = {
      img: require("../../images/icon-colorfull-add-default.png"),
      title: "新增",
      subtitle: "手工录入",
      onChange: () => {
        this._handleAdd();
      },
    };
    if (this.checkPrivByOperId(buttionIds.add).isAuth) {
      datas.push(addDatas);
    }

    const invocationServiceFields = this.state.fields.filter((field) =>
      isInvocationServiceComponent(field)
    );
    if (invocationServiceFields.length) {
      if (invocationServiceFields.length > 1) {
        const scanner = {
          img: require("../../images/icon-colorfull-scanner-press.png"),
          title: "扫描",
          subtitle: "相机扫描",
          onChange: () => {
            this.showNagzActionSheet(invocationServiceFields);
          },
        };
        datas.push(scanner);
      } else {
        invocationServiceFields.forEach((field) => {
          datas.push({
            img: require("../../images/icon-colorfull-scanner-press.png"),
            title: field.properties.label,
            onChange: () => {
              this._handleAdd(field.id);
            },
          });
        });
      }
    }

    return (
      <View style={styles.emptyData}>
        {datas.map((data, index) => (
          <TouchableOpacity
            key={index}
            style={styles.emptyDataView}
            onPress={data.onChange}
            activeOpacity={0.8}
          >
            <Image style={styles.emptyDataImg} source={data.img} />
            <View style={styles.emptyDataText}>
              <Text style={styles.emptyDataTitle}>{data.title}</Text>
              {data.subtitle && (
                <Text style={styles.emptyDataSubtitle}>{data.subtitle}</Text>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  setformOperatorStaus = (selectRows, selectedRowKeys) => {
    let selectDatas = {};
    selectRows.forEach((row) => {
      selectDatas[row.id.value] = row.$$status;
    });
    const resDatas = selectedRowKeys.reduce((res, key) => {
      const id = key.split("_")[0];
      return { ...res, [id]: selectDatas[id] };
    }, {});
    selectDatas = resDatas;
    this.formOperatorStaus = { hide: [], locked: [] };
    const formOperaField = this.state.fields.filter(isFormOperationComponent);
    Object.keys(selectDatas).forEach((id) => {
      if (!(formOperaField && formOperaField.length !== 0)) {
        if (selectDatas[id] && selectDatas[id].hide) {
          this.formOperatorStaus.hide = union(
            this.formOperatorStaus.hide,
            selectDatas[id].hide
          );
        }
        if (selectDatas[id] && selectDatas[id].locked) {
          this.formOperatorStaus.locked = union(
            this.formOperatorStaus.locked,
            selectDatas[id].locked
          );
        }
      } else {
        formOperaField.forEach((f) => {
          if (
            selectDatas[id] &&
            (selectDatas[id].hide || selectDatas[id].show) &&
            (selectDatas[id].hide.indexOf(f.id) !== -1 ||
              selectDatas[id].show.indexOf(f.id) !== -1)
          ) {
            this.formOperatorStaus.hide = union(
              this.formOperatorStaus.hide,
              selectDatas[id].hide
            );
          } else if (f.properties.visible === false) {
            this.formOperatorStaus.hide = union(this.formOperatorStaus.hide, [
              f.id,
            ]);
          }
          if (
            selectDatas[id] &&
            (selectDatas[id].locked || selectDatas[id].unlocked) &&
            (selectDatas[id].locked.indexOf(f.id) !== -1 ||
              selectDatas[id].unlocked.indexOf(f.id) !== -1)
          ) {
            this.formOperatorStaus.locked = union(
              this.formOperatorStaus.locked,
              selectDatas[id].locked
            );
          } else if (f.properties.enabled === false) {
            this.formOperatorStaus.locked = union(
              this.formOperatorStaus.locked,
              [f.id]
            );
          }
        });
      }
    });
  };
  checkedFunc = (checked, dataItem) => {
    let selectedRows = this.state.selectedRows;
    let selectedRowKeys = this.state.selectedRowKeys;
    if (checked) {
      selectedRows = selectedRows.filter(
        (d) => d.id.value !== dataItem.id.value
      );
    } else {
      selectedRows.push(dataItem);
    }
    selectedRowKeys = selectedRows.map((d) => d.id.value);
    this.setformOperatorStaus(selectedRows, selectedRowKeys);
    this.setState({
      selectedRows,
      selectedRowKeys,
      showConfirmBut: selectedRows.length,
    });
  };
  multiSelect = (checked, dataItem) => {
    let selectedRows = this.state.selectedRows;
    let selectedRowKeys = this.state.selectedRowKeys;
    const valId = this.props.valueColumn || "id";
    if (checked) {
      selectedRows = selectedRows.filter(
        (d) => d[valId].value !== dataItem[valId].value
      );
    } else {
      selectedRows.push(dataItem);
    }
    selectedRowKeys = selectedRows.map((d) => d[valId].value);
    this.setState({
      selectedRows,
      selectedRowKeys,
      showConfirmBut: selectedRows.length,
    });
  };
  getTreeFieldProps = async () => {
    const {
      fieldConfig,
      treeField: treeFieldId,
      ownerFormId,
      appId,
    } = this.props;
    const formFields =
      _get(fieldConfig, ["config", "form", "fields"], null) ||
      (await getFields(appId, ownerFormId)) ||
      []; // 获取主表所有字段
    const treeField = formFields.find(({ id }) => id === treeFieldId);
    if (treeField && treeField.properties) {
      return treeField.properties;
    }
    return null;
  };
  loadTreeData = async () => {
    const { appId, ownerFormId, fieldConfig } = this.props;
    const treeFieldProps = await this.getTreeFieldProps();
    if (treeFieldProps) {
      const { filter, orderby, fieldid } = treeFieldProps;
      const treeDatas = await getTreeData(appId, ownerFormId, fieldid, {
        filter,
        orderby,
      });
      this.setState({ treeDatas });
    }
  };
  openTreeHandle = () => {
    this.setState({ treeIsOpen: true });
  };
  closeTreeHandle = () => {
    this.setState({ treeIsOpen: false });
  };
  toggleTreeHandle = () => {
    const { treeIsOpen } = this.state;
    this.setState({ treeIsOpen: !treeIsOpen });
  };
  selectTreeItem = async (value) => {
    const { matchTreefield } = this.props;
    this.closeTreeHandle();
    const opt = "eq";
    this.store.setPage(1);
    this.store
      .setQuerys("")
      .addQuerys(`${this.queryBuilds.sqlText}`) // 表头
      .addQuerys(this.searchQuery) // 搜索框的
      .addQuerys(value ? `${matchTreefield} ${opt} ${value}` : "") //树筛选条件
      .addQuerys(this.defaultQuery); // 设计时设定的默认搜索条件
    const result = await this.store.load();
    if (result.errorCode === "0") {
      this.setStorage();
      const { items, title, count } = result.data;
      if (this.props.searchcolumn) {
        const searchPlaceholder = title
          .filter(
            (t) => this.props.searchcolumn.indexOf(Object.keys(t)[0]) !== -1
          )
          .map((t) => `“${t[Object.keys(t)[0]]}”`)
          .join("、");
        this.setState({ searchPlaceholder: `可通过${searchPlaceholder}搜索` });
      }
      if (!items || items.length === 0) {
        this.setState({
          columns: title || [],
          datas: [],
          pagination: {
            ...this.state.pagination,
            current: 1,
            total: Number(count),
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          },
          emptyData: true,
        });
      } else {
        this.setState({
          columns: title || [],
          datas: items,
          pagination: {
            ...this.state.pagination,
            current: 1,
            total: Number(count),
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          },
          emptyData: null,
        });
      }
    } else {
      Toast.fail(result.errorMsg);
    }
  };
  render() {
    const {
      newFormId,
      closeModal,
      displaytype,
      listType,
      goCreatPage,
      isTab,
      multiSelectFn,
      batcheditcolumn = [],
      matchTreefield,
      treeField,
      formFields,
    } = this.props;
    const { treeIsOpen, treeDatas } = this.state;
    const formOperations = this.state.formOperations.filter(
      (f) => batcheditcolumn.indexOf(f.id) !== -1
    );
    const isList = newFormId === "LIST";
    console.log(isList, this.state.emptyData, treeField, this.state.dataLoaded);
    // console.log('asdlakdlka;ldk;lad;la;ldka;lkd;lakd;lakd;ka;d;lasdk;', this.props, this.state.datas)
    return (
      <View style={{ width }}>
        {isList && this.state.emptyData ? (
          this.emptyData()
        ) : this.state.dataLoaded ? (
          [
            !!treeField && (
              <Modal
                presentationStyle="fullScreen"
                popup
                maskClosable
                visible={treeIsOpen}
                animationType="slide-up"
              >
                <TreeSelect
                  treeDatas={treeDatas}
                  closeTreeHandle={this.closeTreeHandle}
                  selectTreeItem={this.selectTreeItem}
                ></TreeSelect>
              </Modal>
            ),
            <DataTableStyle
              changeFilterData={this.changeFilterData}
              changeOrdersData={this.changeOrdersData}
              toggleTreeHandle={this.toggleTreeHandle}
              treeField={treeField}
              openTreeHandle={this.openTreeHandle}
              customerBack={this.props.customerBack}
              ref={this.tableRef}
              toggleMenu={this.props.toggleMenu}
              closeModal={closeModal}
              showHeader={!isList}
              scrollEnabled={this.state.enableScrollViewScroll}
              datas={this.state.datas}
              rowRender={this.rowRender(listType)}
              onLoading={this.changePage}
              pagination={this.state.pagination}
              placeholder={this.state.searchPlaceholder}
              operationFunc={this.operationFunc}
              checkPrivByOperId={(buttonType) =>
                this.checkPrivByOperId(buttonType).isAuth
              }
              views={this.state.views}
              view={this.state.view}
              selectView={this.selectView}
              goCreatPage={goCreatPage}
              searchdDatas={this.state.searchdDatas}
              showConfirmBut={this.state.showConfirmBut}
              isReference={listType === "reference"}
              isRefMulti={listType === "refMulti"}
              showChecked={this.state.showChecked}
              formOperatorStaus={this.formOperatorStaus}
              selectedRows={this.state.selectedRows}
              getDisplayCol={this._getDisplayCol}
              onClickRow={this.onClickRow}
              changeShowChecked={(showChecked) => {
                this.setState({ showChecked });
              }}
              listType={listType}
              formOperations={formOperations}
              handleAdd={this._handleAdd}
              batchEdit={this.batchEdit}
              formField={this.formField}
              isTab={isTab}
              fields={this.state.fields}
              goCopy={this.copyFormDatabtn}
              columns={this.state.columns}
              displaytype={displaytype}
              multiSelectFn={multiSelectFn}
              selectedRowKeys={this.state.selectedRowKeys}
              valueData={this.state.valueData}
              valueColumn={this.props.valueColumn}
              tree={this.props.tree}
              treekey={this.state.treekey}
              mColWidth={this.props.mColWidth}
              rotateScreen={this.props.rotateScreen}
              tableSize={this.props.tableSize}
            />,
          ]
        ) : (
          <View style={{ width, height, backgroundColor: "#fff" }} />
        )}
      </View>
    );
  }
}

DataTable.propTypes = {
  appId: PropTypes.string,
  formId: PropTypes.string,
  displayColumns: PropTypes.arrayOf(PropTypes.string), // eslint-disable-line
  selectionType: PropTypes.oneOf(["checkbox", "radio"]),
  selectRow: PropTypes.func,
  pagination: PropTypes.shape({
    page: PropTypes.number,
    pageSize: PropTypes.number,
  }),
  operations: PropTypes.shape({
    reload: PropTypes.func,
  }),
  showRowSelection: PropTypes.bool,
  closeModal: PropTypes.func,
  toggleMenu: PropTypes.func,
  mobileshowfields: PropTypes.arrayOf(PropTypes.string),
  isTab: PropTypes.bool,
  isSubForm: PropTypes.bool,
  orders: PropTypes.object, // eslint-disable-line
  operationarea: PropTypes.arrayOf(PropTypes.string),
  statisticalColumn: PropTypes.arrayOf(PropTypes.object),
  searchcolumn: PropTypes.arrayOf(PropTypes.string),
  addDefaultValue: PropTypes.string,
  fieldId: PropTypes.string,
  newFormId: PropTypes.string,
  query: PropTypes.string,
  listType: PropTypes.string,
  displaytype: PropTypes.string,
  viewvisiblecolumn: PropTypes.arrayOf(PropTypes.string),
  tableType: PropTypes.string,
  addMutiRow: PropTypes.func,
  multiSelectFn: PropTypes.func,
  goCreatPage: PropTypes.func,
  value: PropTypes.array,
  valueData: PropTypes.array,
  valueColumn: PropTypes.string,
  fieldConfig: PropTypes.shape(PropTypes.object),
};

DataTable.defaultProps = {
  selectionType: "checkbox",
  showRowSelection: true,
  showLable: false,
  fieldConfig: {},
};
export const DataTablePage = DataTable;
export default SideMenu(DataTable);
