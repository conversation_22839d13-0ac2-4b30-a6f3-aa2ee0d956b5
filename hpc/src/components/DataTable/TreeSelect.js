import {
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  TouchableOpacity,
} from "react-native";
import React, { useState, useEffect } from "react";
import { SearchBar } from "@ant-design/react-native";
import _get from "lodash/get";
import _dropRight from 'lodash/dropRight'
import { treeSelectStyles } from "./style";
import folderImg from "../../images/icon-tree_datatable-fold.png";
import fileImg from "../../images/icon-tree_datatable-file.png";
import arrowImg from "../../images/icon-arrow-right-small-default.png";
import backImg from "../../images/icon-arrow-previous-default.png";
import { ScrollView } from "react-native-gesture-handler";
const TreeSelect = ({ treeDatas, selectTreeItem,closeTreeHandle }) => {
  const [filterTreeDatas, setfilterTreeDatas] = useState(treeDatas);
  const [filterPath, setfilterPath] = useState([]);
  console.log(treeDatas);
  useEffect(() => {
    if (filterPath.length) {
      const filterData = _get(treeDatas, filterPath, []);
      console.log(treeDatas, filterData, filterPath);
      setfilterTreeDatas(filterData);
    } else {
      setfilterTreeDatas(treeDatas);
    }
  }, [treeDatas, filterPath]);
  function folderOnClick(index) {
    const newFilterPath = [...filterPath, index, "children"];
    setfilterPath(newFilterPath);
  }
  function fileOnClick(item) {
    console.log(item);
    selectTreeItem(item);
  }
  function goPrev(){
    const newFilterPath = _dropRight(filterPath,2)
    setfilterPath(newFilterPath);
  }
  return (
    <KeyboardAvoidingView>
      <ScrollView style={treeSelectStyles.container}>
        <View
          style={[
            treeSelectStyles.operRow,
            treeSelectStyles.flexRow,
            treeSelectStyles.flexJscb,
          ]}
        >
            {filterPath.length>0?<TouchableOpacity onPress={goPrev}><Image
            source={backImg}
            resizeMode="contain"
            style={treeSelectStyles.treeIcon}
          ></Image></TouchableOpacity>:<View></View>}
          <View style={treeSelectStyles.flexRow}>
            <Text onPress={()=>fileOnClick('')} style={treeSelectStyles.treeItemText}>清空</Text>
            <Text onPress={closeTreeHandle} style={treeSelectStyles.treeItemText}>关闭</Text>
          </View>
        </View>
        <View>
          {filterTreeDatas.map((item, index) => (
            <TouchableOpacity
              onPress={
                item.children
                  ? () => folderOnClick(index)
                  : () => fileOnClick(item.id)
              }
            >
              <View key={item.id} style={treeSelectStyles.treeItem}>
                <View style={treeSelectStyles.flexRow}>
                  <Image
                    source={item.children ? folderImg : fileImg}
                    style={treeSelectStyles.treeIcon}
                  ></Image>
                  <Text style={treeSelectStyles.treeItemText}>
                    {item.display}
                  </Text>
                </View>
                {item.children ? (
                  <Image
                    source={arrowImg}
                    resizeMode="contain"
                    style={treeSelectStyles.arrowIcon}
                  ></Image>
                ) : (
                  <View></View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default TreeSelect;
