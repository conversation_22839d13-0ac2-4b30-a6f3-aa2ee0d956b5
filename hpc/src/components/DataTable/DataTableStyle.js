import React from "react";
import PropTypes from "prop-types";
import {
  Text,
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Animated,
  Keyboard,
  Platform,
} from "react-native";
import { ActionSheet } from "nagz-mobile-lib";
import Orientation from "react-native-orientation";
import { debounce } from "lodash-es";
import ModalHeader from "../../addons/FormComponent/ModalHeader";
import DataTableList from "./DataTableList";
import { buttionIds } from "./Store";
import { dataTableStyles as styles } from "./style";
import { deviceType, screenHeight, screenWidth } from "../../config/sysPara";
import treeFilterImg from "../../images/icon-tree_datatable-tree.png";
export const isIos = Platform.OS === "ios";

const addButImg = require("../../images/icon-add-large-fill-default.png");
const searchButImg = require("../../images/icons-magnifier-search-default.png");
const viewImgSrcUp = require("../../images/icons-arrow-x_small-up-default.png");
const viewImgSrcDown = require("../../images/icons-arrow-x_small-down-default.png");
const arrowPrevious = require("../../images/icon-arrow-previous-default.png");

export default class DataTableStyle extends React.Component {
  constructor(props) {
    super(props);
    this.searchValue = "";
    this.tableList = React.createRef();
  }

  state = {
    showSearchView: false,
    searchLeft: 0,
    keyboardHeight: 0,
    showViewList: false,
    view: {},
    viewImgSrc: viewImgSrcDown,
    searchValue: "",
    showChecked: false,
    treekey: "",
    screenDirection: "PORTRAIT",
  };
  componentWillMount() {
    this.setState({
      view: this.props.view,
      showChecked: this.props.showChecked,
      treekey: this.props.treekey,
      searchLeft: new Animated.Value(12),
    });
    this.keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        this.operationFunc();
      }
    );
  }

  componentWillReceiveProps = ({ view, showChecked, treekey }) => {
    if (view !== this.props.view) {
      this.setState({ view });
    }
    if (showChecked !== this.props.showChecked) {
      this.setState({ showChecked });
    }
    if (treekey !== this.props.treekey) {
      this.setState({ treekey });
    }
  };

  componentWillUnmount() {
    this.keyboardDidHideListener.remove();
    this.state.screenDirection !== "PORTRAIT" && this.rotateScreen();
  }

  reloadList = () => {
    this.tableList.current.onRefresh();
  };
  clickSearchBut = (isTrue) => {
    const width =
      this.state.screenDirection === "PORTRAIT" ? screenWidth : screenHeight;
    if (isTrue) {
      Animated.timing(this.state.searchLeft, {
        toValue: 70 - width,
        duration: 100,
      }).start();
      this.setState({ showSearchView: true });
    } else {
      Animated.timing(this.state.searchLeft, {
        toValue: 12,
        duration: 100,
      }).start();
      this.operationFunc("");
      this.setState({
        showSearchView: false,
        searchValue: "",
      });
    }
  };
  changeText = (v) => {
    this.setState({ searchValue: v });
    this.debounceSearch(v);
  };
  operationFunc = (searchValue = this.state.searchValue) => {
   
    this.props.operationFunc(buttionIds.textSearch, searchValue);
  };
  debounceSearch = debounce(
    (searchValue) => this.operationFunc(searchValue),
    500
  );
  changeView = () => {
    // console.log('adnlkadlkasjdlkajlkajdla')
    if (this.props.checkPrivByOperId(buttionIds.viewQuery)) {
      this.setState({
        showViewList: true,
        viewImgSrc: viewImgSrcUp,
      });
      const BUTTONS = this.props.views.map((d) => d.name);
      BUTTONS.push("取消");
      ActionSheet.showActionSheetWithOptions(
        {
          options: BUTTONS,
          cancelButtonIndex: BUTTONS.length - 1,
          wrapProps: {
            onTouchStart: (e) => e.preventDefault(),
          },
        },
        (buttonIndex) => {
          if (
            buttonIndex < this.props.views.length &&
            this.props.views[buttonIndex]
          ) {
            const item = this.props.views[buttonIndex];
            this.props.selectView(item.id);
          }
          this.setState({
            showViewList: false,
            viewImgSrc: viewImgSrcDown,
          });
        }
      );
    }
  };

  closeModal = () => {
    this.props.closeModal(this.props.listType === "dataTable", {
      formUpdate: Math.random(),
    });
  };
  rotateScreen = () => {
    if (this.state.screenDirection === "LANDSCAPE") {
      Orientation.lockToPortrait();
      this.setState({
        screenDirection: "PORTRAIT",
      });
      if (this.props.rotateScreen) this.props.rotateScreen("PORTRAIT");
    } else {
      if (deviceType === 2) {
        Orientation.lockToLandscapeRight();
      } else {
        Orientation.lockToLandscapeLeft();
      }

      this.setState({
        screenDirection: "LANDSCAPE",
      });
      if (this.props.rotateScreen) this.props.rotateScreen("LANDSCAPE");
    }
  };
  leftView = () => {
    const { treeField, toggleTreeHandle } = this.props;
    return (
      !this.state.showSearchView && (
        <View
          style={{
            height: "100%",
            flexDirection: "row",
            justifyContent: "flex-start",
            alignItems: "center",
          }}
        >
          <TouchableOpacity
            style={{
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
            onPress={this.closeModal}
          >
            <Image source={arrowPrevious} style={{ alignSelf: "center" }} />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
              marginLeft: 10,
            }}
            onPress={this.props.toggleMenu}
          >
            <Image
              style={{ alignSelf: "center" }}
              source={require("../../images/icons-burger-outline-line-default.png")}
            />
          </TouchableOpacity>
          {!!treeField && (
            <TouchableOpacity
              style={{
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 10,
              }}
              onPress={toggleTreeHandle}
            >
              <Image style={{ alignSelf: "center" }} source={treeFilterImg} />
            </TouchableOpacity>
          )}
        </View>
      )
    );
  };

  centerText = () => {
    let number = 1;
    if (this.state.view.name.length * 18 > 132) {
      number = 2;
    }
    const viewQuery = this.props.checkPrivByOperId(buttionIds.viewQuery);
    if (this.state.showSearchView) {
      return null;
    } else if (viewQuery) {
      return (
        <TouchableOpacity
          style={{
            flexDirection: "row",
            width: 150,
            height: "100%",
            justifyContent: "center",
          }}
          onPress={this.changeView}
        >
          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text
              numberOfLines={number}
              style={[
                styles.headerCenter,
                { fontSize: number === 1 ? 18 : 14 },
              ]}
            >
              {" "}
              {this.state.view.name}{" "}
            </Text>
          </View>
          <View
            style={{
              width: 18,
              height: "100%",
              flexDirection: "row",
              justifyContent: "center",
            }}
          >
            <Image
              source={this.state.viewImgSrc}
              style={{ alignSelf: "center" }}
            />
          </View>
        </TouchableOpacity>
      );
    }
    return this.props.formField
      ? this.props.formField.properties.alias ||
          this.props.formField.properties.label ||
          ""
      : "";
  };
  rightView = () => {
    const showTextSearch = this.props.checkPrivByOperId(buttionIds.textSearch);
    const showAdd = this.props.checkPrivByOperId(buttionIds.add);
    const showBatch = this.props.checkPrivByOperId(buttionIds.batch);
    const width =
      this.state.screenDirection === "PORTRAIT" ? screenWidth : screenHeight;
    return (
      <View style={styles.headerRight}>
        {showTextSearch && (
          <Animated.View
            style={[
              styles.headerSearchBut,
              {
                borderBottomWidth: this.state.showSearchView ? 1 : 0,
                left: this.state.searchLeft,
                width: width - 40,
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => {
                this.clickSearchBut(true);
              }}
              activeOpacity={1}
            >
              <Image source={searchButImg} />
            </TouchableOpacity>
            {this.state.showSearchView && (
              <TextInput
                value={this.state.searchValue}
                style={styles.searchInput}
                placeholder="搜索"
                autoFocus={this.state.showSearchView}
                onChangeText={this.changeText}
                onCancel={() => this.setState({ searchValue: "" })}
                underlineColorAndroid="transparent"
              />
            )}
            {this.state.showSearchView && (
              <Text
                style={{ fontSize: 18 }}
                onPress={() => {
                  this.clickSearchBut(false);
                }}
              >
                取消
              </Text>
            )}
          </Animated.View>
        )}
        {
          <TouchableOpacity onPress={this.rotateScreen}>
            <Image
              style={{ width: 19, height: 19, resizeMode: "contain" }}
              source={
                this.state.screenDirection === "PORTRAIT"
                  ? require("../../images/icons-rotate-landscape-default.png")
                  : require("../../images/icons-rotate-longitudinal-default.png")
              }
            />
          </TouchableOpacity>
        }
        {this.props.isReference || this.props.isRefMulti
          ? showAdd && (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  if (this.props.goCreatPage) this.props.goCreatPage();
                }}
                style={[
                  styles.headerAddBut,
                  {
                    zIndex: this.state.showSearchView ? 0 : 40,
                    right: showTextSearch ? 40 : 0,
                  },
                ]}
              >
                <Image source={addButImg} />
              </TouchableOpacity>
            )
          : showBatch &&
            ModalHeader.EditBut({
              onPress: () => {
                this.props.changeShowChecked(!this.state.showChecked);
              },
              isEdit: this.state.showChecked,
              style: {
                position: "relative",
                right: showTextSearch ? 0 : -20,
                width: showTextSearch ? 66 : 34,
              },
            })}
      </View>
    );
  };

  isRefMultiLeft = () =>
    !this.state.showSearchView && (
      <Text
        onPress={this.closeModal}
        style={{ fontSize: 18, color: "#41454b" }}
      >
        取消
      </Text>
    );

  render() {
    const { showSearchView } = this.state;
    const { showHeader, onConfirm, placeholder, datas, isTab, isRefMulti } =
      this.props;
    console.log("datas",  this.props);
    const width =
      this.state.screenDirection === "PORTRAIT" ? screenWidth : screenHeight;
    const height =
      this.state.screenDirection === "PORTRAIT" ? screenHeight : screenWidth;
    let headerStyle = isTab ? { paddingTop: 0, height: 44 } : {};
    headerStyle = { ...headerStyle, width };
    const listHeight = isIos && height === 812 ? height - 88 + 34 : height - 64;
    headerStyle = {
      ...headerStyle,
      height:
        isIos && this.state.screenDirection === "PORTRAIT"
          ? height === 812
            ? 88
            : 64
          : 44,
      paddingTop:
        isIos && this.state.screenDirection === "PORTRAIT"
          ? height === 812
            ? 44
            : 20
          : 0,
    };
    return (
      <View style={{ position: "absolute", width, height }}>
        {showHeader && (
          <ModalHeader
            style={headerStyle}
            header={{
              leftView: isTab
                ? null
                : isRefMulti
                ? this.isRefMultiLeft()
                : this.leftView(),
              centerText: this.centerText(),
              rightView: this.rightView(),
            }}
          />
        )}
        {showSearchView ? (
          <View style={[styles.searchView, isTab ? { top: 44 } : {}]}>
            {!datas.length ? (
              <View
                style={{
                  height: listHeight,
                  width,
                  backgroundColor: "#fff",
                  alignItems: "center",
                  paddingLeft: 20,
                  paddingRight: 20,
                }}
              >
                <Text style={{ marginTop: 30, fontSize: 12, color: "#1B1B1B" }}>
                  {placeholder}
                </Text>
              </View>
            ) : (
              <DataTableList
                {...this.props}
                datas={datas}
                style={{
                  height: listHeight - (isTab ? 44 : 0),
                  width,
                  listHeight,
                }}
                onConfirm={onConfirm}
                showChecked={this.state.showChecked}
                isTab={isTab}
                ref={this.tableList}
                screenDirection={this.state.screenDirection}
                rotateScreen={this.rotateScreen}
                height={height}
                width={width}
              />
            )}
          </View>
        ) : (
          <DataTableList
            {...this.props}
            style={{ height: listHeight - (isTab ? 44 : 0), width, listHeight }}
            showChecked={this.state.showChecked}
            treekey={this.state.treekey}
            isTab={isTab}
            ref={this.tableList}
            height={height}
            width={width}
            rotateScreen={this.rotateScreen}
            screenDirection={this.state.screenDirection}
          />
        )}
      </View>
    );
  }
}
