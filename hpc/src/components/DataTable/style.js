
import { StyleSheet, Platform, Dimensions } from 'react-native'
import { screenHeight, screenWidth } from '../../config/sysPara'

export const width = Dimensions.get('window').width
export const height = Dimensions.get('window').height

export const isIos = Platform.OS === 'ios'
export const listHeight = isIos && height === 812 ? height - 88 + 34 : height - 64
const topHeight = isIos ? (height === 812 ? 88 : 64) : 44

export const styles = StyleSheet.create({
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  emptyData: {
    height: listHeight,
    width: screenWidth,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f7f7',
    paddingBottom: 100,
  },
  emptyDataView: {
    flexDirection: 'row',
    width: 100,
    height: 40,
    marginTop: 40,
  },
  emptyDataImg: {
    height: 40,
  },
  emptyDataText: {
    height: 40,
    justifyContent: 'center',
  },
  emptyDataTitle: {
    color: '#41454B',
    fontSize: 14,
    marginBottom: 5,
  },
  emptyDataSubtitle: {
    color: '#888',
    fontSize: 10,
  },
})

export const dataTableStyles = StyleSheet.create({
  headerCenter: {
    fontWeight: 'bold',
    fontSize: 18,
    color: '#41454b',
    textAlign: 'center',
  },
  headerRight: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    width: 50,
    alignItems: 'center',
    position: 'relative',
  },
  headerSearchBut: {
    margin: 0,
    height: 30,
    marginLeft: 20,
    marginRight: 20,
    position: 'absolute',
    backgroundColor: '#fff',
    borderColor: '#E5E5E5',
    zIndex: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAddBut: {
    width: 18,
    height: 18,
    marginLeft: 10,
    position: 'absolute',
  },
  searchInput: {
    flex: 1,
    padding: 0,
    paddingLeft: 10,
    paddingRight: 10,
    fontSize: 16,
  },
  searchView: {
    position: 'absolute',
    top: topHeight,
    zIndex: 10,
    width,
    height,
    backgroundColor: 'rgba(0,0,0,.5)',
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
  },
})

export const dataTableListStyles = StyleSheet.create({
  DataTableList: {
    backgroundColor: '#F8F7F7',
  },
  dataList: {
    width,
    position: 'relative',
  },
  confirmBut: {
    position: 'absolute',
    bottom: isIos ? (height === 812 ? 34 : 0) : 0,
    width: '100%',
  },
})

export const rowStyle = StyleSheet.create({
  displayRow: {
    height: 70,
    paddingHorizontal: 18,
    paddingVertical: 14,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  formStatus: {
    backgroundColor: '#fff',
    paddingHorizontal: 5,
    paddingVertical: 1.5,
    borderColor: '#d9d9d9',
    borderRadius: 4,
    borderWidth: 1,
    justifyContent: 'center',
  },
  viewLeftFun: {
    height: 21,
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewLeftValue0: {
    lineHeight: 21,
    height: 21,
    color: '#1B1B1B',
    fontSize: 14,
    
  },
  viewLeftValue1: {
    fontSize: 14,
    color: '#A0A2A5',
    marginRight: 7,
  },
  viewLeftText: {
    flex:1,

    color: '#1B1B1B',
    fontSize: 14,
  },
  viewRightView: {
    justifyContent: 'center',
    height: 42,
    alignItems: 'flex-end',
  },
  formStatusFun: {
    fontSize: 12,
    backgroundColor: 'rgba(0,0,0,0)',
  },
})
export const tableListto = {
  columnItem:{
    height:45,
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'space-between',
    marginLeft:20,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",

  },

  modalHeader:{
    display:'flex',
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
    height:67,
    borderBottomWidth:1,
    paddingHorizontal:15,
    borderBottomColor:'rgba(0, 0, 0, 0.06)'
  },
  modalInput:{
    paddingVertical:0,
    fontSize:14,
    borderRadius:6,
    marginTop:9,
    borderWidth:1,
    borderColor:'#D9D9D9',
    backgroundColor:"#fff",
    lineHeight:22,
    height:32,
    marginHorizontal:25
  },
  modalBtn:{
    marginHorizontal:20,
    marginBottom:20,
    backgroundColor:'#1890FF',
    height:32,
    borderRadius:2
  },
  modalResetBtn:{
    marginHorizontal:20,
    marginBottom:20,
    backgroundColor:'#FF8E8E',
    height:32,
    borderRadius:2
  },
  modalTip:{
    marginTop:20,
    color:'rgba(0,0,0,0.65)',
    fontSize:12,
    marginLeft:20
  },

  flexRow:{
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
  },
  operRow:{
    height:38,
    display:'flex',
    flexDirection:'row',
    justifyContent:'center',
    backgroundColor:'#fff'
  },
  operItem:{
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
    paddingHorizontal:15
  },
  rowContainer: {
    alignItems:'center',

    flexDirection: 'row',
    height: 70,
    backgroundColor: '#fff',
  },
  rowSmallContainer:{
    flexDirection: 'row',
    alignItems:'center',
    height: 40,
    backgroundColor: '#fff',
  },
  rowItemtitleText: {
    color: '#1B1B1B', fontSize: 12,
  },
  rowItem: {
    paddingLeft: 20, height: 30,  justifyContent: 'center',
  },
  rowItemText: {
    color: '#4A4A4A', fontSize: 12,
  },
  tableContainer: {
    position: 'relative',
  },
  tableHeaderTitleContainer: {
    flexDirection: 'row',
    shadowColor: '#000000',
    height: 24,
    shadowOpacity: 0.1,
    shadowOffset: {
      width: 5,
      height: 5,
    },
  },
  tableHeader: {
    height: 24,
    justifyContent: 'center',
    paddingLeft: 20,
  },
  tableList: {
    marginTop: 36,
    height: height - 173,
  },
  tableHeaderContainer: {
    borderColor: '#E5E5E5',
    position: 'absolute',
    height: 36,
  },
  tableHeaderLine: {
    borderBottomWidth: 1,
    borderColor: '#E5E5E5',
    height: 12,
    width: '100%',
  },
}

export const tagColor = {
  green: {
    color: '#52c41a',
    backgroundColor: '#f6ffed',
    borderColor: '#b7eb8f',
  },
  magenta: {
    color: '#eb2f96',
    backgroundColor: '#fff0f6',
    borderColor: '#ffadd2',
  },
  red: {
    color: '#f5222d',
    backgroundColor: '#fff1f0',
    borderColor: '#ffa39e',
  },
  volcano: {
    color: '#fa541c',
    backgroundColor: '#fff2e8',
    borderColor: '#ffbb96',
  },
  orange: {
    color: '#fa8c16',
    backgroundColor: '#fff7e6',
    borderColor: '#ffd591',
  },
  gold: {
    color: '#faad14',
    backgroundColor: '#fffbe6',
    borderColor: '#ffe58f',
  },
  lime: {
    color: '#a0d911',
    backgroundColor: '#fcffe6',
    borderColor: '#eaff8f',
  },
  cyan: {
    color: '#13c2c2',
    backgroundColor: '#e6fffb',
    borderColor: '#87e8de',
  },
  blue: {
    color: '#1890ff',
    backgroundColor: '#e6f7ff',
    borderColor: '#91d5ff',
  },
  geekblue: {
    color: '#2f54eb',
    backgroundColor: '#f0f5ff',
    borderColor: '#adc6ff',
  },
  purple: {
    color: '#722ed1',
    backgroundColor: '#f9f0ff',
    borderColor: '#d3adf7',
  },
}

export const  treeSelectStyles = StyleSheet.create({
  container:{
    display:'flex',
    flexDirection:'column',
    height:height-20,
    width: width,
  },
  searchBar:{
    width:width
  },
  treeItem:{
    display:'flex',
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
    height:44,
    fontWeight:"400",
    fontSize:14,
    color:'#000',
    marginLeft:20,
    borderBottomWidth:StyleSheet.hairlineWidth,
    borderBottomColor:'rgba(0, 0, 0, 0.05)'
  },
  treeItemText:{
    fontWeight:"400",
    fontSize:14,
    color:'#000',
    marginLeft:6
  },
  operRow:{
    height:60,
    paddingHorizontal:20,
  },
  flexRow:{
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
  },
  flexJscb:{
    justifyContent:'space-between',

  },
  arrowIcon:{
    width:22,
    marginRight:16
  },
  treeIcon:{
    width:16,
    height:16,
  }

})