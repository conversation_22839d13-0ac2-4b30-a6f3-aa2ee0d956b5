
const random = () => Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, 9))

export default class QueryBuilds {
  constructor() {
    this.sqlText = ''
    this.queryTexts = []
  }

  add=(queryText) => {
    this.queryTexts.push(queryText)
    this.buildQueryText()
    return this
  }
  findById = (id) => {
    this.queryTexts.find(queryText => queryText.id === id)
  }
  buildQueryText=() => {
    this.sqlText = ''
    this.queryTexts.forEach((queryBuild) => {
      this.sqlText += `${queryBuild.text} ${queryBuild.link ? '' : 'and '}`
    })
    if (this.sqlText.lastIndexOf('and ') === this.sqlText.length - 4) {
      this.sqlText = this.sqlText.slice(0, this.sqlText.length - 4)
    }
  }

  remove=(qid) => {
    const index = this.queryTexts.findIndex(({ id }) => id === qid)
    if (index !== -1) {
      this.queryTexts = [...this.queryTexts.slice(0, index), ...this.queryTexts.slice(index + 1)]
      this.buildQueryText()
    }
  }
  update=(qid, qb) => {
    const index = this.queryTexts.findIndex(({ id }) => id === qid)
    if (index !== -1) {
      qb.id = qid
      this.queryTexts = [
        ...this.queryTexts.slice(0, index),
        qb,
        ...this.queryTexts.slice(index + 1),
      ]
      this.buildQueryText()
    }
  }
}

export class QueryBuild {
  constructor({ key, operator, value, link = '' }) {
    this.text = ''
    if (value) {
      this.text += `${key} ${operator} ${value} ${link} `
    }
    this.link = ''
    this.id = key||random()
  }
  static OPERATOR_LINK='like'
  static OPERATOR_IN='in'
  static OPERATOR_NE='ne'
  static OPERATOR_GT='gt'
  static OPERATOR_GE='ge'
  static OPERATOR_LT='lt'
  static OPERATOR_LE='le'


  updateText = (text) => {
    this.text = text
    return this
  }

  leftParenthesis = () => {
    this.text += '( '
    return this
  }
  rightParenthesis = () => {
    this.text += ') '
    return this
  }
  and = () => {
    this.text += 'and '
    this.link = 'and '
    return this
  }
  beforeAnd=() => {
    this.text = ` and ${this.text}`
    return this
  }
  or = () => {
    this.text += 'or '
    this.link = 'or '
    return this
  }
  beforeOr=() => {
    this.text = ` or ${this.text}`
    return this
  }
  key = (key = '') => {
    this.text += `${key} `
    return this
  }
  value = (value = '') => {
    this.text += `${value} `
    return this
  }
  operator = (op = '') => {
    this.text += `${op} `
    return this
  }
}
