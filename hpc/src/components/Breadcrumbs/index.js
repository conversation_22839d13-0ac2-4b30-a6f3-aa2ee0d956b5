import React from 'react';
import { View,Text,ScrollView} from 'react-native';
import { Flex} from '@ant-design/react-native';

import { screenWidth } from '../../config/sysPara';

//需要传入层级数据，需要显示的属性以及改变层级时的回调函数
//面包屑在屏幕中的位置由外层组件决定（目前没有看到需要一定要置顶）
//TODO: 1.支持Android的回退键；
class Breadcrumbs extends React.Component {
    render() {
        const { crumbsDatas, dispalyVol } = this.props;
        const height = crumbsDatas.length ? 40 : 0;

        return (
            <Flex style={{
                backgroundColor:"#fff",
                width: screenWidth,
                height:height,
                paddingLeft:8,
                justifyContent:"center"
            }}>
                <ScrollView 
                    horizontal={true} 
                    showsHorizontalScrollIndicator={false}>
                    {
                        crumbsDatas.map((data , index) => {
                            const isLast = crumbsDatas.length - 1 === index
                            return (
                                <Text
                                    key = {index}
                                    style={{color: isLast ? '#333' : '#0790fa', paddingLeft: 3}} 
                                    onPress = {() => {if(!isLast) this.props.changePage(data, index)}}
                                >
                                    {dispalyVol ? data[dispalyVol] : data}
                                    <Text style={{color:"#333"}}>{isLast? '' : ' >'}</Text>
                                </Text>
                            )
                        })
                    }
                </ScrollView>
            </Flex>
        )
    }
}

export default Breadcrumbs