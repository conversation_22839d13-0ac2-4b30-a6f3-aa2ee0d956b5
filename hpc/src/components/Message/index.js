import PropTypes, { element } from 'prop-types'
import React, { Component } from 'react'
import { View, TouchableOpacity, Image, Text, FlatList, StyleSheet, Dimensions, Modal, Platform } from 'react-native'
import { connect } from 'react-redux'
import { Portal, Toast, Badge, ActionSheet, Modal as AntdModal } from '@ant-design/react-native'
import ModalHeader from '../../addons/FormComponent/ModalHeader'
import SideMenu from '../SideMenu'
import { getAppList, getMessageDatas, getMessageUnreadCount, changeMessage, getAppCounts } from './Store'
import { GO_EDIT } from '../../utils/jump'
import styles from './styles'
import ListItem from './ListItem'

const isTrueApp = (appId) => !!(appId && appId !== '0' && appId !== 0)
const deviceHeight = Dimensions.get('window').height
const deviceWidth = Dimensions.get('window').width
const platformFixHeight = Platform.OS === 'android' ? 24 : 0

const HeaderLeftView = ({ toggleMenu }) => (
  <View style={{ height: '100%', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
    <TouchableOpacity style={{ height: '100%', justifyContent: 'center', alignItems: 'center' }} onPress={toggleMenu}>
      <Image style={{ alignSelf: 'center' }} source={require('../../images/icons-burger-outline-line-default.png')} />
    </TouchableOpacity>
  </View>
)

const HeaderCenterView = ({ text, showChangeAppView }) => {
  let number = 1
  if (text.length * 18 > 132) {
    number = 2
  }
  return (
    <TouchableOpacity style={{ flexDirection: 'row', width: 150, height: '100%', justifyContent: 'center' }} onPress={showChangeAppView}>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text numberOfLines={number} style={[styles.headerCenter, { fontSize: number === 1 ? 18 : 14 }]}>
          {' '}
          {text}
          {' '}
        </Text>
      </View>
      <View style={{ width: 18, height: '100%', flexDirection: 'row', justifyContent: 'center' }}>
        <Image source={require('../../images/icons-arrow-x_small-down-default.png')} style={{ alignSelf: 'center' }} />
      </View>
    </TouchableOpacity>
  )
}

const HeaderRightView = ({ changeEditStatus, isEditStatus }) => (
  <View style={styles.headerRight}>
    {
      ModalHeader.EditBut({
        onPress: changeEditStatus,
        isEdit: isEditStatus,
        style: { position: 'relative', right: -20, width: 54 },
      })
    }
  </View>
)

class Message extends Component {
  constructor(props) {
    super(props)
    this.pager = {
      pageNum: 0,
      pageSize: 10,
    }

    this.state = {
      // 是否编辑状态
      isEditStatus: false,
      // 应用数据
      appListDatas: [],
      // 应用消息数据
      appMessageDatas: [],
      appsMsgCount: [],

      totalCount: 0,
      // 未读数量
      unreadNum: null,
      // 筛选
      filter: 'all',
      // 当前应用名
      currentAppName: '',
      // 当前应用id
      currentAppId: props.currentAppId,
      // 当前选中的消息id
      selectedMessageIds: new Map(),
      modalVisible: false,
      isRefreshing: false,
    }
  }

  componentDidMount = async () => {
    const { currentAppId, filter } = this.state
    if (isTrueApp(currentAppId)) {
      const appListDatas = await getAppList()
      const unreadNum = await getMessageUnreadCount(currentAppId)
      const currentAppName = appListDatas.find((item) => item.id === currentAppId).name
      this.refreshMessageDatas(currentAppId, filter)
      this.setState({
        appListDatas,
        currentAppName,
        unreadNum,
      })
    }
    const appsMsgCount = await getAppCounts(0)
    this.setState({ appsMsgCount })
  }

  componentWillUnmount() {
    if (this.actionSheet) {
      this.actionSheet.close()
    }
  }

  // 更新未读数量
  updateUnreadCount = async (id) => {
    const { currentAppId } = this.state
    const appId = id || currentAppId
    const unreadNum = await getMessageUnreadCount(appId)
    this.setState({
      unreadNum,
    })
  }

  // 刷新消息数据
  refreshMessageDatas = async (currentAppId, filter) => {
    this.pager = {
      pageNum: 0,
      pageSize: 10,
    }
    this.setState({
      isRefreshing: true,
    })
    const tKey = Toast.loading('', 0)
    const data = await getMessageDatas(currentAppId, this.pager, filter)
    Portal.remove(tKey)
    if (data) {
      this.setState({
        appMessageDatas: data.notifications,
        totalCount: data.totalCount,
        isRefreshing: false,
      })
    }
  }

  loadMoreMessageDatas = async () => {
    const { appMessageDatas, currentAppId, filter } = this.state
    this.setState({
      isRefreshing: true,
    })
    const tKey = Toast.loading('', 0)
    const data = await getMessageDatas(currentAppId, this.pager, filter)
    Portal.remove(tKey)
    this.listloadmoreLock = false
    if (data) {
      this.setState({
        appMessageDatas: appMessageDatas.concat(data.notifications),
        totalCount: data.totalCount,
        isRefreshing: false,
        selectedMessageIds: new Map(),
      })
    }
  }

  refreshList = () => {
    const { currentAppId, filter } = this.state
    this.updateUnreadCount()
    this.refreshMessageDatas(currentAppId, filter)
  }

  loadmoreList = () => {
    const { appMessageDatas, totalCount } = this.state
    if (appMessageDatas.length && appMessageDatas.length < totalCount) {
      if (this.listloadmoreLock) {
        return
      }
      this.listloadmoreLock = true
      this.loadMoreMessageDatas()
    }
  }

  changeEditStatus = () => {
    const { isEditStatus } = this.state
    this.setState({
      isEditStatus: !isEditStatus,
      selectedMessageIds: new Map(),
    })
  }

  showChangeAppView = () => {
    this.setState({
      modalVisible: true,
    })
  }

  hideChangeAppView = () => {
    this.setState({
      modalVisible: false,
    })
  }

  goAppForm = (item) => {
    const { currentAppId } = this.state
    GO_EDIT(null, currentAppId, item.formId, null, item.dataId, false)
  }

  selectMessage = (id) => {
    this.setState((state) => {
      const selectedMessageIds = new Map(state.selectedMessageIds)
      selectedMessageIds.set(id, !selectedMessageIds.get(id))
      return { selectedMessageIds }
    })
  }

  checkMessage = async (item) => {
    const { currentAppId, appMessageDatas } = this.state
    if (!item.isRead) {
      const data = [{
        id: item.id, isRead: 1,
      }]
      const res = await changeMessage('change', currentAppId, data)
      if (res) {
        this.updateUnreadCount()
        const newData = appMessageDatas.filter((message) => item.id !== message.id)
        this.setState({
          appMessageDatas: newData,
        })
        // this.refreshMessageDatas(currentAppId, filter)
      }
    }
    this.goAppForm(item)
  }

  selectTab = (newfilter) => {
    const { filter, currentAppId } = this.state
    if (newfilter === filter) {
      return
    }
    this.setState({
      filter: newfilter,
      selectedMessageIds: new Map(),
    })
    this.updateUnreadCount()
    this.refreshMessageDatas(currentAppId, newfilter)
  }

  selectApp = async (data) => {
    const { filter, appListDatas } = this.state
    const currentAppId = data.id
    const unreadNum = await getMessageUnreadCount(currentAppId)
    const currentAppName = appListDatas.find((item) => item.id === currentAppId).name
    this.refreshMessageDatas(currentAppId, filter)
    this.setState({
      currentAppId,
      currentAppName,
      unreadNum,
      modalVisible: false,
    })
  }

  // 批量操作
  batchOper = () => {
    const BUTTONS = [
      '标记为已读',
      '标记为未读',
      '删除',
      '取消',
    ]
    this.actionSheet = ActionSheet
    this.actionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        cancelButtonIndex: 3,
        destructiveButtonIndex: 2,
      },
      async (buttonIndex) => {
        const { currentAppId, selectedMessageIds } = this.state
        if (selectedMessageIds.size === 0 && buttonIndex !== 3) {
          Toast.info('请选择消息', 1)
          return
        }
        if (buttonIndex === 0) {
          const data = []
          selectedMessageIds.forEach((value, key) => (data.push({ id: key, isRead: 1 })))
          const res = await changeMessage('change', currentAppId, data)
          if (res) {
            this.updateUnreadCount()
            this.refreshMessageDatas(currentAppId, this.state.filter)
          }
        } else if (buttonIndex === 1) {
          const data = []
          selectedMessageIds.forEach((value, key) => (data.push({ id: key, isRead: 0 })))
          const res = await changeMessage('change', currentAppId, data)
          if (res) {
            this.updateUnreadCount()
            this.refreshMessageDatas(currentAppId, this.state.filter)
          }
        } else if (buttonIndex === 2) {
          AntdModal.alert('', '确定要删除消息吗？', [
            { text: '取消', onPress: () => console.log('cancel'), style: 'cancel' },
            {
              text: '删除',
              onPress: async () => {
                const data = []
                selectedMessageIds.forEach((value, key) => (data.push(key)))
                const res = await changeMessage('del', currentAppId, data)
                if (res) {
                  this.updateUnreadCount()
                  this.refreshMessageDatas(currentAppId, this.state.filter)
                }
              },
            },
          ])
        }
      },
    )
  }

  renderItem = ({ item, index }) => (
    <ListItem item={item} isEditStatus={this.state.isEditStatus} selected={!!this.state.selectedMessageIds.get(item.id)} selectMessage={this.selectMessage} checkMessage={this.checkMessage} />
  )

  renderAppItem = ({ item, index }) => {
    const msgApp = this.state.appsMsgCount.find((element) => element.appId === item.id)

    return (
      <TouchableOpacity onPress={() => this.selectApp(item)}>
        <View style={{ marginLeft: 15, padding: 15, flex: 1, flexDirection: 'row' }}>
          <Text style={{ fontSize: 16, color: this.state.currentAppId === item.id ? '#17A9FF' : '#1B1B1B' }}>{item.name}</Text>
          {msgApp.msgCount > 0 && (
            <View style={{ marginLeft: 15, width: 20, height: 20, borderRadius: 10, backgroundColor: '#FF3B30', justifyContent: 'center', alignItems: 'center' }}>
              <Text style={{ color: '#fff', fontSize: 12 }}>{msgApp.msgCount > 99 ? '...' : msgApp.msgCount}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    )
  }

  render() {
    const { isEditStatus, currentAppName, appMessageDatas, filter, unreadNum, appListDatas } = this.state
    const leftviewProps = {
      goback: this.goback, toggleMenu: this.props.toggleMenu,
    }
    const centerViewProps = {
      text: currentAppName, showChangeAppView: this.showChangeAppView,
    }
    const rightViewProps = {
      changeEditStatus: this.changeEditStatus, isEditStatus,
    }
    return (
      <View style={{ height: deviceHeight - platformFixHeight, backgroundColor: '#F8F7F7' }}>
        <ModalHeader
          header={{
            leftView: <HeaderLeftView {...leftviewProps} />,
            centerText: <HeaderCenterView {...centerViewProps} />,
            rightView: <HeaderRightView {...rightViewProps} />,
          }}
        />
        <View style={{ backgroundColor: '#fff', paddingHorizontal: 10, flexDirection: 'row', justifyContent: 'space-between', height: 50, borderBottomWidth: StyleSheet.hairlineWidth, borderBottomColor: '#e5e5e5' }}>
          <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity onPress={() => this.selectTab('all')} style={{ justifyContent: 'center', paddingHorizontal: 10, borderBottomWidth: 2, borderBottomColor: filter === 'all' ? '#17A9FF' : '#fff' }}><Text>全部</Text></TouchableOpacity>
            <TouchableOpacity onPress={() => this.selectTab('unread')} style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', paddingHorizontal: 10, borderBottomWidth: 2, borderBottomColor: filter === 'unread' ? '#17A9FF' : '#fff' }}>
              <Text>未读</Text>
              {!!unreadNum && <View style={{ width: 20, height: 20, borderRadius: 10, backgroundColor: '#FF3B30', justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>{unreadNum > 99 ? '...' : unreadNum}</Text></View>}
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.selectTab('read')} style={{ justifyContent: 'center', paddingHorizontal: 10, borderBottomWidth: 2, borderBottomColor: filter === 'read' ? '#17A9FF' : '#fff' }}><Text>已读</Text></TouchableOpacity>
          </View>
          <View>
            {isEditStatus
              ? (
                <View style={{ justifyContent: 'center', height: 50 }}>
                  <TouchableOpacity onPress={this.batchOper}>
                    <Image source={require('../../images/icons-more-dot-default.png')} />
                  </TouchableOpacity>
                </View>
              )
              : (
                <View style={{ justifyContent: 'center', height: 50, opacity: 0.6 }}>
                  <Image source={require('../../images/icons-more-dot-default.png')} />
                </View>
              )}
          </View>
        </View>
        <FlatList
          data={appMessageDatas}
          style={{ flex: 1 }}
          keyExtractor={(item) => item.id}
          renderItem={this.renderItem}
          extraData={this.state}
          onRefresh={this.refreshList}
          refreshing={this.state.isRefreshing}
          onEndReached={this.loadmoreList}
          onEndReachedThreshold={0.1}
        />
        <Modal
          animationType="slide"
          transparent
          visible={this.state.modalVisible}
          stlye={{
            // backgroundColor: '#dedede',
          }}
          onRequestClose={this.hideChangeAppView}
        >
          <TouchableOpacity onPress={this.hideChangeAppView} style={{ backgroundColor: '#000', opacity: 0.3, height: 200 }} />
          <View style={{ borderTopColor: '#f2f2f2', borderTopWidth: 1, backgroundColor: '#fff', width: deviceWidth, height: deviceHeight - 200 - platformFixHeight }}>
            <Text style={{ color: '#111', fontSize: 18, paddingLeft: 20, paddingVertical: 10, borderBottomWidth: 1, borderBottomColor: '#f2f2f2' }}>请选择应用</Text>
            <FlatList
              data={appListDatas}
              style={{ flex: 1 }}
              keyExtractor={(item) => item.id}
              renderItem={this.renderAppItem}
              extraData={this.state}
            />
          </View>
        </Modal>
      </View>
    )
  }
}

const mapStateToProps = () => ({

})
const mapDispatchToProps = () => ({

})

Message.propTypes = {
  toggleMenu: PropTypes.func,
}

export default connect(mapStateToProps, mapDispatchToProps)(SideMenu(Message))
