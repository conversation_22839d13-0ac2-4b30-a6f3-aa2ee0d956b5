import React from 'react'
import { View, Image, Text, ScrollView, TouchableOpacity, Dimensions, Platform } from 'react-native'
import { List, ActionSheet } from '@ant-design/react-native'
import PropTypes from 'prop-types'
import { createForm } from 'rc-form'
import ImagePicker from 'react-native-image-crop-picker'
import dismissKeyboard from 'dismissKeyboard'
import SideMenu from '../../../components/SideMenu'
import NavigationService from '../../../NavigationService'
import { getItem, setItem } from '../../../utils/storage/asyncStore'
import { wechatSubmit } from '../../PwdSetting/store'

const { width } = Dimensions.get('window')
class UserForm extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      sex: [this.props.user.sex.toString()],
      clicked: 'none',
      image: null,
      nickname: this.props.user.nickname ? this.props.user.nickname : '',
      user: this.props.user,
    }
  }

  componentWillMount = async () => {
    const img = await getItem('userPhotos', 666)
    this.setState({
      image: img,
    })
  }

  onUnbindWX = () => {
    wechatSubmit(token).then(() => {
      this.props.user.unionId = ''
      user.unionId = ''
      this.setState({
        user,
      })
    })
  }

  _onclick = () => {
    NavigationService.refresh({
      backTitle: '取消',
      hideBackImage: true,
      onBack: () => { this.cancel() },
      onRight: () => { this.submit() },
      rightTitle: '提交'
    })
  }

  cancel = () => {
    dismissKeyboard()
    NavigationService.refresh({
      hideBackImage: false,
      backTitle: '',
      rightTitle: '',
      onBack: () => { NavigationService.back() },
    })
  }

  submit = () => {
    dismissKeyboard()
    const { user } = this.props

    this.props.form.validateFields((err, values) => {
      const newUser = { ...user, ...values }
      user.nickname = values.nickname
      values.sex = user.sex

      if (!err) {
        this.props.save({ ...values, headImage: '' }, user.userId)
      } else if (err.nickname.errors[0].message) {
        Toast.fail(err.nickname.errors[0].message)
      }
    })
  }

  onChange = (value) => {
    this.setState({
      sex: value,
    })
    this.props.user.sex = parseInt(value[0])
    this._onclick()
  }

  showActionSheet = () => {
    const BUTTONS = ['拍照', '从相册选择头像', '取消']
    that = this
    ActionSheet.showActionSheetWithOptions({
      options: BUTTONS,
      cancelButtonIndex: BUTTONS.length - 1,
      // destructiveButtonIndex: BUTTONS.length - 2,
      // title: '标题',
      title: '请选择您的头像',
      // 'data-seed': 'logId',
    },
      (buttonIndex) => {
        that.setState({ clicked: BUTTONS[buttonIndex] })
        if (buttonIndex == 0) { // 调用拍照
          ImagePicker.openCamera({
            width: 300,
            height: 400,
            cropping: true,
            includeBase64: true,
          }).then((image) => {
            that.setState({ image })
            // 存储
            setItem('userPhotos', image, 666, null)
            that.props.headImage = image
            this.props.updatePhoto()
            this.props.updateMenuPhoto()
          })
        } else if (buttonIndex == 1) { // 调用选择图片
          ImagePicker.openPicker({
            width: 300,
            height: 400,
            cropping: true,
            includeBase64: true,
          }).then((image) => {
            that.setState({ image })
            setItem('userPhotos', image, 666, null)
            this.props.updatePhoto()
            this.props.updateMenuPhoto()
          }).catch((error) => {   // 执行失败
            console.error(error)
          })
        }
      })
  }

  renderImage = image => <Image style={{ width: 54, height: 54, resizeMode: 'cover', borderRadius: 27 }} source={image} />

  renderUserImage = () => {
    const { user } = this.props
    if (user.headImage) {
      return (<TouchableOpacity style={{ flex: 0 }}><Image
        resizeMode="contain"
        source={{ uri: user.headImage }}
        style={{ height: 211, width, }}
      />
      </TouchableOpacity>)
    }
    return (<TouchableOpacity style={{ flex: 0 }}><Image
      resizeMode="contain"
      source={user && user.sex === 1 ? require('../UserForm/defaultAvatar_girl.png') : require('../UserForm/defaultAvatar_boy.png')}
      style={{ height: 211, width, }}
    /></TouchableOpacity>)
  }

  regEmail = (email) => {
    if (String(email).indexOf('@') > 0) {
      let str = email.split('@'),
        _s = ''
      if (str[0].length > 2) {
        for (let i = 0; i < str[0].length - 2; i++) {
          _s += '*'
        }
      }
      var new_email = str[0].substr(0, 2) + _s + '@' + str[1]
    }
    return new_email
  }

  updateUser = (type, val) => {
    console.log('asdhasdhjkajsldjlaksd', type, val)
    const { user } = this.state
    if (type === "userName") {
      user.userName = val
    }
    if (type === "nickname") {
      user.nickname = val
    }
    this.setState({
      user,
    })
  }

  render() {
    const { user } = this.state
    return (
      <View style={{ flex: 1 }}>
        <ScrollView
          style={{ backgroundColor: '#fff' }}
          contentContainerStyle={{ alignItems: 'center' }}
        >
          {this.renderUserImage()}
          <List style={{ marginTop: 10 }} styles={{ borderBottomWidth: 0 }}>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>用户名</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>{user.userName}</Text>
                </View>
                <TouchableOpacity onPress={() => NavigationService.navigate('pwdSetting', { type: 'userName', ...user, updateUser: this.updateUser })} style={{ flex: 0 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>更换</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>姓名</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>{user.nickname}</Text>
                </View>
                <TouchableOpacity onPress={() => NavigationService.navigate('pwdSetting', { type: 'nickname', ...user, updateUser: this.updateUser })} style={{ flex: 0 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>更换</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>手机</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>{user.mobile.substr(0, 3) + "****" + user.mobile.substr(7)}</Text>
                </View>
                {/* <TouchableOpacity onPress={() => NavigationService.navigate('pwdSetting', { type: 'mobile', ...user, save: this.props.save })} style={{ flex: 0 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>更换</Text>
                </TouchableOpacity> */}
              </View>
            </View>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>密码</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>已设置</Text>
                </View>
                <TouchableOpacity onPress={() => NavigationService.navigate('pwdSetting', { type: 'pwd', ...user, logout: this.props.logout })} style={{ flex: 0 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>更换</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>邮箱</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>{user.email ? this.regEmail(user.email) : '未绑定'}</Text>
                </View>
                {/* <TouchableOpacity onPress={() => NavigationService.navigate('pwdSetting', { type: 'email', ...user })} style={{ flex: 0 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>{user.email ? '更换' : '未绑定'}</Text>
                </TouchableOpacity> */}
              </View>
            </View>
            <View style={{ marginTop: 35, height: 68, width: width - 40, borderBottomWidth: 1, borderBottomColor: '#E5E5E5' }}>
              <Text style={{ fontSize: 18, color: '#4A4A4A' }}>微信</Text>
              <View style={{ flexDirection: 'row', display: 'flex' }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ marginTop: 20, fontSize: 16, color: '#9B9B9B' }}>{user.unionId ? '已设置' : '暂未绑定，请前往PC端绑定'}</Text>
                </View>
                {
                  user.unionId ?
                    <TouchableOpacity style={{ flex: 0 }} onPress={this.onUnbindWX}>
                      <Text style={{ marginTop: 20, fontSize: 16, color: '#17A9FF' }}>解绑</Text>
                    </TouchableOpacity>
                    : null
                }
              </View>
            </View>
            <TouchableOpacity style={{ marginTop: 35, height: 68, width: width - 40 }} onPress={() => this.props.logout()}>
              <Text style={{ fontSize: 18, color: '#E53538' }}>退出</Text>
            </TouchableOpacity>
          </List>
        </ScrollView>
      </View>
    )
  }
}
UserForm.propTypes = {
  form: PropTypes.shape({
    validateFields: PropTypes.func.isRequired,
    getFieldDecorator: PropTypes.func.isRequired,
  }),
  user: PropTypes.shape({
    mobile: PropTypes.string.isRequired,
  }),
  save: PropTypes.func.isRequired,
}
export default createForm()(SideMenu(UserForm))
