import React from 'react'
import { View, ScrollView, Text, Dimensions } from 'react-native'
import { List, Switch } from '@ant-design/react-native'
import { createForm } from 'rc-form'
import SideMenu from '../../SideMenu'
import { getUserSettings, updateUserSettings } from '../../../utils/storage'

const { width } = Dimensions.get('window')
class UserGeneralSettings extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      userSettings: { msgShow: true },
    }
  }

  componentDidMount = async () => {
    const settings = await getUserSettings()
    if (settings) {
      this.setState({
        userSettings: settings,
      })
    }
  }

  onSwitchChange = (value) => {
    this.setState({
      userSettings: { msgShow: value },
    })
    this.props.save({ msgShow: value })
  }

  render() {
    console.log('#### ', this.state)
    return (
      <View style={{ flex: 1 }}>
        <ScrollView
          style={{ backgroundColor: '#fff' }}
          contentContainerStyle={{ alignItems: 'center' }}
        >
          <List style={{ marginTop: 10, width: width - 10 }}>
            <List.Item
              extra={(
                <Switch
                  checked={this.state.userSettings.msgShow}
                  onChange={this.onSwitchChange}
                />
              )}
            >
              <View style={{ width: width - 40 }}>
                <Text style={{ fontSize: 18, color: '#1B1B1B' }}>浮动消息显示</Text>
              </View>
            </List.Item>

          </List>
        </ScrollView>
      </View>
    )
  }
}

export default createForm()(SideMenu(UserGeneralSettings))
