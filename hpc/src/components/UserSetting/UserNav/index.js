import React from 'react'
import { ScrollView, View, Text, Image, NativeAppEventEmitter, TouchableOpacity } from 'react-native'
import { List, Modal } from '@ant-design/react-native'
import DeviceInfo from 'react-native-device-info'

import { getItem } from '../../../utils/storage/asyncStore'
import IntentModule from './customerService'
import { deviceType, envParm } from '../../../config/sysPara'
import SideMenu from '../../SideMenu'
import NavigationService from '../../../NavigationService'

// RN调用原生
const { NativeModules } = require('react-native')

const { RNBridgeModule } = NativeModules// 调用原生
// var RNModules = NativeModules.RTModule;
let subscription// 订阅者

const { Item } = List
const { Brief } = Item
const { version } = envParm

class UserNav extends React.Component {
  // RN->原生
  constructor(props) {
    super(props)
    this.state = {
      events: '',
      msg: '',
      image: null,
      newUser: props.user,
      version: '',
    }
  }

  // 获取Promise对象处理
  async _updateEvents() {
    try {
      const events = await RNBridgeModule.RNInvokeOCPromise({ name: 'contact' })
      this.setState({ events })
    } catch (e) {
      this.setState({ events: e.message })
    }
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      newUser: nextProps.user,
    })
    this.updatePhoto()
  }

  componentWillMount = async () => {
    this.updatePhoto()
  }

  updatePhoto = async () => {
    const img = await getItem('userPhotos', 666)
    this.setState({
      image: img,
    })
  }

  componentDidMount() {
    subscription = NativeAppEventEmitter.addListener(
      'EventReminder',
      (reminder) => {
        const { errorCode } = reminder
        if (errorCode === 0) {
          this.setState({ msg: reminder.name })
        } else {
          this.setState({ msg: reminder.msg })
        }
      },
    )
    this.setState({
      version: DeviceInfo.getVersion(),
    })
  }

  componentWillUnmount() {
    subscription.remove()
  }

  btnOnClick = () => {
    const { user } = this.props

    if (deviceType === 2) {
      RNBridgeModule.RNInvokeOCCallBack({ name: user.nickname, userId: user.mobile }, (error, events) => {
        if (error) {
          console.error(error)
        } else {
          this.setState({ events })
        }
      })
    } else {
      IntentModule.startActivityFromJS()
    }
  }

  renderImage = (image) => (
    <Image
      style={{
        width: 40, height: 40, resizeMode: 'cover', borderRadius: 20,
      }}
      source={image}
    />
  )

  render() {
    const user = this.state.newUser
    const lineHeight = 55
    return (
      <View style={{ flex: 1, backgroundColor: '#fff' }}>
        <ScrollView>
          <View
            style={{ marginTop: 36, borderBottomWidth: 0 }}
          >
            <TouchableOpacity onPress={() => {
              NavigationService.navigate('userInfo', {
                user, save: this.props.save, headImage: this.state.image, updatePhoto: this.updatePhoto, logout: this.props.logout, updateMenuPhoto: this.props.updateMenuPhoto,
              })
            }}
            >
              <View style={{
                flexDirection: 'row', justifyContent: 'space-between', marginLeft: 15, marginRight: 15,
              }}
              >
                <View>
                  <View><Text style={{ marginLeft: 9, color: '#41454B', fontSize: 28, marginBottom: 5 }}>{user ? user.nickname : '无数据'}</Text></View>
                  <View><Text style={{ marginLeft: 9, color: '#9B9B9B', fontSize: 16 }}>{user ? user.mobile.substr(0, 3) + "****" + user.mobile.substr(7) : '无数据'}</Text></View>
                </View>
                <Image
                  style={{
                    width: 60, height: 60, resizeMode: 'cover', borderRadius: 30,
                  }}
                  source={user && user.headImage ? { uri: user.headImage } : (user && user.sex === 1 ? require('../UserForm/defaultAvatar_girl.png') : require('../UserForm/defaultAvatar_boy.png'))}
                />
              </View>
            </TouchableOpacity>
          </View>
          <List
            style={{ marginTop: 41 }}
            styles={{ borderBottomWidth: 0 }}
          >
            <Item
              arrow="horizontal"
              onClick={() => {
                NavigationService.navigate('userInfo', {
                  user, save: this.props.save, headImage: this.state.image, updatePhoto: this.updatePhoto, logout: this.props.logout,
                })
              }}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{ height: lineHeight, flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginLeft: 10,
                }}
                >
                  账号管理

                </Text>
              </View>
            </Item>
            <Item
              arrow="horizontal"
              onClick={() => {
                NavigationService.navigate('userGenSettings', {
                  save: this.props.saveSettings,
                })
              }}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{ height: lineHeight, flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginLeft: 10,
                }}
                >
                  通用设置

                </Text>
              </View>
            </Item>

            <Item
              arrow="horizontal"
              onClick={() => { NavigationService.navigate('callCenterNum', { version: this.state.version }) }}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{
                height: lineHeight, flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0,
              }}
              >
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginTop: 0, marginLeft: 10, borderBottomWidth: 0,
                }}
                >
                  联系我们
                </Text>
              </View>
            </Item>
            {/* <Item
              arrow="horizontal"
              onClick={() => { NavigationService.navigate('innerOfficialWeb', { version: this.state.version }) }}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{
                height: lineHeight, flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0,
              }}
              >
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginTop: 0, marginLeft: 10, borderBottomWidth: 0,
                }}
                >
                  用户协议
                </Text>
              </View>
            </Item>
            <Item
              arrow="horizontal"
              onClick={() => Modal.alert('版本信息', `版本号:${this.state.version}\nJS版本号:${version}`)}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{
                height: lineHeight, flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0,
              }}
              >
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginTop: 0, marginLeft: 10, borderBottomWidth: 0,
                }}
                >
                  本机信息
                </Text>
              </View>
            </Item> */}

            <Item
              arrow="horizontal"
              onClick={() => { NavigationService.navigate('aboutUs', { versionC: this.state.version, versionT: version, ...user }) }}
              style={{ height: 68, borderBottomWidth: 0 }}
            >
              <View style={{
                height: lineHeight, flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0,
              }}
              >
                <Text style={{
                  fontSize: 18, color: '#4A4A4A', textAlign: 'center', marginTop: 0, marginLeft: 10, borderBottomWidth: 0,
                }}
                >
                  关于

                </Text>
              </View>
            </Item>
          </List>
        </ScrollView>
      </View>
    )
  }
}
export default SideMenu(UserNav)
