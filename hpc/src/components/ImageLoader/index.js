import React from 'react'
import PropTypes from 'prop-types'

import {
  View,
  Text,
  ActivityIndicator,
  Image,
} from 'react-native'

const styles = {
  placeHolder: {
    height: 45,
    width: 45,
    position: 'absolute',
    backgroundColor: '#f3f3f3',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  container: {
    height: 45,
    width: 45,
    alignContent: 'center',
    justifyContent: 'center',
    padding: 2,
    backgroundColor: '#f3f3f3',
  },
}

class ImageLoader extends React.Component {

  constructor(props) {
    super(props)
    this.state = {
      loading: false,
      error: '',
      src: this.props.src,
    }
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      src: nextProps.src,
      error: nextProps.src ? '' : '加载失败',
    })
  }

  render() {
    console.log('图片', this.props, this.state)
    const { style = { height: 45, width: 45 }, textStyle = {}, cententStyle = {}, resizeMode } = this.props
    return (
      <View>
        {
          this.state.error ?
            <View style={{ ...styles.container, ...cententStyle }}>
              <Text style={{ textAlign: 'center', ...textStyle }}>{this.state.error}</Text>
            </View>
            :
            <Image
              source={{ uri: this.state.src }}
              style={{ ...style }}
              resizeMode={resizeMode}
              onLoadStart={() => this.setState({ loading: true })}
              onLoadEnd={() => { }}
              onError={() => { this.setState({ error: '加载失败', loading: false }) }}
              onLoad={() => this.setState({ loading: false })}
            />
        }
        {
          this.state.loading ?
            <View style={{ ...styles.placeHolder, ...cententStyle }}>
              <ActivityIndicator />
            </View> : null
        }
      </View>
    )
  }
}
ImageLoader.propTypes = {
  src: PropTypes.string,
  resizeMode: PropTypes.string,
  style: PropTypes.object,
  cententStyle: PropTypes.object,
  textStyle: PropTypes.object,
}
export default ImageLoader
