import PropTypes from 'prop-types';
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import AppList from './AppList';
import { deviceType } from '../../config/sysPara';

export default class AppHome extends React.Component {
  state={
    showCreateAppModel: false,
  }

  showCreateAppModel = () => {
    this.props.onClickCreateApp()
    this.setState({
      showCreateAppModel: true,
    })
  }

  hideCreateAppModel = () => {
    this.setState({
      showCreateAppModel: false,
    })
  }

  render() {
    const { owned, joined } = this.props
    const length = owned.length + joined.length
    // 设置applist弹出框高度为最多显示8个item，每个item的高度为45
    const height = length >= 8 ? 45 * 8 : length * 45
    return (
      <View style={[styles.container,{height:height + 45 + 10}]}>
        <View style={styles.title}>
          <Text style={{fontSize:16}}>应用切换</Text>
        </View>
        <View style={{height}}>
          <AppList
            apps={[...owned,...joined]}
            createApp={this.showCreateAppModel}
            enterApp={this.props.enterApp}
            setAsDefaultApp={this.props.setAsDefaultApp}
            deleteApp={this.props.deleteApp}
            selectCallBack={this.props.selectCallBack}
            refreshTitle={this.props.refreshTitle}
            style={this.props.style}
          />
        </View>
      </View>
    )
  }
}

const { height, width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    justifyContent:'center',
    alignItems:'center',
    borderRadius:5,
    backgroundColor:'#fff',
    width: width - 60
  },
  title: {
    height:45,
    width: width - 60,
    justifyContent:'center',
    alignItems:'center',
    borderBottomColor:'#ddd',
    borderBottomWidth: deviceType === 2 ? 0.5 : 0.6,
    // borderTopLeftRadius:10,
    // borderTopRightRadius:10
  },
});

AppHome.propTypes = {
  owned: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
    })),
  joined: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
    })),
  appTemplates: PropTypes.arrayOf(PropTypes.object),
  appId: PropTypes.number,
  onClickCreateApp: PropTypes.func.isRequired,
  createApp: PropTypes.func.isRequired,
  enterApp: PropTypes.func.isRequired,
  editApp: PropTypes.func.isRequired,
  publishApp: PropTypes.func.isRequired,
  setAsDefaultApp: PropTypes.func.isRequired,
  deleteApp: PropTypes.func.isRequired,
}

AppHome.defaultProps = {
  appTemplates: [],
  createSuccess: false,
  appId: 0,
}
