import PropTypes from 'prop-types'
import React from 'react'
import {
  View,
  Text,
  TouchableHighlight,
} from 'react-native'
import { SwipeAction, Modal } from '@ant-design/react-native'
import NavigationService from '../../NavigationService'
import { delayLongPress } from '../../config/sysPara'
import AGZListView from '../AGZListView'
import AGZIcon from '../Icon'
import getColor from '../../utils/colors'

// eslint-disable-next-line
export default class AppList extends React.Component {
  render() {
    const {
      apps,
      createApp,
      enterApp,
      deleteApp,
      setAsDefaultApp } = this.props

    // appsNew = [...apps,{id:'0',name:'创建应用',icon:'add-circle'}]

    const row = app => (
      <SwipeAction
        key={app.id}
        style={{ backgroundColor: 'gray' }}
        autoClose
        disabled
        right={[
          {
            text: 'Cancel',
            onPress: () => console.log('cancel'),
            style: { backgroundColor: '#ddd', color: 'white' },
          },
          {
            text: 'Delete',
            onPress: () => Modal.alert('删除', '确定删除?', [
                { text: '取消', onPress: () => console.log('cancel') },
                { text: '确定', onPress: () => deleteApp(app.id), style: { fontWeight: 'bold' } },
            ]),
            style: { backgroundColor: '#F4333C', color: 'white' },
          },
        ]}
      >
        <TouchableHighlight
          underlayColor="rgb(210, 230, 255)"
          delayLongPress={Number(delayLongPress)}
          onPress={() => {
            if (app.id === '0') {
              createApp(app)
            } else {
              if (this.props.selectCallBack) {
                this.props.selectCallBack(app.id)
              }
              // enterApp(app.id,app.name)()
              NavigationService.refresh({ appId: app.id, appName: app.name })
            }
          }
        }
        >
          <View style={{ height: 45, flex: 1, flexDirection: 'row', backgroundColor: 'white', alignItems: 'center' }}>
            <AGZIcon name={app.icon} backcolor={getColor(app.id)} color="#fff" width={32} leftMargin={10} iconSize={20} />
            <Text style={{ fontSize: 17, marginLeft: 10 }}>{app.name}</Text>
          </View>
        </TouchableHighlight>
      </SwipeAction>
      )

    return (
      <AGZListView
        style={this.props.style}
        datas={apps}
        rowRender={row}
      />
    )
  }
}

AppList.propTypes = {
  apps: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
    })),
  createApp: PropTypes.func,
  enterApp: PropTypes.func,
  deleteApp: PropTypes.func,
  setAsDefaultApp: PropTypes.func,
}

AppList.defaultProps = {
  apps: [],
}
