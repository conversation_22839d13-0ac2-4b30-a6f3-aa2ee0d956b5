import React, { Component } from 'react'
import { View, Text, TouchableHighlight } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'

import getIconName from '../../config/iconConvert'
import { delayLongPress } from '../../config/sysPara'

export default class AGZIcon extends Component {
  render() {
    if (this.props.shape === 'round') {
      return (
        <TouchableHighlight
          delayLongPress={Number(delayLongPress)}
          style={{
            height: this.props.height ? this.props.height : 40, width: this.props.width ? this.props.width : 40, margin: 0,
            backgroundColor: this.props.backcolor ? this.props.backcolor : '#55a2ed',
            borderRadius: this.props.height ? this.props.height * 0.5 : 20,
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'center',
          }}
        >
          <Icon name={getIconName(this.props.name)} size={this.props.iconSize ? this.props.iconSize : 24} color={this.props.color ? this.props.color : '#fff'} />
        </TouchableHighlight>
      )
    } else {
      return (
        <View
          style={{
            opacity: this.props.opacity,
            backgroundColor: this.props.backcolor ? this.props.backcolor : '#55a2ed',
            height: this.props.height >= 0 ? this.props.height : 32, width: this.props.width >= 0 ? this.props.width : 32,
            justifyContent: 'center', alignItems: 'center', alignSelf: 'center',
            marginLeft: this.props.leftMargin ? this.props.leftMargin : 0, borderRadius: 8 }}
        >
          <Icon name={getIconName(this.props.name)} size={this.props.iconSize ? this.props.iconSize : 24} color={this.props.color ? this.props.color : '#fff'} />
        </View>
      )
    }
  }
}

AGZIcon.defaultProps = {
  shape: 'rect',
}
