import PropTypes from 'prop-types'
import React from 'react'
import { View, Text, FlatList, TouchableOpacity, Image } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { Tabs, Toast, InputItem } from '@ant-design/react-native'
import max from 'lodash/max'
import moment from 'moment'

import Field from './Field'
import {
  COMPONENT_FORM_ID,
  isFormOperationComponent,
  isCopyFormDataComponent,
  isMustRenderComponent,
} from '../../../addons/constants'
import { screenWidth } from '../../../config/sysPara'
import { getAuditRecords, getAuditComments } from '../../Workflow/Store'

const getRowHeight = fields => max(fields.map(field => field.properties.height || 1)) || 1

const getTabs = (fields) => {
  const tabFieldIds = []
  return fields.map((field) => {
    if (field.componentId === '32') {
      return {
        ...field,
        properties: {
          ...field.properties,
          tabs: field.properties.tabs.map(t => ({
            ...t,
            fields: t.fields.map((fieldId) => {
              tabFieldIds.push(fieldId)
              return fields.find(f => f.id === fieldId)
            }).filter(f => f),
          })),
        },
      }
    }
    return field
  }).filter(field => tabFieldIds.indexOf(field.id) === -1)
}

const getRowList = (items, isRunTime, maxCol = 4) => {
  items = getTabs(items)
  let list = []
  const hideItems = []
  let rowList = []
  // const reportList = []
  items.filter(field => (
    isRunTime ?
      field.id !== COMPONENT_FORM_ID
      && !isFormOperationComponent(field)
      && !isCopyFormDataComponent(field)
      : field.id !== COMPONENT_FORM_ID)).forEach((item) => {
        if ((isRunTime && item.properties.visible === false) || (!isRunTime && item.properties.designvisible === false)) {
          if (isMustRenderComponent(item)) {
            hideItems.push(item)
          }
          return
        }
        if (list.length !== 0 && (item.properties.singleRow || list.reduce((total, field) => field.properties.col + total, item.properties.col) > maxCol)) {
          rowList = rowList.concat(list)
          list = []
        }
        list.push(item)
      })
  rowList = rowList.concat(list)

  if (hideItems.length > 0) {
    rowList = rowList.concat(hideItems)
  }

  // if (reportList.length > 0) {
  //   const reportItems = { id: '', componentId: '4', type: 'Report', properties: { col: 4, label: '报表', reportArr: reportList } }
  //   rowList = rowList.concat(reportItems)
  // }
  return rowList
}

class Fields extends React.PureComponent {
  constructor(props) {
    super(props)
    this.cmpFocusArray = []
    this.state = {
      rowList: getRowList(this.props.items, this.props.isRunTime),
      auditRecords: [],
      commentstyle: 1,
    }
  }

  componentWillMount = () => {
    if (this.props.isWorkFlowForm || this.props.isWfComplete) {
      this.loadAuditRecords()
    }
  }

  componentWillReceiveProps = ({ items, isWorkFlowForm, isWfComplete }) => {
    if (this.props.items !== items) {
      this.setState({
        rowList: getRowList(items, this.props.isRunTime),
      })
    }
    if ((this.props.isWorkFlowForm !== isWorkFlowForm || this.props.isWfComplete !== isWfComplete) && (isWorkFlowForm || isWfComplete)) {
      this.loadAuditRecords()
    }
  }

  changeFormsFocus = (fieldId) => {
    // const currentIndex = this.cmpFocusArray.findIndex(item => item.key === fieldId)
    // const nextObj = this.cmpFocusArray[currentIndex + 1]
    // if (nextObj && nextObj.cmp && nextObj.cmp.refCmp) {
    //   nextObj.cmp.refCmp.cmpFocus()
    // }
  }

  loadAuditRecords = async () => {
    const {
      appId,
      formId,
      dataId,
    } = this.props
    if (dataId) {
      const auditRecords = this.state.commentstyle === 1 ? await getAuditComments(appId, formId, dataId) : await getAuditRecords(appId, formId, dataId)
      this.setState({ auditRecords })
    }
  }

  renderRow = (isInTab = false) => (fields) => {
    let needChangeFocusCmpCount = 0
    if (!fields) {
      return null
    }
    return fields.filter(({ type }) => type !== 'Form').map((field) => {
      if (field.componentId === '32') {
        return this.renderTab(field)
      }
      needChangeFocusCmpCount += 1
      return this.renderFocusField(field, isInTab, needChangeFocusCmpCount)
    })
  }

  renderFocusField = (field, isInTab, needChangeFocusCmpCount) => {
    const {
      items, onSelect, selectId,
      removeField, copyField, isRunTime,
      updateValue, appId, isView, updateEveryValue, fieldConfig,
      selectFields, selectFieldIds, updateValidation, updateExtraProps,
      inputBlur, isTab, copyDataId, operator, formType, isWorkFlowForm,
    } = this.props
    return (<Field
      changeFormsFocus={this.changeFormsFocus}
      ref={fieldCmp => this.cmpFocusArray[needChangeFocusCmpCount - 1] = { key: field.id, cmp: fieldCmp }}
      formId={this.props.formId}
      dataId={this.props.dataId}
      copyDataId={copyDataId}
      field={field}
      key={field.id}
      index={items.findIndex(item => item.id === field.id)}
      onSelect={onSelect}
      selectId={selectId}
      copyField={copyField}
      removeField={removeField}
      isRunTime={isRunTime}
      updateValue={updateValue}
      updateEveryValue={updateEveryValue}
      updateExtraProps={updateExtraProps}
      appId={appId}
      isView={isView}
      selectFields={selectFields}
      selectFieldIds={selectFieldIds}
      updateValidation={updateValidation}
      updateEditedFlag={this.props.updateEditedFlag}
      currEditFieldId={this.props.currEditFieldId}
      isInTab={isInTab}
      viewData={this.props.viewData}
      changeESVS={this.changeEnableScrollViewScroll}
      enableScrollViewScroll={this.state.enableScrollViewScroll}
      listItemCount={this.state.rowList.length}
      showCameraFun={this.props.showCameraFun}
      InvoServiceId={this.props.InvoServiceId}
      inputBlur={inputBlur}
      isTab={isTab}
      refPropDatas={this.props.refPropDatas ? this.props.refPropDatas[field.id] : undefined}
      refDisplay={this.props.refDisplay ? this.props.refDisplay[field.id] : undefined}
      operator={operator}
      formType={formType}
      formSubmitHandle={this.props.formSubmitHandle}
      fieldConfig={fieldConfig}
      isWorkFlowForm={isWorkFlowForm}
      renderWizardFields={this.props.renderWizardFields}
    />)
  }

  changeTab = fieldId => (key) => {
    if (this.props.updateValue) {
      this.props.updateValue({
        [fieldId]: {
          activeKey: key, // web端：标识哪个页签是当前页签，不是当前页签的不渲染，提高性能。移动端暂不实现
        },
      })
    }
  }

  renderTab = (field) => {
    if (this.props.isRunTime) {
      const tabs = field.properties.tabs.map((t, i) => ({ title: t.name }))

      return (
        <View key={field.id} style={{ flex: 1, width: screenWidth }}>
          <Tabs
            tabs={tabs}
            swipeable={false}
            tabBarPosition="top"
          >
            {
              field.properties.tabs.map((t, i) => this.renderTabPane(t, i))
            }
          </Tabs>
        </View>
      )
    }
  }

  renderTabPane = (tabPane, index) => {
    const paneFields = getRowList(tabPane.fields, this.props.isRunTime, this.MAX_COL)
    const rowRender = this.renderRow(true)
    return (
      <View key={index}>
        {rowRender(paneFields)}
      </View>
    )
  }

  changeEnableScrollViewScroll = (isTrue) => {
    this.setState({ enableScrollViewScroll: isTrue })
  }

  renderFlatItem = (rowData) => {
    rowData = rowData[0]
    return (
      <TouchableOpacity
        onPress={() => {

        }}
        style={{
          height: 49, paddingLeft: 10, justifyContent: 'center', backgroundColor: '#fff',
        }}
      >
        <Text style={{ fontSize: 16 }}>{rowData.properties.label}</Text>
      </TouchableOpacity>
    )
  }

  renderList = (formDataInited) => {
    if (formDataInited) {
      if (this.state.rowList.length == 1) {
        return this.state.rowList.map(this.renderRow())
      }
      return (<FlatList
        style={{ flex: 1 }}
        data={this.state.rowList}
        renderItem={item => this.renderFlatItem(item.item)}
        ItemSeparatorComponent={() => <View style={{ height: 1, backgroundColor: '#f3f3f3' }} />}
        keyExtractor={(item, index) => item[0].id}
      />)
    }
  }

  sizeChange = (width, height) => {
    if (height === 0) {
      return
    }
    this.scrollHeight = height
  }
  viewSizeChange = (e) => {
    if (e.nativeEvent.layout.height === 0) {
      return
    }
    this.fieldHeight = e.nativeEvent.layout.height
  }

  renderTimeLine1 = (row, index) => {
    const { actId, actName, userName, actTime, comments = [], useTimes } = row
    return (
      <View
        key={actId}
        style={{ flexDirection: 'row' }}
      >
        <View>
          <View style={{ width: 8, height: 8, borderRadius: 8, backgroundColor: '#108ee9' }} />
          <View style={{ display: this.state.auditRecords.length === index + 1 ? 'none' : 'flex', flex: 1, borderLeftWidth: 2, borderColor: '#e8e8e8', marginLeft: 3 }} />
        </View>
        <View style={{ marginLeft: 4, marginTop: -7, paddingBottom: 10 }}>
          <Text style={{ lineHeight: 22, color: '#1b1b1b', fontWeight: '900' }}>{actName}</Text>
          {userName &&
            <Text style={{ lineHeight: 22, color: '#000000a4', fontSize: 12 }}>
              {userName} · {actTime}
            </Text>}
          {useTimes && <Text style={{ fontSize: 12, color: 'rgba(0,0,0,0.45)' }}>{useTimes}</Text>}
          {comments.map(({ auditorName, auditOprName, auditTime, auditOpinions, durationStr }) => (
            <View style={{ paddingBottom: 10 }}>
              <Text style={{ color: '#000000a4', fontSize: 12 }}>
                {auditorName} · <Text style={{ fontWeight: '900' }}>{auditOprName}</Text> · {auditTime}
              </Text>
              {auditOpinions ? <Text style={{ lineHeight: 22, color: '#1b1b1b' }}>{auditOpinions}</Text> : null}
              <Text style={{ fontSize: 12, color: 'rgba(0,0,0,0.45)' }}>{durationStr}</Text>
            </View>
          ))}
        </View>
      </View>
    )
  }

  renderTimeLine2 = (row, index) => {
    const { id, auditorName, auditTime, auditOpinions, oprName } = row
    const time = moment(Number(auditTime)).format('YYYY-MM-DD HH:mm:ss')
    return (
      <View
        key={id}
        style={{ flexDirection: 'row' }}
      >
        <View>
          <View style={{ width: 8, height: 8, borderRadius: 8, backgroundColor: '#108ee9' }} />
          <View style={{ display: this.state.auditRecords.length === index + 1 ? 'none' : 'flex', flex: 1, borderLeftWidth: 2, borderColor: '#e8e8e8', marginLeft: 3 }} />
        </View>
        <View style={{ marginLeft: 4, marginTop: -7, paddingBottom: 20 }}>
          <Text style={{ lineHeight: 22, color: '#000000a4', fontSize: 12 }}>{auditorName} · <Text style={{ fontWeight: '900' }}>{oprName}</Text> · {time}</Text>
          <Text style={{ lineHeight: 22, color: '#1b1b1b' }}>{auditOpinions}</Text>
        </View>
      </View>
    )
  }

  refScrollView = ref => this.scrollViewCmp = ref
  render = () => {
    const { formDataInited, isTab, noDisplay, isWorkFlowForm, isWfComplete } = this.props
    if (isTab) {
      return (
        <View
          onLayout={this.viewSizeChange}
          style={{ flex: 1, display: noDisplay ? 'none' : 'flex' }}
          onStartShouldSetResponderCapture={() => {
            this.setState({ enableScrollViewScroll: true })
          }}
        >
          {formDataInited && this.renderRow()(this.state.rowList)}
          {(isWorkFlowForm || isWfComplete) && this.state.auditRecords.length > 0 &&
            <View style={{ backgroundColor: '#fff', marginVertical: 10 }}>
              <Text style={{ paddingLeft: 20, lineHeight: 45, color: '#1b1b1b', borderColor: '#e5e5e5', borderBottomWidth: 1 }}>审批意见</Text>
              <View style={{ padding: 20, paddingBottom: 0 }}>
                {this.state.commentstyle === 1 ? this.state.auditRecords.map(this.renderTimeLine1) : this.state.auditRecords.map(this.renderTimeLine2)}
              </View>
            </View>}
        </View>
      )
    }
    // console.log('asbd,andand.amx cmzbnckasdklal;dma cmasdnl;ajd;ljas;d', this.props)
    let list2 = false
    if (this.props.items.length > 0) {
      for (let i = 0; i < this.props.items.length; i++) {
        if (this.props.items[i].componentId === '161') {
          list2 = true
        }
      }
    }
    return (
      <View
        onLayout={this.viewSizeChange}
        style={{
          flex: 1,
          marginTop: list2 ? 0 : 10,
          display: noDisplay ? 'none' : 'flex'
        }}
        onStartShouldSetResponderCapture={() => {
          this.setState({ enableScrollViewScroll: true })
        }}
      >
        {
          list2 ?
            formDataInited && this.renderRow()(this.state.rowList)
            :
            <KeyboardAwareScrollView
              onContentSizeChange={this.sizeChange}
              keyboardOpeningTime={0}
              innerRef={this.refScrollView}
              scrollEnabled={this.state.enableScrollViewScroll}
              style={{ marginTop: 0, flex: 1 }}
            >
              {formDataInited && this.renderRow()(this.state.rowList)}
              {(isWorkFlowForm || isWfComplete) && this.state.auditRecords.length > 0 &&
                <View style={{ backgroundColor: '#fff', marginVertical: 10 }}>
                  <Text style={{ paddingLeft: 20, lineHeight: 45, color: '#1b1b1b', borderColor: '#e5e5e5', borderBottomWidth: 1 }}>审批意见</Text>
                  <View style={{ padding: 20, paddingBottom: 0 }}>
                    {this.state.commentstyle === 1 ? this.state.auditRecords.map(this.renderTimeLine1) : this.state.auditRecords.map(this.renderTimeLine2)}
                  </View>
                </View>}
            </KeyboardAwareScrollView>
        }
      </View>
    )
  }
}

Fields.propTypes = {
  items: PropTypes.arrayOf(PropTypes.object).isRequired,
  onSelect: PropTypes.func,
  selectId: PropTypes.string,
  formId: PropTypes.string,
  dataId: PropTypes.string,
  appId: PropTypes.string,
  removeField: PropTypes.func,
  copyField: PropTypes.func,
  updateValue: PropTypes.func,
  isView: PropTypes.bool,
  formDataInited: PropTypes.bool,
  selectFields: PropTypes.func,
  updateValidation: PropTypes.func,
  selectFieldIds: PropTypes.arrayOf(PropTypes.string),
  updateExtraProps: PropTypes.func,
  showCameraFun: PropTypes.func,
  inputBlur: PropTypes.func,
  isTab: PropTypes.bool,
}
export default Fields
