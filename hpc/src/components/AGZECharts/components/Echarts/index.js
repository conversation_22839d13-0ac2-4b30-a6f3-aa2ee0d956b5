import React, { Component } from 'react';
import {  View, StyleSheet, Platform } from 'react-native';
import renderChart from './renderChart';
import {WebView} from 'react-native-webview'
import echarts from './echarts.min';

export default class App extends Component {
  componentWillReceiveProps(nextProps) {
    if(nextProps.option !== this.props.option) {
      this.refs.chart.reload();
    }
  }

  render() {
    return (
      <View style={{flex: 1, height: this.props.height || 400,}}>
        <WebView
          ref="chart"
          scrollEnabled = {false}
          pagingEnabled = {true}
          injectedJavaScript = {renderChart(this.props)}
          style={{
            height: this.props.height || 400,
          }}
          source={__DEV__ ? require('./tpl.html'):(Platform.OS === 'ios' ? require('./tpl.html') : { uri: 'file:///android_asset/tpl.html' })}
        />
      </View>
    );
  }
}
