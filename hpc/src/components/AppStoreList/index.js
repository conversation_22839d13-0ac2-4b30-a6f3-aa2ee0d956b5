import PropTypes from 'prop-types';
import React from 'react';
import { View, Text, StyleSheet, Image, TouchableHighlight,Dimensions } from 'react-native';
import { Icon, SearchBar } from '@ant-design/react-native';

import { delayLongPress, screenWidth } from '../../config/sysPara';
import AGZ<PERSON>istView from '../AGZListView';
import AGZIcon from '../Icon';
import getColor from '../../utils/colors';
import dismissKeyboard from 'dismissKeyboard';

let index;
NUM_ROWS = 20;
pageIndex = 0;

var {height,width} = Dimensions.get('window');
export default class AppStoreList extends React.Component {
  state = {
    filter: '',
  }

  onChange = (value) => {
    this.setState({
      filter: value,
    })
  };

  clear = () => {
    dismissKeyboard();
    this.setState({ filter: '' ,});
  };

  render() {
    const row = (obj) => {
      return (
        <TouchableHighlight underlayColor="rgb(210, 230, 255)" delayLongPress={Number(delayLongPress)} 
          onPress={() => {
            /*if (this.props.goDetail) {
              this.props.goDetail((obj.id))
            } else {*/
              // Save step state for use in other steps of the wizard
              if (this.props.saveState) {
                this.props.saveState(0,{key:obj.id})
                // Go to next step
                this.props.nextFn()
              } else {
                Toast.info('暂无动态')
              }
           // }
          }}>
          <View key={obj.id} style={styles.row}>
            <AGZIcon name={obj.icon} backcolor={getColor(obj.id)} />
            <Text style={styles.rowText}>
              {obj.name}
            </Text>
            <Icon type='right' size={20} color={'#ddd'} style={{ justifyContent:'center',alignItems: 'center'}}/>
          </View>
        </TouchableHighlight>
      );
    };


    return (
      <View style={styles.listViewStyle}>
        <View>
        <SearchBar 
          value={this.state.value}
          placeholder="搜索应用"
          onSubmit={this.onChange}
          onClear={this.clear}
          onCancel={this.clear}
          onChange={this.onChange} />
        </View>
        <AGZListView 
          datas={this.props.templates.filter((template) => {
            const exp = new RegExp(this.state.filter, 'gi')
            return exp.test(template.name) || exp.test(template.decription)
          })} 
          rowRender={row}/>
      </View>
    );
  }
}

AppStoreList.propTypes = {
  templates: PropTypes.arrayOf(PropTypes.object),
}

var styles = StyleSheet.create({
  row: {
    flex:1,
    flexDirection:'row',
    marginLeft:0,
    height:50,
    padding: 2,
    backgroundColor: 'white',
    justifyContent:'center',
    alignItems:'center'
  },
  rowText: {
    fontSize: 17,
    marginLeft:15,
    justifyContent:'center',
    alignItems:'center',
    width: screenWidth-100
  },
  listViewStyle:{
    height:height -49-12
  }
});