import React, { Component } from 'react'
import Dimensions from 'Dimensions'
import {
  View,
  Text,
  TouchableHighlight,
  StyleSheet,
} from 'react-native'
import { Grid } from '@ant-design/react-native'
import { delayLongPress } from '../../config/sysPara'
import AGZIcon from '../Icon'
import getColor from '../../utils/colors'
import NavigationService from '../../NavigationService'

class AGZGrid extends Component {
  _onPressButton(form) {
    switch (form.type) {
      case 0:  // 目录类型，目前业务定义为只有一级目录

        break
      case 1:  // 表单类型
        this.props.onSelectForm(this.props.appId, form)
        break
      case 2: // 用户管理
        NavigationService.navigate('inviteUsers', { appId: this.props.appId })
        break
      case 3: // 应用设置
        break
    }
  }
  render() {
    const { gridData } = this.props
    return (
      <View style={{ marginTop: 0 }}>
        <Grid
          data={gridData}
          columnNum={4}
          hasLine={false}
          renderItem={dataItem => (
            <TouchableHighlight
              underlayColor="rgb(210, 230, 255)" delayLongPress={Number(delayLongPress)}
              onPress={() => this._onPressButton(dataItem)}
            >

              <View style={{ alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginTop: 22.5 }}>
                {/**
                    <AGZIcon name={dataItem.icon}  backcolor={getColor(dataItem.id)} color="#fff" style={{alignSelf:'center',alignItems:'center'}} width = {32} iconSize={20}/>
                    */}
                <AGZIcon name={dataItem.icon} backcolor={getColor(dataItem.id)} color="#fff" style={{ alignSelf: 'center', alignItems: 'center', opacity: dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 1 : 0 }} opacity={dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 1 : 0} width={dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 32 : 0} height={dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 32 : 0} iconSize={20} />
                <View
                  style={{
                    opacity: dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 0 : 1,
                    backgroundColor: getColor(dataItem.id),
                    height: dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 0 : 32,
                    width: dataItem.icon === 'add' || dataItem.icon === 'information-circle' || dataItem.icon === 'people' ? 0 : 32,
                    justifyContent: 'center', alignItems: 'center', alignSelf: 'center',
                    marginLeft: 0, borderRadius: 8 }}
                >
                  <Text style={{ color: '#fff', fontSize: 14, fontWeight: 'bold' }}>{dataItem.name.substr(0, 1)}</Text>
                </View>
                <Text style={{ marginTop: 5, fontSize: 11, alignSelf: 'center', alignItems: 'center' }}>{dataItem.name}</Text>
              </View>
            </TouchableHighlight>
                )}
        />
      </View>
    )
  }
}

const widOrHei = Dimensions.get('window').width / 4
const styles = StyleSheet.create({
  touchBox: {
    alignItems: 'center',
    justifyContent: 'center',
    height: widOrHei,
    width: widOrHei,
  },
  touchBoxView: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchBoxImg: {
    marginBottom: 10,
  },
})

export default AGZGrid
