import React from 'react'
import { Image, StyleSheet, Dimensions, View, Text, AsyncStorage, ActivityIndicator, TouchableOpacity, NetInfo, Keyboard, Alert, StatusBar, Platform, ScrollView } from 'react-native'
import { List, InputItem, Button, Toast, Modal } from '@ant-design/react-native'
import {companyName} from '@project-config'

import { createForm } from 'rc-form'
// import DropdownAlert from 'react-native-dropdownalert'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import DeviceInfo from 'react-native-device-info'
import { envParm } from '../../config/sysPara'
import NavigationService from '../../NavigationService'

import {
  RULE_NULL_MOBILE,
  RULE_INVALID_MOBILE,
  RULE_NULL_PASSWORD,
  RULE_INVALID_PASSWORD,
  RULE_NULL_CAPTCHA,
  RULE_INVALID_CAPTCHA,
} from '../../utils/validation/rules'
import { GO_SIGNUP, GO_SIGNUPR, GO_NETSOLUTION, GO_MODAL, GO_INNEROFFICIALWEB } from '../../utils/jump'
import { getToken, getUserinfo, getUserSettings } from '../../utils/storage'

const bgColor = ['#DFEBE8', '#DAD9D0', '#FEFFF5', '#EFF9F9', '#EFF9F9', '#D7E9F4', '#EDCFC4', '#D3EFEC', '#534E74', '#F7F0C5', '#FCF0E0', '#B5B3D1']
const bgIamge = [
  require('../../images/Login/bg/app-login-background-01.png'),
  require('../../images/Login/bg/app-login-background-02.png'),
  require('../../images/Login/bg/app-login-background-03.png'),
  require('../../images/Login/bg/app-login-background-04.png'),
  require('../../images/Login/bg/app-login-background-05.png'),
  require('../../images/Login/bg/app-login-background-06.png'),
  require('../../images/Login/bg/app-login-background-07.png'),
  require('../../images/Login/bg/app-login-background-08.png'),
  require('../../images/Login/bg/app-login-background-09.png'),
  require('../../images/Login/bg/app-login-background-10.png'),
  require('../../images/Login/bg/app-login-background-11.png'),
  require('../../images/Login/bg/app-login-background-12.png')
]
const ListItem = List.Item
const { height, width } = Dimensions.get('window')
const statusBarHeight = StatusBar.currentHeight; // 安卓获取状态栏高度
// const { StatusBarManager } = NativeModules; // ios获取状态栏高度

/**
 * 判断IphoneX， 用于 UI适配
 * 直接一次生成结果，后续不再可执行
 */
const isIphoneX = (function () {
  // iPhoneX SIZE
  const X_WIDTH = 375
  const X_HEIGHT = 812

  // DEVICE SIZE
  const { width, height } = Dimensions.get('window')
  const sizes = [X_WIDTH, X_HEIGHT]

  return Platform.OS === 'ios'
    ? sizes.includes(width) && sizes.includes(height)
    : false

})()

class Login extends React.PureComponent {
  state = {
    hasToken: false,
    isConnected: true,
    visible: false,
  }
  componentDidMount = async () => {
    let privacyRes = null
    try {
      const resultA = await AsyncStorage.getItem('PrivacyPolicy')
      privacyRes = JSON.parse(resultA)
    } catch (e) {
      __DEV__ && console.log('获取本地用户数据失败', privacyRes, e)
    }
    if (!privacyRes) {
      this.setState({
        visible: true
      })
    }
  }
  componentWillMount = async () => {
    const res = await this._checkToken()
    console.log('_checkToken',res)
    if (res) {
      this.setState({ hasToken: true })
    } else {
      this.setState({ hasToken: false })
    }
    NetInfo.isConnected.addEventListener(
      'connectionChange',
      this._handleConnectivityChange,
    )
    NetInfo.isConnected.fetch().done((isConnected) => { this.setState({ isConnected }) })
  }

  componentWillUnmount() {
    NetInfo.isConnected.removeEventListener(
      'connectionChange',
      this._handleConnectivityChange,
    )
  }

  _handleConnectivityChange = (isConnected) => {
    this.setState({
      isConnected,
    })
    if (isConnected && this.state.hasToken) {
      this._checkToken().then((res) => {
        if (res) {
          this.setState({ hasToken: true })
        } else {
          this.setState({ hasToken: false })
        }
      })
    }
  }

  // 样式数组处理方法
  convertArray = (item) => {
    return item instanceof Array ? item : [item]
  }

  // 样式合并
  styleMerge = (src, ...styles) => {
    src = this.convertArray(src)
    return src.concat(...styles)
  }

  // 登录页的背景样式
  backgroundIamge = () => {
    let nowTime = new Date()
    const month = nowTime.getMonth() + 1
    return month - 1
  }

  // 获取状态栏高度
  getCurrentHeight() {
    return StatusBar.currentHeight || (isIphoneX ? 44 : 20)
  }

  async _checkToken() {
    try {
      const token = await getToken()

      if (token) {
        const user = await getUserinfo()
        const userSettings = await getUserSettings()
        //  loginCcServer(user, token)
        this.props.dispachGlobalUser(user)
        this.props.dispachGenUserSettings(userSettings)
        this.props.getDefaultApp()
        return true
      }
      this.setState({hasToken:false})
      this.props.dispachLogOut()
      return false

    } catch (e) {
      console.log(e)
      return false

    }
    return false
  }

  toNextEditing = () => {
    this.pwdInput.focus()
  }
  openVersion = () => {
    const envurl = envParm.URLS.replace('http://', '').split('/')[0]
    Alert.alert(`${companyName}版本`, `JSVERSION: ${envParm.version}\nAGZVERSION: ${DeviceInfo.getVersion()}\nENV: ${envurl}`)
  }

  onPrivacyPolicy = () => {
    try {
      AsyncStorage.setItem('PrivacyPolicy', JSON.stringify({ PrivacyPolicy: true }))
    } catch (e) {
      __DEV__ && console.log('储存本地用户数据失败', e)
      return
    }
    this.setState({
      visible: false
    })
  }
  render() {
    const {
      form,
      login,
      refreshCaptcha,
      captchaImg,
      showCaptcha,
      mobile,
      remember,
      setShow,
      isShsow
    } = this.props
    const retry = () => {
      // 检测网络
      NetInfo.isConnected.fetch().done((isConnected) => {
        if (!isConnected) {
          Toast.fail('无法连接', 1)
        } else {
          this._checkToken().then((res) => {
            if (!res || this.props.failFlag) {
              this.setState({ hasToken: true })
            }
          })
        }
      })
    }
    // 已经登录且有网 true
    if (this.state.hasToken && this.state.isConnected) {
      return (
        <View style={{
          flex: 1, backgroundColor: 'white', justifyContent: 'center', alignItems: 'center',
        }}
        >
          <ActivityIndicator size="large" />
        </View>
      )
    } else if (!this.state.isConnected) {
      return (
        <View style={styles.container}>
          <Image
            style={{
              height: height * 0.232 > 155 ? height * 0.232 : 155, width: width * 0.298 > 112 ? width * 0.298 : 112, marginTop: height * 0.172, marginLeft: width * 0.368,
            }}
            source={require('../../images/icons-network_unavalible.png')}
          />
          <Text style={{
            fontSize: 18, fontWeight: 'bold', color: '#41454B', marginTop: 20, marginLeft: width * 0.381,
          }}
          >网络不可用
          </Text>
          <Text style={{
            fontSize: 14, color: '#9B9B9B', marginTop: 10, marginLeft: width * 0.221,
          }}
          >请检查您的手机是否已经连接网络
          </Text>
          <Button
            type="primary"
            style={{
              height: 42,
              marginLeft: 20,
              marginRight: 20,
              marginTop: 50,
              borderRadius: 5,
              borderWidth: 0,
              backgroundColor: '#4CBDFF',
            }}
            onPress={retry}
          >重试
          </Button>
        </View>
      )
    }

    const { getFieldDecorator, validateFields } = form
    const handleSubmit = () => {
      Keyboard.dismiss()
      validateFields((err, values) => {
        if (values.mobile === ':dev' || values.mobile === ':Dev') {
          AsyncStorage.setItem('EasterEgg', JSON.stringify({ Pattern: ":dev" }))
        }
        if (values.mobile === ':prod' || values.mobile === ':Prod') {
          AsyncStorage.setItem('EasterEgg', JSON.stringify({ Pattern: ":prod" }))
        }
        if (!err) {
          login(values)
        } else {
          let message = ''
          if (err.mobile && err.mobile.errors.length > 0) {
            message += err.mobile.errors[0].message
            message += '\n'
          }
          if (err.password && err.password.errors.length > 0) {
            message += err.password.errors[0].message
            message += '\n'
          }
          if (err.captcha && err.captcha.errors.length > 0) {
            message += err.captcha.errors[0].message
          }
          Toast.fail(message, 2)
        }
      })
    }

    let capHeigh = 0
    if (showCaptcha) {
      capHeigh = 58
    }
    const newStyle = {}
    return (
      <KeyboardAwareScrollView
        style={this.styleMerge(styles.container, { backgroundColor: bgColor[this.backgroundIamge()] })}
        keyboardShouldPersistTaps="always"
      >

        <View style={{ height: height - this.getCurrentHeight() }}>
          <Image style={{ position: 'absolute', top: height - 210, left: 0, width: '100%' }} source={bgIamge[this.backgroundIamge()]} />
          <View style={{ marginTop: this.getCurrentHeight() + 30, flexDirection: 'row' }}>
            <View style={{ alignItems: 'center', marginLeft: 20 }}>
              <Text style={{
                fontSize: 16, fontWeight: 'bold', color: '#444752'
              }} >
                登录
              </Text>
              <View style={{ width: 24, height: 2, backgroundColor: '#444752', marginTop: 6 }} />
            </View>
            <TouchableOpacity onPress={() => GO_SIGNUPR(false)} >
              <Text style={{
                fontSize: 16, fontWeight: '400', color: '#707588', marginLeft: 30
              }} >
                立即注册
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{ flexDirection: 'row', marginLeft: 20, marginTop: 21 }}>
            <Text style={{ fontSize: 12, fontWeight: '400', color: "rgb(168, 169, 173)" }}>点击登录代表您阅读并同意</Text>
            <TouchableOpacity onPress={() => { NavigationService.navigate('servicePage') }} >
              <Text style={{
                fontSize: 12, fontWeight: '400', color: "rgb(0, 156, 216)"
              }} >
                《服务协议》
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => { NavigationService.navigate('privacyPage') }} >
              <Text style={{
                fontSize: 12, fontWeight: '400', color: "rgb(0, 156, 216)"
              }} >
                《隐私政策》
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{ height: 33 }} />
          <View style={{ paddingLeft: 5, paddingRight: 5 }}>
            {getFieldDecorator('mobile', {
              // rules: [RULE_INVALID_MOBILE, RULE_NULL_MOBILE],
              initialValue: mobile,
              // getValueFromEvent: e => e.replace(/\s+/g, ''),
            })(<InputItem
              style={styles.textInputStyle}
              styles={{ container: { marginRight: 15, paddingRight: 0 } }}
              placeholder="请输入手机号或者邮箱"
              type="text"
              autoComplete="off"
              returnKeyType="next"
              labelNumber={2}
              onSubmitEditing={this.toNextEditing}
              caretHidden={false}// 光标
              underlineColorAndroid="transparent"
            >
              <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 8 }}>
                <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-password.png')} />
              </View>
            </InputItem>)}
            <View style={{ height: 28 }} />
            <View style={{ width: '100%', }}>

              {getFieldDecorator('password', {
                rules: [RULE_INVALID_PASSWORD, RULE_NULL_PASSWORD],
              })(<InputItem
                ref={input => this.pwdInput = input}
                style={styles.textInputStyle}
                styles={{ container: { marginRight: 15, paddingRight: 0 } }}
                placeholder="请输入密码"
                type="password"
                secureTextEntry
                autoComplete="off"
                editable
                labelNumber={2}
                disabled={false}
                underlineColorAndroid="transparent"
              >
                <View style={{ width: 44, height: 44, flexDirection: 'row', backgroundColor: '#fff', alignContent: 'center', justifyContent: 'center', paddingTop: 8 }}>
                  <Image style={{ width: 25, height: 25 }} source={require('../../images/Login/Icon/login-icon-user_name.png')} />
                </View>
              </InputItem>)}
            </View>
          </View>
          <View style={{
            flexDirection: 'row', justifyContent: 'space-between', borderBottomColor: '#E5E5E5', marginTop: capHeigh ? 16 : 0, marginLeft: 20, marginRight: 20, height: Number(capHeigh), opacity: capHeigh ? 1 : 0,
          }}
          >
            {showCaptcha &&
              <View style={{ flexDirection: 'column', flex: 1 }}>
                <Text style={{ fontSize: 14, color: '#41454B', fontWeight: 'bold' }}>验证码</Text>
                {showCaptcha &&
                {
                  ...getFieldDecorator('captcha', {
                    rules: [RULE_INVALID_CAPTCHA, RULE_NULL_CAPTCHA],
                  })(<InputItem
                    style={{ marginRight: 10, borderBottomWidth: 0, marginLeft: 0 }}
                    placeholder="请输入验证码"
                    autoComplete="off"
                    underlineColorAndroid="transparent"
                  />),
                }
                }
              </View>}
            {showCaptcha &&
              <TouchableOpacity onPress={refreshCaptcha}>
                <Image
                  style={{
                    width: 88, height: 42, backgroundColor: 'white', borderRadius: 5,
                  }}
                  source={captchaImg ? { uri: captchaImg } : require('../../images/verification-code-invalidation-default.png')}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            }
          </View>
          <TouchableOpacity type="primary" style={styles.loginBtnStyle} onPress={handleSubmit} >
            <Text style={styles.loginBtnStyleText}>
              登录
            </Text>
            <Image style={{ marginTop: -2 }} source={require('../../images/Login/Icon/login-icon-arrow_right.png')} />
          </TouchableOpacity>
          {/* <Text
            style={{
              fontSize: 14, width: 72, color: '#A0A2A5', backgroundColor: 'transparent', marginRight: 20,
            }}
            
          >
            </Text> */}
          {/* <Text
            style={{
              backgroundColor: 'transparent', fontSize: 14, fontWeight: 'bold', color: '#17A9FF', textAlign: 'center', marginTop: 40,
            }}
            onPress={() => GO_SIGNUP(false)}
          >
            注册华普云账号
          </Text> */}
          <View style={{ flexDirection: 'row', marginLeft: 20, marginTop: 28 }}>
            <Text style={{ color: 'rgb(78,80,83)' }}>忘记密码？</Text>
            <TouchableOpacity onPress={() => GO_SIGNUP(true)} >
              <Text style={{
                fontSize: 14, fontWeight: '400', color: "rgb(0, 156, 216)"
              }} >
                立即找回
              </Text>
            </TouchableOpacity>
          </View>
          {/* <DropdownAlert closeInterval={1100} ref={ref => this.dropdown = ref} /> */}
        </View>
        {/* {
          envParm.bucket === 'huapuc-pd' && <View style={{ height: showCaptcha ? 100 : 160, position: 'absolute', bottom: 10 }}>
            <Text
              onPress={this.openVersion}
              style={{
                width, bottom: 0, position: 'absolute', fontSize: 10, color: '#A0A2A5', textAlign: 'center',
              }}
            >© 华普云信息技术（武汉）有限公司
            </Text>
          </View>
        } */}
        {/* <Modal
          animationType="none"
          transparent={true}
          visible={true}
          style={{backgroundColor: 'rgba(4.25,4.25,4.25,0.4)'}}
        >
          <View style={{ marginLeft: '5%', marginTop: '25%', width: '90%', height: '60%', backgroundColor: 'rgba(4.25,4.25,4.25,0.4)', borderRadius: 4 }}>

          </View>
        </Modal> */}
        {
          this.state.visible ?
            <View style={{
              width: '100%',
              height: height + this.getCurrentHeight(),
              backgroundColor: 'rgba(4.25,4.25,4.25,0.4)',
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center'
            }}>

              <View style={{ width: '90%', height: '60%', backgroundColor: '#fff', borderRadius: 4 }}>
                <Text style={{ color: 'rgba(0,0,0,0.85)', textAlign: 'center', fontSize: 17, marginTop: 22, fontWeight: '700' }}>隐私保护政策</Text>
                <View style={{ padding: 16 }}>
                  <ScrollView style={{ height: height * 0.6 - 22 - 10 - 80, padding: 6 }}>
                    <Text style={{ color: 'rgba(60.56,60.56,60.56,1)', fontSize: 14, fontWeight: '400', lineHeight: 22, marginTop: 5 }}>
                      1.为了给您提供服务，我们可能会向您申请摄像头权限、麦克风权限、手机存储权限、网络权限；
                    </Text>
                    <Text style={{ color: 'rgba(60.56,60.56,60.56,1)', fontSize: 14, fontWeight: '400', lineHeight: 22, marginTop: 5 }}>
                      2.为了帮助您了解、发现自己的联系人，我们可能会向您申请通讯录的权限；
                    </Text>
                    <Text style={{ color: 'rgba(60.56,60.56,60.56,1)', fontSize: 14, fontWeight: '400', lineHeight: 22, marginTop: 5 }}>
                      3.为了提供地图服务我们可能会向您申请位置权限；
                    </Text>
                    <Text style={{ color: 'rgba(60.56,60.56,60.56,1)', fontSize: 14, fontWeight: '400', lineHeight: 22, marginTop: 5 }}>
                      4.为了账号安全，我们可能会向您申请系统设备权限手机设备信息、日志信息；
                    </Text>
                    <Text style={{ color: 'rgba(60.56,60.56,60.56,1)', fontSize: 14, fontWeight: '400', lineHeight: 22, marginTop: 5 }}>
                      5.我们会努力采取各种安全技术保护您的个人信息。未经您的同意，我们不会从第三方获取、共享或者对外提供您的信息。
                    </Text>
                  </ScrollView>
                  <TouchableOpacity onPress={this.onPrivacyPolicy}>
                    <View style={{ width: '100%', height: 44, backgroundColor: 'rgba(24,144,255,1)', borderRadius: 5, alignItems: 'center', justifyContent: 'center', marginTop: 12 }}>
                      <Text style={{ fontSize: 16, color: '#fff' }}>我已知晓并同意上述条款</Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            :
            null
        }
      </KeyboardAwareScrollView >
    )
  }
}

Login.defaultProps = {
  captchaImg: '',
  refreshCaptcha: null,
  mobile: '',
  remember: false,
  showCaptcha: false,
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    backgroundColor: 'white',
  },
  pic: {
    width: 65,
    height: 65,
    marginTop: 129,
    marginLeft: (width - 80) / 2,
    marginBottom: 22.5,
  },
  textInputStyle: {
    flex: 1,
    width: '100%',
    color: '#9B9B9B',
    height: 44,
    fontSize: 14,
    backgroundColor: 'white',
    paddingLeft: 10,
    // marginLeft: 20,
    // marginRight: 20,
    borderRadius: 2,
  },
  captchaInputStyle: {
    width: width * 52 / 100,
    height: 40,
    backgroundColor: 'white',
    marginLeft: 20,
  },
  loginBtnStyle: {
    height: 44,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 30,
    backgroundColor: '#4CBDFF',
    borderWidth: 0,
    borderRadius: 2,
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 12,
  },
  loginBtnStyleText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: '500',
  },
  settingStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: width - 40,
  },
})
export default createForm()(Login)
