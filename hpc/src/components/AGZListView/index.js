import PropTypes from 'prop-types'
import React from 'react'
import {
  View,
  StyleSheet,
  ListView,
  RefreshControl,
  FlatList,
  Text,
} from 'react-native'
import { ActivityIndicator, Button } from '@ant-design/react-native'
import { deviceType } from '../../config/sysPara'
/*
    组件入参：
        datas（必选）: 数据源，数据结构中必须包含id
        rowRender（必选）：行渲染组件，入参为datas[]，包含点击某条数据时的处理函数
        displayTitle（可选）：如果有则在行渲染组件中处理
        onLoading（可选）：向下滚动时加载数据的函数；不传入时表示一次性传入了所有数据
        pagination（可选）：父组件可以指定从第几页开始
*/

NUM_ROWS = 20
pageIndex = 0

export default class AGZListView extends React.PureComponent {
  _data = []
  constructor(props) {
    super(props)
    this.state = {
      data: this.props.datas,
      isLoading: false,
      pagination: this.props.pagination,
      hasMore: true,
      refreshing: false,
    }
    this._data = this.props.datas
  }

  // If you use redux, the data maybe at props, you need use `componentWillReceiveProps`
  componentWillReceiveProps(nextProps) {
    if (nextProps.datas[0] !== this.props.datas[0] || nextProps.datas.length !== this.props.datas.length) {
      this.setState({
        data: nextProps.datas,
      })
      this._data = nextProps.datas
    }
    if (nextProps.pagination && this.state.refreshing && nextProps.pagination.current !== this.props.pagination.current) {
      // @ant-design/react-native的ListView在refresh时，加载了两页数据，所以这里current赋值未2
      this.setState({
        pagination: {
          ...this.state.pagination,
          current: 2,
        },
        hasMore: true,
        refreshing: false,
      })
    }
  }

  onRefresh = async () => {
    if (this.props.onLoading) {
      this.onEndReachedCalledDuringMomentum = false
      this.setState({ refreshing: true })
      const datas = await this.props.onLoading(1)
      if (datas) {
        this.setState({
          data: datas,
          pagination: {
            ...this.state.pagination,
            current: 1,
          },
          hasMore: true,
        })
        this._data = datas
      }
      this.setState({ refreshing: false })
    }
  };

  onEndReached = async (event) => {
    // load new data
    // hasMore: from backend data, indicates whether it is the last page, here is false
    if (!this.onEndReachedCalledDuringMomentum) {
      if (!this.props.onLoading) {
        return
      }
      if (this.state.isLoading || !this.state.hasMore) {
        return
      }
      this.setState({
        isLoading: true,
        pagination: {
          ...this.state.pagination,
          current: ++this.state.pagination.current,
        },
      })
      const datas = await this.props.onLoading(this.state.pagination.current)
      if (datas) {
        this._data = this._data.concat(datas)
        this.setState({
          data: this._data,
          isLoading: false,
          hasMore: true,
        })
      } else {
        this.setState({
          isLoading: false,
          hasMore: false,
        })
      }
      this.onEndReachedCalledDuringMomentum = true
    }
  }

  render() {
    if (!this.props.datas || this.props.datas.length < 1) {
      return (<View />)
    }
    const separator = (sectionID, rowID) => (
      <View
        key={`${sectionID}-${rowID}`}
        style={{
          // backgroundColor: '#ebebef',
          // height: 0.6,
          // borderTopWidth: 0.3,
          borderBottomWidth: deviceType === 2 ? 0.5 : 0.6,
          borderBottomColor: '#ddd',
          marginLeft: 0,
        }}
      />
    )

    const row = rowData =>
      // console.log('AGZLISTVIEW',rowData);
      this.props.rowRender(rowData, this.props.displayTitle)

    return (
      <FlatList
        ref="lv"
        style={this.props.style}
        data={this.state.data}
        onEndReached={() => { this.onEndReached() }}
        onEndReachedThreshold={0.01}
        onRefresh={this.onRefresh}
        refreshing={this.state.refreshing}
        renderItem={({ item, index }) => this.props.rowRender(item, this.props.displayTitle)}
        keyExtractor={(item, index) => index}
        removeClippedSubviews={false}
        onMomentumScrollBegin={() => { this.onEndReachedCalledDuringMomentum = false }}
        ListFooterComponent={() => (<View>
          {this.state.isLoading &&
            <ActivityIndicator
              text="Loading..."
            />}
                                    </View>)}
        ItemSeparatorComponent={separator}
      />
    )
  }
}

AGZListView.propTypes = {
  datas: PropTypes.arrayOf(PropTypes.object),
  rowRender: PropTypes.func.isRequired,
}

const styles = StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'row',
    height: 50,
    padding: 2,
    backgroundColor: 'white',
  },
  rowTitle: {
    height: 10,
    lineHeight: 1,
    color: '#888',
    fontSize: 2,
    borderBottomWidth: 1,
  },
  rowText: {
    fontSize: 25,
    marginLeft: 10,
  },
})
