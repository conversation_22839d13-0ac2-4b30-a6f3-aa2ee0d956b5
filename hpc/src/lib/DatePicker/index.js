import React, { Component } from 'react';
import { View, Text, TouchableOpacity,TouchableWithoutFeedback, Image } from 'react-native';
import PropTypes from 'prop-types';
import AntdDatePicker from '@ant-design/react-native/lib/date-picker';
import { AnimatedImage } from '../AnimatedImage';
import styles from './style';

const CustomChildren = ({displayValue, showMoreTitle,hideMoreTitle, titleShowMore,contentOnClick, onPress, children, isRequire, extra, defaultExtra }) =>{
  return (
  <TouchableOpacity
    activeOpacity={0.8}
    onPress={onPress}
  >
    <View style={styles.container}>
    <TouchableWithoutFeedback onPressIn={showMoreTitle} onPressOut={hideMoreTitle}>
      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
        {isRequire ? <View style={styles.isRequire}>
          <Image
            resizeMode={'contain'}
            source={require('../Image/icon-asterisk-default.png')}
          />
        </View> : <Text style={styles.isRequireText} />}
        <Text numberOfLines={1} style={styles.leftText}>{children}</Text>
      </View>
      </TouchableWithoutFeedback>
      <View
        style={{ flex: 1, flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center',
        }}
      >
        <Text numberOfLines={1} style={extra === defaultExtra ? styles.rightDefaultText : styles.rightText}>{displayValue?extra:defaultExtra}</Text>
        <AnimatedImage onPress={contentOnClick} style={{ height: 50, justifyContent: 'center', paddingLeft: 7, paddingRight: 13 }} />
      </View>
      {titleShowMore ?  <View style={[styles.container, { position: 'absolute', top: 0, overflow: 'hidden' }]}>
      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
        {isRequire ? <View style={styles.isRequire}>
          <Image
            resizeMode={'contain'}
            source={require('../Image/icon-asterisk-default.png')}
          />
        </View> : <Text style={styles.isRequireText} />}
        <Text numberOfLines={1} style={styles.fullTitle}>{children}</Text>
        </View></View>:null}
    </View>
  </TouchableOpacity>
  )};
/**
 * 时间选择器
 * @name Picker
 * @param {string}  [mode=date]  日期选择的类型, 可以是日期date,时间time,日期+时间datetime,年year,月month
 * @param {date}  value  选择的时间，为Date对象
 * @param {date}  minDate  最小可选择的时间，为Date对象
 * @param {date}  maxDate  最大可选择的时间，为Date对象
 * @param {number}  [minuteStep=1]  分钟数递增步长设置
 * @param {bool}  disabled 是否不可用
 * @param {function}  [format=(value: Date) => date string] (value: Date) => date string / format string(对应 mode 下格式分别为:YYYY-MM-DD,HH:mm,YYYY-MM-DD HH:mm)
 * @param {function}  onChange 选中后的回调
 * @param {function}  onValueChange 每列数据选择变化后的回调函数
 * @param {string}  title pickerview的标题
 * @param {string}  [extra=请选择] picker缺省值
 * @param {string}  label label名
 * @param {bool}  isRequire 是否必填
 * @param {func}  contentOnClick 整项的点击事件
 */
export class  DatePicker extends Component {
  state = { 
    titleShowMore:false
  }
  showMoreTitle=()=>{
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: true,
      });
    }
  }
  hideMoreTitle=()=>{
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: false,
      });
    }
  }
  render() {
    const { isRequire, label, mode, value, minDate, maxDate, minuteStep, locale, disabled, onChange, onValueChange, format, title, extra, contentOnClick } = this.props;
    const pickerProps = {
      mode, value:value||new Date(), minDate, maxDate, minuteStep, locale, disabled, onChange, onValueChange, format, title, extra,
    };
    return (
      <View >
        <AntdDatePicker
          {...pickerProps}
        >
          <CustomChildren displayValue={value} showMoreTitle={this.showMoreTitle} hideMoreTitle={this.hideMoreTitle} titleShowMore={this.state.titleShowMore} defaultExtra={extra} contentOnClick={contentOnClick} isRequire={isRequire}>{label}</CustomChildren>
        </AntdDatePicker>
      </View>
    );
  }
}

DatePicker.propTypes = {
  mode: PropTypes.string,
  value: PropTypes.object,
  minDate: PropTypes.object,
  maxDate: PropTypes.object,
  minuteStep: PropTypes.number,
  locale: PropTypes.object,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  onValueChange: PropTypes.func,
  format: PropTypes.func,
  title: PropTypes.string,
  extra: PropTypes.string,
  isRequire: PropTypes.bool,
  label: PropTypes.string,
  contentOnClick: PropTypes.func,
};
DatePicker.defaultProps = {
  isRequire: false,
  label: '时间选择器',
  extra: '请选择',
};

