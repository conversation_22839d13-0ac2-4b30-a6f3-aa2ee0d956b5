import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import styles from './style/styles';
import variables from '../themes/defaultStyles';

/**
 * 单行文本
 * @name SingleLine
 * @param {bool} [isRequire=false] 显示必填符号
 * @param {bool} [editable=true] 是否可输入
 * @param {string} label 标题
 * @param {number} [labelMaxLength=6] 标题最大长度
 * @param {style-object} labelStyle 标题样式，不传则为默认样式
 * @param {string} [placeholder=请输入] 右侧提示信息
 * @param {string} value 右侧显示的值
 * @param {element} [rightIconView=<View />] 最右侧显示的图标，大小为（14，14）
 * @param {string} [inputType=default] 键盘类型 (default,numeric,phone-pad,email-address...)
 * @param {func} onBlur 失去焦点时触发的方法，参数为value
 * @param {func} onChange 控制输入变化，必须实现才能正常输入
 * @param {func} onPress 点击事件，如果不传则为输入框，如果传递则为文本显示
 * @param {func} onRightIconPress 右侧图标点击事件，如果不传则不可点击
 */
export class SimpleLine extends PureComponent {

  static defaultProps ={
    isRequire: false,
    editable: true,
    label: '',
    placeholder: '请输入',
    value: '',
    rightIconView: <View />,
    onBlur: () => {},
    inputType: 'default',
    onChange: () => {},
    labelMaxLength: 6,
    labelStyle: {},
  }

  onChange = (value) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(value);
    }
  }

  isRequireRender = isRequire => (
    isRequire ?
      <View style={styles.isRequire}>
        <Text style={styles.isRequireText}>*</Text>
      </View>
    :
    <View style={styles.isRequire} />
  )

  closeKeyboard = () => {
    if (this.inputRef) {
      this.inputRef.blur();
    }
  }

  renderTitle = label => label.length > this.props.labelMaxLength ? label.substring(0, this.props.labelMaxLength) : label
  render() {
    const {
      isRequire,
      label,
      placeholder,
      editable,
      value,
      onPress,
      rightIconView,
      onBlur,
      inputType,
      labelStyle,
      onRightIconPress,
    } = this.props;
    return (<TouchableOpacity
      activeOpacity={onPress ? 0.8 : 1}
      onPress={() => {
        this.closeKeyboard();
        if (onPress) {
          onPress();
        }
      }}
      style={styles.container}
    >
      <View style={styles.titleContainer}>
        { this.isRequireRender(isRequire)}
        <Text style={[styles.title, labelStyle]}>{this.renderTitle(label)}</Text>
      </View>
      <View style={styles.rightContainer}>
        {
          onPress ?
            <View style={styles.rightTextContainer}>
              <Text
                style={[styles.rightText,
                  value ? { color: variables.color_text_base } : { color: variables.color_gray }]}
              >{ value || placeholder}</Text>
            </View>
          :
          <TextInput
            ref={(el) => { this.inputRef = el; }}
            style={styles.input}
            underlineColorAndroid="transparent"
            placeholder={placeholder}
            placeholderTextColor={variables.color_text_placeholder}
            editable={editable}
            onChange={event => this.onChange(event.nativeEvent.text)}
            value={value}
            keyboardType={inputType}
            onBlur={() => onBlur(value)}
            onFocus={() => {
              this.setState({
                closeKeyboard: false,
              });
            }}
          />
        }
        <TouchableOpacity
          onPress={onRightIconPress}
          style={styles.rightIconContainer}
          activeOpacity={onRightIconPress ? 0.8 : 1}
        >
          {rightIconView}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>);
  }
}

SimpleLine.propTypes = {
  isRequire: PropTypes.bool,
  editable: PropTypes.bool,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onPress: PropTypes.func,
  rightIconView: PropTypes.node,
  onBlur: PropTypes.func,
  inputType: PropTypes.string,
  onChange: PropTypes.func,
  labelMaxLength: PropTypes.number,
  labelStyle: PropTypes.object,
  onRightIconPress: PropTypes.func,
};

export default SimpleLine;
