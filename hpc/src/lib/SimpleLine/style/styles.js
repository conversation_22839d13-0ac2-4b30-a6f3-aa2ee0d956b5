import {
  StyleSheet,
  Dimensions,
} from 'react-native';
import variables from '../../themes/defaultStyles';

const { height, width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    width,
    height: variables.line_height_default,
    backgroundColor: variables.fill_base,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  rightContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  input: {
    textAlign: 'right',
    fontSize: variables.font_size_base,
    width: width * (140 / 350),
    color: variables.color_text_base,
  },
  rightTextContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  rightText: {
    fontSize: variables.font_size_base,
  },
  isRequire: {
    width: variables.h_spacing_md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
  },
  title: {
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  isRequireText: {
    color: variables.color_gray,
  },
  rightIconContainer: {
    width: 14,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: variables.h_spacing_md,
    paddingRight: variables.h_spacing_md,
  },
});

export default styles;
