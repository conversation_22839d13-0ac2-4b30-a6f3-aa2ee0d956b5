import { PermissionsAndroid } from 'react-native'
import {companyName} from '@project-config'

export const CAMPERMISSIONS_CAMERAERA = async () => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.CAMERA,
      {
        title: '申请摄像头权限',
        message: `${companyName}需要调用你的摄像头，用来拍照上传`,
        buttonNeutral: '下次询问',
        buttonNegative: '不允许',
        buttonPositive: '允许',
      },
    )
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('现在你获得摄像头权限了')
      return true
    }
    console.log('用户拒绝了')
    return false
  } catch (err) {
    console.warn(err)
  }
}
export const CAMPERMISSIONS_RECORD_AUDIO = async () => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      {
        title: '申请麦克风权限',
        message: `${companyName}需要调用你的麦克风，用来拍摄视频`,
        buttonNeutral: '下次询问',
        buttonNegative: '不允许',
        buttonPositive: '允许',
      },
    )
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('现在你获得克风权限了')
      return true
    }
    console.log('用户拒绝了')
    return false
  } catch (err) {
    console.warn(err)
  }
}

export const CAMPERMISSIONS_WRITE = async () => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      {
        title: '手机读写权限申请',
        message: `${companyName}需要有读写文件的权限，用来上传和下载附件`,
        buttonNeutral: '下次询问',
        buttonNegative: '不允许',
        buttonPositive: '允许',
      },
    )
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('现在你获得写入')
      return true
    }
    console.log('用户拒绝了')
    return false
  } catch (err) {
    console.warn(err)
  }
}
export const CAMPERMISSIONS_LOCATION = async () => {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: '获取定位',
        message: `${companyName}需要获取你的定位`,
        buttonNeutral: '下次询问',
        buttonNegative: '不允许',
        buttonPositive: '允许',
      },
    )
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('成功获取位置权限')
      return true
    }
    console.log('用户拒绝了')
    return false
  } catch (err) {
    console.warn(err)
  }
}
