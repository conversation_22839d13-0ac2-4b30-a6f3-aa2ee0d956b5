import { getItem, setItem, removeItem } from './asyncStore'
import * as KEY from './constants'
import { getWithCache } from '../../request'

const CACHE_TIME = 5000
const MAX = 10
let globalToken = null
export const serializeKey = (path, options) => path + JSON.stringify(options)

export const clearCache = (key) => {
  removeItem(key)
}

const getCacheInterval = key => new Promise((resovle) => {
  let count = 0
  const timer = setInterval(() => {
    const cached = getItem(key)
    count += 1
    if (count > MAX) {
      resovle(undefined)
    }
    if (cached) {
      clearInterval(timer)
      resovle(cached)
    }
  }, 200)
})

export const setCache = (path, options, result) => {
  let key = serializeKey(path, options)
  key = key.replace(new RegExp('_', 'g'), '-------')
  setItem(key, result, 0, CACHE_TIME)
}

export const getCache = async (path, options) => {
  let key = serializeKey(path, options)
  key = key.replace(new RegExp('-------', 'g'), '_')
  let cached = await getItem(key)
  if (cached === null) {
    cached = getCacheInterval(key)
  }
  if (cached === undefined) {
    setCache(path, options, null)
  }
  return cached
}

// 超时时间为12小时（1000*3600*12）
export const setTheAppContent = (navData, appId) => {
  setItem(KEY.THE_APP_CONTENT_KEY, navData, appId, 1000 * 10 * 60)
}
export const getTheAppContent = async (appId) => {
  try {
    const navData = await getItem(KEY.THE_APP_CONTENT_KEY, appId)
    return navData
  } catch (e) {
    console.log(e)
    return null
  }
}

// 超时时间为12小时（1000*3600*12）
export const setTheAppSettingNav = (navSetting, appId) => {
  setItem(KEY.THE_APP_SETTING_NAV_KEY, navSetting, appId)
}
export const getTheAppSettingNav = async (appId) => {
  try {
    const navSetting = await getItem(KEY.THE_APP_SETTING_NAV_KEY, appId)
    return navSetting
  } catch (e) {
    console.log(e)
    return null
  }
}

export const setUserInfo = (token, user) => {
  setItem(KEY.TOKEN_STORAGE_KEY, token)
  setItem(KEY.GLOBAL_USER_STORAGE_KEY, user)
}

export const getUserinfo = async () => {
  const user = await getItem(KEY.GLOBAL_USER_STORAGE_KEY)
  return user
}

export const updateUserInfo = async (user) => {
  const originUser = await getUserinfo()
  console.log('asdhlkajdlajsdlkjalsda', user, originUser)

  setItem(KEY.GLOBAL_USER_STORAGE_KEY, { ...originUser, ...user })
}

export const setUserSettings = (settings) => {
  setItem(KEY.GEN_USER_SETTINGS_KEY, settings)
}

export const getUserSettings = async () => {
  const settings = await getItem(KEY.GEN_USER_SETTINGS_KEY)
  return settings
}

export const updateUserSettings = async (settings) => {
  const originSettings = await getUserSettings()
  setItem(KEY.GEN_USER_SETTINGS_KEY, { ...originSettings, ...settings })
}

export const updateNewUser = async (user) => {
  const originUser = await getUserinfo()
  return { ...originUser, ...user }
}

export const getUserId = async () => {
  const userInfo = await getUserinfo()
  return userInfo.userId
}

export const clearUserInfo = () => removeItem(KEY.GLOBAL_USER_STORAGE_KEY)

export const setToken = (token) => {
  globalToken = token
  setItem(KEY.TOKEN_STORAGE_KEY, token)
}
export const getToken = async () => {
  if (globalToken) {
    return globalToken
  }
  const token = await getItem(KEY.TOKEN_STORAGE_KEY)
  return token === null ? '' : token
}
export const clearToken = () => {
  globalToken = null
  removeItem(KEY.TOKEN_STORAGE_KEY)
}

export const setLastApp = appId => setItem(KEY.LAST_APPID_STORAGE_KEY, appId)
export const getLastApp = async () => {
  const appId = await getItem(KEY.LAST_APPID_STORAGE_KEY)
  return appId === null ? 0 : appId
}
export const clearLastApp = () => removeItem(KEY.LAST_APPID_STORAGE_KEY)

export const getAddress = async () => {
  try {
    const address = await getItem(KEY.GLOBAL_ADDRESS_KEY)
    return address
  } catch (e) {
    console.log(e)
    return null
  }
}
export const setAddress = address => setItem(KEY.GLOBAL_ADDRESS_KEY, address)



export const setCurrentAppId = async (appId) => {
  await setItem(KEY.CURRENT_APPID_KEY, appId)
}

export const setCurrentAppName = async (appname) => {
  await setItem(KEY.CURRENT_APP_NAME, appname)
}

export const getCurrentAppId = async () => {
  try {
    const appId = await getItem(KEY.CURRENT_APPID_KEY)
    return appId
  } catch (e) {
    console.log(e)
    return null
  }
}

export const getCurrentAppName = async () => {
  try {
    const appName = await getItem(KEY.CURRENT_APP_NAME)
    return appName
  } catch (e) {
    console.log(e)
    return null
  }
}

export const getAppUserId = async () => {
  const appId = await getCurrentAppId()
  const res = await getWithCache(`/apps/${appId}/users/currAppUserId`)()
  const appUserId = res.data.appUserId
  setItem(KEY.CURRAPP_USERID_KEY, appUserId)
  return appUserId
}

export const setView = async (appId, formId, tableKey, viewID) => {
  const userId = await getUserId()
  setItem(`${appId}-${formId}-${userId}-${tableKey}-viewID`, viewID)
}
export const getSelectView = async (appId, formId, tableKey) => {
  const userId = await getUserId()
  return await getItem(`${appId}-${formId}-${userId}-${tableKey}-viewID`)
}

export const setCurrPage = async (appId, formId, tableKey, currPage, op) => {
  const userId = await getUserId()
  setItem(`${appId}-${formId}-${userId}-${tableKey}-currpage-${op}`, currPage)
}
export const getCurrPage = async (appId, formId, tableKey, op) => {
  const userId = await getUserId()
  const currpage = await getItem(`${appId}-${formId}-${userId}-${tableKey}-currpage-${op}`)
  return currpage && Number(currpage)
}

export const setPageSize = async (appId, formId, tableKey, pagesize, op) => {
  const userId = await getUserId()
  setItem(`${appId}-${formId}-${userId}-${tableKey}-pagesize-${op}`, pagesize)
}
export const getPageSize = async (appId, formId, tableKey, op) => {
  const userId = await getUserId()
  const pagesize = await getItem(`${appId}-${formId}-${userId}-${tableKey}-pagesize-${op}`)
  return pagesize && Number(pagesize)
}

export const setSelectIds = async (appId, formId, tableKey, ids, op) => {
  const userId = await getUserId()
  setItem(`${appId}-${formId}-${userId}-${tableKey}-selectIds-${op}`, ids)
}
export const getSelectIds = async (appId, formId, tableKey, op) => {
  const userId = await getUserId()
  return await getItem(`${appId}-${formId}-${userId}-${tableKey}-selectIds-${op}`)
}

