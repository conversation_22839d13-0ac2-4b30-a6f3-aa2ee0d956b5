import { AsyncStorage as storage } from 'react-native'

export const setItem = async (key, value) => {
  if (!key || !value) return
  // storage.save({ key, id, data: value, expires })
  if (typeof value === 'object') {
    await storage.setItem(key, JSON.stringify(value))
  } else {
    await storage.setItem(key, value.toString())
  }
}
export const getItem = async (key) => {
  if (!key) return null
  const value = await storage.getItem(key)
  try {
    const object = JSON.parse(value)

    if (typeof object === 'number') {
      return value
    }
    return object
  } catch (e) {
    return `${value}`
  }
}

export const removeItem = (key) => {
  if (!key) return
  storage.removeItem(key)
}

export const removeItemWait = async (key) => {
  if (!key) return
  await storage.removeItem(key)
}
