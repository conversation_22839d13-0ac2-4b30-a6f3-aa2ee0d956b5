/**
 * 表单组件数据流测试工具
 * 用于验证表单组件的数据流是否正常工作
 */

import { deepEqual } from '../addons/FormComponent/common';

// 模拟表单数据
const mockFormData = {
  addressField: {
    country: '中国',
    province: '北京市',
    city: '北京市',
    area: '朝阳区',
    room: '测试地址',
    place: '',
    lng: '',
    lat: '',
  },
  pictureField: ['file1', 'file2'],
  videoField: ['video1'],
  textField: '测试文本',
};

// 模拟 changeValue 调用记录
let changeValueCalls = [];

// 模拟 changeValue 函数
const mockChangeValue = (updates) => {
  changeValueCalls.push({
    timestamp: Date.now(),
    updates: JSON.parse(JSON.stringify(updates)),
  });
  console.log('changeValue called:', updates);
};

// 重置测试状态
export const resetTest = () => {
  changeValueCalls = [];
};

// 获取 changeValue 调用记录
export const getChangeValueCalls = () => changeValueCalls;

// 测试数据流是否正常
export const testDataFlow = () => {
  console.log('开始测试表单组件数据流...');
  
  resetTest();
  
  // 测试 1: 模拟地址组件更新
  console.log('\n测试 1: 地址组件更新');
  mockChangeValue({
    addressField: {
      defaultValue: {
        ...mockFormData.addressField,
        room: '更新后的地址',
      },
    },
  });
  
  // 测试 2: 模拟图片组件更新
  console.log('\n测试 2: 图片组件更新');
  mockChangeValue({
    pictureField: {
      defaultValue: ['file1', 'file2', 'file3'],
    },
  });
  
  // 测试 3: 模拟视频组件更新
  console.log('\n测试 3: 视频组件更新');
  mockChangeValue({
    videoField: {
      defaultValue: ['video1', 'video2'],
    },
  });
  
  // 分析调用记录
  const calls = getChangeValueCalls();
  console.log('\n调用记录分析:');
  console.log(`总调用次数: ${calls.length}`);
  
  // 检查是否有重复调用
  const duplicateCalls = [];
  for (let i = 0; i < calls.length - 1; i++) {
    for (let j = i + 1; j < calls.length; j++) {
      if (deepEqual(calls[i].updates, calls[j].updates)) {
        duplicateCalls.push({ index1: i, index2: j });
      }
    }
  }
  
  if (duplicateCalls.length > 0) {
    console.warn('发现重复调用:', duplicateCalls);
  } else {
    console.log('✅ 没有发现重复调用');
  }
  
  // 检查调用间隔
  const intervals = [];
  for (let i = 1; i < calls.length; i++) {
    intervals.push(calls[i].timestamp - calls[i - 1].timestamp);
  }
  
  const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
  console.log(`平均调用间隔: ${avgInterval.toFixed(2)}ms`);
  
  if (avgInterval < 50) {
    console.warn('⚠️ 调用间隔过短，可能存在频繁更新问题');
  } else {
    console.log('✅ 调用间隔正常');
  }
  
  return {
    totalCalls: calls.length,
    duplicateCalls: duplicateCalls.length,
    avgInterval,
    isHealthy: duplicateCalls.length === 0 && avgInterval >= 50,
  };
};

// 测试深度比较函数
export const testDeepEqual = () => {
  console.log('\n测试深度比较函数...');
  
  const obj1 = { a: 1, b: { c: 2 } };
  const obj2 = { a: 1, b: { c: 2 } };
  const obj3 = { a: 1, b: { c: 3 } };
  
  console.log('obj1 === obj2:', deepEqual(obj1, obj2)); // 应该是 true
  console.log('obj1 === obj3:', deepEqual(obj1, obj3)); // 应该是 false
  
  const arr1 = [1, 2, 3];
  const arr2 = [1, 2, 3];
  const arr3 = [1, 2, 4];
  
  console.log('arr1 === arr2:', deepEqual(arr1, arr2)); // 应该是 true
  console.log('arr1 === arr3:', deepEqual(arr1, arr3)); // 应该是 false
  
  return {
    objectComparison: deepEqual(obj1, obj2),
    arrayComparison: deepEqual(arr1, arr2),
    differentObjectComparison: deepEqual(obj1, obj3),
    differentArrayComparison: deepEqual(arr1, arr3),
  };
};

// 运行所有测试
export const runAllTests = () => {
  console.log('=== 表单组件数据流测试 ===');
  
  const deepEqualTest = testDeepEqual();
  const dataFlowTest = testDataFlow();
  
  console.log('\n=== 测试结果汇总 ===');
  console.log('深度比较测试:', deepEqualTest);
  console.log('数据流测试:', dataFlowTest);
  
  const overallHealth = deepEqualTest.objectComparison && 
                       deepEqualTest.arrayComparison && 
                       !deepEqualTest.differentObjectComparison && 
                       !deepEqualTest.differentArrayComparison && 
                       dataFlowTest.isHealthy;
  
  console.log(`\n总体健康状态: ${overallHealth ? '✅ 健康' : '❌ 存在问题'}`);
  
  return {
    deepEqualTest,
    dataFlowTest,
    overallHealth,
  };
};

export default {
  testDataFlow,
  testDeepEqual,
  runAllTests,
  resetTest,
  getChangeValueCalls,
};
