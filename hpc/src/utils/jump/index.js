import { NavigationActions, StackActions, SwitchActions } from 'react-navigation'
import { buttionIds } from '../../components/DataTable/Store'
import NavigationService, { getNavigatorInstance, getCurrentRoute } from '../../NavigationService'

let lastBottomRouterNavs = []
export const GO_APPS_BOTTOM_TAB = (appId, appName, navs = []) => {
  const navInstance = getNavigatorInstance()
  const currentRoute = NavigationService.getCurrentRoute(navInstance.state.nav)
  //  当前路由为热更新界面时，阻拦后续跳转事件
  if (navInstance && currentRoute.routeName === 'hotFixModal') {
    return
  }
  let currentMainTabKey = ''
  // 根据配置的导航个数决定跳转到对应的tab页
  if (navs.length === 1) {
    currentMainTabKey = 'bottomTabOne'
  } else if (navs.length === 2) {
    currentMainTabKey = 'bottomTabTwo'
  } else if (navs.length === 3) {
    currentMainTabKey = 'bottomTabThree'
  } else if (navs.length === 4) {
    currentMainTabKey = 'bottomTabFour'
  } else if (navs.length === 5) {
    currentMainTabKey = 'bottomTabFive'
  }
  // 获取当前的tab主导航，然后主导航下的每个路由都注入参数
  const curentMainTabChildrenRoutes = navInstance.state.nav.routes.find((item) => item.key === currentMainTabKey).routes
  curentMainTabChildrenRoutes.map((item, index) => {
    navInstance.dispatch(NavigationActions.setParams({
      params: {
        appId, appName, currentNavData: navs[index],
      },
      key: item.key,
    }))
  })
  if (navs.length === lastBottomRouterNavs.length) {
    // 获取当前为主导航的tab，并重置主导航的stack
    const defaultNavigatiorIndex = navs.findIndex((item) => item.refId === '1111')
    const defaultNavigatior = curentMainTabChildrenRoutes[defaultNavigatiorIndex]
    const resetHomeAction = StackActions.reset({
      index: 0,
      actions: [NavigationActions.navigate({
        routeName: 'home',
        params: { appId, formId: defaultNavigatior.refId, appName, isCustomMainNavHeader: true },
      })],
      key: defaultNavigatior.key,
    })
    navInstance.dispatch(resetHomeAction)
    // 当跳转后的导航始终激活第一个tab的路由，当第一个tab路由不是主导航时，需要重置当前激活的路由stack
    if (curentMainTabChildrenRoutes[0].key !== defaultNavigatior.key) {
      // const switchAction = SwitchActions.jumpTo({
      //   routeName: curentMainTabChildrenRoutes[0].key,
      //   key: currentMainTabKey,
      // })
      // navInstance.dispatch(switchAction)
      // const resetAction = StackActions.reset({
      //   index: 0,
      //   actions: [StackActions.push({ routeName: 'appForm',
      //     params: { appId, formId: navs[0].refId, appName, isCustomMainNavHeader: true },
      //   })],
      //   key: curentMainTabChildrenRoutes[0].key,
      // })
      const resetAction = NavigationActions.navigate({
        routeName: curentMainTabChildrenRoutes[0].key,
        params: { appId, appName },
        action: StackActions.reset({
          index: 0,
          actions: [NavigationActions.navigate({
            routeName: 'appForm',
            params: { appId, formId: navs[0].refId, appName, isCustomMainNavHeader: true },
          })],
          key: curentMainTabChildrenRoutes[0].key,
        }),
      })
      navInstance.dispatch(resetAction)
    }
    lastBottomRouterNavs = navs
    return
  }
  lastBottomRouterNavs = navs
  // 根据tab导航的第一项是否为默认主导航判断执行不同的aciton
  if (navs[0].refId === '1111') {
    const action = NavigationActions.navigate({
      routeName: currentMainTabKey,
      params: { appId, appName },
      action: StackActions.reset({
        index: 0,
        actions: [NavigationActions.navigate({
          routeName: 'home',
          params: { appId, appName, isCustomMainNavHeader: true },
        })],
      }),
    })
    navInstance.dispatch(action)
  } else {
    const action = NavigationActions.navigate({
      routeName: currentMainTabKey,
      params: { appId, appName },
      action: NavigationActions.navigate({
        routeName: curentMainTabChildrenRoutes[0].key,
        params: { appId, appName },
        action: StackActions.reset({
          index: 0,
          actions: [NavigationActions.navigate({
            routeName: 'appForm',
            params: { appId, formId: navs[0].refId, appName, isCustomMainNavHeader: true },
          })],
          key: curentMainTabChildrenRoutes[0].key,
        }),
      }),
    })
    navInstance.dispatch(action)
  }
}

/*
export const GO_FORM_DESIGN = (appId, formId) =>
  push(`/apps/editApp/${appId}/form/${formId}/formDesign`)

export const GO_APP_SETTING = appId => push(`/apps/editApp/${appId}/appSetting`)

export const GO_APP_DIR = (appId, dirId) => push(`/apps/editApp/${appId}/dirs/${dirId}`)

*/
// not use start
// export const GO_RUNTIME = (appId, appName, dirId) => Actions.theApp({ appId, appName, dirId })
export const GO_TEMPLATE_DETAIL = (templateId) => NavigationService.push('appStoreDetail', { templateId })
// not use end

export const GO_CONVERT = (callback, appId, formId, dataId, fieldId, fromFormId) => NavigationService.push('appForm', {
  callback, appId, formId, srcDataId: dataId, fieldId, fromFormId, operator: buttionIds.convert,
})

// GO_CREATER 和 GO_EDIT 中的 callback是为了强制刷新列表页，以显示这两个操作导致的数据变化
export const GO_CREATE = (callback, appId, formId, formType = 0, formName, query, InvoServiceId) => {
  NavigationService.push('appForm', {
    callback, appId, formId, formType, formName, operator: buttionIds.add, query, InvoServiceId,
  })
}
export const GO_EDIT = (callback, appId, formId, formName, dataId, isView) => NavigationService.push('appForm', {
  callback, appId, formId, formName, dataId, isView, operator: buttionIds.edit,
})
export const GO_EDIT2 = (callback, appId, formId, formName, dataId, isView) => NavigationService.push('appForm', {
  callback, appId, formId, formName, dataId, isView, operator: buttionIds.edit,
})

export const GO_COPY = (callback, appId, formId, dataId, fieldId) => NavigationService.push('appForm', {
  callback, appId, formId, dataId, fieldId, operator: buttionIds.copyAdd,
})

export const GO_REPORT = (callback, appId, formId, formType = 1, formName) => NavigationService.push('appReport', {
  callback, appId, formId, formType, formName, operator: buttionIds.add,
})

export const GO_REPORTDETAIL = (props) => NavigationService.push('reportDetail', props)

export const GO_LIST = (appId, formId, name, operations = 3) => NavigationService.push('appList', {
  appId, formId, formName: name, operations,
})
export const GO_REF_LIST = (params) => NavigationService.push('refList', params)

// 需指定对应key，因为现在有多个AppNavigationStack，如不指定会进入 bottomtab的堆栈里去
export const GO_APPS_HOME = (appId, appName) => {
  const navInstance = getNavigatorInstance()
  const currentRoute = getCurrentRoute(navInstance.state.nav)
  //  当前路由为热更新界面时，阻拦后续跳转事件
  if (navInstance && currentRoute.routeName === 'hotFixModal') {
    return
  }
  const action = NavigationActions.navigate({
    routeName: 'app',
    action: NavigationActions.navigate({
      routeName: 'home',
      params: { appId, appName },
    }),
  })
  navInstance.dispatch(action)
  // NavigationService.navigate('home', { appId, appName }, null, 'app')
}
export const GO_FILEMANAGER = (callback) => NavigationService.push('fileManager', { onSelect: callback })

// export const GO_LOGIN = (failFlag=false) => Actions.auth({failFlag:true});
export const GO_LOGIN = () => {
  NavigationService.goBackToLogin()
}
export const GO_SIGNUP = (isFindPassword) => NavigationService.push('signUp', { isFindPassword })
export const GO_SIGNUPR = (isFindPassword) =>  NavigationService.push('SignUpRegister', { isFindPassword })
export const GO_INNEROFFICIALWEB = (isFindPassword) => NavigationService.push('innerOfficialWeb', { isFindPassword })
export const GO_COUNTRYCODE = (props) => NavigationService.push('countryCode', props)
export const GO_VERIFYCODE = (props) => NavigationService.push('verifyCode', props)
export const GO_SIGNUPVERIFYCODE = (props) => NavigationService.push('sigupVerifyCode', props)
export const GO_NETSOLUTION = () => NavigationService.push('netSolution')

export const GO_AIGONGZUOWEB = () => NavigationService.push('officialWeb')

export const GO_APPFORM = (appId, formId, formData) => NavigationService.push('appForm', { appId, formId, formData })
export const GO_FULLEDITORVIEW = (props = {}) => NavigationService.push('fullEditorView', props)
export const GO_REPORTLIST = (props = {}) => NavigationService.push('reportList', props)
export const GO_FULLREPORTVIEW = (props) => NavigationService.push('fullReportView', props)
export const GO_RUNTIMENAV = (appId) => NavigationService.push('appNav', { appId })
export const GO_MODAL = (props) => { NavigationService.push('rootRouterModal', props) }
export const GO_AUTH_MODAL = (props) => { NavigationService.navigate('hotFixModal', props) }

export const GOBACK = () => NavigationService.back()
export const GO_FILEDETAIL = (props) => NavigationService.push('fileDetail', { ...props })
export const GO_FILELIST = (props = {}) => NavigationService.push('fileList', { ...props })
export const GO_PICTURELIST = (props = {}) => NavigationService.push('pictureList', { ...props })
export const GO_ROOTMODAL = (props = {}) => NavigationService.push('rootRouterModal', props)
export const GO_USERSETTING = (props = {}) => NavigationService.push('userSetting', { ...props })
export const GO_ABOUTUS = () => NavigationService.push('aboutUs')
export const GO_REFPROPERTYMODAL = (props = {}) => NavigationService.push('refPropertyModal', props)

export const GO_WIFILIST = (appId) => NavigationService.push('wifiList', { appId })
export const GO_ATTENDANCERECORDLIST = (appId, formId, display) => NavigationService.push('attendanceRecordList', { appId, formId, display })
export const GO_ATTENDANCECONFIG = (appId) => NavigationService.push('attendanceConfig', { appId })
export const GO_MESSAGEBOX = (currentAppId) => NavigationService.push('messageBox', { currentAppId })
export const GO_WORKFLOWBOX = (currentAppId) => NavigationService.push('workflowBox', { currentAppId })
