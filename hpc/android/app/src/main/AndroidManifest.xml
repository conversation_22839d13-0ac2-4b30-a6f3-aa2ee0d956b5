<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.huapucloud_mobile"
    android:versionCode="1"
    android:versionName="1.0">
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"></uses-permission>
<!-- 这个权限用于访问GPS定位-->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"></uses-permission>
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.CAMERA"/>

<uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-sdk
        android:targetSdkVersion="22"
        tools:overrideLibrary="org.lovebing.reactnative.baidumap"
        />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/appicon"
      android:roundIcon="@mipmap/appicon"
      android:usesCleartextTraffic="true"
      android:allowBackup="true"
      android:theme="@style/AppTheme">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
        android:windowSoftInputMode="adjustResize">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
      <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
        <activity
            android:name="com.huapucloud_mobile.chat.ChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            >
        </activity>
        <activity
            android:name="com.huapucloud_mobile.chat.OfflineMessageActicity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            >
        </activity>
        <activity
            android:name="com.huapucloud_mobile.chat.ImageViewLookActivity"
            android:screenOrientation="portrait"
            >
        </activity>


        <service
            android:name="com.moor.imkf.tcpservice.service.IMService"
            >
        </service>

        <receiver
            android:name="com.moor.imkf.receiver.NetWorkReceiver"
            >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>


        <!--????????????????-->
        <receiver
            android:name="com.huapucloud_mobile.receiver.NewMsgReceiver"
            android:enabled="true"
            >
            <intent-filter android:priority="2147483647" >
                <action android:name="action" />
            </intent-filter>
        </receiver>
         <receiver android:name="com.huapucloud_mobile.alipush.MyMessageReceiver">
          <intent-filter>
              <action android:name="com.alibaba.push2.action.NOTIFICATION_OPENED" />
          </intent-filter>
          <intent-filter>
              <action android:name="com.alibaba.push2.action.NOTIFICATION_REMOVED" />
          </intent-filter>
          <intent-filter>
              <action android:name="com.taobao.accs.intent.action.COMMAND" />
          </intent-filter>
          <intent-filter>
              <action android:name="com.taobao.taobao.intent.action.COMMAND" />
          </intent-filter>
          <intent-filter>
              <action android:name="com.alibaba.sdk.android.push.RECEIVE" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.USER_PRESENT" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.BOOT_COMPLETED" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.PACKAGE_REMOVED" />
              <data android:scheme="package" />
          </intent-filter>
      </receiver>

          <meta-data android:name="com.baidu.lbsapi.API_KEY"
            android:value="HGZKsgZeDgNxFcEhKivPdcumNmvAqkuR"/>
        <meta-data android:name="com.alibaba.app.appkey" android:value="25234669"/> <!-- 请填写你自己的- appKey -->
        <meta-data android:name="com.alibaba.app.appsecret" android:value="bc7ce9559cfc2ea1f14fff3d2595106c"/> <!-- 请填写你自己的appSecret -->
        <meta-data
            android:name="com.google.firebase.ml.vision.DEPENDENCIES"
            android:value="ocr" />
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:grantUriPermissions="true"
            tools:replace="android:authorities"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
    </application>

</manifest>
