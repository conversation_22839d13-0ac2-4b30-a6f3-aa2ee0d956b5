package com.huapucloud_mobile;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.support.multidex.MultiDexApplication;

import com.alibaba.sdk.android.push.register.HuaWeiRegister;
import com.alibaba.sdk.android.push.register.MiPushRegister;
import com.facebook.react.ReactApplication;
import com.reactlibrary.RNWifiPackage;
import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;
import com.reactnativecommunity.webview.RNCWebViewPackage;
import com.reactnativecommunity.geolocation.GeolocationPackage;
import com.pusherman.networkinfo.RNNetworkInfoPackage;
import com.devstepbcn.wifi.AndroidWifiPackage;
import com.brentvatne.react.ReactVideoPackage;
import com.swmansion.reanimated.ReanimatedPackage;
import com.swmansion.gesturehandler.react.RNGestureHandlerPackage;
import com.huapucloud_mobile.badge.BadgePackage;
import com.zmxv.RNSound.RNSoundPackage;
import com.reactnative.ivpusic.imagepicker.PickerPackage;
import com.huapucloud_mobile.alipush.AliPushModule;
import com.huapucloud_mobile.alipush.AliPushPackage;
import com.alibaba.sdk.android.push.CommonCallback;
import com.alibaba.sdk.android.push.noonesdk.PushServiceFactory;
import com.huapucloud_mobile.aliyunoss.aliyunossPackage;

import com.huapucloud_mobile.update.UpdateContext;
import com.huapucloud_mobile.update.UpdatePackage;

import org.lovebing.reactnative.baidumap.BaiduMapPackage;
import com.huapucloud_mobile.openFile.OpenFilePackage;
import org.reactnative.camera.RNCameraPackage;
import com.oblador.vectoricons.VectorIconsPackage;
import org.devio.rn.splashscreen.SplashScreenReactPackage;
import com.github.yamill.orientation.OrientationPackage;
import com.imagepicker.ImagePickerPackage;
import com.rnfs.RNFSPackage;
import com.learnium.RNDeviceInfo.RNDeviceInfo;
import com.rt2zz.reactnativecontacts.ReactNativeContacts;

import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainReactPackage;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.soloader.SoLoader;

import java.util.Arrays;
import java.util.List;

import me.leolin.shortcutbadger.ShortcutBadger;

public class MainApplication extends MultiDexApplication implements ReactApplication {
  private static MainApplication mobileApplication;
  public static boolean isKFSDK = false;
  private final ReactNativeHost mReactNativeHost = new ReactNativeHost(this) {
    @Override
    protected String getJSBundleFile() {

      return UpdateContext.getBundleUrl(MainApplication.this);
    }

    @Override
    public boolean getUseDeveloperSupport() {
      return BuildConfig.DEBUG;
    }

    @Override
    protected List<ReactPackage> getPackages() {
      return Arrays.<ReactPackage>asList(
          new MainReactPackage(),
            new RNWifiPackage(),
            new AsyncStoragePackage(),
            new RNCWebViewPackage(),
            new GeolocationPackage(),
            new RNNetworkInfoPackage(),
            new AndroidWifiPackage(),
            new ReactVideoPackage(),
            new ReanimatedPackage(),
            new RNGestureHandlerPackage(),
            new RNSoundPackage(),
            new PickerPackage(),
            new RNCameraPackage(),
            new VectorIconsPackage(),
            new SplashScreenReactPackage(),
            new OrientationPackage(),
            new ImagePickerPackage(),
            new RNFSPackage(),
            new RNDeviceInfo(),
           new BaiduMapPackage(),
              new aliyunossPackage(),
              new UpdatePackage(),
              new ReactNativeContacts(),
              new IntentReactPackage(),
              new OpenFilePackage(),
              new ApkInstallerPackage(),
              new AliPushPackage(),
              new BadgePackage()
      );
    }

    @Override
    protected String getJSMainModuleName() {
      return "index";
    }
  };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    super.onCreate();
    mobileApplication = this;
    new Thread(new Runnable() {
      @Override
      public void run() {
        com.huapucloud_mobile.utils.FaceConversionUtil.getInstace().getFileText(
                MainApplication.getInstance());
      }
    }).start();
    SoLoader.init(this, /* native exopackage */ false);
    this.initCloudChannel();
    this.initPushService(); // 注册消息补偿推送通道
    ShortcutBadger.removeCount(this.getApplicationContext()); //移出小圆点

  }

  private void initCloudChannel() {
    this.createNotificationChannel();
    PushServiceFactory.init(this);
    PushServiceFactory.getCloudPushService().register(this, new CommonCallback() {
      @Override
      public void onSuccess(String s) {
        WritableMap params = Arguments.createMap();
        params.putBoolean("success", true);
        AliPushModule.sendEvent("onInit", params);
      }
      @Override
      public void onFailed(String s, String s1) {
        WritableMap params = Arguments.createMap();
        params.putBoolean("success", false);
        params.putString("errorMsg", "errorCode:" + s + ". errorMsg:" + s1);
        AliPushModule.sendEvent("onInit", params);
      }
    });
  }
  private void initPushService(){
    // 注册方法会自动判断是否支持小米系统推送，如不支持会跳过注册。
    MiPushRegister.register(this, "2882303761517986315", "5741798644315");
    // 注册方法会自动判断是否支持华为系统推送，如不支持会跳过注册。
    HuaWeiRegister.register(this);
    //GCM/FCM辅助通道注册
    // GcmRegister.register(this, sendId, applicationId); //sendId/applicationId为步骤获得的参数
    // OPPO通道注册
    // OppoRegister.register(applicationContext, appKey, appSecret); // appKey/appSecret在OPPO通道开发者平台获取
  }

  private void createNotificationChannel() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
      // 通知渠道的id
      String id = "com.huapucloud_mobile";
      // 用户可以看到的通知渠道的名字.
      CharSequence name = "华普云通知";
      // 用户可以看到的通知渠道的描述
      String description = "华普云通知给你发送的通知";
      int importance = NotificationManager.IMPORTANCE_HIGH;
      NotificationChannel mChannel = new NotificationChannel(id, name, importance);
      // 配置通知渠道的属性
      mChannel.setDescription(description);
      // 设置通知出现时的闪灯（如果 android 设备支持的话）
      mChannel.enableLights(true);
      mChannel.setLightColor(Color.RED);
      // 设置通知出现时的震动（如果 android 设备支持的话）
      mChannel.enableVibration(true);
      mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
      //最后在notificationmanager中创建该通知渠道
      mNotificationManager.createNotificationChannel(mChannel);
    }
  }
  public static MainApplication getInstance() {
    return mobileApplication;
  }
}
