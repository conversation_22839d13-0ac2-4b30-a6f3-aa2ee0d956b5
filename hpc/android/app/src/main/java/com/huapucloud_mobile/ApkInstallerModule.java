package com.huapucloud_mobile;
import android.util.Log;
import android.widget.Toast;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import java.util.HashMap;
import java.util.Map;
import android.os.Build;
import android.content.Intent;
import android.net.Uri;
import java.io.File;
import android.support.v4.content.FileProvider;
public class ApkInstallerModule extends ReactContextBaseJavaModule {
    private ReactApplicationContext _context = null;
    // private static final String DURATION_SHORT_KEY = "SHORT";
    // private static final String DURATION_LONG_KEY = "LONG";
    public ApkInstallerModule(ReactApplicationContext reactContext) {
        super(reactContext);
        _context = reactContext;
    }
    @Override
    public String getName() {
        return "ApkInstaller";
    }
    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        // constants.put(DURATION_SHORT_KEY, Toast.LENGTH_SHORT);
        // constants.put(DURATION_LONG_KEY, Toast.LENGTH_LONG);
        return constants;
    }
    @ReactMethod
    // public void show(String message, int duration) {
    public void test(String message) {
        Toast.makeText(getReactApplicationContext(), message, Toast.LENGTH_LONG).show();
    }

    @ReactMethod
    private void install(String fileSavePath) {
        File file = new File(fileSavePath);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri data;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {//判断版本大于等于7.0
            // "sven.com.fileprovider.fileprovider"即是在清单文件中配置的authorities
            data = FileProvider.getUriForFile(_context, BuildConfig.APPLICATION_ID + "" + ".provider", file);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);// 给目标应用一个临时授权
        } else {
            data = Uri.fromFile(file);
        }
        intent.setDataAndType(data, "application/vnd.android.package-archive");
        _context.startActivity(intent);
    }
}
