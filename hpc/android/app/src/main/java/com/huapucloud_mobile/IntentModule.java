
package com.huapucloud_mobile;

import com.facebook.react.bridge.ReactContextBaseJavaModule;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.JSApplicationIllegalArgumentException;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.moor.imkf.GetPeersListener;
import com.moor.imkf.IMChatManager;
import com.moor.imkf.InitListener;
import com.moor.imkf.model.entity.Peer;
import com.moor.imkf.utils.NetUtils;
import com.huapucloud_mobile.chat.ChatActivity;
import com.huapucloud_mobile.chat.PeerDialog;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangbin on 2017/7/4.
 */

public class IntentModule extends ReactContextBaseJavaModule {

    private Context mContext;

    public IntentModule(ReactApplicationContext reactContext) {
        super(reactContext);
        mContext = reactContext;
    }

    @Override
    public String getName() {
        return "IntentModule";
    }

    /**
     * 从JS页面跳转到原生activity   同时也可以从JS传递相关数据到原生
     */
    @ReactMethod
    public void startActivityFromJS(){
        try{
            Activity currentActivity = getCurrentActivity();
            if(Build.VERSION.SDK_INT < 23) {
                init();
            }else {
                //6.0
                if(ContextCompat.checkSelfPermission(currentActivity, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
                    //该权限已经有了
                    init();
                }else {
                    //申请该权限
                    ActivityCompat.requestPermissions(currentActivity, new String[]{Manifest.permission.READ_PHONE_STATE}, 0x1111);
                }
            }
        }catch(Exception e){
            throw new JSApplicationIllegalArgumentException(
                    "不能打开Activity : "+e.getMessage());
        }
    }

    private void init() {

        if(!NetUtils.hasDataConnection(mContext)) {
            Toast.makeText(mContext, "当前没有网络连接", Toast.LENGTH_SHORT).show();
            return;
        }

        if (MainApplication.isKFSDK) {
            getPeers();
        } else {
            startKFService();
        }
    }

    private void getPeers() {
        IMChatManager.getInstance().getPeers(new GetPeersListener() {
            @Override
            public void onSuccess(List<Peer> peers) {
                System.out.println("获取技能组成功");
                if (peers.size() > 1) {
                    Activity currentActivity = getCurrentActivity();
                    PeerDialog dialog = new PeerDialog();
                    Bundle b = new Bundle();
                    b.putSerializable("Peers", (Serializable) peers);
                    b.putString("type", "init");
                    dialog.setArguments(b);
                    dialog.show(currentActivity.getFragmentManager(), "");

                } else if (peers.size() == 1) {
                    startChatActivity(peers.get(0).getId());
                } else {
                    startChatActivity("");
                }
            }

            @Override
            public void onFailed() {
                System.out.println("获取技能组失败了");
            }
        });
    }

    private void startKFService() {

        new Thread() {
            @Override
            public void run() {
                IMChatManager.getInstance().setOnInitListener(new InitListener() {
                    @Override
                    public void oninitSuccess() {
                        MainApplication.isKFSDK = true;
//                        loadingDialog.dismiss();
                        getPeers();
                        Log.d("MobileApplication", "sdk初始化成功");

                    }

                    @Override
                    public void onInitFailed() {
                        MainApplication.isKFSDK = false;
//                        loadingDialog.dismiss();
                        Toast.makeText(mContext, "客服初始化失败", Toast.LENGTH_SHORT).show();
                        Log.d("MobileApplication", "sdk初始化失败, 请填写正确的accessid");
                    }
                });

                //初始化IMSdk,填入相关参数
                IMChatManager.getInstance().init(MainApplication.getInstance(), "action", "498b06e0-5c95-11e7-947f-2d9bc05894c7", "8001@hongtu", "8001@hongtu");

            }
        }.start();

    }

    private void startChatActivity(String peerId) {
        Intent chatIntent = new Intent(mContext, ChatActivity.class);
        chatIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        chatIntent.putExtra("PeerId", peerId);
        mContext.startActivity(chatIntent);
    }

}
