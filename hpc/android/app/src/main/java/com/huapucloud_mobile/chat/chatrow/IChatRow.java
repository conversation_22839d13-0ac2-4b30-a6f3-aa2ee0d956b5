package com.huapucloud_mobile.chat.chatrow;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.huapucloud_mobile.chat.holder.BaseHolder;
import com.moor.imkf.model.entity.FromToMessage;

/**
 * Created by long<PERSON> on 2016/3/9.
 */
public interface IChatRow {


    View buildChatView(LayoutInflater inflater, View convertView);


    void buildChattingBaseData(Context context, BaseHolder baseHolder, FromToMessage detail, int position);


    int getChatViewType();
}
