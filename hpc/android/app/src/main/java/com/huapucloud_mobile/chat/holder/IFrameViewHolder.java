package com.huapucloud_mobile.chat.holder;

import android.view.View;
import android.webkit.WebView;

import com.huapucloud_mobile.R;

/**
 * Created by <PERSON><PERSON> on 2016/3/10.
 */
public class IFrameViewHolder extends BaseHolder {

    private WebView mWebView;

    public IFrameViewHolder(int type) {
        super(type);
    }

    public BaseHolder initBaseHolder(View baseView, boolean isReceive) {
        super.initBaseHolder(baseView);

        //通过baseview找到对应组件
        mWebView = (WebView) baseView.findViewById(R.id.chat_webview);
        if(isReceive) {
            type = 10;
            return this;
        }
        return this;
    }

    public WebView getWebView() {
        if(mWebView == null) {
            mWebView = (WebView) getBaseView().findViewById(R.id.chat_webview);
        }
        return mWebView;
    }
}
