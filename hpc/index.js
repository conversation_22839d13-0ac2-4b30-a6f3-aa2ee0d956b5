/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 * @flow
 */
import {
  AppRegistry,
} from 'react-native'
import 'react-native-get-random-values'
import Root from './src'

// const config = new Configuration('d1dec3ca876de5436b5fbd1552b9ea02')
// config.appVersion = require('./package.json').version
// const bugsnag = new Client(config)

// bugsnag.notify(new Error("Test iOS Error"));
AppRegistry.registerComponent('NAGZ_mobile', () => Root)
