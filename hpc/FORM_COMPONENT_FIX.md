# 表单组件数据流修复方案

## 问题分析

### 根本原因
项目中大量使用了已废弃的 `componentWillReceiveProps` 生命周期方法，在 React 17+ 中会导致不可预测的行为，特别是：

1. **多次调用问题**：在严格模式下可能被多次调用
2. **无限循环**：组件状态更新触发其他组件的 props 变化，形成循环
3. **数据重置**：表单项之间的数据相互重置

### 影响的组件
- AddressLocation
- Attachment/Picture
- Reference
- SubForm
- ComputedValue
- RefProperty
- 以及其他使用 `componentWillReceiveProps` 的组件

## 修复方案

### 1. 生命周期方法升级

将 `componentWillReceiveProps` 替换为 `componentDidUpdate`：

```javascript
// 修复前
componentWillReceiveProps = ({ defaultValue }) => {
  if (this.props.defaultValue !== defaultValue) {
    this.setState({ value: defaultValue });
  }
}

// 修复后
componentDidUpdate(prevProps) {
  const { defaultValue } = this.props;
  if (prevProps.defaultValue !== defaultValue) {
    this.setState({ value: defaultValue });
  }
}
```

### 2. 深度比较优化

使用深度比较避免不必要的更新：

```javascript
import { deepEqual } from '../common';

componentDidUpdate(prevProps) {
  const { defaultValue } = this.props;
  if (!deepEqual(prevProps.defaultValue, defaultValue)) {
    this.setState({ value: defaultValue });
  }
}
```

### 3. 防抖机制

为 `changeValue` 函数添加防抖机制：

```javascript
import { createDebouncedChangeValue } from '../common';

componentWillMount() {
  this.debouncedChangeValue = createDebouncedChangeValue(this.props.changeValue, 150);
}

updateValue = (newValue) => {
  this.debouncedChangeValue({
    [this.props.fieldId]: {
      defaultValue: newValue,
    },
  });
}
```

## 已修复的组件

### 1. AddressLocation 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 添加深度比较避免不必要更新
- ✅ 使用防抖的 `changeValue` 函数
- ✅ 优化 room 字段更新逻辑

### 2. Attachment 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 添加条件检查避免无效更新

### 3. Reference 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 优化显示值和验证更新逻辑

### 4. SubForm 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 优化关系变更处理逻辑

### 5. ComputedValue 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 优化计算值更新逻辑

### 6. RefProperty 组件
- ✅ 替换 `componentWillReceiveProps` 为 `componentDidUpdate`
- ✅ 优化数据加载和显示值更新

## 新增工具函数

### 1. createDebouncedChangeValue
防抖的 `changeValue` 函数，避免频繁的状态更新：

```javascript
export function createDebouncedChangeValue(changeValue, delay = 100) {
  let timeoutId = null;
  let pendingUpdates = {};
  
  return (updates) => {
    Object.assign(pendingUpdates, updates);
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      if (changeValue && Object.keys(pendingUpdates).length > 0) {
        changeValue(pendingUpdates);
        pendingUpdates = {};
      }
      timeoutId = null;
    }, delay);
  };
}
```

### 2. deepEqual
深度比较函数，避免不必要的更新：

```javascript
export function deepEqual(obj1, obj2) {
  if (obj1 === obj2) return true;
  if (obj1 == null || obj2 == null) return obj1 === obj2;
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }
  
  return true;
}
```

## 测试验证

使用 `src/utils/formComponentTest.js` 进行测试：

```javascript
import { runAllTests } from './src/utils/formComponentTest';

// 运行所有测试
const testResults = runAllTests();
console.log('测试结果:', testResults);
```

## 预期效果

修复后应该解决以下问题：

1. ✅ 表单项之间不再相互重置数据
2. ✅ 减少不必要的组件重新渲染
3. ✅ 避免无限循环更新
4. ✅ 提升表单性能和稳定性

## 注意事项

1. **测试充分**：修复后需要充分测试各种表单场景
2. **向后兼容**：确保修复不影响现有功能
3. **性能监控**：关注修复后的性能表现
4. **逐步部署**：建议分批部署，观察效果

## 后续优化建议

1. **全面升级**：将所有使用 `componentWillReceiveProps` 的组件都进行升级
2. **React Hooks**：考虑将类组件迁移到函数组件 + Hooks
3. **状态管理优化**：优化 Redux 状态管理，减少不必要的状态更新
4. **性能监控**：添加性能监控，及时发现数据流问题
