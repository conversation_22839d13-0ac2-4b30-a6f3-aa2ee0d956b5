{"name": "NHPC_mobile", "version": "1.1.53", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "android": "node node_modules/react-native/local-cli/cli.js run-android", "clearstart": "node node_modules/react-native/local-cli/cli.js start --reset-cache ", "build": "cd android && ./gradlew assembleRelease", "link": "node node_modules/react-native/local-cli/cli.js link", "test": "jest", "build-ad": "cd android && ./gradlew assembleRelease && cd ..", "android-linux": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res ", "sourceMapAndroid": "react-native bundle  --platform android --dev false --entry-file index.js --bundle-output android-release.bundle --sourcemap-output android-release.bundle.map", "sourceMapiOS": "react-native bundle  --platform ios --dev false --entry-file index.js --bundle-output ios-release.bundle --sourcemap-output ios-release.bundle.map", "jetify": "npx jetify", "build-ios": "node node_modules/react-native/local-cli/cli.js bundle --entry-file index.js  --platform ios --dev false --bundle-output ./ios/bundle/index.jsbundle --assets-dest ./ios/bundle"}, "dependencies": {"@ant-design/react-native": "3.1.5", "@ptomasroos/react-native-multi-slider": "^1.0.0", "@react-native-community/async-storage": "1.11.0", "@react-native-community/cli": "^1.12.0", "@react-native-community/geolocation": "1.4.2", "@react-native-community/hooks": "^2.8.1", "ahooks": "^3.1.9", "blueimp-md5": "2.10.0", "dayjs": "^1.10.7", "eventemitter3": "^4.0.0", "jexl": "1.1.4", "lodash": "4.17.15", "lodash-es": "4.17.15", "mobx": "4.3.1", "mobx-react": "5.1.0", "moment": "2.29.1", "nagz-mobile-lib": "file:../nagz-mobile-lib", "nanoid": "^3.2.0", "node-emoji": "1.8.1", "pinyin": "2.8.3", "prop-types": "15.6.0", "rc-form": "1.5.0", "react": "16.8.3", "react-native": "0.59.9", "react-native-action-button": "2.8.1", "react-native-android-wifi": "0.0.41", "react-native-animatable": "1.2.4", "react-native-baidu-map": "1.0.5", "react-native-calendar": "0.13.1", "react-native-calendars": "1.212.0", "react-native-camera": "2.11.2", "react-native-collapsible": "0.10.0", "react-native-contacts": "^2.1.3", "react-native-device-info": "5.6.5", "react-native-dropdownalert": "3.1.2", "react-native-echarts-wrapper": "^2.0.0", "react-native-fs": "2.13.3", "react-native-gesture-handler": "1.2.1", "react-native-get-random-values": "^1.7.2", "react-native-image-crop-picker": "0.24.1", "react-native-image-picker": "0.28.1", "react-native-image-progress": "1.1.1", "react-native-keyboard-aware-scroll-view": "0.4.4", "react-native-modal-dropdown": "0.5.0", "react-native-network-info": "5.1.0", "react-native-orientation": "3.1.3", "react-native-reanimated": "1.2.0", "react-native-side-menu": "1.1.3", "react-native-sortable-grid": "2.0.0", "react-native-sound": "0.10.12", "react-native-splash-screen": "3.2.0", "react-native-swipeout": "2.3.6", "react-native-swiper": "1.5.13", "react-native-vector-icons": "6.4.2", "react-native-video": "5.0.0", "react-native-webview": "7.6.0", "react-native-wifi": "^1.0.1", "react-navigation": "3.11.1", "react-redux": "7.2.1", "redux": "4.0.5", "redux-logger": "3.0.6", "redux-saga": "1.1.3", "rn-placeholder": "2.0.0", "uuid": "3.3.2"}, "devDependencies": {"@babel/core": "^7.17.2", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-flow": "^7.0.0", "@babel/runtime": "^7.17.2", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^8.0.2", "babel-jest": "^27.5.1", "babel-plugin-import": "1.6.2", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-remove-console": "6.8.5", "eslint": "^4.12.0", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-flowtype": "2.39.1", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.2", "eslint-plugin-react": "^7.5.1", "jest": "^27.5.1", "jetifier": "^1.6.6", "metro-react-native-babel-preset": "0.68.0", "react-test-renderer": "16.8.3", "remote-redux-devtools": "0.5.12"}, "jest": {"preset": "react-native"}}