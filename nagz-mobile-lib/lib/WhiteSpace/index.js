
import React from 'react';
import PropTypes from 'prop-types';
import { View } from 'react-native';
import varibles from '../themes/defaultStyles';

/**
 * 上下留白
 * @param {string} [size='md'] 上下留白的间距，可选xs,sm,md,lg,xl
 */
export class WhiteSpace extends React.Component {
  static propTypes = {
    size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
    style: PropTypes.object,
  }

  static defaultProps = {
    size: 'md',
  };

  render() {
    const { size, style } = this.props;
    return (
      <View style={[{ height: varibles[`v_spacing_${size}`] }, style]} />
    );
  }
}
