import React from 'react';
import {
  View,
  Text,
  Animated,
  ActivityIndicator,
  Image,
} from 'react-native';
import PropTypes from 'prop-types';
import styles from './style';

/**
 * 轻提示组件
 * @name Toast
 * @param {element} content 提示内容
 * @param {number} [duration=1] 自动关闭的延时，单位秒
 * @param {func} [onClose] 关闭后回调
 * @param {string} type 提示类型{info,success,fail,offline,loading}
 * @param {bool} [mask=true] 是否显示透明蒙层，防止触摸穿透
 * @param {func} [onAnimationEnd] 提示结束关闭提示
 */

export default class ToastContainer extends React.Component {
  state={
    fadeAnim: new Animated.Value(0),
  }

  componentDidMount() {
    const { onClose, onAnimationEnd } = this.props;
    const duration = typeof this.props.duration === 'number' ? this.props.duration : 1;
    const timing = Animated.timing;
    if (this.anim) { this.anim = null; }
    const animArr = [
      timing(this.state.fadeAnim, { toValue: 1, duration: 200 }),
      Animated.delay(duration * 1000),
    ];
    if (duration > 0) {
      animArr.push(
        timing(this.state.fadeAnim, { toValue: 0, duration: 200 }),
      );
    }
    this.anim = Animated.sequence(animArr);
    this.anim.start(() => {
      if (duration > 0) {
        this.anim = null;
        if (onClose) { onClose(); }
        if (onAnimationEnd) { onAnimationEnd(); }
      }
    });
  }

  componentWillUnmount() {
    if (this.anim) {
      this.anim.stop();
      this.anim = null;
    }
  }

  render() {
    const { type, content, mask, isModal } = this.props;
    let iconDom = null;
    const iconType = {
      success: require('./images/success.png'),
      fail: require('./images/fail.png'),
      offline: require('./images/offline.png'),
    };
    if (type === 'info') {
      iconDom = null;
    } else if (type === 'loading') {
      iconDom = (<ActivityIndicator
        animating
        style={styles.centering}
        color="white"
        size="large"
      />);
    } else {
      iconDom = (<Image
        source={iconType[type]}
        style={styles.image}
      />);
    }
    return (
      <View style={[styles.container, { backgroundColor: isModal ? 'transparent' : 'rgba(0,0,0,.5)' }]} pointerEvents={mask ? undefined : 'box-none'}>
        <View style={[styles.innerContainer]}>
          <Animated.View style={{ opacity: this.state.fadeAnim }}>
            <View style={[styles.innerWrap, styles.textToast]}>
              {iconDom}
              <Text style={styles.content}>{content}</Text>
            </View>
          </Animated.View>
        </View>
      </View>
    );
  }
}

ToastContainer.propTypes = {
  content: PropTypes.any,
  duration: PropTypes.number,
  onClose: PropTypes.func,
  type: PropTypes.string,
  mask: PropTypes.bool,
  isModal: PropTypes.bool,
  onAnimationEnd: PropTypes.func,
};

ToastContainer.defaultProps = {
  content: '',
  duration: 1,
  onClose: () => {},
  type: 'info',
  mask: true,
  onAnimationEnd: () => {},
};
