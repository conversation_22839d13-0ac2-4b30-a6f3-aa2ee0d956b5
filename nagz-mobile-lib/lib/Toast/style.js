
import variables from '../themes/defaultStyles';

export default {
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: variables.toast_zindex,
  },
  innerContainer: {
    backgroundColor: variables.toast_fill,
    borderRadius: variables.radius_md,
    paddingTop: variables.v_spacing_md,
    paddingBottom: variables.v_spacing_md,
    paddingRight: variables.h_spacing_md,
    paddingLeft: variables.h_spacing_md,
    shadowColor: variables.docked_button_shadow_color,
    shadowOpacity: variables.toast_shadow_opacity,
    shadowOffset: {
      width: variables.docked_button_shadow_width,
      height: variables.docked_button_shadow_height,
    },
    shadowRadius: variables.docked_button_shadow_radius,
  },
  innerWrap: {
    alignItems: 'center',
    minWidth: 100,
  },
  iconToast: {
    borderRadius: variables.radius_lg,
    padding: variables.v_spacing_lg,
  },
  textToast: {
    borderRadius: variables.radius_sm,
    paddingVertical: variables.v_spacing_md,
    paddingHorizontal: variables.v_spacing_lg,
  },
  content: {
    color: variables.color_text_base_inverse,
    fontSize: variables.font_size_base,
  },
  image: {
    width: variables.icon_size_lg,
    height: variables.icon_size_lg,
    marginBottom: variables.v_spacing_xs,
  },
  centering: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: variables.v_spacing_md,
  },
};
