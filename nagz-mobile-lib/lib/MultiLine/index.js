import React, { Component } from 'react';
import { View, TextInput, Text, TouchableOpacity, Image } from 'react-native';
import PropTypes from 'prop-types';
import styles from './style';
import { SimpleLine, Modal } from '../index';
import variables from '../themes/defaultStyles';

/**
 * 多行文本
 * @name MultiLine
 * @param {bool}  [isRequire=false] 是否必须
 * @param {bool}  [editable=true] 是否可以编辑 true表示可以编辑
 * @param {bool}  [enabled=true] 是否启用
 * @param {string}  [label=多行文本] label名
 * @param {string}  [placeholder=请输入内容] 默认占位符
 * @param {bool}  [clear=false] 是否显示清除按钮(ios only)
 * @param {string}  value 文本框内容
 * @param {string}  defaultValue 文本框初始默认内容
 * @param {function}  onChange onChange方法
 * @param {function}  onPress 组件头部点击事件
 * @param {object}  inputStyle 输入框的样式
 * @param {string}  rightTip 右边的暗提示
 * @param {bool}  isOnlyDisplay 是否仅作为显示文本
 */

export class MultiLine extends Component {
  state = { value: '' }
  onChange=(event) => {
    const text = event.nativeEvent.text;
    const { onChange } = this.props;
    if (onChange) {
      onChange(text);
    } else {
      this.setState({
        value: text,
      });
    }
  }
  render() {
    const {
      placeholder, clear, value, defaultValue, isRequire, editable, enabled, label, onPress, inputStyle, rightTip, isOnlyDisplay, ...restProps
    } = this.props;
    const rightIconView = (
      <Image
        resizeMode="contain"
        style={styles.rightIcon}
        source={require('../Image/icon-arrow-right-small-default.png')}
      />);
    const simpleLineProp = {
      isRequire, editable: false, enabled, label, rightIconView, placeholder: rightTip, onPress,
    };

    return (
      <View style={styles.container}>
        <SimpleLine {...simpleLineProp} />
        {isOnlyDisplay ? <Text numberOfLines={6} style={[styles.textarea, inputStyle]}>{value}</Text> : <TextInput
          {...restProps}
          value={value}
          editable={editable && enabled}
          defaultValue={defaultValue || value}
          clearButtonMode={clear ? 'while-editing' : 'never'}
          underlineColorAndroid="transparent"
          onChange={this.onChange}
          placeholder={placeholder}
          placeholderTextColor={variables.color_text_placeholder}
          multiline
          style={[styles.textarea, inputStyle]}
        />}
        <Modal />
      </View>
    );
  }
}

MultiLine.propTypes = {
  isRequire: PropTypes.bool,
  editable: PropTypes.bool,
  enabled: PropTypes.bool,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  clear: PropTypes.bool,
  value: PropTypes.string,
  defaultValue: PropTypes.string,
  onChange: PropTypes.func,
  onPress: PropTypes.func,
  inputStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  rightTip: PropTypes.string,
  isOnlyDisplay: PropTypes.bool,
};

MultiLine.defaultProps = {
  isRequire: false,
  editable: true,
  enabled: true,
  label: '多行文本',
  placeholder: '请输入内容',
  clear: false,
  value: '',
  isOnlyDisplay: false,
};
