import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Modal from 'react-native-modal';
import styles from './styles';

class ActionSheet extends PureComponent {

  state = {
    visible: this.props.visible,
  }

  selectedIndex = null

  onAnimationEnd = () => {
    const { onAnimationEnd, callback } = this.props;
    if (onAnimationEnd) {
      onAnimationEnd();
    }
    if (callback) {
      callback(this.selectedIndex);
    }
  }
  close() {
    this.setState({
      visible: false,
    });
  }

  confirm(index) {
    this.selectedIndex = index;
    this.setState({
      visible: false,
    });
  }

  render() {
    const { config } = this.props;
    const {
      message, options, destructiveButtonIndex, cancelButtonIndex,
    } = config;
    const contentScrollView = [];
    let cancelButton = null;
    options.forEach((item, index) => {
      const cont = (<View key={index} style={[cancelButtonIndex === index ? styles.cancelBtn : undefined]}>
        <TouchableOpacity
          style={[styles.btn, index === 0 ? styles.noBorder : undefined]}
          onPress={() => this.confirm(index)}
        >
          <Text
            style={[destructiveButtonIndex === index ? styles.destructiveBtn : styles.normalText,
              cancelButtonIndex === index ? styles.cancelBtnText : undefined,
            ]}
          >
            {item}
          </Text>
        </TouchableOpacity>
        {cancelButtonIndex === index ? <View style={styles.cancelBtnMask} /> : null}
      </View>);
      if (cancelButtonIndex === index) {
        cancelButton = cont;
      } else {
        contentScrollView.push(cont);
      }
    });
    return (
      <View>
        <Modal
          isVisible={this.state.visible}
          style={styles.content}
          backdropOpacity={0.5}
          onBackButtonPress={() => {}}
          onBackdropPress={() => {
            this.setState({
              visible: false,
            });
          }}
          onModalHide={this.onAnimationEnd}
        >
          <View style={styles.container}>
            {!!message && <View style={styles.message} key="1"><Text style={styles.messageText}>{message}</Text></View>}
            <View>
              <ScrollView style={styles.contentScrollView}>{contentScrollView}</ScrollView>
              <View>{cancelButton}</View>
            </View>
          </View>
        </Modal>
      </View>

    );
  }
}

ActionSheet.propTypes = {
  callback: PropTypes.func,
  config: PropTypes.object,
  onAnimationEnd: PropTypes.func,
  visible: PropTypes.bool,
};

export default ActionSheet;
