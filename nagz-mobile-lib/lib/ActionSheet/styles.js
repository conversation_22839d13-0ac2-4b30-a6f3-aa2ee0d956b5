import {
  StyleSheet,
  Dimensions,
 } from 'react-native';
import variables from '../themes/defaultStyles';

const { width, height } = Dimensions.get('window');
const btnHeight = 57;

export default StyleSheet.create({
  container: {
    backgroundColor: variables.fill_base,
  },
  content: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  title: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleText: {
    fontSize: variables.font_size_caption_sm,
    color: variables.color_text_base,
  },
  message: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 18,
    paddingBottom: 18,
    borderBottomWidth: 1,
    borderBottomColor: variables.fill_line,
  },
  messageText: {
    fontSize: variables.font_size_caption_sm,
    color: variables.color_text_caption,
  },
  btn: {
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: variables.fill_line,
    marginLeft: 10,
    height: btnHeight,
    width: width - 20,
  },
  cancelBtn: {
    borderTopWidth: 5,
    borderTopColor: variables.fill_big_ling,
  },
  cancelBtnText: {
    fontSize: variables.font_size_base,
    color: '#41454B',
    fontWeight: 'bold',
  },
  destructiveBtn: {
    fontSize: variables.font_size_caption,
    color: variables.color_text_warning,
    fontWeight: 'bold',
  },
  normalText: {
    fontSize: variables.font_size_caption,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  noBorder: {
    borderTopWidth: 0,
    borderBottomWidth: 0,
  },
  contentScrollView: {
    maxHeight: height - btnHeight - 20,
  },
});
