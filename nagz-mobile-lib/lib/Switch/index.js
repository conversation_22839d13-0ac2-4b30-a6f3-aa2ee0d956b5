import React from 'react';
import PropTypes from 'prop-types';
import {
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import style from './style';

const styles = StyleSheet.create(style);

/**
 * Switch 开关组件
 * @param {boolean} [checked=false] 是否选中
 * @param {Function} onChange (checked: Boolean): void, 事件触发的回调函数
 * @param {boolean} [disabled=false] 是否不可修改
 */
export class Switch extends React.Component {

  onPress = () => {
    if (this.props.onChange) {
      this.props.onChange(!this.props.checked);
    }
  }

  render() {
    const { checked, disabled } = this.props;
    const containerStyle = [
      styles.container,
      checked ? styles.open_container : styles.close_container,
      disabled && styles.disabled_container,
    ];
    const slidingStyle = [
      styles.sliding,
      checked ? styles.open_sliding : styles.close_sliding,
    ];
    return (
      <TouchableOpacity
        style={containerStyle}
        onPress={this.onPress}
        activeOpacity={0.8}
        disabled={disabled}
      >
        <Animatable.View
          transition="left"
          style={slidingStyle}
        />
      </TouchableOpacity>
    );
  }
}

Switch.propTypes = {
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
};

Switch.defaultProps = {
  checked: false,
  disabled: false,
  onChange: () => {},
};
