import variables from '../themes/defaultStyles';

export default {
  container: {
    width: variables.switch_width,
    height: variables.switch_height,
    borderRadius: variables.switch_radius,
    position: 'relative',
  },
  sliding: {
    width: variables.switch_sliding_radius,
    height: variables.switch_sliding_radius,
    borderRadius: variables.switch_sliding_radius,
    backgroundColor: variables.switch_sliding_color,
    position: 'relative',
    top: variables.switch_gap,
    shadowColor: variables.switch_shadow_color,
    shadowOpacity: variables.switch_shadow_opacity,
    shadowOffset: {
      width: variables.switch_shadow_width,
      height: variables.switch_shadow_height,
    },
    shadowRadius: variables.switch_shadow_radius,
  },
  open_container: {
    backgroundColor: variables.switch_fill,
  },
  close_container: {
    backgroundColor: variables.switch_close_fill,
  },
  open_sliding: {
    left: variables.switch_width - variables.switch_sliding_radius - variables.switch_gap,
  },
  close_sliding: {
    left: variables.switch_gap,
  },
  disabled_container: {
    opacity: Number(variables.opacity_disabled),
  },
};
