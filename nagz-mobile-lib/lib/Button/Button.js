
import React from 'react';
import PropTypes from 'prop-types';
import {
  StyleSheet,
  TouchableOpacity,
  Text,
  View,
} from 'react-native';
import { Image } from 'react-native-animatable';
import { dockedButtonStyle, buttonStyle } from './style';

/**
 * 占整行的按钮, 一般显示在屏幕最下方
 * @param {Function} onPress 按下按钮的回调
 * @param {node} prefixIcon 按钮文字前面的图标
 * @param {Boolean} [loading] 是否正在loading
 * @param {style} style 自定义的style
 * @param {inline} 是否是行内按钮
 * @param {node} children 按钮中的内容
 */
export class Button extends React.Component {
  static propTypes = {
    onPress: PropTypes.func,
    prefixIcon: PropTypes.element,
    children: PropTypes.node,
    loading: PropTypes.bool,
    style: PropTypes.shape({
      container: PropTypes.number,
      textContent: PropTypes.number,
      prefixIcon: PropTypes.number,
    }),
    inline: PropTypes.bool,
    disabled: PropTypes.bool,
  }

  static defaultProps = {
    onPress: () => {},
    loading: false,
  }

  state = {
    pressIn: false,
  }

  componentWillMount = () => {
    this.styles = this.props.inline ? StyleSheet.create(buttonStyle) : StyleSheet.create(dockedButtonStyle);
  }
  onPressIn = () => {
    this.setState({
      pressIn: true,
    });
  }
  onPressOut = () => {
    this.setState({
      pressIn: false,
    });
  }

  render() {
    const { loading, style, disabled, onPress } = this.props;

    const containerStyle = [
      this.styles.container,
      style && style.container,
      disabled && this.styles.disabledContainer,
    ];

    const prefixIconStyle = [
      this.styles.prefixIcon,
      style && style.prefixIcon,
    ];

    const textContextStyle = [
      this.styles.textContent,
      style && style.textContent,
      disabled && this.styles.disabledTextContent,
    ];
    if (loading) {
      return (
        <View
          style={containerStyle}
        >
          <Image
            source={require('../style/images/loading.png')}
            animation="agzRotation"
            iterationCount="infinite"
            easing="linear"
            duration={600}
          />
        </View>
      );
    }
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onPress}
        style={containerStyle}
        disabled={disabled}
      >
        {this.props.prefixIcon &&
        <View
          style={prefixIconStyle}
        >
          {this.props.prefixIcon}
        </View>
      }
        {
            typeof this.props.children === 'string' ?
              <Text
                style={textContextStyle}
              >
                {this.props.children}
              </Text> :
            this.props.children
          }
      </TouchableOpacity>
    );
  }
}
