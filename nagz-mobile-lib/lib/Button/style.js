import variables from '../themes/defaultStyles';

export const dockedButtonStyle = {
  container: {
    height: variables.docked_button_height,
    backgroundColor: variables.color_text_base_inverse,
    shadowColor: variables.docked_button_shadow_color,
    shadowOpacity: variables.docked_button_shadow_opacity,
    shadowOffset: {
      width: variables.docked_button_shadow_width,
      height: variables.docked_button_shadow_height,
    },
    shadowRadius: variables.docked_button_shadow_radius,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContent: {
    color: variables.brand_primary,
    fontSize: variables.docked_button_font_size,
  },
  prefixIcon: {
    marginRight: 10,
  },
  disabledContainer: {
    backgroundColor: variables.fill_disabled,
  },
  disabledTextContent: {
    color: variables.color_text_disabled,
  },
};

export const buttonStyle = {
  container: {
    height: variables.button_height,
    width: variables.button_width,
    backgroundColor: variables.button_background_color,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: variables.brand_primary,
    borderWidth: 1,
    borderRadius: 4,
  },
  textContent: {
    color: variables.brand_primary,
    fontSize: variables.button_font_size,
  },
  prefixIcon: {
    marginRight: 10,
  },
  disabledContainer: {
    borderColor: variables.color_text_disabled,
    backgroundColor: variables.fill_disabled,
  },
  disabledTextContent: {
    color: variables.color_text_disabled,
  },
};
