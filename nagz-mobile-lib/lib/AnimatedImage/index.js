
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  TouchableOpacity,
  Animated,
  View,
} from 'react-native';

/**
 * 按钮动画组件
 * @name AnimatedImage
 * @param {func} onPress 点击触发事件
 * @param {func} onPressIn 点击前触发事件
 * @param {func} onPressOut 点击后触发事件
 * @param {any} source 图片
 * @param {object} style 样式
 */

export class AnimatedImage extends PureComponent {

  state={
    scale: new Animated.Value(1),
  }

  onPressIn=() => {
    if (this.props.onPressIn) {
      this.props.onPressIn();
    } else {
      Animated.timing(this.state.scale, {
        toValue: 2,
        duration: 20,
      }).start();
    }
  }
  onPressOut=() => {
    if (this.props.onPressOut) {
      this.props.onPressOut();
    } else {
      Animated.timing(this.state.scale, {
        toValue: 1,
        duration: 20,
      }).start();
    }
  }

  render() {
    return (
      <TouchableOpacity
        onPress={this.props.onPress}
        onPressIn={this.onPressIn}
        onPressOut={this.onPressOut}
        activeOpacity={1}
        style={this.props.style}
      >
        <View
          style={{
            width: 14,
            height: 14,
          }}
        >
          <Animated.Image
            style={{
              width: 14.2,
              height: 14.2,
              transform: [
                {
                  scale: this.state.scale,
                },
              ],
            }}
            source={this.props.source}
          />
        </View>

      </TouchableOpacity>
    );
  }

}

AnimatedImage.propTypes = {
  onPress: PropTypes.func,
  onPressIn: PropTypes.func,
  onPressOut: PropTypes.func,
  source: PropTypes.any,
  style: PropTypes.object,
};

AnimatedImage.defaultProps = {
  onPress: () => {},
  source: require('../Image/icon-awsome-button.png'),
  style: {},
  onPressIn: null,
  onPressOut: null,
};
