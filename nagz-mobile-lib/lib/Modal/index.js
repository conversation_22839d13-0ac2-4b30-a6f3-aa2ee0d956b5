import React, { Component } from 'react';
import { View, Text, Platform } from 'react-native';
import DefaultModal from 'react-native-modal';
import PropTypes from 'prop-types';
import styles from './style';
import ToastContainer from '../Toast/ToastContainer';

/**
 * 遮罩层，其它属性配置都与react-native-modal 一样
 * @name Modal
 * @param {bool}  [isVisible=false] 是否显示
 * @param {bool}  [avoidKeyboard=false] 是否避免挡住输入法
 * @param {element}  children modal内部组件
 * @param {function}  onSwipe 划动事件
 * @param {string}  swipeDirection 划动方向
 * @param {string}  animationIn 遮罩进入动画
 * @param {string}  animationOut 遮罩退出动画
 * @param {object}  header header:{leftView:标题左边的组件 centerText:标题中间的文字 rightView：右边的组件}
 * @param {object}  toast toast:{isVisible:是否显示 style:样式 content：提示文字 duration：关闭时间 onClose：关闭后回调 type：类型}
 */
export class Modal extends Component {
  state = {
    toastIsVisible: this.props.toast.isVisible,
  }

  componentWillReceiveProps=({ toast }) => {
    if (toast.isVisible !== this.props.toast.isVisible) {
      this.setState({ toastIsVisible: toast.isVisible });
    }
  }
  onAnimationEnd=() => {
    if (this.state.toastIsVisible) {
      this.setState({ toastIsVisible: false });
    }
  }
  render() {
    const { isVisible, avoidKeyboard, children, onSwipe, swipeDirection, animationIn, animationOut, style, header, toast, ...restProps } = this.props;
    return (
      <DefaultModal
        onSwipe={onSwipe}
        swipeDirection={swipeDirection}
        animationIn={animationIn}
        animationOut={animationOut}
        avoidKeyboard={avoidKeyboard}
        isVisible={isVisible}
        style={[styles.modalContainer, style]}
        {...restProps}
      >
        <View style={{ flex: 1, paddingTop: Platform.OS === 'ios' ? 20 : 0 }}>
          {header ? <View style={{ height: 44, flexDirection: 'row', paddingHorizontal: 18, alignItems: 'center' }}>
            <View>{header.leftView}</View>
            <View style={{ flex: 1 }}><Text style={{ fontWeight: 'bold', fontSize: 18, color: '#41454b', textAlign: 'center' }}>{header.centerText}</Text></View>
            <View>{header.rightView}</View>
          </View> : null}
          {children}
        </View>
        <DefaultModal
          isVisible={this.state.toastIsVisible}
          style={[styles.modalContainer, toast.style]}
        >
          {this.state.toastIsVisible &&
          <ToastContainer
            content={toast.content}
            duration={toast.duration}
            onClose={toast.onClose}
            type={toast.type}
            onAnimationEnd={this.onAnimationEnd}
            isModal
          />
        }
        </DefaultModal>
      </DefaultModal>
    );
  }
}
Modal.propTypes = {
  isVisible: PropTypes.bool,
  children: PropTypes.element,
  avoidKeyboard: PropTypes.bool,
  onSwipe: PropTypes.func,
  swipeDirection: PropTypes.string,
  animationIn: PropTypes.any,
  animationOut: PropTypes.any,
  header: PropTypes.shape({
    leftView: PropTypes.element,
    centerText: PropTypes.string,
    rightView: PropTypes.element,
  }),
  toast: PropTypes.shape({
    isVisible: PropTypes.bool,
    style: PropTypes.object,
    content: PropTypes.any,
    duration: PropTypes.number,
    onClose: PropTypes.func,
    type: PropTypes.string,
  }),
};
Modal.defaultProps = {
  isVisible: false,
  avoidKeyboard: false,
  swipeDirection: 'right',
  animationIn: 'slideInRight',
  animationOut: 'slideOutRight',
  header: null,
  toast: {
    isVisible: false,
    style: {},
    content: null,
    duration: 1,
    onClose: () => {},
    type: 'info',
  },
};
