import React from 'react';
import PropTypes from 'prop-types';
import { Image, View, TouchableOpacity, Text } from 'react-native';
import itemStyles from './styles';

/**
 * 列表子组件
 * @name ListItem
 * @param {element} [children] 右侧内容
 * @param {bool} [multipleLine=false] 默认是否多行
 * @param {element} [thumb] 缩略图
 * @param {element} [extra]
 * @param {string} arrow 箭头方向（horizontal,up,down,empty）
 * @param {style-object} style 样式
 * @param {func} onClick 点击改变出发事件
 * @param {bool} [wrap=false] 是否换行，默认情况下，文字超长会被隐藏
 * @param {bool} [disabled=false] 是否禁止点击
 * @param {string} [align=middle] 子元素垂直对齐，可选top,middle,bottom
 * @param {bool} [hideBottomLine=false] 是否隐藏底边线
 * @param {object} contentStyle 内容样式
 */

const fn = () => {};

export default class Item extends React.Component {
  render() {
    const {
      children, multipleLine, thumb, extra, arrow, style, onClick,
      wrap, disabled, align, hideBottomLine, contentStyle, ...restProps
    } = this.props;

    let numberOfLines = {};
    if (wrap === false) {
      numberOfLines = {
        numberOfLines: 1,
      };
    }

    let underlayColor = {};

    if (!disabled && onClick) {
      underlayColor = {
        underlayColor: itemStyles.underlayColor.backgroundColor,
        activeOpacity: 0.8,
      };
    } else {
      underlayColor = {
        activeOpacity: 1,
      };
    }

    let alignStyle = {};

    if (align === 'top') {
      alignStyle = {
        alignItems: 'flex-start',
      };
    } else if (align === 'bottom') {
      alignStyle = {
        alignItems: 'flex-end',
      };
    }

    let contentDom;
    if (Array.isArray(children)) {
      const tempContentDom = [];
      children.forEach((el, index) => {
        if (React.isValidElement(el)) {
          tempContentDom.push(el);
        } else {
          tempContentDom.push(
            <Text style={[itemStyles.Content]} {...numberOfLines} key={`${index}-children`}>{el}</Text>,
          );
        }
      });
      contentDom = <View style={[itemStyles.column]}>{tempContentDom}</View>;
    } else if (React.isValidElement(children)) {
      contentDom = <View style={[itemStyles.column]}>{children}</View>;
    } else {
      contentDom = (
        <View style={[itemStyles.column]}>
          <Text style={[itemStyles.Content]} {...numberOfLines}>{children}</Text>
        </View>
        );
    }

    let extraDom;
    if (extra) {
      extraDom = (
        <View style={[itemStyles.column]}>
          <Text style={[itemStyles.Extra]} {...numberOfLines}>{extra}</Text>
        </View>
      );
      if (React.isValidElement(extra)) {
        const extraChildren = extra.props.children;
        if (Array.isArray(extraChildren)) {
          const tempExtraDom = [];
          extraChildren.forEach((el, index) => {
            if (typeof el === 'string') {
              tempExtraDom.push(
                <Text
                  {...numberOfLines}
                  style={[itemStyles.Extra]}
                  key={`${index}-children`}
                >
                  {el}
                </Text>,
              );
            } else {
              tempExtraDom.push(el);
            }
          });
          extraDom = (
            <View style={[itemStyles.column]}>
              {tempExtraDom}
            </View>
          );
        } else {
          extraDom = extra;
        }
      }
    }

    const arrEnum = {
      horizontal: <Image source={require('../style/images/arrows.png')} style={itemStyles.Arrow} />,
      down: <Image source={require('../style/images/arrow-downs.png')} style={itemStyles.ArrowV} />,
      up: <Image source={require('../style/images/arrow-ups.png')} style={itemStyles.ArrowV} />,
    };
    const itemView = (
      <View {...restProps} style={[itemStyles.Item, style]}>
        {typeof thumb === 'string' ? (
          <Image
            source={{ uri: thumb }}
            style={[itemStyles.Thumb, multipleLine && itemStyles.multipleThumb]}
          />
        ) : thumb}
        <View style={[itemStyles.Line, hideBottomLine && itemStyles.hideLine, multipleLine && itemStyles.multipleLine, multipleLine && alignStyle, contentStyle]}>
          {contentDom}
          {extraDom}
          {arrow ? typeof arrow === 'string' ? (arrEnum[arrow] || <View style={itemStyles.Arrow} />) : arrow : null}
        </View>
      </View>
    );

    return (
      <TouchableOpacity
        {...underlayColor}
        onPress={onClick || fn}
      >
        {itemView}
      </TouchableOpacity>
    );
  }
}

Item.propTypes = {
  children: PropTypes.any,
  multipleLine: PropTypes.bool,
  thumb: PropTypes.any,
  extra: PropTypes.any,
  arrow: PropTypes.any,
  style: PropTypes.any,
  onClick: PropTypes.func,
  wrap: PropTypes.bool,
  disabled: PropTypes.bool,
  align: PropTypes.string,
  hideBottomLine: PropTypes.bool,
  contentStyle: PropTypes.any,
};

Item.defaultProps = {
  children: '',
  multipleLine: false,
  thumb: null,
  extra: '',
  arrow: null,
  style: {},
  onClick: fn,
  wrap: false,
  disabled: false,
  align: 'middle',
  hideBottomLine: false,
  contentStyle: {},
};

