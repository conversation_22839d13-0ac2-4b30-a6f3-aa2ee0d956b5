import React from 'react';
import PropTypes from 'prop-types';
import { View, Text } from 'react-native';
import _styles from './styles';

/**
* 列表组件
* @name List
* @param {element} [children] 内容
* @param {style-object} style 样式
* @param {func} renderHeader 列头内容
* @param {func} renderFooter 列为内容
*/

const fn = () => '';

export default class List extends React.Component {

  render() {
    const {
        children, style, renderHeader, renderFooter, ...restProps
      } = this.props;

    let headerDom = fn;
    let footerDom = fn;

    if (renderHeader) {
      let content = typeof renderHeader === 'function' ? renderHeader() : renderHeader;
      if (typeof content === 'string') {
        content = <Text style={_styles.Header}>{content}</Text>;
      }
      headerDom = <View>{content}</View>;
    }
    if (renderFooter) {
      let content = typeof renderFooter === 'function' ? renderFooter() : renderFooter;
      if (typeof content === 'string') {
        content = <Text style={_styles.Footer}>{content}</Text>;
      }
      footerDom = <View>{content}</View>;
    }

    return (<View {...restProps} style={style}>
      {headerDom}
      <View style={_styles.Body}>
        {children}
        <View style={[_styles.BodyBottomLine]} />
      </View>
      {footerDom}
    </View>);
  }
}
List.propTypes = {
  children: PropTypes.any,
  style: PropTypes.object,
  renderHeader: PropTypes.func,
  renderFooter: PropTypes.func,
};

List.defaultProps = {
  children: '',
  style: {},
  renderHeader: fn,
  renderFooter: fn,
};

