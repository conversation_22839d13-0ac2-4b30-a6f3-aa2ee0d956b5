import React from 'react';
import PropTypes from 'prop-types';
import { View, Text } from 'react-native';
import itemStyles from './styles';

/**
* 列表右侧内容
* @name Brief
* @param {element} [children] 内容
* @param {style-object} style 样式
* @param {bool} [wrap=false] 是否换行，默认情况下，文字超长会被隐藏
*/

export default class Brief extends React.Component {

  render() {
    const { children, style, wrap } = this.props;

    let numberOfLines = {};

    if (wrap === false) {
      numberOfLines = {
        numberOfLines: 1,
      };
    }
    return (
      <View style={[itemStyles.Brief]}>
        {React.isValidElement(children) ? { children }
        : <Text style={[itemStyles.BriefText, style]} {...numberOfLines}>{children}</Text>
        }
      </View>
    );
  }
}
Brief.propTypes = {
  children: PropTypes.any,
  style: PropTypes.object,
  wrap: PropTypes.bool,
};

Brief.defaultProps = {
  children: '',
  style: {},
  wrap: false,
};
