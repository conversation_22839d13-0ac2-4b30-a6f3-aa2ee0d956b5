import React from 'react';
import PropTypes from 'prop-types';
import { TouchableOpacity, Image, Text, View } from 'react-native';
import styles from './style';

class Radio extends React.Component {
  state = {
    checked: this.props.checked || this.props.defaultChecked || false,
  };
  componentWillReceiveProps=({ checked }) => {
    if (checked !== this.props.checked) {
      this.setState({ checked });
    }
  }
  render() {
    const { style, disabled, children } = this.props;

    let imgSrc;
    if (this.state.checked) {
      if (disabled) {
        imgSrc = require('../Image/icon-radio-checked-disable.png');
      } else {
        imgSrc = require('../Image/icon-radio-checked-default.png');
      }
    }
    return (<TouchableOpacity
      activeOpacity={this.state.checked ? 0.8 : 1}
      onPress={this.handleClick}
    >
      <View style={[styles.wrapper]}>
        <Image source={imgSrc} style={[styles.icon, style]} />
        {children && typeof children === 'string' ? <Text>{children}</Text> : children}
      </View>
    </TouchableOpacity>
    );
  }
}

Radio.propTypes = {
  style: PropTypes.object,
  children: PropTypes.any,
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  defaultChecked: PropTypes.bool,
};

Radio.defaultProps = {
  style: {},
  children: null,
  checked: false,
  disabled: false,
  defaultChecked: false,
};

export default Radio;
