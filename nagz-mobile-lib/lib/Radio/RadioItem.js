import React from 'react';
import PropTypes from 'prop-types';
import { Text, View } from 'react-native';
import Radio from './Radio';
import { List } from '../List';
import styles from './style';

const ListItem = List.Item;
const refRadio = 'radio';

class RadioItem extends React.Component {
  state={
    checked: this.props.checked || this.props.defaultChecked || false,
  }

  componentWillReceiveProps=({ checked }) => {
    if (checked !== this.props.checked) {
      this.setState({ checked });
    }
  }

  handleClick = () => {
    if (this.props.onChange) {
      this.props.onChange();
    }
  }

  render() {
    const { style, radioStyle, defaultChecked, disabled, children, onChange, hideBottomLine } = this.props;

    let contentDom = null;
    if (children && Array.isArray(children)) {
      const tempContentDom = [];
      children.forEach((el, index) => {
        if (React.isValidElement(el)) {
          tempContentDom.push(el);
        } else {
          const contentStyle = [styles.radioItemContent, disabled ? styles.radioItemContentDisable : {}];
          tempContentDom.push(
            <Text style={contentStyle} key={`${index}-children`}>{el}</Text>,
            );
        }
      });
      contentDom = <View style={[styles.column]}>{tempContentDom}</View>;
    } else if (children && React.isValidElement(children)) {
      contentDom = <View style={{ flex: 1 }}>{children}</View>;
    } else {
      const contentStyle = [styles.radioItemContent, disabled ? styles.radioItemContentDisable : {}];
      contentDom = (<Text style={contentStyle} numberOfLines={1}>
        {children}
      </Text>);
    }

    const radioEl = (
      <Radio
        ref={refRadio}
        style={radioStyle}
        defaultChecked={defaultChecked}
        checked={this.state.checked}
        onChange={onChange}
        disabled={disabled}
      />
      );

    return (
      <ListItem
        style={style}
        onClick={disabled ? undefined : this.handleClick}
        extra={radioEl}
        hideBottomLine={hideBottomLine}
      >
        {contentDom}
      </ListItem>
    );
  }
}

RadioItem.propTypes = {
  style: PropTypes.array,
  radioStyle: PropTypes.object,
  defaultChecked: PropTypes.bool,
  checked: PropTypes.bool,
  children: PropTypes.any,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  hideBottomLine: PropTypes.bool,
};

RadioItem.defaultProps = {
  style: [],
  radioStyle: {},
  defaultChecked: false,
  children: null,
  checked: false,
  disabled: false,
  onChange: () => {},
  hideBottomLine: true,
};

export default RadioItem;
