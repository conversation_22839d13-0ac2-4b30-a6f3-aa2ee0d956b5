import React from 'react';
import PropTypes from 'prop-types';
import { TextInput as RnTextInput } from 'react-native';
import styles from './styles';
import variables from '../themes/defaultStyles';

/**
 * 单行文本
 * @name TextInput
 * @param {bool} [editable=true] 是否可输入
 * @param {string} [placeholder=请输入] 右侧提示信息
 * @param {string|number} value 右侧显示的值
 * @param {string|number} defaultValue 右侧显示的默认值
 * @param {string} [inputType=default] 键盘类型 (default,numeric,phone-pad,email-address...)
 * @param {func} onBlur 失去焦点时触发的方法，参数为value
 * @param {func} onChange 值改变时的回调，参数为value
 * @param {func} onBlur 失去焦点时触发的方法，参数为value
 * @param {object} style 样式
 * @param {func} onFocus 聚焦
 * @param {func} autoFocus 自动聚焦
 * @param {document} inputRef 输入框dom
 */
export class TextInput extends React.Component {

  static defaultProps ={
    editable: true,
    inputType: 'default',
  }

  onChange = (e) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(e.nativeEvent.text);
    }
  }
  onBlur = (e) => {
    const { onBlur } = this.props;
    if (onBlur) {
      onBlur(e.nativeEvent.text);
    }
  }
  render() {
    const { placeholder, editable, value, inputType, style, onFocus, autoFocus, inputRef, defaultValue, onSubmitEditing, returnKeyType } = this.props;
    const inputStyle = [
      styles.input,
      style,
    ];
    return (<RnTextInput
      ref={inputRef}
      style={inputStyle}
      underlineColorAndroid="transparent"
      placeholder={placeholder}
      placeholderTextColor={variables.color_text_placeholder}
      editable={editable}
      onChange={this.onChange}
      value={value}
      defaultValue={defaultValue}
      keyboardType={inputType}
      onBlur={this.onBlur}
      onFocus={onFocus}
      autoFocus={autoFocus}
      returnKeyType={returnKeyType}
      onSubmitEditing={onSubmitEditing}
    />);
  }
}

TextInput.propTypes = {
  editable: PropTypes.bool,
  autoFocus: PropTypes.bool,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onBlur: PropTypes.func,
  inputType: PropTypes.string,
  onChange: PropTypes.func,
  onFocus: PropTypes.func,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.number]),
  inputRef: PropTypes.func,
};
