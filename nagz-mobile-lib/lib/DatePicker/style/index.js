import {
  StyleSheet,
  Dimensions,
} from 'react-native';
import variables from '../../themes/defaultStyles';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    width,
    height: variables.line_height_default,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: variables.fill_base,
  },
  leftText: {
    width: (width - 74) * 0.3,
    textAlign: 'left',
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  fullTitle: {
    flex: 1,
    textAlign: 'left',
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  rightIcon: {
    width: 14,
    height: variables.line_height_default,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 14,
    paddingRight: variables.h_spacing_md,
  },
  rightText: {
    width: (width - 74) * 0.7,
    textAlign: 'right',
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
  },
  rightDefaultText: {
    width: (width - 74) * 0.7,
    textAlign: 'right',
    fontSize: variables.font_size_base,
    color: variables.color_text_placeholder,
  },
  isRequireText: {
    width: variables.h_spacing_md,
    alignItems: 'center',
    justifyContent: 'center',
    color: variables.color_gray,
    textAlign: 'center',
  },
  isRequire: {
    width: variables.h_spacing_md,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default styles;
