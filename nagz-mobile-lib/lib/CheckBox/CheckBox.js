import React from 'react';
import PropTypes from 'prop-types';
import {
  TouchableOpacity,
  View,
  Text,
  Image,
} from 'react-native';
import styles from './styles';

/**
 * 复选框组件
 * @name CheckBox
 * @param {style-object} [style={}] ListItem样式
 * @param {bool} [defaultChecked=false] 默认是否选中
 * @param {bool} [checked=false] 是否选中
 * @param {bool} [disabled=false] 是否禁用
 * @param {element} [children] 右侧内容
 * @param {func} onChange 选中状态改变出发事件
 */

class CheckBox extends React.Component {
  state={
    checked: this.props.checked || this.props.defaultChecked || false,
  }
  componentWillReceiveProps=({ checked }) => {
    if (checked !== this.props.checked) {
      this.setState({ checked });
    }
  }
  handleClick=() => {
    if (this.props.disabled) {
      return;
    }
    const checked = !this.state.checked;
    if (!(typeof this.props.checked === 'boolean')) {
      this.setState({
        checked,
      });
    }
    if (this.props.onChange) {
      this.props.onChange({ target: { checked } });
    }
  }
  render() {
    const { disabled, children, style } = this.props;
    let imgSrc;
    if (this.state.checked) {
      if (disabled) {
        imgSrc = require('../Image/icon-checkbox-checked-disable.png');
      } else {
        imgSrc = require('../Image/icon-checkbox-checked-default.png');
      }
    } else if (disabled) {
      imgSrc = require('../Image/icon-checkbox-un_checked-disable.png');
    } else {
      imgSrc = require('../Image/icon-checkbox-un_checked-default.png');
    }
    return (
      <TouchableOpacity
        activeOpacity={this.state.checked ? 0.8 : 1}
        onPress={this.handleClick}
      >
        <View style={styles.wrapper}>
          <Image source={imgSrc} style={[styles.icon, style]} />
          {typeof children === 'string' ? (<Text style={styles.iconRight}>{children}</Text>) : children}
        </View>
      </TouchableOpacity>
    );
  }
}

CheckBox.propTypes = {
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  defaultChecked: PropTypes.bool,
  children: PropTypes.any,
  style: PropTypes.any,
  onChange: PropTypes.func,
};

CheckBox.defaultProps = {
  checked: false,
  defaultChecked: false,
  disabled: false,
  onChange: () => {},
  children: '',
  style: {},
};

export default CheckBox;
