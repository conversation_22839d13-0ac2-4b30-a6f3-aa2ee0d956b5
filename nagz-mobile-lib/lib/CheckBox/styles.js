import {
  StyleSheet,
  Dimensions,
} from 'react-native';
import variables from '../themes/defaultStyles';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  ListItem: {
    width,
    height: 50,
    backgroundColor: variables.fill_base,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  icon: {
    width: variables.icon_size_sm,
    height: variables.icon_size_sm,
  },
  iconRight: {
    marginLeft: variables.h_spacing_md,
  },
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxItemCheckbox: {
    marginRight: variables.h_spacing_md,
    alignSelf: 'center',
  },
  checkboxRightDisableText: {
    color: variables.color_gray,
    fontSize: variables.font_size_base,
  },
  checkboxRightText: {
    color: variables.color_text_base,
    fontSize: variables.font_size_base,
  },
});

export default styles;
