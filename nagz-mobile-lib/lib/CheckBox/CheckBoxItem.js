import React from 'react';
import PropTypes from 'prop-types';
import { Text } from 'react-native';
import { List } from '../List';
import styles from './styles';
import CheckBox from './CheckBox';

/**
 * 复选框组件
 * @name CheckBoxItem
 * @param {style-object} [style={}] ListItem样式
 * @param {style-object} [checkboxStyle={}] checkboxStyle样式
 * @param {bool} [defaultChecked=false] 复选框默认状态
 * @param {bool} [checked=false] 复选框状态
 * @param {bool} [disabled=false] 复选框是否禁用
 * @param {element} [children] 复选框右侧内容
 * @param {element} [extra] 组件右侧内容
 * @param {func} onChange 选中状态改变出发事件
 * @param {bool} hideBottomLine 隐藏底线
 * @param {bool} showChecked 显示复选框
 */

const refCheckbox = 'checkbox';
const ListItem = List.Item;
const fn = () => {};

class CheckBoxItem extends React.Component {
  state={
    checked: this.props.checked || this.props.defaultChecked || false,
  }
  componentWillReceiveProps=({ checked }) => {
    if (checked !== this.props.checked) {
      this.setState({ checked });
    }
  }
  handleClick = () => {
    if (this.props.onChange) {
      this.props.onChange({
        target: {
          checked: !this.state.checked,
        },
      });
    }
  }
  setNativeProps(nativeProps) {
    this._root.setNativeProps(nativeProps);
  }
  render() {
    const { style, checkboxStyle, defaultChecked, showChecked, checked, disabled, children, extra, onChange, hideBottomLine } = this.props;

    const thumbEl = (
      <CheckBox
        ref={refCheckbox}
        style={[styles.checkboxItemCheckbox, checkboxStyle]}
        defaultChecked={defaultChecked}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
    );
    let label = '';
    if (typeof children === 'string') {
      const labelStyle = disabled ? styles.checkboxRightDisableText : styles.checkboxRightTexts;
      label = (
        <Text style={labelStyle}>{children}</Text>
      );
    } else {
      label = children;
    }
    return (
      <ListItem
        ref={(c) => { this._root = c; }}
        {...this.props}
        style={[styles.ListItem, style]}
        onClick={disabled ? undefined : this.handleClick}
        extra={extra}
        thumb={showChecked ? thumbEl : null}
        hideBottomLine={hideBottomLine}
      >
        {label}
      </ListItem>
    );
  }
}

CheckBoxItem.propTypes = {
  style: PropTypes.any,
  checkboxStyle: PropTypes.object,
  defaultChecked: PropTypes.bool,
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  children: PropTypes.any,
  extra: PropTypes.any,
  onChange: PropTypes.func,
  hideBottomLine: PropTypes.bool,
  showChecked: PropTypes.bool,
};

CheckBoxItem.defaultProps = {
  style: {},
  checkboxStyle: {},
  defaultChecked: false,
  checked: false,
  disabled: false,
  children: '',
  extra: '',
  onChange: fn,
  hideBottomLine: true,
  showChecked: true,
};

export default CheckBoxItem;
