import React from 'react';
import PropTypes from 'prop-types';
import { View } from 'react-native';
import varibles from '../themes/defaultStyles';

/**
 * 两翼留白
 * @param {string} [size='md'] 两翼留白的间距，可选sm,md,lg
 */
export class WingBlank extends React.Component {
  static propTypes = {
    size: PropTypes.oneOf(['sm', 'md', 'lg']),
    style: PropTypes.object,
    children: PropTypes.node,
  }
  static defaultProps = {
    size: 'md',
  }
  render() {
    const { size, style, children } = this.props;
    const margin = varibles[`h_spacing_${size}`];
    return (
      <View style={[{ marginLeft: margin, marginRight: margin }, style]}>
        {children}
      </View>
    );
  }
}
