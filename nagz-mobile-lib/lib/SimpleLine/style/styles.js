import {
  StyleSheet,
  Dimensions,
} from 'react-native';
import variables from '../../themes/defaultStyles';

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    width,
    height: variables.line_height_default,
    backgroundColor: variables.fill_base,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  rightContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  centerContainer: {
    width: (width - 74) * 0.7,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  centerInput: {
    textAlign: 'right',
    fontSize: variables.font_size_base,
    width: (width - 74) * 0.7,
    color: variables.color_text_base,
  },
  centerText: {
    width: (width - 74) * 0.7,
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  rightText: {
    fontSize: variables.font_size_base,
  },
  isRequire: {
    width: variables.h_spacing_md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
  },
  title: {
    width: (width - 74) * 0.3,
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  fullTitle: {
    flex: 1,
    fontSize: variables.font_size_base,
    color: variables.color_text_base,
    fontWeight: 'bold',
  },
  isRequireText: {
    color: variables.color_gray,
  },
  rightIconContainer: {
    width: 14,
    height: variables.line_height_default,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 14,
    paddingRight: variables.h_spacing_md,
  },
});

export default styles;
