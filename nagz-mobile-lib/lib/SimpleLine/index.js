import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import styles from './style/styles';
import variables from '../themes/defaultStyles';
import { TextInput } from '../TextInput';

/**
 * 单行文本
 * @name SingleLine
 * @param {bool} [isRequire=false] 显示必填符号
 * @param {bool} [editable=true] 是否可输入
 * @param {bool} [enabled=true] 是否启用
 * @param {bool} [isText=true] 中间部分是否是文本显示，默认显示文本（解决android滑动） 可配合onPress事件，选择显示内容
 * @param {string} label 标题
 * @param {style-object} labelStyle 标题样式，不传则为默认样式
 * @param {string} [placeholder=请输入] 右侧提示信息
 * @param {string} value 右侧显示的值
 * @param {string} defaultValue 右侧显示的默认值
 * @param {element} [rightIconView=<View />] 最右侧显示的图标，大小为（14，14）
 * @param {string} [inputType=default] 键盘类型 (default,numeric,phone-pad,email-address...)
 * @param {func} onBlur 失去焦点时触发的方法，参数为value
 * @param {func} onChange 控制输入变化，必须实现才能正常输入
 * @param {func} onPress 中间部分点击事件
 * @param {func} onRightIconPress 右侧图标点击事件，如果不传则不可点击
 * @param {node} children 行中的内容
 * @param {string} returnKeyType 返回键类型
 * @param {string} autoFocus 返回键类型
 * @param {function} onSubmitEditing 返回键事件
 */
export class SimpleLine extends PureComponent {

  static defaultProps ={
    isRequire: false,
    editable: true,
    enabled: true,
    label: '',
    isText: true,
    placeholder: '请输入',
    value: '',
    rightIconView: <View />,
    onBlur: () => {},
    inputType: 'default',
    onChange: () => {},
    labelStyle: {},
  }
  state={
    titleShowMore: false,
  }

  isRequireRender = isRequire => (
    isRequire ?
      <View style={styles.isRequire}>
        <Image
          resizeMode={'contain'}
          source={require('../Image/icon-asterisk-default.png')}
        />
      </View>
    :
    <View style={styles.isRequire} />
  )

  closeKeyboard = () => {
    if (this.inputRef) {
      this.inputRef.blur();
    }
  }
  focus=() => {
    if (this.inputRef) {
      this.inputRef.focus();
    }
  }
  showMoreTitle=() => {
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: true,
      });
    }
  }
  hideMoreTitle=() => {
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: false,
      });
    }
  }

  render() {
    const {
      isRequire,
      label,
      placeholder,
      editable,
      enabled,
      value,
      defaultValue,
      onChange,
      onPress,
      isText,
      rightIconView,
      onBlur,
      inputType,
      labelStyle,
      onRightIconPress,
      children,
      returnKeyType,
      onSubmitEditing,
      autoFocus,
    } = this.props;
    return (
      <View style={styles.container}>
        <TouchableWithoutFeedback onPressIn={this.showMoreTitle} onPressOut={this.hideMoreTitle}>
          <View style={styles.titleContainer}>
            { this.isRequireRender(isRequire)}
            <Text numberOfLines={1} style={[styles.title, labelStyle]}>{label}</Text>
          </View>
        </TouchableWithoutFeedback>
        <View style={styles.rightContainer}>
          <TouchableOpacity
            activeOpacity={onPress ? 0.8 : 1} onPress={onPress} style={styles.centerContainer}
          >
            {children || (<TextInput
              inputRef={(el) => { this.inputRef = el; }}
              style={styles.centerInput}
              placeholder={placeholder}
              editable={editable}
              onChange={onChange}
              value={value}
              defaultValue={defaultValue}
              inputType={inputType}
              onBlur={onBlur}
              onSubmitEditing={onSubmitEditing}
              returnKeyType={returnKeyType}
              autoFocus={autoFocus}
              onFocus={() => {
                this.setState({
                  closeKeyboard: false,
                });
              }}
            />)
          }
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onRightIconPress}
            style={styles.rightIconContainer}
            activeOpacity={onRightIconPress ? 0.8 : 1}
          >
            {rightIconView}
          </TouchableOpacity>
        </View>
        {this.state.titleShowMore ? <View style={[styles.container, { position: 'absolute', top: 0, overflow: 'hidden' }]}>
          <View style={[styles.titleContainer]}>
            { this.isRequireRender(isRequire)}
            <Text numberOfLines={1} style={[styles.fullTitle, labelStyle]}>{label}</Text>
          </View>
        </View> : null
     }
      </View>);
  }
}

SimpleLine.propTypes = {
  isRequire: PropTypes.bool,
  editable: PropTypes.bool,
  autoFocus: PropTypes.bool,
  enabled: PropTypes.bool,
  isText: PropTypes.bool,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onPress: PropTypes.func,
  rightIconView: PropTypes.node,
  onBlur: PropTypes.func,
  inputType: PropTypes.string,
  onChange: PropTypes.func,
  labelStyle: PropTypes.object,
  onRightIconPress: PropTypes.func,
  children: PropTypes.node,
  returnKeyType: PropTypes.string,
  onSubmitEditing: PropTypes.func,
};

export default SimpleLine;
