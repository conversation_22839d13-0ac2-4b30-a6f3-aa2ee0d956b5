import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import RNAccordion from 'react-native-collapsible/Accordion';
import styles from './styles';

/**
 * 手风琴组件
 * @name Accordion
 * @param {object} [sections={label:'',value:''}] 展示所需数据
 * @param {func} [renderContent] 展开的内容
 * @param {number} [duration=300] 展开的动画时长
 * @param {func} [onChange=false] 点击展开时的回调，会回传index，当index === false时为关闭状态
 */

const imgSrcDown = require('../style/images/icon-arrow-down-small-expand.png');
const imgSrcUp = require('../style/images/icon-arrow-up-small-collapse.png');

class Accordion extends PureComponent {

  static defaultProps = {
    duration: 300,
    sections: { label: '', value: '' },
  }

  state = {
    srcImg: imgSrcDown,
    activeSections: [],

  }

  onChange = (index) => {
    if (!index.length) {
      this.setState({
        srcImg: imgSrcDown,
        activeSections: index,
      });
    } else {
      this.setState({
        srcImg: imgSrcUp,
        activeSections: index,
      });
    }
    if (this.props.onChange) {
      this.props.onChange(index);
    }
  }

  renderHeader = (content, index, isActive, sections) => (<View style={styles.container}>
    <View style={styles.titleContainer}>
      <View style={styles.isRequire} />
      <Text style={styles.title}>{sections[0].label}</Text>
    </View>
    <View style={styles.rightTextContainer}>
      <Text
        style={styles.rightText}
      >{sections[0].value}</Text>
      <View style={styles.rightIconContainer}>
        <Image
          style={styles.img}
          source={this.state.srcImg}
        />
      </View>
    </View>
  </View>)

  render() {
    const {
      renderContent,
      sections,
      duration,
    } = this.props;
    return (
      <RNAccordion
        activeSections={this.state.activeSections}
        underlayColor="transparent"
        sections={[sections]}
        renderHeader={this.renderHeader}
        renderContent={renderContent}
        duration={duration}
        onChange={this.onChange}
        touchableProps={{ activeOpacity: 0.8 }}
        touchableComponent={TouchableOpacity}
      />
    );
  }
}

Accordion.propTypes = {
  sections: PropTypes.object,
  renderContent: PropTypes.func,
  duration: PropTypes.number,
  onChange: PropTypes.func,
};

export default Accordion;
