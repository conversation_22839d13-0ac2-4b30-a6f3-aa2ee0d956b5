import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TouchableWithoutFeedback, Image } from 'react-native';
import PropTypes from 'prop-types';
import AntdPicker from '@ant-design/react-native/lib/picker';
import styles from './style';

const CustomChildren = ({
  showMoreTitle, hideMoreTitle, titleShowMore, customClick, onPress, children, isRequire, extra, label, defaultExtra,
}) => (

  <View style={styles.container}>
    <TouchableWithoutFeedback onPressIn={showMoreTitle} onPressOut={hideMoreTitle}>
      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
        {isRequire ? <View style={styles.isRequire}><Image resizeMode="contain" source={require('../Image/icon-asterisk-default.png')} /></View> : <Text style={styles.isRequireText} />}
        <Text numberOfLines={1} style={styles.leftText}>{label}</Text>
      </View>
    </TouchableWithoutFeedback>
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={customClick || onPress}
    >
      <View style={styles.rightContainer}>

        <View style={styles.rightContent}>
          {children ||
            (<Text numberOfLines={1} style={extra === defaultExtra ? styles.rightDefaultText : styles.rightText}>{extra}</Text>)
          }
        </View>

        <View
          style={styles.rightIcon}
        >
          <Image
            style={{ width: 14, height: 14 }}
            resizeMode="contain"
            source={require('../Image/icon-arrow-right-small-default.png')}
          />
        </View>
      </View>
    </TouchableOpacity>
    {titleShowMore ? <View style={[styles.container, { position: 'absolute', top: 0, overflow: 'hidden' }]}>
      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
        {isRequire ? <View style={styles.isRequire}>
          <Image
            style={{ width: 14, height: 14 }}
            resizeMode="contain"
            source={require('../Image/icon-asterisk-default.png')}
          />
                     </View> : <Text style={styles.isRequireText} />}
        <Text numberOfLines={1} style={styles.fullTitle}>{label}</Text>
      </View>
                     </View> : null
     }
  </View>
);
/**
 * 选择器
 * @name Picker
 * @param {array}  data   数据源，格式为<{value, label, children: Array}>
 * @param {array}  value  选择的值, 格式是[value1, value2, value3], 对应数据源的相应级层value
 * @param {function}  [format=(values) => { return values.join(','); }] 格式化选中值的函数
 * @param {number}  [cols=3] 列数
 * @param {function}  onChange 选中后的回调
 * @param {function}  onPickerChange 每列数据选择变化后的回调函数
 * @param {function}  customClick 右边内容的点击事件的回调
 * @param {object}  itemStyle 每列样式
 * @param {object}  indicatorStyle indicator 样式
 * @param {string}  okText 选中的文案
 * @param {string}  dismissText 取消选中的文案
 * @param {function}  onOk 点击选中时执行的回调
 * @param {function}  onDismiss 点击取消时执行的回调
 * @param {string}  title pickerview的标题
 * @param {string}  [extra=请选择]  picker缺省值
 * @param {bool}  disabled 是否不可用
 * @param {bool}  cascade 是否联动
 * @param {bool}  isRequire 是否必填
 * @param {bool}  label label名
 * @param {node} children 行中的内容
 */
export class Picker extends Component {
  state = {
    value: '',
    titleShowMore: false,
  }
  showMoreTitle=() => {
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: true,
      });
    }
  }
  hideMoreTitle=() => {
    const { label } = this.props;
    if (label.length > 5) {
      this.setState({
        titleShowMore: false,
      });
    }
  }
  render() {
    const {
      data, children, title, value, isRequire, label, format, cols, onChange, customClick, onPickerChange, itemStyle, indicatorStyle, dismissText, okText, onOk, onDismiss, disabled, cascade, extra,
    } = this.props;
    const antdProps = {
      data, title, value, isRequire, label, format, cols, onChange, onPickerChange, itemStyle, indicatorStyle, dismissText, okText, onOk, onDismiss, disabled, cascade, extra,
    };
    return (
      <View>
        <AntdPicker
          {...antdProps}
        >
          <CustomChildren showMoreTitle={this.showMoreTitle} hideMoreTitle={this.hideMoreTitle} titleShowMore={this.state.titleShowMore} defaultExtra={extra} isRequire={isRequire} label={label} value={value} customClick={customClick}>{children}</CustomChildren>
        </AntdPicker>
      </View>
    );
  }
}
Picker.propTypes = {
  data: PropTypes.array,
  value: PropTypes.array,
  format: PropTypes.func,
  cols: PropTypes.number,
  onChange: PropTypes.func,
  customClick: PropTypes.func,
  onPickerChange: PropTypes.func,
  itemStyle: PropTypes.object,
  indicatorStyle: PropTypes.object,
  okText: PropTypes.string,
  dismissText: PropTypes.string,
  onOk: PropTypes.func,
  onDismiss: PropTypes.func,
  title: PropTypes.string,
  disabled: PropTypes.bool,
  cascade: PropTypes.bool,
  extra: PropTypes.string,
  isRequire: PropTypes.bool,
  label: PropTypes.string,
  children: PropTypes.node,
};
Picker.defaultProps = {
  isRequire: false,
  label: '选择器',
  extra: '请选择',
  customClick: null,
};
