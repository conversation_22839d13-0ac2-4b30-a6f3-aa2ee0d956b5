import {
  StyleSheet,
  Dimensions,
} from 'react-native';
import variables from '../../themes/defaultStyles';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
container: {
  width,
  height: variables.line_height_default,
  backgroundColor: variables.fill_base,
  alignItems: 'center',
  justifyContent: 'space-between',
  flexDirection: 'row',
},
leftText: {
  width: (width - 74) * 0.3,
  textAlign: 'left',
  fontSize: variables.font_size_base,
  color: variables.color_text_base,
  fontWeight: 'bold',
},
fullTitle: {
  flex: 1,
  fontSize: variables.font_size_base,
  color: variables.color_text_base,
  fontWeight: 'bold',
},
rightContainer: {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
},
rightContent: {
  width: (width - 74) * 0.7,
  height: variables.line_height_default,
  alignItems: 'center',
  justifyContent: 'center',
},
rightText: {
  width: (width - 74) * 0.7,
  textAlign: 'right',
  fontSize: variables.font_size_base,
  color: variables.color_text_base,
},
rightDefaultText: {
  width: (width - 74) * 0.7,
  textAlign: 'right',
  fontSize: variables.font_size_base,
  color: variables.color_text_placeholder,
},
rightIcon: {
  width: 14,
  height: variables.line_height_default,
  alignItems: 'center',
  justifyContent: 'center',
  paddingLeft: 14,
  paddingRight: variables.h_spacing_md,

},
isRequireText: {
  width: variables.h_spacing_md,
  alignItems: 'center',
  justifyContent: 'center',
  color: variables.color_gray,
  textAlign: 'center',
},
isRequire: {
  width: variables.h_spacing_md,
  alignItems: 'center',
  justifyContent: 'center',
},
});
export default styles;
