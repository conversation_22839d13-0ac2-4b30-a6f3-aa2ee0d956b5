
import React, { PureComponent } from 'react';
import {
  View,
  TouchableOpacity,
  Keyboard,
  Image,
} from 'react-native';
import { Picker, SimpleLine, WhiteSpace, MultiLine, TextInput } from '../../../../lib/index';

const base64Icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAQAAACSR7JhAAADtUlEQVR4Ac3YA2Bj6QLH0XPT1Fzbtm29tW3btm3bfLZtv7e2ObZnms7d8Uw098tuetPzrxv8wiISrtVudrG2JXQZ4VOv+qUfmqCGGl1mqLhoA52oZlb0mrjsnhKpgeUNEs91Z0pd1kvihA3ULGVHiQO2narKSHKkEMulm9VgUyE60s1aWoMQUbpZOWE+kaqs4eLEjdIlZTcFZB0ndc1+lhB1lZrIuk5P2aib1NBpZaL+JaOGIt0ls47SKzLC7CqrlGF6RZ09HGoNy1lYl2aRSWL5GuzqWU1KafRdoRp0iOQEiDzgZPnG6DbldcomadViflnl/cL93tOoVbsOLVM2jylvdWjXolWX1hmfZbGR/wjypDjFLSZIRov09BgYmtUqPQPlQrPapecLgTIy0jMgPKtTeob2zWtrGH3xvjUkPCtNg/tm1rjwrMa+mdUkPd3hWbH0jArPGiU9ufCsNNWFZ40wpwn+62/66R2RUtoso1OB34tnLOcy7YB1fUdc9e0q3yru8PGM773vXsuZ5YIZX+5xmHwHGVvlrGPN6ZSiP1smOsMMde40wKv2VmwPPVXNut4sVpUreZiLBHi0qln/VQeI/LTMYXpsJtFiclUN+5HVZazim+Ky+7sAvxWnvjXrJFneVtLWLyPJu9K3cXLWeOlbMTlrIelbMDlrLenrjEQOtIF+fuI9xRp9ZBFp6+b6WT8RrxEpdK64BuvHgDk+vUy+b5hYk6zfyfs051gRoNO1usU12WWRWL73/MMEy9pMi9qIrR4ZpV16Rrvduxazmy1FSvuFXRkqTnE7m2kdb5U8xGjLw/spRr1uTov4uOgQE+0N/DvFrG/Jt7i/FzwxbA9kDanhf2w+t4V97G8lrT7wc08aA2QNUkuTfW/KimT01wdlfK4yEw030VfT0RtZbzjeMprNq8m8tnSTASrTLti64oBNdpmMQm0eEwvfPwRbUBywG5TzjPCsdwk3IeAXjQblLCoXnDVeoAz6SfJNk5TTzytCNZk/POtTSV40NwOFWzw86wNJRpubpXsn60NJFlHeqlYRbslqZm2jnEZ3qcSKgm0kTli3zZVS7y/iivZTweYXJ26Y+RTbV1zh3hYkgyFGSTKPfRVbRqWWVReaxYeSLarYv1Qqsmh1s95S7G+eEWK0f3jYKTbV6bOwepjfhtafsvUsqrQvrGC8YhmnO9cSCk3yuY984F1vesdHYhWJ5FvASlacshUsajFt2mUM9pqzvKGcyNJW0arTKN1GGGzQlH0tXwLDgQTurS8eIQAAAABJRU5ErkJggg==';

/**
 * 快速复制demo模板
 */
const seasons = [
  [
    {
      label: '湖北省武汉市',
      value: '2013',
    },
    {
      label: '---',
      value: '',
    },
    {
      label: '2014',
      value: '2014',
    },
    {
      label: '很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长',
      value: '很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长',
    },
    {
      label: '4333333333333333333333333',
      value: '夏',
    },
  ],
];
class PickerDemo extends PureComponent {
  state = {
    val: [],
    sValue: '',
    value1: '',
  };
  onChange1 = (value1) => {
    this.setState({
      value1,
    });
  }

  render() {
    return (
      <View flex={1}>
        <Picker
          label={'测试文本长度专用测试文本长度专用测试文本长度专用'}
          data={seasons}
          title="选择季节"
          cascade={false}
          isRequire
          extra="请选择(可选)"
          onOk={(v) => { console.log(v, 1); this.setState({ sValue: v[0] }); }}
        >
          <TextInput
            placeholder="编号"
            // editable={false}
            value={this.state.sValue}
            onChange={(v) => { console.log(v, 2); this.setState({ sValue: v }); }}
            style={{ width: '100%' }}
          />
        </Picker>

        <Picker
          label={'测试文本长度专用测试文本长度专用测试文本长度专用'}
          data={seasons}
          title="选择季节"
          cascade={false}
          extra="请选择(可选)"
          value={this.state.val}
          onOk={v => this.setState({ val: v })}
        />

        <MultiLine />

        <WhiteSpace size="sm" />

        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
        // 关闭键盘
            Keyboard.dismiss();
          }}
          style={{ flex: 1 }}
        >
          <SimpleLine
            label={'输入框输入框数字输入框'}
            value={this.state.value1}
            onChange={this.onChange1}
            rightIconView={
              <Image
                style={{ width: 14, height: 14 }}
                source={{ uri: base64Icon }}
              />}
          />
        </TouchableOpacity>
      </View>
    );
  }
  }
export default PickerDemo;
