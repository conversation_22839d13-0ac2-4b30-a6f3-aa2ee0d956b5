import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import styles from './style/styles';
import NavigatorInfo from '../../NavigatorInfo';

class Main extends PureComponent {

  constructor(props) {
    super(props);
    this.state = {
      data: Object.keys(NavigatorInfo),
    };
  }

  renderItem = item =>
    <TouchableOpacity
      onPress={() => { this.props.navigation.navigate(item.item); }}
      style={styles.itemContainer}
    >
      <Text>{item.item}</Text>
    </TouchableOpacity>

  render() {
    return (
      <View style={styles.container}>
        <FlatList
          data={this.state.data}
          renderItem={this.renderItem}
          keyExtractor={item => item}
        />
      </View>
    );
  }
}

Main.propTypes = {
  navigation: PropTypes.object,
};

export default Main;
