import React from 'react';
import { WingBlank, WhiteSpace, Button, Toast } from '../../../../lib/index';

export default class ToastDemo extends React.Component {
  componentDidMount() {
    Toast.loading('Loading...', 30, () => {
      console.log('Load complete !!!');
    });
    setTimeout(() => {
      Toast.hide();
    }, 3000);
  }
  showToast=() => { Toast.info('text only', 1); }
  showToastNoMask = () => { Toast.info('without mask', 3, null, false); }
  successToast = () => { Toast.success('Load success !!!', 1); }
  failToast = () => { Toast.fail('Load failed !!!', 1); }
  offline = () => { Toast.offline('Network connection failed !!!', 1); }
  loadingToast = () => { Toast.loading('Loading...', 1); }
  render() {
    return (
      <WingBlank>
        <WhiteSpace />
        <Button onPress={this.showToast}>Toast.info</Button>
        <WhiteSpace />
        <Button onPress={this.showToastNoMask}>Toast.info without mask</Button>
        <WhiteSpace />
        <Button onPress={this.successToast}>Toast.success</Button>
        <WhiteSpace />
        <Button onPress={this.failToast}>Toast.fail</Button>
        <WhiteSpace />
        <Button onPress={this.offline}>Toast.offline</Button>
        <WhiteSpace />
        <Button onPress={this.loadingToast}>Toast.loading</Button>
        <WhiteSpace />
      </WingBlank>
    );
  }
}
