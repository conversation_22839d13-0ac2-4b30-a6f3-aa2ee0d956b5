import React, { PureComponent } from 'react';
import {
  View,
  StyleSheet,
  Text,
} from 'react-native';
import { Switch } from '../../../../lib/index';
import style from './style';

const styles = StyleSheet.create(style);

class ButtonDemo extends PureComponent {
  state = {
    value: true,
  }

  onChange = () => {
    this.setState({
      value: !this.state.value,
    });
  }

  render() {
    return (<View
      style={styles.container}
    >
      <View>
        <Text>
        Normal Switch
        </Text>
        <Switch
          checked={this.state.value}
          onChange={this.onChange}
        />
        <Switch
          checked={!this.state.value}
          onChange={this.onChange}
        />
      </View>
      <View>
        <Text>
          Disabled Switch
        </Text>
        <Switch
          checked={this.state.value}
          disabled
        />
        <Switch
          checked={!this.state.value}
          disabled
        />
      </View>
    </View>);
  }
}

export default ButtonDemo;
