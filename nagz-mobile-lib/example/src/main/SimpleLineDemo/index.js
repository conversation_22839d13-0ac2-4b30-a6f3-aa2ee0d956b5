import React, { PureComponent } from 'react';
import {
  Image,
  TouchableOpacity,
  Keyboard,
  View,
} from 'react-native';
import { SimpleLine, Toast, TextInput, AnimatedImage } from '../../../../lib/index';

const base64Icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAQAAACSR7JhAAADtUlEQVR4Ac3YA2Bj6QLH0XPT1Fzbtm29tW3btm3bfLZtv7e2ObZnms7d8Uw098tuetPzrxv8wiISrtVudrG2JXQZ4VOv+qUfmqCGGl1mqLhoA52oZlb0mrjsnhKpgeUNEs91Z0pd1kvihA3ULGVHiQO2narKSHKkEMulm9VgUyE60s1aWoMQUbpZOWE+kaqs4eLEjdIlZTcFZB0ndc1+lhB1lZrIuk5P2aib1NBpZaL+JaOGIt0ls47SKzLC7CqrlGF6RZ09HGoNy1lYl2aRSWL5GuzqWU1KafRdoRp0iOQEiDzgZPnG6DbldcomadViflnl/cL93tOoVbsOLVM2jylvdWjXolWX1hmfZbGR/wjypDjFLSZIRov09BgYmtUqPQPlQrPapecLgTIy0jMgPKtTeob2zWtrGH3xvjUkPCtNg/tm1rjwrMa+mdUkPd3hWbH0jArPGiU9ufCsNNWFZ40wpwn+62/66R2RUtoso1OB34tnLOcy7YB1fUdc9e0q3yru8PGM773vXsuZ5YIZX+5xmHwHGVvlrGPN6ZSiP1smOsMMde40wKv2VmwPPVXNut4sVpUreZiLBHi0qln/VQeI/LTMYXpsJtFiclUN+5HVZazim+Ky+7sAvxWnvjXrJFneVtLWLyPJu9K3cXLWeOlbMTlrIelbMDlrLenrjEQOtIF+fuI9xRp9ZBFp6+b6WT8RrxEpdK64BuvHgDk+vUy+b5hYk6zfyfs051gRoNO1usU12WWRWL73/MMEy9pMi9qIrR4ZpV16Rrvduxazmy1FSvuFXRkqTnE7m2kdb5U8xGjLw/spRr1uTov4uOgQE+0N/DvFrG/Jt7i/FzwxbA9kDanhf2w+t4V97G8lrT7wc08aA2QNUkuTfW/KimT01wdlfK4yEw030VfT0RtZbzjeMprNq8m8tnSTASrTLti64oBNdpmMQm0eEwvfPwRbUBywG5TzjPCsdwk3IeAXjQblLCoXnDVeoAz6SfJNk5TTzytCNZk/POtTSV40NwOFWzw86wNJRpubpXsn60NJFlHeqlYRbslqZm2jnEZ3qcSKgm0kTli3zZVS7y/iivZTweYXJ26Y+RTbV1zh3hYkgyFGSTKPfRVbRqWWVReaxYeSLarYv1Qqsmh1s95S7G+eEWK0f3jYKTbV6bOwepjfhtafsvUsqrQvrGC8YhmnO9cSCk3yuY984F1vesdHYhWJ5FvASlacshUsajFt2mUM9pqzvKGcyNJW0arTKN1GGGzQlH0tXwLDgQTurS8eIQAAAABJRU5ErkJggg==';

class TextInputDemo extends PureComponent {

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      value1: '',
      value2: '',
      value3: '',
      value4: '',
      value5: '',
      test: 'a',
    };
  }

  onChange1 = (value1) => {
    this.setState({
      value1,
    });
  }

  onChange2 = (value2) => {
    this.setState({
      value2,
    });
  }

  onChange3 = (value3) => {
    this.setState({
      value3,
    });
  }

  onChange4 = (value4) => {
    this.setState({
      value4,
    });
  }

  onChange5 = (value5) => {
    this.setState({
      value5,
    });
  }

  render() {
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
        // 关闭键盘
          Keyboard.dismiss();
        }}
        style={{ flex: 1 }}
      >
        <SimpleLine
          label={'输入框输入框数字输入框'}
          value={this.state.value1}
          onChange={this.onChange1}
        />
        <SimpleLine
          label={'带图标输234'}
          value={this.state.value2}
          onChange={this.onChange2}
          rightIconView={
            <Image
              style={{ width: 14, height: 14 }}
              source={{ uri: base64Icon }}
            />}
        />
        <SimpleLine
          label={'数字输入框数'}
          inputType="numeric"
          value={this.state.value3}
          onChange={this.onChange3}
          rightIconView={
            <AnimatedImage
              onPress={() => { this.setState({ value3: '123456' }); }}
              style={{ justifyContent: 'center', alignItems: 'center', width: 34, height: 50 }}
              source={require('../../../lib/Image/icon-awsome-button.png')}
            />
          }
        />
        <SimpleLine
          label={'必填输入框'}
          isRequire
          value={this.state.value4}
          onChange={this.onChange4}
          rightIconView={
            <Image
              style={{ width: 14, height: 14 }}
              source={{ uri: base64Icon }}
            />
         }
          onBlur={(value) => {
            this.setState({
              value,
            });
          }}
        />
        <SimpleLine
          value={this.state.value5}
          label={'图标可点击'}
          onChange={this.onChange5}
          rightIconView={
            <Image
              style={{ width: 14, height: 14 }}
              source={{ uri: base64Icon }}
            />
        }
          onRightIconPress={() => {
            Toast.info('test', 1);
          }}
        />
        <SimpleLine
          label={'不可输入'}
          value={'这是显示信息'}
          editable={false}
        />
        <SimpleLine
          value={this.state.value}
          label={'可点击'}
          onPress={() => {
            this.setState({
              test: 'b',
              value: '2018-01-17弹性盒子元素的侧轴弹性盒子元素的侧轴',
            });
          }}
          rightIconView={
            <Image
              style={{ width: 14, height: 14 }}
              source={{ uri: base64Icon }}
            />
         }
        />
        <SimpleLine
          value={this.state.value}
          label={'可点击'}
          rightIconView={
            <Image
              style={{ width: 14, height: 14 }}
              source={{ uri: base64Icon }}
            />
         }
        >
          <View style={{ flexDirection: 'row' }}>
            <TextInput
              placeholder="标识"
              defaultValue="agz"
              style={{ width: 100 }}
            />
            <TextInput
              placeholder="编号"
              defaultValue="0001"
              style={{ width: 100 }}
            />
          </View>
        </SimpleLine>
      </TouchableOpacity>);
  }
}

export default TextInputDemo;
