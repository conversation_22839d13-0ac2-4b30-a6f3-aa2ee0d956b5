import React, { PureComponent } from 'react';
import {
  View,
  Button,
  Text,
} from 'react-native';
import { ActionSheet } from '../../../../lib/index';

const wrapProps = {
  onTouchStart: e => e.preventDefault(),
};

class ActionSheetDemo extends PureComponent {

  state = {
    clicked: '',
  }

  showActionSheet = () => {
    const BUTTONS = ['Operation1', 'Operation2', 'Operation3', 'Delete', 'Cancel'];
    ActionSheet.showActionSheetWithOptions({
      options: BUTTONS,
      cancelButtonIndex: BUTTONS.length - 1,
      destructiveButtonIndex: BUTTONS.length - 2,
      message: 'I am description, description, description',
      'data-seed': 'logId',
      wrapProps,
    },
    (buttonIndex) => {
      this.setState({ clicked: BUTTONS[buttonIndex] });
    });
  }

  render() {
    return (
      <View>
        <Button
          onPress={this.showActionSheet}
          title={'打开菜单'}
        />
        <Text style={{ fontSize: 16, textAlign: 'center' }}>{`选择了:${this.state.clicked}`}</Text>
      </View>
    );
  }
}

export default ActionSheetDemo;

