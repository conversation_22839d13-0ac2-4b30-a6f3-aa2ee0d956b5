import React, { PureComponent } from 'react';
import {
  View, Text, Dimensions, Button, TouchableOpacity,
} from 'react-native';
import { Modal } from '../../../../lib/index';

const width = Dimensions.get('window').width;
const height = Dimensions.get('window').height;
class ModalDemo extends PureComponent {
  state={
    isVisible: false,
    toastIsVisible: false,
  }
  onSwipe=() => {
    console.log('swipe');
    this.setState({
      isVisible: false,
    });
  }
  showModal=() => {
    this.setState({
      isVisible: true,
    });
  }
  render() {
    return (
      <View>
        <Button
          onPress={this.showModal}
          title="show Modal"
          color="#841584"
        />
        <Modal
          isVisible={this.state.isVisible} onSwipe={this.onSwipe}
          animationIn={{ from: { opacity: 0 }, to: { opacity: 1 } }}
          style={{ backgroundColor: 'rgba(255,0,0,.2)' }}
          toast={{
            isVisible: this.state.toastIsVisible,
            onClose: () => { this.setState({ toastIsVisible: false }); },
            content: 'loading...',
            type: 'loading',
          }}
        >
          <View style={{ width: 200, height: 200, backgroundColor: '#fff' }}>
            <Text>123123</Text>
            <Text>123123</Text>
            <TouchableOpacity onPress={() => { this.setState({ toastIsVisible: true }); }}>
              <Text>onclick show toast</Text>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    );
  }
  }
export default ModalDemo;
