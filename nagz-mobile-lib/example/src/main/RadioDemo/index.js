import React from 'react';
import { Radio, List } from '../../../../lib/index';

const RadioItem = Radio.RadioItem;

export default class RadioDemo extends React.Component {
  state = {
    dataValue: 3,
  };

  onChange=(value) => {
    this.setState({ dataValue: value });
  }

  render() {
    const data = [
      { value: 0, label: '可选', disabled: false },
      { value: 1, label: '可选', disabled: false },
      { value: 3, label: '禁选选中', disabled: true },
      { value: 2, label: '禁选未选中', disabled: true },
    ];
    return (
      <List renderHeader={() => 'RadioItem demo'}>
        {data.map(i => (
          <RadioItem key={i.value} checked={this.state.dataValue === i.value} disabled={i.disabled} onChange={() => this.onChange(i.value)}>
            {i.label}
          </RadioItem>
      ))}
      </List>
    );
  }
}
