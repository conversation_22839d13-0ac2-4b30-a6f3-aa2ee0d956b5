import React from 'react';
import { View, StyleSheet } from 'react-native';
import { WingBlank } from '../../../../lib/index';
import style from './style';

const styles = StyleSheet.create(style);

class WingBlankDemo extends React.Component {

  render() {
    return (
      <View>
        <WingBlank size="sm">
          <View style={styles.section1} />
        </WingBlank>
        <WingBlank>
          <View style={styles.section1} />
        </WingBlank>
        <WingBlank size="lg">
          <View style={styles.section1} />
        </WingBlank>
      </View>
    );
  }
}

export default WingBlankDemo;
