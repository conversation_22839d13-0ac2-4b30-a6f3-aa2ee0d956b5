import React from 'react';
import { View, StyleSheet } from 'react-native';
import { WhiteSpace } from '../../../../lib/index';
import style from './style';

const styles = StyleSheet.create(style);

class WhiteSpaceDemo extends React.Component {

  render() {
    return (
      <View>
        <View style={styles.section1} />
        <WhiteSpace size="xs" />
        <View style={styles.section1} />
        <WhiteSpace size="sm" />
        <View style={styles.section1} />
        <WhiteSpace />
        <View style={styles.section1} />
        <WhiteSpace size="lg" />
        <View style={styles.section1} />
        <WhiteSpace size="xl" />
        <View style={styles.section1} />
      </View>
    );
  }
}

export default WhiteSpaceDemo;
