import React, { PureComponent } from 'react';
import {
  View,
  Text,
} from 'react-native';
import { Accordion } from '../../../../lib/index';
import styles from './styles';

class AccordionDemo extends PureComponent {

  render() {
    return (
      <View>
        <Accordion
          renderContent={() => <View style={styles.container}><Text>test</Text></View>}
          sections={{ label: 'title', value: 'value' }}
          duration={300}
          onChange={(index) => {
            if (index === false) {
              console.log('关闭手风琴');
            } else {
              console.log('打开手风琴');
            }
          }}
        />
      </View>
    );
  }
}

export default AccordionDemo;

