import React from 'react';
import { Text, View } from 'react-native';
import { TextInput } from '../../../../lib/index';

class TextInputDemo extends React.Component {

  state = {
    value: '123',
  }

  onChange = (value) => {
    this.setState({
      value,
    });
  }

  onBlur = (value) => {
    this.setState({
      value,
    });
  }
  render() {
    return (
      <View>
        <Text>{this.state.value}</Text>
        <Text>
          不受控
        </Text>
        <TextInput defaultValue={this.state.value} />
        <Text>
          受控
        </Text>
        <TextInput value={this.state.value} onChange={this.onChange} />
        <Text>
          onBlur
        </Text>
        <TextInput defaultValue={this.state.value} onBlur={this.onBlur} />
      </View>
    );
  }
}

export default TextInputDemo;
