import React, { PureComponent } from 'react';
import {
  View,
} from 'react-native';
import { MultiLine } from '../../../../lib/index';

class MultiLineDemo extends PureComponent {
  state = { value: '' }
  onChange=(text) => {
    this.setState({
      value: text,
    });
  }
  render() {
    return (
      <MultiLine onChange={this.onChange} value={this.state.value} />
    );
  }
}

export default MultiLineDemo;
