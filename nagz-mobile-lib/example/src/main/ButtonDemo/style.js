import { Dimensions } from 'react-native';
import { variables } from '../../../../lib/index';

const { width } = Dimensions.get('window');

export default {
  buttonContainer: {
    flexDirection: 'row',
    shadowColor: variables.docked_button_shadow_color,
    shadowOpacity: variables.docked_button_shadow_opacity,
    shadowOffset: {
      width: variables.docked_button_shadow_width,
      height: variables.docked_button_shadow_height,
    },
    shadowRadius: variables.docked_button_shadow_radius,
  },
  buttonLeft: {
    width: (width / 2) - variables.h_spacing_sm,
    borderRightWidth: 0,
    shadowRadius: 0,
  },
  buttonRight: {
    width: (width / 2) - variables.h_spacing_sm,
    borderLeftWidth: 0,
    shadowRadius: 0,
  },
};
