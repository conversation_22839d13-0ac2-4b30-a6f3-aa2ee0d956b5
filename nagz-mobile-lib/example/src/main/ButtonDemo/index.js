import React, { PureComponent } from 'react';
import { Text, StyleSheet, View } from 'react-native';
import { Button, WingBlank, WhiteSpace } from '../../../../lib/index';
import style from './style';

const styles = StyleSheet.create(style);

class ButtonDemo extends PureComponent {
  state = {
    count: 0,
  }

  onPress = () => {
    this.setState({
      count: this.state.count + 1,
    });
  }

  render() {
    return (<WingBlank size="sm">
      <View>
        <Text>
          click {this.state.count} times.
        </Text>
      </View>
      <View>
        <WhiteSpace />
        <Button onPress={this.onPress}>
          新增
          </Button>
        <WhiteSpace />

        <Button onPress={this.onPress} disabled>
          新增
          </Button>
        <WhiteSpace />

        <Button loading onPress={this.onPress}>
          新增
          </Button>
      </View>
      <WhiteSpace />
      <View
        style={styles.buttonContainer}
      >
        <Button
          prefixIcon={<Text>+</Text>}
          style={{ container: styles.buttonLeft }}
          onPress={this.onPress}
        >
        新增
        </Button>
        <Button
          style={{ container: styles.buttonRight }}
          onPress={this.onPress}
        >
        保存并新增
        </Button>
      </View>
      <WhiteSpace />
      <Button
        onPress={this.onPress}
        inline
      >
        保存并新增
      </Button>
      <Button
        onPress={this.onPress}
        inline
        disabled
      >
        保存并新增
      </Button>
      <Button
        onPress={this.onPress}
        inline
        prefixIcon={<Text>+</Text>}
      >
        保存
      </Button>
    </WingBlank>);
  }
}

export default ButtonDemo;
