import React from 'react';
import { View, Text } from 'react-native';
import { CheckBox, List } from '../../../../lib/index';

const CheckboxItem = CheckBox.CheckBoxItem;

export default class CheckBoxDemo extends React.Component {
  state={
    data: [
      { value: 0, label: '可选选中', checked: true, disabled: false, extra: <Text style={{ color: 'red' }}>Text</Text> },
      { value: 1, label: '可选未选中', checked: false, disabled: false, extra: 'test' },
      { value: 2, label: '禁选未选中', checked: false, disabled: true },
      { value: 3, label: '禁选选中', checked: true, disabled: true },
    ],
  }
  onChange = (index) => {
    const data = [...this.state.data];
    data[index].checked = !data[index].checked;
    this.setState({ data });
  }
  render() {
    return (<View>
      <List
        renderHeader={() => 'CheckboxItem demo'}
      >
        {this.state.data.map((i, index) => (
          <CheckboxItem
            key={i.value}
            checked={i.checked}
            disabled={i.disabled}
            onChange={() => this.onChange(index)}
            extra={i.extra || ''}
          >
            {i.label}
          </CheckboxItem>
        ))}
      </List>
    </View>);
  }
}
