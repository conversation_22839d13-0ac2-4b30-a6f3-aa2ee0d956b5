import app from './main/app/index';

import SimpleLineDemo from './main/SimpleLineDemo';
import MultiLineDemo from './main/MultiLineDemo';
import SwitchDemo from './main/SwitchDemo';
import WingBlankDemo from './main/WingBlankDemo';
import WhiteSpaceDemo from './main/WhiteSpaceDemo';
import ActionSheetDemo from './main/ActionSheetDemo';
import CheckBoxDemo from './main/CheckBoxDemo';
import ModalDemo from './main/ModalDemo';
import ListDemo from './main/ListDemo';
import ButtonDemo from './main/ButtonDemo';
import PickerDemo from './main/PickerDemo';
import RadioDemo from './main/RadioDemo';
import AccordionDemo from './main/AccordionDemo';
import ToastDemo from './main/ToastDemo';
import DatePickerDemo from './main/DatePickerDemo';
import TextInputDemo from './main/TextInputDemo';

// 主页面
export const MAIN_NAME = 'app';
export const MAIN = app;

export default {

  MultiLine: MultiLineDemo, // 多行文本
  SimpleLine: SimpleLineDemo,  // 单行文本
  Switch: SwitchDemo,  // 开关
  'WhiteSpace(上下留白)': WhiteSpaceDemo,  // 上下留白
  'WingBlank(两翼留白)': WingBlankDemo,  // 两翼留白
  'ActionSheet(菜单)': ActionSheetDemo,  // 两翼留白
  CheckBox: CheckBoxDemo, // 复选框
  Modal: ModalDemo, // 弹出层
  List: ListDemo, // 列表
  Button: ButtonDemo,  // 两翼留白
  Picker: PickerDemo,
  Radio: RadioDemo, // 单选
  'Accordion(手风琴)': AccordionDemo, // 手风琴
  Toast: ToastDemo, // 轻提示
  DatePicker: DatePickerDemo,
  TextInput: TextInputDemo,
};
