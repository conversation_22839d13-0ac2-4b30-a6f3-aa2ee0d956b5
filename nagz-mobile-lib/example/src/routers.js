import { StackNavigator } from 'react-navigation';
import CardStackStyleInterpolator from 'react-navigation/src/views/CardStack/CardStackStyleInterpolator'; // 页面跳转样式
import NavigatorInfo, { MAIN } from './NavigatorInfo';
import config from '../package.json'; // 从package.json文件中获取版本号

const TransitionConfiguration = () => ({
  screenInterpolator: (sceneProps) => {
    const { scene } = sceneProps;
    const { route } = scene;
    const params = route.params || {};
    const transition = params.transition || 'forHorizontal';
    return CardStackStyleInterpolator[transition](sceneProps);
  },
});

const routes = {};

const StackOptions = ({ navigation }, headerTitle) => {
  const headerStyle = { backgroundColor: '#fff', borderBottomWidth: 1, borderBottomColor: 'rgb(241,241,242)' };
  const headerTitleStyle = { fontSize: 18, color: 'rgba(0,0,0,0.8)', fontWeight: 'normal' };
  const headerBackTitle = false;
  const gesturesEnabled = true;
  return { headerStyle, headerTitleStyle, headerBackTitle, gesturesEnabled, headerTitle };
};
// 主页面
routes.app = {
  screen: MAIN,
  navigationOptions: ({ navigation }) => StackOptions({ navigation }, `爱工作组件库 v${config.version}`),
};

Object.keys(NavigatorInfo)
  .forEach((displayName) => {
    routes[displayName] = {
      screen: NavigatorInfo[displayName],
      navigationOptions: ({ navigation }) => StackOptions({ navigation }, displayName),
    };
  });

const Routers = StackNavigator(routes, {
  headerMode: 'screen',
  transitionConfig: TransitionConfiguration,
});

export default Routers;
