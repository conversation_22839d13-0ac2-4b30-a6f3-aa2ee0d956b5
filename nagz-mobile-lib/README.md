### 爱工作组件库(nagz-mobile-lib) 2018-01-15

* 开发过程

```
git clone -<NAME_EMAIL>:WFDEV/NAGZ_Mobile_Lib.git

npm install 

or

yarn install
```

* 文档生成
是用[documentation.js](https://github.com/documentationjs/documentation)生成
使用方法：

```
$ npm install -g documentation
documentation serve ./lib/**
```

格式： /** @param {类型} 变量名 描述 **/

* 测试发布到本地npm的库

添加本地npm库，这里建议使用`nrm`来管理npm源

```
npm install -g nrm
nrm add <registry> http://mongo01.demo.com:4387
nrm use <registry>
```

也可以直接添加npm本地库地址

```
npm set <registry> http://mongo01.demo.com:4387
```
然后新建一个项目（这里建议使用和移动端项目同样的版本）

```
react-native init --version 0.50.3
yarn add nagz-mobile-lib
```

#### 文件组织结构

```
.
├── __tests__                       # 测试目录
├── android                         # 安卓项目目录
├── ios                             # ios项目目录
├── lib                             # lib source code
│   ├── demo                        # demo目录
│   │   ├── style                   # demo样式目录
│   │   │   ├── demo.js             # demo样式
│   │   ├── demo.js                 # demo组件
│   ├── themes                      # 公共样式目录
│   │   ├── defaultStyles.js        # 公共样式
├── node_modules                    # 依赖包
├── src                             # Application source code
│   ├── common                      # 通用文件
│   ├── main                        # 项目主入口目录
│   │   ├── style                   # 样式目录
│   │   │   ├── styles.js           # 样式
│   ├── app                         # 项目主页目录
│   │   ├── style                   # 样式目录
│   │   │   ├── styles.js           # 样式
│   │   ├── index.js                # 主页
│   ├── TextInputDemo               # 单行文本演示目录
│   │   ├── style                   # 样式目录
│   │   │   ├── styles.js           # 样式
│   │   ├── index.js                # 单行文本演示
│   ├── app.js                      # 项目入口
│   ├── NavigatorInfo.js            # 导航信息汇总文件 （组件演示前需进行配置）
│   ├── routers.js                  # 导航配置
└── index.js                        # react-native 入口
└── .eslintignore                   # eslint 配置文件
└── .eslintrc                       # eslint 配置文件
└── .gitignore                      # git 配置文件
└── .npmignore                      # npm 发布配置文件
└── package.json                    # npm 配置文件
└── README.md                       # 帮助文档文件
```

#### 开发需知

1.考虑到工作量的问题，组件库中各个组件的实现优先考虑直接引用`antd` > 封装`antd` > 改`antd`的源码 (抄) > 自己实现

2.文档, 先写齐`propTypes`和`defaultProps`， 之后可以通过工具生成文档。

#### 开发流程

* `fork`代码 => `merge requests` => `merge` => `dev` merge to `master` => `master: npm publish`

* 在`lib`中新建组件完成后，需在`NavigatorInfo.js`文件中进行配置，例如
  
  ```
  import app from './main/index';
  import TextInputDemo from './main/TextInputDemo';

  // 主页面
  export const MAIN_NAME = 'app';
  export const MAIN = app;

  export default {
    TextInput: TextInputDemo,  // 单行文本
  };

  ```

* 在首页列表出现新增的`TextInput`项，点击即会跳转到`TextInput`组件

#### 开发规范

1.样式与组件分离

2.编写样式时尽量使用公共样式

3.eslint,airbnb


