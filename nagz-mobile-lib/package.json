{"_args": [["nagz-mobile-lib@0.0.54", "/Users/<USER>/work/NAGZ_mobile_RN"]], "_from": "nagz-mobile-lib@0.0.54", "_id": "nagz-mobile-lib@0.0.54", "_inBundle": false, "_integrity": "sha512-E88v6f2wHmRH2PjfrNd1y/R42k8Mw9kKO7qIO2nyU/qSrUqbg+jexntb+SmOR1TtHSr2LjxiNmNdVPONwbvzHw==", "_location": "/nagz-mobile-lib", "_phantomChildren": {"loose-envify": "1.4.0", "object-assign": "4.1.1", "react-is": "16.11.0"}, "_requested": {"type": "version", "registry": true, "raw": "nagz-mobile-lib@0.0.54", "name": "nagz-mobile-lib", "escapedName": "nagz-mobile-lib", "rawSpec": "0.0.54", "saveSpec": null, "fetchSpec": "0.0.54"}, "_requiredBy": ["/"], "_resolved": "http://mongo01.demo.com:4873/nagz-mobile-lib/-/nagz-mobile-lib-0.0.54.tgz", "_spec": "0.0.54", "_where": "/Users/<USER>/work/NAGZ_mobile_RN", "author": {"name": "hongtoo.co"}, "dependencies": {"react-native-animatable": "^1.3.2", "react-native-collapsible": "^1.4.0", "react-native-modal": "^9.0.0"}, "description": "* 开发\b过程", "devDependencies": {"@ant-design/react-native": "^3.1.5", "babel-core": "7.0.0-bridge.0", "babel-eslint": "7.1.1", "babel-jest": "24.5.0", "documentation": "5.3.5", "eslint": "3.11.1", "eslint-config-airbnb": "13.0.0", "eslint-config-airbnb-base": "10.0.1", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "2.2.3", "eslint-plugin-react": "6.6.0", "jest": "24.5.0", "metro-react-native-babel-preset": "0.53.1", "prop-types": "16.6.3", "react": "16.6.3", "react-native": "^0.58.6", "react-navigation": "^1.0.0-beta.26", "react-test-renderer": "16.6.3"}, "jest": {"preset": "react-native"}, "main": "lib/index.js", "name": "nagz-mobile-lib", "scripts": {"doc": "node node_modules/documentation/bin/documentation.js serve --watch lib/index.js", "start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest"}, "version": "0.0.54"}